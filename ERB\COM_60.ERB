﻿;────────────────────────────────────
;060,逃げる
;────────────────────────────────────
@COM60
A = 0
B = 0
A += (2 * ABL:MASTER:2 + CFLAG:MASTER:0 + 1) * (BASE:MASTER:0 + 300) / (MAXBASE:MASTER:0 + 300) + 1
B += 2 * ABL:TARGET:2 + CFLAG:TARGET:0 + 1
;アクセサリ効果
A = A * (200 + FLAG:3187) / 200
;絶頂ボーナス
SIF TFLAG:403
	B = 1
LOCAL = TFLAG:50 ? (5 * TFLAG:64) # 30
A = A * (BASE:MASTER:1 + 500) * LOCAL * (TFLAG:400 + 5) / (100 * (MAXBASE:MASTER:1 + 500))
IF B > A
	BASE:MASTER:0 -= (B - A) * 100 / B + 50
ELSE
	BASE:MASTER:0 -= 50
ENDIF
BASE:MASTER:1 = BASE:MASTER:1 * 9 / 10
PRINTL 
PRINTFORML %CALLNAME:MASTER%逃走了！
PRINTW ・
PRINTW ・
PRINTW ・
IF NO:TARGET == 48
	PRINTFORMW 逃离不了エスト大人！
	PRINTL 
ELSEIF A > RAND:B
	TFLAG:400 = 0
	BEGIN AFTERTRAIN
ELSE
	PRINTFORMW 但是被追回了
	PRINTL 
	TFLAG:94 = 6
	TFLAG:371 = 3
	TFLAG:400 = 0
ENDIF
RETURN 1


;────────────────────────────────────
;061,なすがまま
;────────────────────────────────────
@COM61
TFLAG:94 = 2
RETURN 1


;────────────────────────────────────
;062,もがく
;────────────────────────────────────
@COM62
A = 0
B = 0
A += (3 * ABL:MASTER:2 + CFLAG:MASTER:0) * BASE:MASTER:0 / MAXBASE:MASTER:0 + TFLAG:401 + 1
B += 2 * ABL:TARGET:2 + CFLAG:TARGET:0 + 1
A = A * (TFLAG:403 + 1)
;イキそう
SIF TFLAG:170
	A = A * 2 / 3
;焦らし
SIF TFLAG:126
	A = A * 2 / 3
SIF B > A
	BASE:MASTER:0 -= (B - A) * 100 / B + 30
BASE:MASTER:1 = BASE:MASTER:1 * 9 / 10

PRINTL 
IF A > RAND:B
	PRINTFORMW %CALLNAME:MASTER%解除了拘束站了起来！
	TEQUIP:37 = 0
	TEQUIP:38 = 0
	TEQUIP:40 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TEQUIP:46 = 0
	TEQUIP:47 = 0
	TEQUIP:70 = 0
	TEQUIP:71 = 0
	TFLAG:112 = 0
	TFLAG:94 = 5
	TFLAG:401 = 0
	TFLAG:300 = 1
	TFLAG:404 = 2
ELSE
	PRINTFORMW %CALLNAME:TARGET%对挣扎的%CALLNAME:MASTER%继续刺激着…
	TFLAG:94 = 6
	TFLAG:401 += 2
ENDIF
PRINTL 
RETURN 1


;────────────────────────────────────
;063,アイテム
;────────────────────────────────────
@COM63
PRINTL [0] - 任凭摆布
SIF ITEM:500
	PRINTL [1] - 镇静剂
SIF ITEM:501
	PRINTL [2] - 强化镇静剂
SIF ITEM:506
	PRINTL [3] - 迷药
$INPUT_LOOP
INPUT
IF RESULT == 1 && ITEM:500
	PRINTL 
	PRINTFORML %CALLNAME:MASTER%使用了镇静剂
	PRINTW 理性回复了20％
	PRINTL 
	ITEM:500 -= 1
	BASE:MASTER:5 += MAXBASE:MASTER:5 / 5
	SIF BASE:MASTER:5 > MAXBASE:MASTER:5
		BASE:MASTER:5 = MAXBASE:MASTER:5
	TFLAG:300 = 1
ELSEIF RESULT == 2 && ITEM:501
	PRINTL 
	PRINTFORML %CALLNAME:MASTER%使用了强化镇静剂
	PRINTW 理性回复了50％
	PRINTL 
	ITEM:501 -= 1
	BASE:MASTER:5 += MAXBASE:MASTER:5 / 2
	SIF BASE:MASTER:5 > MAXBASE:MASTER:5
		BASE:MASTER:5 = MAXBASE:MASTER:5
	TFLAG:300 = 2
ELSEIF RESULT == 3 && ITEM:506
	ITEM:506 -= 1
	PRINTL 
	PRINTFORML %CALLNAME:MASTER%使用了迷药
	PRINTW 脱离这个地方了
	PRINTL 
	TFLAG:300 = 3
	BEGIN AFTERTRAIN
ELSE
	SELECTCOM = 61
	TFLAG:94 = 2
	PRINTL 
ENDIF
RETURN 1

;────────────────────────────────────
;064,様子をみる
;────────────────────────────────────
@COM64
TFLAG:400 += 1 + TFLAG:400 / 4
PRINTL 
PRINTFORMW %CALLNAME:MASTER%找到了逃走的机会
TFLAG:94 = 1
RETURN 1
;────────────────────────────────────
;065,アナライズ
;────────────────────────────────────
@COM65
CALL SHOW_CHARADATA(1)
TFLAG:94 = 1
RETURN 1
