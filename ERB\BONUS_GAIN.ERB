﻿;────────────────────────────────────
;ボーナス取得のメニュー。ボーナスの種類と必要ポイントは仮の設定です
;────────────────────────────────────
@BONUS_GAIN
DRAWLINE
PRINTFORML %CALLNAME:MASTER%被%CALLNAME:TARGET%调教中
SIF ASSI:1 >= 0
	PRINTFORML 　(助手１：%CALLNAME:(ASSI:1)%)　\@ ASSI:2 >= 0 ? (助手２：%CALLNAME:(ASSI:2)%) # \@
PRINTFORML [LV {CFLAG:TARGET:0}　EXP {CFLAG:TARGET:1}/{CFLAG:TARGET:4}　NEXT {CFLAG:TARGET:4-CFLAG:TARGET:1}]
PRINTFORML (剩余MP {CFLAG:201})

;────────────────────────────────────
;これまで取得的ボーナスの表示。いろいろやっているけど手抜き
SIF FLAG:11
	PRINTFORML 追加的调教者：{FLAG:11,2}人
;分類の手法は素質と一緒です
VARSET LOCAL
FOR LOCAL, 0, 50
	SIF !FLAG:(1600 + LOCAL)
		CONTINUE
	LOCAL:1 = ITEM_TYPE(LOCAL)
	SIF LOCAL:1
		LOCAL:(100 * LOCAL:1 + 1 + LOCAL:(100 * LOCAL:1)++) = LOCAL
NEXT
SIF ITEM:70
	LOCAL:(301 + LOCAL:300++) = 70
IF LOCAL:100 || LOCAL:200 || LOCAL:300
	PRINTL □ 取得的道具 □---------------------------------------------------------------
	FOR LOCAL, 1, 4
		;调教分類番号を配列の添え字に変換
		LOCAL:5 = LOCAL * 100
		;その分類の调教道具を1つも所持していなければ何もしない
		SIF !LOCAL:(LOCAL:5)
			CONTINUE
		;调教道具分類名を取得→出力
		LOCALS = %ITEM_TYPENAME(LOCAL)%
		PRINTFORM %LOCALS%：
		LOCAL:1 = 0
		LOCAL:2 = STRLENS(LOCALS) + 2
		;调教道具を所持している数だけループ
		FOR LOCAL:3, 0, LOCAL:(LOCAL:5)
			;调教道具名を取得
			LOCALS = %ITEMNAME:(LOCAL:(LOCAL:5 + 1 + LOCAL:3))%
			LOCAL:4 = STRLENS(LOCALS)
			LOCAL:1 += LOCAL:4 + 3
			;改行処理
			IF LOCAL:1 > 78 - LOCAL:2
				PRINTL 
				PRINTS " " * LOCAL:2
				LOCAL:1 = LOCAL:2 + 3 + LOCAL:4
			ENDIF
			;调教道具名の出力
			PRINTFORM [%LOCALS%] 
		NEXT
		PRINTL 
	NEXT
ENDIF
;数が少ないのでとりあえずこのままで
IF TALENT:55 || TALENT:59 || TALENT:91 || TALENT:121 || TALENT:130
	PRINTL □ 取得的素质 □---------------------------------------------------------------
	IF FLAG:2003
		SIF TALENT:121
			PRINTFORML 性別：[%TALENTNAME:121%]
		SIF TALENT:55 || TALENT:59 || TALENT:91 || TALENT:130
			PRINT 技能：
	ENDIF
	PRINTFORM \@ TALENT:55 ? [%TALENTNAME:55%]%" "% # \@\@ TALENT:59 ? [%TALENTNAME:59%]%" "% # \@\@ TALENT:91 ? [%TALENTNAME:91%]%" "% # \@
	PRINTFORM \@ TALENT:121 && !FLAG:2003 ? [%TALENTNAME:121%]%" "% # \@\@ TALENT:130 ? [%TALENTNAME:130%]%" "% # \@
	SIF TALENT:55 || TALENT:59 || TALENT:91 || (TALENT:121 && !FLAG:2003) || TALENT:130
		PRINTL 
ENDIF

;────────────────────────────────────
;ボーナスメニューの表示
;名前の余白分
LOCAL = MAX(8 - STRLENSU(CALLNAME:TARGET), 0)
DRAWLINE
PRINTL [ 0] 返回
SIF FLAG:2000
	PRINTFORML \@ CAPACITY() < FLAG:50 || FLAG:1701 ? [ 1] 追加新的调教者　　　　　　　　　　　　　　　 1000MP # [--] 据点现在满员了 \@
SELECTCASE ABL:2
	CASE 0
		PRINTFORML [ 2] %CALLNAME:TARGET%的调教技巧才能开花了%"　" * LOCAL%　   50MP
	CASE 1
		PRINTFORML [ 2] %CALLNAME:TARGET%的调教技巧提升了%"　" * LOCAL%　　　　　　  400MP + LV 2
	CASE 2
		PRINTFORML [ 2] %CALLNAME:TARGET%的调教技巧提升了%"　" * LOCAL%　　　　　　 1000MP + LV 5
	CASE 3
		PRINTFORML [ 2] %CALLNAME:TARGET%的调教技巧提升了%"　" * LOCAL%　　　　　　 3000MP + LV 9
	CASE 4
		PRINTFORML [ 2] %CALLNAME:TARGET%的调教技巧提升了%"　" * LOCAL%　　　　　　10000MP + LV15
	CASE 99
		PRINTFORML [--] %CALLNAME:TARGET%的调教技巧MAX了
	CASEELSE
		PRINTFORML [--] 通过调教%CALLNAME:TARGET%的技巧提高了
ENDSELECT
PRINTL [ 3] 素质的习得
PRINTL [ 4] 技能训练师
PRINTL [ 5] 道具的取得
PRINTFORML \@ (!FLAG:1701 && FLAG:1609) || ITEM:9 ? [--] 道具已经被定制 # [10] 道具定制　　　　　　　　　　　　 2000MP + [%TALENTNAME:59%] \@
SIF !TALENT:122
	PRINTFORML [11] %CALLNAME:TARGET%\@ TALENT:121 ? 的男根消失了%"　"% # 变成扶她了 \@%"　" * LOCAL%　　　　　　　 1000MP
SELECTCASE TALENT:MASTER:132
	CASE 0
		PRINTL [12] 取得察言观色的能力　　　　　　　　   50MP
	CASE 1
		PRINTL [12] 强化察言观色的能力　　　　　　　　   50MP
	CASEELSE
		PRINTL [--] 察言观色的能力已经得到加强
ENDSELECT
IF TALENT:131
	PRINTL [13] 呼叫默认助手(未实装)
ELSEIF CHARANUM > 2
	PRINTL [13] 其他调教者助手(未实装)
ELSE
	PRINTL [--] 目前没有人选可以当助手
ENDIF
PRINTFORML [14] cosplay套装入手(未完成)\@ ITEM:70 ? # %"　　　　　　　 "%1000MP \@
PRINTL [15] 调教以外时间用的道具入手(未实装)
PRINTFORML \@ MAXBASE:MASTER:2 < 2500 + CFLAG:MASTER:203 * 500 ? [--] 可以通过调教来强化精力 # [16] 提高精力的极限　　　　　　　　　　　　 1000MP \@
DRAWLINE

;────────────────────────────────────
;入力処理
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
		RETURN 0
	CASE 1
		IF !FLAG:2000 || (!FLAG:1701 && CAPACITY() >= FLAG:50)
			$FAILED
			CLEARLINE 1
			GOTO INPUT_LOOP
		ELSEIF CFLAG:201 < 1000
			$FAILED_BY_MP
			CLEARLINE 1
			REUSELASTLINE MP不足了
			GOTO INPUT_LOOP
		ENDIF
		CALL NEW_TRAINER
		RETURN 0
	CASE 2
		SELECTCASE ABL:2
			CASE 0
				SIF CFLAG:201 < 50
					GOTO FAILED_BY_MP
				CFLAG:201 -= 50
			CASE 1
				IF CFLAG:201 < 400
					GOTO FAILED_BY_MP
				ELSEIF CFLAG:0 < 2
					CLEARLINE 1
					REUSELASTLINE %CALLNAME:TARGET%想提高技巧还需要积累更多的经验(调教LV2必要)
					GOTO INPUT_LOOP
				ENDIF
				CFLAG:201 -= 400
			CASE 2
				IF CFLAG:201 < 1000
					GOTO FAILED_BY_MP
				ELSEIF CFLAG:0 < 5
					CLEARLINE 1
					REUSELASTLINE %CALLNAME:TARGET%想提高技巧还需要积累更多的经验(调教LV5必要)
					GOTO INPUT_LOOP
				ENDIF
				CFLAG:201 -= 1000
			CASE 3
				IF CFLAG:201 < 3000
					GOTO FAILED_BY_MP
				ELSEIF CFLAG:0 < 9
					CLEARLINE 1
					REUSELASTLINE %CALLNAME:TARGET%想提高技巧还需要积累更多的经验(调教LV9必要)
					GOTO INPUT_LOOP
				ENDIF
				CFLAG:201 -= 3000
			CASE 4
				IF CFLAG:201 < 10000
					GOTO FAILED_BY_MP
				ELSEIF CFLAG:0 < 15
					CLEARLINE 1
					REUSELASTLINE %CALLNAME:TARGET%想提高技巧还需要积累更多的经验(调教LV15必要)
					GOTO INPUT_LOOP
				ENDIF
				CFLAG:201 -= 10000
			CASEELSE
				GOTO FAILED
		ENDSELECT
		PRINTFORMW %CALLNAME:TARGET%的调教技巧变成了<{++ABL:2}>
	CASE 3
		PRINTL [ 0] - 返回
		PRINTFORML \@ TALENT:55  ? [--] 已经学习了调和知识       # [ 1] 学习调和知识　　　　　　　　　　　　　　　 1000MP + LV 5 \@
		PRINTFORML \@ TALENT:91  ? [--] 已经学习了魅惑的技术     # [ 2] 学习魅惑的技术　　　　　　　　　　　　　　 1000MP + LV 5 \@
		PRINTFORML \@ TALENT:130 ? [--] 已经学习了禁断的知识     # [ 3] 学习禁断的知识 　　　　　　　　　　　　　　10000MP + LV 8 \@
		PRINTFORML \@ TALENT:59  ? [--] 已学习使用道具的技术     # [ 4] 学习使用道具的技术　　　　　　　　　　　　 1000MP + LV 6 \@
		$INPUT_LOOP_SKILL
		INPUT
		SELECTCASE RESULT
			CASE 1
				IF TALENT:55
					CLEARLINE 1
					GOTO INPUT_LOOP_SKILL
				ELSEIF CFLAG:201 < 1000
					CLEARLINE 1
					REUSELASTLINE MP不足了
					GOTO INPUT_LOOP_SKILL
				ELSEIF CFLAG:0 < 5
					CLEARLINE 1
					REUSELASTLINE 想学习调和知识还需要积累更多的经验(调教LV5必要)
					GOTO INPUT_LOOP_SKILL
				ENDIF
				CFLAG:201 -= 1000
				TALENT:55 = 1
				PRINTW 学习了调和知识
			CASE 2
				IF TALENT:91
					CLEARLINE 1
					GOTO INPUT_LOOP_SKILL
				ELSEIF CFLAG:201 < 1000
					CLEARLINE 1
					REUSELASTLINE MP不足了
					GOTO INPUT_LOOP_SKILL
				ELSEIF CFLAG:0 < 5
					CLEARLINE 1
					REUSELASTLINE 想学习魅惑还需要积累更多的经验(调教LV5必要)
					GOTO INPUT_LOOP_SKILL
				ENDIF
				CFLAG:201 -= 1000
				TALENT:91 = 1
				PRINTW 学习了魅惑
			CASE 3
				IF TALENT:130
					CLEARLINE 1
					GOTO INPUT_LOOP_SKILL
				ELSEIF CFLAG:201 < 10000
					CLEARLINE 1
					REUSELASTLINE MP不足了
					GOTO INPUT_LOOP_SKILL
				ELSEIF CFLAG:0 < 8
					CLEARLINE 1
					REUSELASTLINE 想要学习禁断的知识还需要积累更多的经验(调教LV8必要)
					GOTO INPUT_LOOP_SKILL
				ENDIF
				CFLAG:201 -= 10000
				TALENT:130 = 1
				PRINTW 学习了禁断的知识
			CASE 4
				IF TALENT:59
					CLEARLINE 1
					GOTO INPUT_LOOP_SKILL
				ELSEIF CFLAG:201 < 1000
					CLEARLINE 1
					REUSELASTLINE MP不足了
					GOTO INPUT_LOOP_SKILL
				ELSEIF CFLAG:0 < 6
					CLEARLINE 1
					REUSELASTLINE 想要学习使用道具的技術还需要积累更多的经验(调教LV6必要)
					GOTO INPUT_LOOP_SKILL
				ENDIF
				CFLAG:201 -= 1000
				TALENT:59 = 1
				PRINTW 学习了使用道具的技术
			CASEELSE
				RESTART
		ENDSELECT
	CASE 4
		PRINTL [ 0] - 返回
		PRINTFORML \@ TALENT:52  ? [--] 已经学习了善用舌头         # [ 1] 学习善用舌头　　　　　　　　　　　　　　　　10000MP + LV 10 \@
		PRINTFORML \@ TALENT:57  ? [--] 已经学习了灵巧的手指       # [ 2] 学习灵巧手指的技术　　　　　　　　　　　　10000MP + LV 10 \@
		PRINTFORML \@ TALENT:58  ? [--] 已经学习了擅长捆绑       # [ 3] 学习擅长捆绑的知识　　　　　　　　　　　　10000MP + LV 10 \@
		PRINTFORML \@ TALENT:83  ? [--] 已经学习了施虐狂           # [ 4] 学习施虐狂的技术　　　　　　　　　　　　　　10000MP + LV 10 \@
		PRINTFORML \@ TALENT:64  ? [--] 已经学习了脏污无视       # [ 5] 学习脏污无视的技术　　　　　　　　　　　　10000MP + LV 10 \@
		$INPUT_LOOP_SKILL2
		INPUT
		SELECTCASE RESULT
			CASE 1
				IF TALENT:51
					CLEARLINE 1
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:201 < 10000
					CLEARLINE 1
					REUSELASTLINE MP不足了
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:0 < 10
					CLEARLINE 1
					REUSELASTLINE 想要学习善用舌头还需要积累更多的经验(调教LV10必要)
					GOTO INPUT_LOOP_SKILL2
				ENDIF
				CFLAG:201 -= 10000
				TALENT:52 = 1
				PRINTW 学习了善用舌头
			CASE 2
				IF TALENT:57
					CLEARLINE 1
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:201 < 10000
					CLEARLINE 1
					REUSELASTLINE MP不足了
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:0 < 10
					CLEARLINE 1
					REUSELASTLINE 想要学习灵活的手指还需要积累更多的经验(调教LV10必要)
					GOTO INPUT_LOOP_SKILL2
				ENDIF
				CFLAG:201 -= 10000
				TALENT:57 = 1
				PRINTW 学习了灵巧的手指
			CASE 3
				IF TALENT:58
					CLEARLINE 1
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:201 < 10000
					CLEARLINE 1
					REUSELASTLINE MP不足了
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:0 < 10
					CLEARLINE 1
					REUSELASTLINE 想要学习擅长捆绑还需要积累更多的经验(调教LV10必要)
					GOTO INPUT_LOOP_SKILL2
				ENDIF
				CFLAG:201 -= 10000
				TALENT:58 = 1
				PRINTW 学习了擅长捆绑
			CASE 4
				IF TALENT:83
					CLEARLINE 1
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:201 < 10000
					CLEARLINE 1
					REUSELASTLINE MP不足了
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:0 < 10
					CLEARLINE 1
					REUSELASTLINE 想要学习施虐狂的技术还需要积累更多的经验(调教LV10必要)
					GOTO INPUT_LOOP_SKILL2
				ENDIF
				CFLAG:201 -= 10000
				TALENT:施虐狂 = 1
				PRINTW 学习了施虐狂的技术
			CASE 5
				IF TALENT:64
					CLEARLINE 1
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:201 < 10000
					CLEARLINE 1
					REUSELASTLINE MP不足了
					GOTO INPUT_LOOP_SKILL2
				ELSEIF CFLAG:0 < 10
					CLEARLINE 1
					REUSELASTLINE 想要学习脏污无视的技术还需要积累更多的经验(调教LV10必要)
					GOTO INPUT_LOOP_SKILL2
				ENDIF
				CFLAG:201 -= 10000
				TALENT:脏污无视 = 1
				PRINTW 学习了脏污无视的技术
			CASEELSE
				RESTART
		ENDSELECT
	CASE 10
		IF (!FLAG:1701 && FLAG:1609) || ITEM:9
			GOTO FAILED
		ELSEIF CFLAG:201 < 2000
			GOTO FAILED_BY_MP
		ELSEIF !TALENT:59
			CLEARLINE 1
			REUSELASTLINE 想要定制道具，必须学习使用道具的技术
			GOTO INPUT_LOOP
		ENDIF
		CFLAG:201 -= 2000
		ITEM:9 = ++FLAG:1609
		PRINTW 自定义道具
	CASE 11
		SIF TALENT:122
			GOTO FAILED
		SIF CFLAG:201 < 1000
			GOTO FAILED_BY_MP
		CFLAG:201 -= 1000
		TALENT:121 = !TALENT:121
		;童貞化。性交経験は逆レイプの経験等と混ざるので微妙だと思いますが…
		TALENT:1 = TALENT:121 && !EXP:7
		;射精ゲージの初期化
		;遅漏の射精ゲージって15000でしたっけ？20000だったような…ん？
		;そもそも、MAXBASE:2は射精ゲージですが、Reverseのシステムでは射精に至るまでのゲージではなくタンクの残量だと思いますので
		;意味が違う気もします。精力絶倫/精力薄弱の処理なら妥当だと思いますが…うーん
		;各所の整合性も取れなくなりそうなので、私は考えることをやめた
		MAXBASE:2 = TALENT:121 ? 10000 - 5000 * TALENT:123 + 5000 * TALENT:124 # 0
		PRINTFORMW %CALLNAME:TARGET%变成\@ TALENT:121 ? 扶她 # 女孩子 \@了
	CASE 12
		SIF TALENT:MASTER:132 == 2
			GOTO FAILED
		SIF CFLAG:201 < 50
			GOTO FAILED_BY_MP
		CFLAG:201 -= 50
		PRINTFORMW %CALLNAME:MASTER%\@ TALENT:MASTER:132++ ? 强化察言观色的能力 # 取得察言观色的能力 \@结束
	CASE 14
		IF ITEM:70
			CLEARLINE 1
			REUSELASTLINE cosplay套装入手了
			GOTO INPUT_LOOP
		ELSEIF CFLAG:201 < 1000
			GOTO FAILED_BY_MP
		ENDIF
		CFLAG:201 -= 1000
		ITEM:70 = 1
		PRINTW 取得cosplay套装
	CASE 16
		SIF MAXBASE:MASTER:2 < 2500 + CFLAG:MASTER:203 * 500
			GOTO FAILED
		SIF CFLAG:201 < 1000
			GOTO FAILED_BY_MP
		CFLAG:201 -= 1000
		CFLAG:MASTER:203++
		PRINTW 精力的极限值上升了
	CASEELSE
		GOTO FAILED
ENDSELECT
CALL KOJO_EVENT(103, RESULT)
RESTART


;────────────────────────────────────
;すでに取得的ボーナスを新しい调教者に適用
;────────────────────────────────────
@BONUS_APPLY
