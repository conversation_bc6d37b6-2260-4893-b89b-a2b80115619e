﻿;────────────────────────────────────
;愛撫、性交等の継続、解除、処理
;────────────────────────────────────
@SHOW_TOUCH
FOR LOCAL,1,<PERSON><PERSON><PERSON><PERSON>
	SIF LOCAL != TARGET
		CONTINUE
	SELECTCASE TEQUIP:(LOCAL):101
		CASE 3
			PRINT 手淫 
		CASE 4
			PRINT 口交 
		CASE 6
			PRINT 逆强奸 
		CASE 7
			PRINT 逆肛门 
		CASE 8
			PRINT 足交施虐 
	ENDSELECT
	SELECTCASE TEQUIP:(LOCAL):102
		CASE 1
			PRINT 素股 
		CASE 3
			PRINT 阴蒂爱抚 
		CASE 4
			PRINT 舔阴 
	ENDSELECT
	SELECTCASE TEQUIP:(LOCAL):103
		CASE 1
			PRINT 手淫 
		CASE 5
			PRINT 揉胸 
	ENDSELECT
	SELECTCASE TEQUIP:(LOCAL):104
		CASE 1
			PRINT 口交 
		CASE 2
			PRINT 使舔阴 
		CASE 4
			PRINT 接吻 
		CASE 5
			PRINT 吸乳头 
		CASE 7
			PRINT 肛门侍奉 
	ENDSELECT
	SELECTCASE TEQUIP:(LOCAL):105
		CASE 1
			PRINT 乳交 
		CASE 3
			PRINT 胸爱抚 
		CASE 4
			PRINT 舔乳头 
	ENDSELECT
	SELECTCASE TEQUIP:(LOCAL):106
		CASE 1
			PRINT 性交 
		CASE 3
			PRINT 指插入 
	ENDSELECT
	SELECTCASE TEQUIP:(LOCAL):107
		CASE 1
			PRINT 肛门性交 
		CASE 3
			PRINT 玩弄肛门 
		CASE 4
			PRINT 舔肛门 
	ENDSELECT
	SELECTCASE TEQUIP:(LOCAL):108
		CASE 1
			PRINT 足交 
	ENDSELECT
	PRINTFORML (%CALLNAME:(LOCAL)%)
NEXT
PRINTL 
@TOUCH_SUCCESSION(ARG)
VARSET LOCAL
SELECTCASE TFLAG:90
	;会話系現状維持
	CASE 0 TO 4,9
	;愛撫/手淫する
	CASE 10
		IF TFLAG:93
			CALL TOUCH_SET(103,1,ARG)
		ELSE
			CALL TOUCH_SET(103,2,ARG)
		ENDIF
	;胸愛撫/乳首吸い/乳の揉み合い
	CASE 11
		IF TFLAG:93 == 1
			CALL TOUCH_SET(104,5,ARG)
		ELSE
			CALL TOUCH_SET(103,5,ARG)
		ENDIF
	;クンニ/フェラする
	CASE 12
		IF TFLAG:93
			CALL TOUCH_SET(104,1,ARG)
		ELSE
			CALL TOUCH_SET(104,2,ARG)
		ENDIF
		TEQUIP:ARG:105 = TEQUIP:ARG:105 == 4 ? 0  # TEQUIP:ARG:105
		
	;アナル愛撫
	CASE 13
		CALL TOUCH_SET(103,7,ARG)
	;アナル舐め
	CASE 14
		CALL TOUCH_SET(104,7,ARG)
	;キス
	CASE 15
		CALL TOUCH_SET(104,4,ARG)
	;貝あわせ/素股する
	CASE 16
		IF TFLAG:93
			CALL TOUCH_SET(102,1,ARG)
		ELSE
			CALL TOUCH_SET(102,2,ARG)
		ENDIF
	;パイズリする
	CASE 17
		CALL TOUCH_SET(105,1,ARG)
	;足コキする
	CASE 18
		CALL TOUCH_SET(108,1,ARG)
	;ローター
	CASE 20
		IF PENIS(MASTER)
			CALL TOUCH_SET(103,1,ARG)
		ELSE
			CALL TOUCH_SET(103,2,ARG)
		ENDIF
	;正常位
	CASE 30
		CALL TOUCH_SET(101,6,ARG,1)
		TEQUIP:70 = 1
	;後背位
	CASE 31
		CALL TOUCH_SET(101,6,ARG,1)
		TEQUIP:70 = 2
	;騎乗位
	CASE 32
		CALL TOUCH_SET(101,6,ARG,1)
		TEQUIP:70 = 3
	;対面座位
	CASE 33
		CALL TOUCH_SET(101,6,ARG,1)
		TEQUIP:70 = 4
	;背面座位
	CASE 34
		CALL TOUCH_SET(101,6,ARG,1)
		TEQUIP:70 = 5
	;アナル
	CASE 35
		CALL TOUCH_SET(101,7,ARG,1)
		TEQUIP:70 = 6
	;逆レイプ
	CASE 36
		CALL TOUCH_SET(106,1,ARG,1)
		TEQUIP:71 = 3
	;手淫/愛撫強制
	CASE 50
		IF PENIS(ARG)
			CALL TOUCH_SET(101,3,ARG)
		ELSE
			CALL TOUCH_SET(102,3,ARG)
		ENDIF
	;フェラチオ/クンニ強制
	CASE 51
		IF PENIS(ARG)
			CALL TOUCH_SET(101,4,ARG)
		ELSE
			CALL TOUCH_SET(102,4,ARG)
		ENDIF
	;パイズリ
	CASE 52
		CALL TOUCH_SET(101,5,ARG)
		CALL TOUCH_SET(101,4,ARG)
	;素股
	CASE 53
		CALL TOUCH_SET(101,2,ARG)
	;足コキ
	CASE 54
		CALL TOUCH_SET(101,8,ARG)
	;足舐め強制
	CASE 55
		CALL TOUCH_SET(108,4,ARG)
	;イラマチオ
	CASE 56
		CALL TOUCH_SET(101,4,ARG)
	;胸愛撫強制
	CASE 57
		IF TFLAG:93
			CALL TOUCH_SET(105,4,ARG)
		ELSE
			CALL TOUCH_SET(105,3,ARG)
		ENDIF
	;スパンキング
	CASE 60
		TEQUIP:ARG:103 = 0
	;正常位させる
	CASE 95
		CALL TOUCH_SET(106,1,ARG,1)
		TEQUIP:71 = 1
	;後背位させる
	CASE 96
		CALL TOUCH_SET(106,1,ARG,1)
		TEQUIP:71 = 2
	;対面座位させる
	CASE 97
		CALL TOUCH_SET(106,1,ARG,1)
		TEQUIP:71 = 4
	;背面座位させる
	CASE 98
		CALL TOUCH_SET(106,1,ARG,1)
		TEQUIP:71 = 5
	;アナルセックスさせる
	CASE 99
		CALL TOUCH_SET(106,1,ARG,1)
		TEQUIP:71 = 6
	CASEELSE
		CALL TOUCH_SET(0,0,0,1)
ENDSELECT
IF TEQUIP:44
	IF TALENT:122
		CALL TOUCH_SET(101,4,ARG)
	ELSE
		CALL TOUCH_SET(102,4,ARG)
	ENDIF
ELSEIF TEQUIP:45
	CALL TOUCH_SET(107,4,ARG)
ENDIF

IF TEQUIP:ARG:103 == 1
	TEQUIP:ARG:100 = 1
ELSEIF TEQUIP:ARG:104 == 1
	TEQUIP:ARG:100 = 2
ELSE
	TEQUIP:ARG:100 = 0
ENDIF

SIF !TEQUIP:ARG:70
	TEQUIP:ARG:101 = 0
IF !TEQUIP:ARG:71
	TEQUIP:ARG:106 = 0
	TEQUIP:ARG:107 = 0
ENDIF

;MASTERとTARGETの接触部位を設定する
@TOUCH_SET(ARG,ARG:1,ARG:2,ARG:3)
;ARG TARGETの接触部位(101=ペニス 102=クリトリス 103=指 104=口 105=胸 106=膣 107=アナル 108=足)
;ARG:1 MASTERの接触部位(1=ペニス 2=クリトリス 3=指 4=口 5=胸 6=膣 7=アナル 8=足)
;ARG:2 対象のキャラ番号
;ARG:3 真ならリセットする
VARSET LOCAL
;足は呼ばれるとキャンセルする
TEQUIP:(ARG:2):108 = 0
IF ARG:3
	FOR LOCAL,1,CHARANUM
		FOR LOCAL:1,1,8
			TEQUIP:LOCAL:(100 + LOCAL:1) = 0
		NEXT
	NEXT
ENDIF
SIF ARG <= 100
	RETURN 0
IF ARG:1 == 3
	LOCAL:2 = 2
ELSE
	LOCAL:2 = 1
ENDIF

FOR LOCAL,1,CHARANUM
	FOR LOCAL:1,1,8
		SIF TEQUIP:LOCAL:(100 + LOCAL:1) == 8
			TEQUIP:LOCAL:(100 + LOCAL:1) = 0
		IF TEQUIP:LOCAL:(100 + LOCAL:1) == ARG:1
			LOCAL:2 --
			SIF LOCAL:2 < 1
				TEQUIP:LOCAL:(100 + LOCAL:1) = 0
		ENDIF
	NEXT
NEXT
TEQUIP:(ARG:2):ARG = ARG:1

;MASTERの接触部位をリセットする
@TOUCH_RESET_M(ARG)
;ARG:1 MASTERの接触部位(1=ペニス 2=クリトリス 3=指 4=口 5=胸 6=膣 7=アナル)

FOR LOCAL,1,CHARANUM
	FOR LOCAL:1,1,8
		SIF TEQUIP:LOCAL:(100 + LOCAL:1) == ARG
				TEQUIP:LOCAL:(100 + LOCAL:1) = 0
	NEXT
NEXT

@TOUCH(ARG,ARG:1,ARG:2)
;ARG MASTERの部位を占有中のTARGET:Xの部位(1=ペニス 2=クリトリス 3=指 4=口 5=胸 6=膣 7=アナル)
;ARG:1 MASTERの部位(1=ペニス 2=クリトリス 3=指 4=口 5=胸 6=膣 7=アナル)
;ARG:2 真なら前ターンの履歴を見る
;戻り値 MASTERの部位を占有中のTARGET:Xがいるならそのキャラ番号を返す
;MASTER_POSE(4,1,1)なら前ターン(第3引数)に誰かがMASTERのペニス(第2引数)を口(第1引数)で占有していた場合、そのキャラ番号を返す
#FUNCTION
ARG:2 = ARG:2 ? 1 # 0
FOR LOCAL,1,CHARANUM
	SIF TEQUIP:LOCAL:(100 + ARG + 10 * ARG:2) == ARG:1
		RETURNF LOCAL
NEXT
