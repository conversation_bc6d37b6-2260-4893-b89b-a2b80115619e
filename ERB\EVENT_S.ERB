﻿;追加处理及びサブイベント

@EVENTTURNEND
#PRI
SIF FLAG:1700
	BEGIN SHOP

;-------------------------------------------------
;暫定の所持金追加処理
;-------------------------------------------------
FOR LOCAL,0,CHARANUM
	SIF TALENT:LOCAL:131
		MONEY += CFLAG:LOCAL:0 * RAND(1,5) * 10
NEXT

;最初に回復処理を入れる
CALL BASE_RECOVERY

;ムラムラフラグ
FOR LOCAL,0,CHARANUM
	CFLAG:LOCAL:161 += 1
NEXT

;妊娠処理
FOR LOCAL,1,CHARANUM
	CALL PREGNACY_CHECK(LOCAL)
NEXT

FOR LOCAL,1,CHARANUM
	CALL MASTER_PREGNACY_CHECK(LOCAL)
NEXT
SIF CFLAG:MASTER:41
	CFLAG:MASTER:41 ++
CALL MASTER_PREGNACY
[SKIPSTART]
;ユニーク夢魔からの脱走イベントなど個別に処理すべきイベント
LOCAL = TARGET
REPEAT CHARANUM
	TARGET = COUNT
	IF (NO:TARGET == 35 || NO:TARGET == 34) && CFLAG:TARGET:92 == 0
		IF FLAG:1701 && TARGET == LOCAL
			CALL EVENT_STML
		ENDIF
	ENDIF
	IF (NO:TARGET == 38 || NO:TARGET == 39 || NO:TARGET == 40) && CFLAG:TARGET:92 == 0
		IF FLAG:1701 && TARGET == LOCAL
			CALL EVENT_SISTERS
		ENDIF
	ENDIF
	;マーシャ
	IF NO:TARGET == 32 && CFLAG:TARGET:92 == 0
		IF FLAG:1701 && TARGET == LOCAL
			CALL EVENT_RET_MARCIA
		ENDIF
	ENDIF
REND
TARGET = LOCAL
[SKIPEND]

SIF TARGET > 0 && CFLAG:TARGET:9 > 3000 && (CFLAG:TARGET:6 < 0 || TALENT:83) && !(EQUIP:MASTER:10 & 1) && !FLAG:1701
	CALL SLAVE_COLLAR

;日常など
SIF FLAG:2006
CALL DAILY_LIFE

;────────────────────────────────────
;薬剤のクールダウン
;────────────────────────────────────
IF FLAG:1700 == 0 && TARGET > 0
	;ローション
	FLAG:20 -= FLAG:20 / (5 - TALENT:55 * 3) + A:30 * (1 + ABL:2) / 3
	SIF FLAG:20 < 0
		FLAG:20 = 0
	;媚薬
	FLAG:21 -= FLAG:21 / (5 - TALENT:55 * 3) + A:30 * (1 + ABL:1) / 3
	SIF FLAG:21 < 0
		FLAG:21 = 0
	;栄養剤
	FLAG:23 -= FLAG:23 / (6 - TALENT:55 * 3) + A:30 * (2 + TALENT:117) / 2
	SIF FLAG:23 < 0
		FLAG:23 = 0
	;治療
	FLAG:24 -= (BASE:0 / 100) * (5 + A:30) / (10 - TALENT:117 * 3)
	SIF FLAG:24 < 0
		FLAG:24 = 0
ENDIF
;────────────────────────────────────
;いろいろ
;────────────────────────────────────
;CALL EVENTCHECK_M
;FLAG:0 = 0

;日時更新
SIF !FLAG:1700
	CALL NEXTTIME
BEGIN SHOP

@PREGNACY_CHECK(ARG)
SIF FLAG:1701
	RETURN 0
CFLAG:ARG:40 = CFLAG:ARG:40 * MIN(CFLAG:ARG:2 + CFLAG:ARG:9 , 9000) / 10000
SIF TALENT:ARG:2 || CFLAG:ARG:41
	CFLAG:ARG:40 = 0

SIF CFLAG:ARG:40 > 10000 && !CFLAG:ARG:41
	CFLAG:ARG:41 = 1
SIF CFLAG:ARG:41
	CFLAG:ARG:41 ++
IF CFLAG:ARG:41 == 5
		DRAWLINE
		PRINTFORMW %CALLNAME:ARG%的肚子微微膨胀着
		PRINTFORMW 看来怀了%CALLNAME:MASTER%的孩子
		CALL KOJO_EVENT(18, 0)
		TALENT:ARG:2 = 1
		TALENT:ARG:114 = 1
		MAXBASE:ARG:3 = 10000
ENDIF
IF TALENT:ARG:2
	IF CFLAG:ARG:41 == 15
		DRAWLINE
		PRINTFORMW %CALLNAME:ARG%的肚子鼓起来了
		PRINTFORMW 差不多就要生了
		CALL KOJO_EVENT(18, 1)
	ELSEIF  CFLAG:ARG:41 >= 16 + RAND:3
			LOCAL = 0
			DRAWLINE
			PRINTFORMW %CALLNAME:ARG%似乎在分娩了
			PRINTFORMW ………
			PRINTFORMW ……
			PRINTFORMW …
			PRINTFORMW 
			PRINTFORM %CALLNAME:ARG%对%CALLNAME:MASTER%
			PRINTFORM 进行着鼓励
		 IF RAND:2 == 0
			PRINTFORM 像妈妈一样可爱的女孩
			CFLAG:ARG:44 ++
			CFLAG:MASTER:44 ++
			LOCAL = 0
		ELSE
			PRINTFORM 像父亲一样健康的男孩
			CFLAG:ARG:43 ++
			CFLAG:MASTER:43 ++
			LOCAL = 1
		ENDIF
		CFLAG:ARG:42 ++
		CFLAG:MASTER:42 ++
		TALENT:ARG:2 = 0
		TALENT:ARG:3 = 1
		CFLAG:ARG:41 = 0
		PRINTFORMW 平安分娩了。
		PRINTFORMW 抱着婴儿的%CALLNAME:ARG%，对着自己的孩子和%CALLNAME:MASTER%微笑着。
		CALL KOJO_EVENT(18, 2 + LOCAL)
	ENDIF
ENDIF

IF !FLAG:2007
	TALENT:ARG:2 = 0
	TALENT:ARG:114 = 0
	CFLAG:ARG:40 = 0
	CFLAG:ARG:41 = 0
ENDIF

;子持ちフラグは両方の妊娠機能オフフラグがある場合のみ削除
IF !FLAG:2007 && FLAG:2008 == 0
	CFLAG:ARG:42 = 0
	CFLAG:MASTER:42 = 0
	CFLAG:ARG:43 = 0
	CFLAG:MASTER:43 = 0
	CFLAG:ARG:44 = 0
	CFLAG:MASTER:44 = 0
ENDIF

;マスターの妊娠チェック
@MASTER_PREGNACY_CHECK(ARG)
SIF FLAG:1701 && !FLAG:2008
	RETURN 0
;コンフィグで愛情によって妊娠率変動を選んでいる場合
IF FLAG:2008 == 1
	CFLAG:ARG:45 = CFLAG:ARG:45 * MIN(CFLAG:ARG:2 + CFLAG:ARG:9 , 9000) / 10000
;コンフィグによって愛情に関係なく妊娠を選んでいる場合
ELSEIF FLAG:2008 == 2
	CFLAG:ARG:45 = CFLAG:ARG:45 * 7 / 10
ENDIF

SIF TALENT:MASTER:2 || CFLAG:MASTER:41
	CFLAG:ARG:45 = 0

IF RAND:(1 + CFLAG:ARG:45 / 10) > RAND:10000 && !CFLAG:MASTER:41
	;妊娠フラグ
	CFLAG:MASTER:41 = 1
	;父親のキャラの通し番号を保存
	CFLAG:MASTER:46 = CFLAG:ARG:91
ENDIF

@MASTER_PREGNACY
IF CFLAG:MASTER:41 == 5
	DRAWLINE
	PRINTFORMW %CALLNAME:MASTER%的肚子微微膨胀着
	;この時点で父親不明
	PRINTFORMW 看来是怀孕了･･･
	;妊娠させた口上もそのうち作りたいな
	TALENT:MASTER:2 = 1
	TALENT:MASTER:114 = 1
	MAXBASE:MASTER:3 = 10000
ENDIF
IF TALENT:MASTER:2
	IF CFLAG:MASTER:41 == 15
		DRAWLINE
		PRINTFORMW %CALLNAME:MASTER%的肚子鼓起来了
		PRINTFORMW 差不多就要生了
	ELSEIF CFLAG:MASTER:41 >= 16 + RAND:3
			;父親検索
			LOCAL:1 = 0
			FOR LOCAL,1,CHARANUM
				IF CFLAG:MASTER:46 == CFLAG:LOCAL:91
					LOCAL:1 = LOCAL
					BREAK
				ENDIF
			NEXT
			LOCAL = 0
			DRAWLINE
			PRINTFORMW %CALLNAME:MASTER%的腹部剧烈疼痛
			PRINTFORMW 孩子终于要出生了
			PRINTFORMW ………
			PRINTFORMW ……
			PRINTFORMW …
			PRINTFORMW
			;監禁出産
			IF FLAG:1701
				PRINTFORM %CALLNAME:MASTER%在被监禁的房间里安静着
				SIF LOCAL:1
					PRINTFORM 像%CALLNAME:(LOCAL:1)%一样的
		 		IF RAND:2 == 0
					PRINTFORM 女孩子
					CFLAG:(LOCAL:1):44 ++
					CFLAG:MASTER:44 ++
					LOCAL = 0
				ELSE
					PRINTFORM 男孩子
					CFLAG:(LOCAL:1):43 ++
					CFLAG:MASTER:43 ++
					LOCAL = 1
				ENDIF
				PRINTFORMW 出生了･･････
			;普通に出産
			ELSE
				PRINTFORM %CALLNAME:MASTER%は
		 		IF RAND:2 == 0
					PRINTFORM 可爱的女孩子
					CFLAG:(LOCAL:1):44 ++
					CFLAG:MASTER:44 ++
					LOCAL = 0
				ELSE
					PRINTFORM 精力充沛的男孩子
					CFLAG:(LOCAL:1):43 ++
					CFLAG:MASTER:43 ++
					LOCAL = 1
				ENDIF
				PRINTFORMW 平安的出生了
				SIF LOCAL:1
					PRINTFORML 总觉得出生的孩子和%CALLNAME:(LOCAL:1)%很像…
				;妊娠させた場合の口上への対応方法考えてから実装
				LOCAL:2 = TARGET
				TARGET = LOCAL:1
	;			CALL KOJO_EVENT(18, 4 + LOCAL)
				TARGET = LOCAL:2
			ENDIF
			CFLAG:MASTER:42 ++
			CFLAG:MASTER:42 ++
			TALENT:MASTER:2 = 0
			TALENT:MASTER:3 = 1
			CFLAG:MASTER:41 = 0
	ENDIF
ENDIF

IF FLAG:2008 == 0
	TALENT:MASTER:2 = 0
	TALENT:MASTER:114 = 0
	CFLAG:MASTER:45 = 0
	CFLAG:MASTER:41 = 0
ENDIF


;サンプルのメローナさん。調教3回以上で帰してくれます。
@EVENT_STML
CFLAG:800 += 1
IF CFLAG:800 >= 4 && CFLAG:801 == 0
PRINTL 
	IF LOCAL == 35
		PRINTFORML %CALLNAME:TARGET%说想要看看%CALLNAME:MASTER%的家
	ELSE
		PRINTFORML %CALLNAME:TARGET%说想让%CALLNAME:MASTER%看看自己的家
	ENDIF
PRINTFORMW 到底是怎么回事呢…
CFLAG:801 = 1
ENDIF
;サンプルのエスプラーナ3姉妹。調教3回以上で帰してくれます。
@EVENT_SISTERS
CFLAG:800 += 1
IF CFLAG:800 >= 4 && CFLAG:801 == 0
PRINTL 
PRINTFORML %CALLNAME:TARGET%说想要看看%CALLNAME:MASTER%的家
PRINTFORMW 到底是怎么回事呢…
CFLAG:801 = 1
ENDIF

;マーシャさんはもう奥手ではないので3回も調教すると家にずかずか乗り込みたいと言います
@EVENT_RET_MARCIA
CFLAG:800 += 1
IF CFLAG:800 >= 4 && CFLAG:801 == 0
PRINTL 
PRINTFORML %CALLNAME:TARGET%说想要看看%CALLNAME:MASTER%的家
PRINTFORMW 到底是怎么回事呢…
CFLAG:801 = 1
ENDIF

@SLAVE_COLLAR
DRAWLINE
PRINTFORML RINTFORML %CALLNAME:TARGET%给了因为激烈的调教而筋疲力尽的%CALLNAME:MASTER%
PRINTFORML 一个带上就能表明自己是奴隶的项圈…
PRINTFORMW 【奴隶项圈】到手了。
EQUIP:MASTER:10 |= 1
