﻿;────────────────────────────────────
;沈黙するの実行判定
;────────────────────────────────────
@COM_ABLE0
;一括管理
SIF GLOBAL_COMABLE(0)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 2 && TFLAG:90 != 3 && TFLAG:90 != 4 && TFLAG:90 != 7 && TFLAG:90 != 8 && TFLAG:90 != 9 && TFLAG:90 != 66 && TFLAG:90 != 73 && TFLAG:90 != 90 && TFLAG:90 != 91 && TFLAG:90 != 92 && TFLAG:90 != 26 && TFLAG:90 != 43 && TFLAG:90 != 44 && TFLAG:90 != 63 && TFLAG:90 != 64 && TFLAG:90 != 65 && TFLAG:67 != 67
	RETURN 0
;調教済みなら答えなきゃならない
SIF (TALENT:MASTER:78 || TALENT:MASTER:79) && (TFLAG:90 == 0 || TFLAG:90 == 1 || TFLAG:90 == 73) && TEQUIP:42 == 0
	RETURN 0
;道具/プレイ解除の判定
SIF TFLAG:90 == 26 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 43 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 44 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 63 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 64 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 65 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 67 && TFLAG:93 != 1
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;気弱く応答するの実行判定
;────────────────────────────────────
@COM_ABLE1
;一括管理
SIF GLOBAL_COMABLE(1)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 2 && TFLAG:90 != 3 && TFLAG:90 != 4 && TFLAG:90 != 5 && TFLAG:90 != 6 && TFLAG:90 != 7 && TFLAG:90 != 8 && TFLAG:90 != 9 && TFLAG:90 != 27 && TFLAG:90 != 66 && TFLAG:90 != 73 && TFLAG:90 != 90 && TFLAG:90 != 91 && TFLAG:90 != 92
	RETURN 0
;道具/プレイ解除の判定
SIF TFLAG:90 == 26 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 43 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 44 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 63 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 64 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 65 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 67 && TFLAG:93 != 1
	RETURN 0
;判定
A = TALENT:MASTER:10 * 7 - TALENT:MASTER:12 * 7 - TALENT:MASTER:15 * 3 - TALENT:MASTER:11 * 4 + TALENT:MASTER:13 * 4 - TALENT:MASTER:16 * 3 + TALENT:MASTER:17 * 3 - TALENT:MASTER:26 * 3 + TALENT:MASTER:27 * 3 + TALENT:MASTER:34 * 3
B = PALAM:7 / 1000 + PALAM:10 / 2000 - PALAM:9 / 1000 + PALAM:6 / 1000 + PALAM:13 / 3000
C = ABL:MASTER:0 * 2 + MARK:MASTER:4 * 2 + TALENT:90 * 5

SIF CFLAG:MASTER:8 == TFLAG:90
	C += MARK:MASTER:4 * 2

SIF TFLAG:61 == 1 || TFLAG:61 == 2
	C -= TFLAG:61 * 3
SIF TFLAG:61 == 3 || TFLAG:61 == 4
	C -= 3
SIF TFLAG:61 == 5
	C += 8
SIF TFLAG:61 == 6
	C -= 8
SIF TFLAG:61 == 7
	C -= 5

;調教者の行動による実行難易度
IF TFLAG:90 == 3
	D = 20
ELSEIF TFLAG:90 == 4
	D = 2
ELSEIF TFLAG:90 == 7
	D = 0
ELSEIF TFLAG:90 == 8
	D = 0
ELSEIF TFLAG:90 == 9
	D = 3
ELSEIF TFLAG:90 == 66
	D = 5
ELSEIF TFLAG:90 == 90
	D = 15
ELSEIF TFLAG:90 == 92
	D = 20
ELSE
	D = 5
ENDIF

;調教始まったばかりの自由度に補正
SIF TFLAG:64 < 2
	D -= 2

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0

;ボールギャグ
SIF TEQUIP:42
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;無愛想に応答するの実行判定
;────────────────────────────────────
@COM_ABLE2
;一括管理
SIF GLOBAL_COMABLE(2)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 2 && TFLAG:90 != 3 && TFLAG:90 != 4 && TFLAG:90 != 5 && TFLAG:90 != 6 && TFLAG:90 != 7 && TFLAG:90 != 8 && TFLAG:90 != 9 && TFLAG:90 != 27 && TFLAG:90 != 66 && TFLAG:90 != 90 && TFLAG:90 != 91 && TFLAG:90 != 92
	RETURN 0
;道具/プレイ解除の判定
SIF TFLAG:90 == 26 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 43 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 44 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 63 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 64 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 65 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 67 && TFLAG:93 != 1
	RETURN 0
;判定
A = TALENT:MASTER:11 * 2 - TALENT:MASTER:13 * 2 + TALENT:MASTER:20 * 3 - TALENT:MASTER:21 * 3 + TALENT:MASTER:22 * 7 - TALENT:MASTER:23 * 7 + TALENT:MASTER:24 * 5 - TALENT:MASTER:20 * 5 + TALENT:MASTER:32 * 7 - TALENT:MASTER:33 * 7 - TALENT:MASTER:78 * 15
B = PALAM:10 / 1500 + PALAM:12 / 1500 + PALAM:13 / 1000 - PALAM:5 / 1500 - PALAM:8 / 1500 - SOURCE:23 / 200
C = MARK:3 * 2 - MARK:MASTER:1 - ABL:MASTER:7 * 2 - TALENT:92 * 4 + TALENT:93 * 4

SIF TFLAG:61 == 1 || TFLAG:61 == 2
	C += TFLAG:61 * 3 - 1
SIF TFLAG:61 == 3
	C += 5
SIF TFLAG:61 == 5
	C -= 5
SIF TFLAG:61 == 6
	C += 3
SIF TFLAG:61 == 7
	C += 5

;調教者の行動による実行難易度
IF TFLAG:90 == 0
	D = 10
ELSEIF TFLAG:90 == 1
	D = 10
ELSEIF TFLAG:90 == 3
	D = 15
ELSEIF TFLAG:90 == 4
	D = 12
ELSEIF TFLAG:90 == 7
	D = 15
ELSEIF TFLAG:90 == 8
	D = 18
ELSEIF TFLAG:90 == 9
	D = 10
ELSEIF TFLAG:90 == 66
	D = 0
ELSE
	D = 5
ENDIF

SIF TFLAG:64 < 2
	D -= 2

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0

;ボールギャグ
SIF TEQUIP:42
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;愛想よく応答するの実行判定
;────────────────────────────────────
@COM_ABLE3
;一括管理
SIF GLOBAL_COMABLE(3)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 2 && TFLAG:90 != 3 && TFLAG:90 != 4 && TFLAG:90 != 5 && TFLAG:90 != 6 && TFLAG:90 != 7 && TFLAG:90 != 27 && TFLAG:90 != 66 && TFLAG:90 != 73 && TFLAG:90 != 90 && TFLAG:90 != 91 && TFLAG:90 != 92
	RETURN 0
;道具/プレイ解除の判定
SIF TFLAG:90 == 26 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 43 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 44 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 63 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 64 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 65 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 67 && TFLAG:93 != 1
	RETURN 0
;判定
A = TALENT:MASTER:13 * 3 - TALENT:MASTER:11 * 3 + TALENT:MASTER:25 * 4 - TALENT:MASTER:24 * 4 + TALENT:MASTER:26 * 5 - TALENT:MASTER:27 * 5  - TALENT:MASTER:28 * 2 + TALENT:MASTER:29 * 2 - TALENT:MASTER:32 * 5 + TALENT:MASTER:32 * 5 + TALENT:MASTER:89 * 15 + TALENT:MASTER:79 * 5 + TALENT:MASTER:88 * 3
B = PALAM:8 / 1000 + PALAM:5 / 1500 + PALAM:6 / 1500 - PALAM:13 / 1000 - PALAM:10 / 1500 - PALAM:12 / 1500 + SOURCE:23 / 200
C = ABL:MASTER:0 + ABL:MASTER:1 * 2 + TALENT:91 * 5 - TALENT:90 * 5 + TALENT:92 * 5 - TALENT:93 * 5 + MARK:MASTER:1 * 2 + CFLAG:2 / 200

;媚薬
SIF TEQUIP:11
	B += 5

;調教レベルを見る
IF C > 0
	IF CFLAG:MASTER:0 < 1
		TIMES C , 0.40
	ELSEIF CFLAG:MASTER:0 < 2
		TIMES C , 0.50
	ELSEIF CFLAG:MASTER:0 < 3
		TIMES C , 0.60
	ELSEIF CFLAG:MASTER:0 < 4
		TIMES C , 0.70
	ELSEIF CFLAG:MASTER:0 < 5
		TIMES C , 0.80
	ELSEIF CFLAG:MASTER:0 < 10
		TIMES C , 0.90
	ENDIF
ENDIF

SIF TFLAG:61 == 1 || TFLAG:61 == 2
	C -= TFLAG:61 * 3
SIF TFLAG:61 == 3 || TFLAG:61 == 4
	C -= 3
SIF TFLAG:61 == 5
	C += 8
SIF TFLAG:61 == 6
	C -= 8
SIF TFLAG:61 == 7
	C -= 5

;調教者の行動による実行難易度
IF TFLAG:90 == 2
	D = 20
ELSEIF TFLAG:90 == 3
	D = 10
ELSEIF TFLAG:90 == 4
	D = 35
ELSEIF TFLAG:90 == 6
	D = 20
ELSEIF TFLAG:90 == 7
	D = 35
	SIF TALENT:MASTER:77
		D = 20
ELSEIF TFLAG:90 == 66
	D = 40
	SIF TALENT:MASTER:77
		D = 20
ELSE
	D = 15
ENDIF

SIF TFLAG:64 < 2
	D -= 2

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0

;ボールギャグ
SIF TEQUIP:42
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;強気に応答するの実行判定
;────────────────────────────────────
@COM_ABLE4
;一括管理
SIF GLOBAL_COMABLE(4)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 2 && TFLAG:90 != 3 && TFLAG:90 != 4 && TFLAG:90 != 5 && TFLAG:90 != 6 && TFLAG:90 != 7 && TFLAG:90 != 8 && TFLAG:90 != 9 && TFLAG:90 != 27 && TFLAG:90 != 66 && TFLAG:90 != 73 && TFLAG:90 != 90 && TFLAG:90 != 91 && TFLAG:90 != 92
	RETURN 0
;道具/プレイ解除の判定
SIF TFLAG:90 == 26 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 43 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 44 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 63 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 64 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 65 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 67 && TFLAG:93 != 1
	RETURN 0
;判定
A = TALENT:12 * 7 - TALENT:10 * 7 + TALENT:MASTER:15 * 3 + TALENT:MASTER:11 * 4 - TALENT:MASTER:13 * 4 + TALENT:MASTER:16 * 3 - TALENT:MASTER:17 * 3 + TALENT:MASTER:26 * 3 - TALENT:MASTER:27 * 3 - TALENT:MASTER:34 * 2
B = PALAM:9 / 500 + PALAM:12 / 1500 + PALAM:13 / 2000 - PALAM:10 / 1500 - PALAM:7 / 1000 - PALAM:8 / 1500 - PALAM:6 / 1500 - SOURCE:23 / 200
C = 1 - ABL:MASTER:0 * 2 - TALENT:90 * 5 - MARK:MASTER:4 * 2

SIF CFLAG:MASTER:8 == TFLAG:90
	C -= MARK:MASTER:4 * 2

SIF TFLAG:61 == 1 || TFLAG:61 == 2
	C -= TFLAG:61 * 3
SIF TFLAG:61 == 3 || TFLAG:61 == 4
	C -= 5
SIF TFLAG:61 == 5
	C -= 4
SIF TFLAG:61 == 6
	C += 6
SIF TFLAG:61 == 8
	C += 6

;調教者の行動による実行難易度
IF TFLAG:90 == 1
	D = 2
ELSEIF TFLAG:90 == 2
	D = 2
ELSEIF TFLAG:90 == 3
	D = 20
ELSEIF TFLAG:90 == 4
	D = 10
ELSEIF TFLAG:90 == 5
	D = 10
ELSEIF TFLAG:90 == 7
	D = 2
	SIF TALENT:MASTER:77
		D = 25
ELSEIF TFLAG:90 == 9
	D = 10
ELSE
	D = 8
ENDIF

SIF TFLAG:64 < 2
	D -= 2

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0

;ボールギャグ
SIF TEQUIP:42
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;皮肉を言うの実行判定
;────────────────────────────────────
@COM_ABLE5
;一括管理
SIF GLOBAL_COMABLE(5)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 3 && TFLAG:90 != 4 && TFLAG:90 != 6 && TFLAG:90 != 7 && TFLAG:90 != 66 && TFLAG:90 != 90 && TFLAG:90 != 91 && TFLAG:90 != 92 && TFLAG:90 != 26 && TFLAG:90 != 43 && TFLAG:90 != 44 && TFLAG:90 != 63 && TFLAG:90 != 64 && TFLAG:90 != 65 && TFLAG:67 != 67
	RETURN 0
;道具/プレイ解除の判定
SIF TFLAG:90 == 26 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 43 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 44 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 63 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 64 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 65 && TFLAG:93 != 1
	RETURN 0
SIF TFLAG:90 == 67 && TFLAG:93 != 1
	RETURN 0
;判定
A = TALENT:MASTER:11 * 7 - TALENT:MASTER:13 * 7 + TALENT:MASTER:12 * 3 - TALENT:MASTER:10 * 3 + TALENT:MASTER:27 * 2 - TALENT:MASTER:26 * 2 + TALENT:MASTER:28 * 5 + TALENT:MASTER:29 * 3
B = PALAM:13 / 1000 + PALAM:12 / 1500 + PALAM:9 / 1500 - PALAM:6 / 1000 - PALAM:8 / 1000 - SOURCE:23 / 500
C = 1 - ABL:MASTER:0 * 3 + TALENT:90 * 5 + MARK:3 * 3

SIF TFLAG:61 == 1 || TFLAG:61 == 2
	C -= TFLAG:61 * 2
SIF TFLAG:61 == 3 || TFLAG:61 == 4
	C -= 3
SIF TFLAG:61 == 5
	C -= 5
SIF TFLAG:61 == 6
	C += 9
SIF TFLAG:61 == 7
	C += 5

;調教者の行動による実行難易度
IF TFLAG:90 == 0
	D = 10
ELSEIF TFLAG:90 == 1
	D = 5
ELSEIF TFLAG:90 == 3
	D = 25
ELSEIF TFLAG:90 == 4
	D = 15
ELSEIF TFLAG:90 == 5
	D = 15
ELSEIF TFLAG:90 == 7
	D = 5
	SIF TALENT:MASTER:77
		D = 25
ELSEIF TFLAG:90 == 9
	D = 15
ELSE
	D = 12
ENDIF

SIF TFLAG:64 < 2
	D -= 2

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0

;ボールギャグ
SIF TEQUIP:42
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;もっとハードにしての実行判定
;────────────────────────────────────
@COM_ABLE6
;一括管理
SIF GLOBAL_COMABLE(6)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 3 && TFLAG:90 != 7 && TFLAG:90 != 10 && TFLAG:90 != 11 && TFLAG:90 != 12 && TFLAG:90 != 15 && TFLAG:90 != 16 && TFLAG:90 != 17 && TFLAG:90 != 19 && TFLAG:90 != 24 && TFLAG:90 != 25 && TFLAG:90 != 30 && TFLAG:90 != 60 && TFLAG:90 != 73 && TFLAG:90 != 90 && TFLAG:90 != 91 && TFLAG:90 != 92 && TFLAG:90 != 26 && TFLAG:90 != 43 && TFLAG:90 != 44 && TFLAG:90 != 63 && TFLAG:90 != 64 && TFLAG:90 != 65 && TFLAG:67 != 67
	RETURN 0
;判定
A = TALENT:MASTER:23 * 2 - TALENT:MASTER:22 * 2 + TALENT:MASTER:25 * 2 - TALENT:MASTER:24 * 2 - TALENT:MASTER:32 * 5 + TALENT:MASTER:33 * 5 - TALENT:MASTER:34 * 2 + TALENT:MASTER:35 * 2 + TALENT:MASTER:63 * 2 - TALENT:MASTER:65 * 2 + TALENT:MASTER:76 * 5 + TALENT:MASTER:77 * 7
B = PALAM:5 / 1000 - PALAM:10 / 1500 - PALAM:12 / 1500
C = ABL:MASTER:0 + ABL:MASTER:1 + ABL:MASTER:11 * 5 + ABL:MASTER:15 * 3 + ABL:MASTER:16 * 3 - TALENT:93 * 5 + MARK:MASTER:0 * 3

;媚薬
SIF TEQUIP:11
	B += 5

SIF TALENT:MASTER:28 && (TALENT:MASTER:78 == 0 || TALENT:MASTER:79 == 0)
	A -= 5

SIF TFLAG:61 == 1 || TFLAG:61 == 2
	C -= TFLAG:61 * 5
SIF TFLAG:61 == 3
	C -= 5
SIF TFLAG:61 == 4
	C += 3
SIF TFLAG:61 == 5
	C += 8

SIF TFLAG:61 == 8
	C += 6

;調教者の行動による実行難易度
IF TFLAG:90 == 3
	D = 15
ELSEIF TFLAG:90 == 7
	D = 40
ELSEIF TFLAG:90 == 15
	D = 20
ELSEIF TFLAG:90 == 20
	D = 25
ELSEIF TFLAG:90 == 30
	D = 35
ELSEIF TFLAG:90 == 73
	D = 28
ELSE
	D = 30
ENDIF

SIF TFLAG:64 < 2
	D -= 2

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0

;ボールギャグ
SIF TEQUIP:42
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;許しを乞うの実行判定
;────────────────────────────────────
@COM_ABLE7
;一括管理
SIF GLOBAL_COMABLE(7)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 2 && TFLAG:90 != 3 && TFLAG:90 != 4 && TFLAG:90 != 7 && TFLAG:90 != 8 && TFLAG:90 != 9 && TFLAG:90 != 13 && TFLAG:90 != 14 && TFLAG:90 != 22 && TFLAG:90 != 23 && TFLAG:90 != 26 && TFLAG:90 != 27 && TFLAG:90 != 31 && TFLAG:90 != 34 && TFLAG:90 != 35 && TFLAG:90 != 36 && TFLAG:90 != 40 && TFLAG:90 != 41 && TFLAG:90 != 43 && TFLAG:90 != 44 && TFLAG:90 != 45 && TFLAG:90 != 55 && TFLAG:90 != 60 && TFLAG:90 != 61 && TFLAG:90 != 62 && TFLAG:90 != 63 && TFLAG:90 != 64 && TFLAG:90 != 67 && TFLAG:90 != 68 && TFLAG:90 != 70 && TFLAG:90 != 71 && TFLAG:90 != 72 && TFLAG:90 != 73 && TFLAG:90 != 74 && TFLAG:90 != 90 && TFLAG:90 != 91 && TFLAG:90 != 92
	RETURN 0
;判定
A = TALENT:MASTER:13 * 2 - TALENT:MASTER:11 * 2 + TALENT:MASTER:17 * 5 - TALENT:MASTER:15 * 5 - TALENT:MASTER:20 * 3 + TALENT:MASTER:21 * 3 - TALENT:MASTER:41 * 5 + TALENT:MASTER:40 * 5
B = PALAM:10 / 800 + PALAM:11 / 1500 + PALAM:12 / 1200 + PALAM:13 / 1500 - PALAM:9 / 1000
C = ABL:MASTER:0 * 3 - ABL:MASTER:11 * 3 - ABL:MASTER:15 * 2 - ABL:MASTER:16 * 2 - MARK:MASTER:0 * 2

;媚薬
SIF TEQUIP:11
	B += 5

SIF TFLAG:61 == 1 || TFLAG:61 == 2
	C += TFLAG:61 * 3
SIF TFLAG:61 == 4
	C += 3
SIF TFLAG:61 == 5
	C -= 5
SIF TFLAG:61 == 6
	C -= 8
SIF TFLAG:61 == 8
	C -= 6

;調教者の行動による実行難易度
IF TFLAG:90 == 0
	D = 25
ELSEIF TFLAG:90 == 1
	D = 25
ELSEIF TFLAG:90 == 2
	D = 22
ELSEIF TFLAG:90 == 3
	D = 15
ELSEIF TFLAG:90 == 4
	D = 15
ELSEIF TFLAG:90 == 7
	D = 0
ELSEIF TFLAG:90 == 8
	D = 10
ELSEIF TFLAG:90 == 26 && TFLAG:93 != 1
	D = 15
ELSEIF TFLAG:90 == 27 && TFLAG:93 != 1
	D = 10
ELSEIF TFLAG:90 == 35
	D = 15
ELSEIF TFLAG:90 == 36
	D = 10
ELSEIF TFLAG:90 == 40 && TFLAG:93 != 1
	D = 15
ELSEIF TFLAG:90 == 44
	D = 10
ELSEIF TFLAG:90 == 61
	D = 18
ELSEIF TFLAG:90 == 62
	D = 12
ELSEIF TFLAG:90 == 63
	D = 15
ELSEIF TFLAG:90 == 67 && TFLAG:93 != 1
	D = 5
ELSEIF TFLAG:90 == 68 && TFLAG:93 != 1
	D = 10
ELSEIF TFLAG:90 == 70
	D = 5
ELSEIF TFLAG:90 == 71
	D = 2
ELSEIF TFLAG:90 == 72
	D = 0
ELSEIF TFLAG:90 == 74
	D = 0
ELSEIF TFLAG:90 == 90
	D = 30
ELSEIF TFLAG:90 == 91
	D = 25
ELSEIF TFLAG:90 == 92
	D = 35
ELSE
	D = 20
ENDIF

SIF TFLAG:64 < 2
	D -= 2

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0

;ボールギャグ
SIF TEQUIP:42
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;気持ち良くしての実行判定
;────────────────────────────────────
@COM_ABLE8
;一括管理
SIF GLOBAL_COMABLE(8)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 2 && TFLAG:90 != 3 && TFLAG:90 != 6 && TFLAG:90 != 27 && TFLAG:90 != 42 && TFLAG:90 != 43 && TFLAG:90 != 44 && TFLAG:90 != 45 && TFLAG:90 != 66 && TFLAG:90 != 73 && TFLAG:90 != 90 && TFLAG:90 != 91 && TFLAG:90 != 92
	RETURN 0
;判定
A = TALENT:MASTER:17 * 3 - TALENT:MASTER:15 * 3 + TALENT:MASTER:21 * 5 - TALENT:MASTER:20 * 5 - TALENT:MASTER:24 * 3 + TALENT:MASTER:25 * 3 - TALENT:MASTER:32 * 7 + TALENT:MASTER:33 * 7 + TALENT:MASTER:35 * 2 - TALENT:MASTER:34 * 2 + TALENT:MASTER:70 * 5 - TALENT:MASTER:70 * 5 + TALENT:MASTER:76 * 5
B = PALAM:5 / 1500 + PALAM:6 / 1000 - PALAM:9 / 1000 - PALAM:13 / 1000
C = ABL:MASTER:1 * 3 + TALENT:91 * 5 + TALENT:92 * 3 - TALENT:93 * 3 + MARK:MASTER:1 * 2 - MARK:3 * 2 - MARK:MASTER:4 * 2

SIF CFLAG:MASTER:8 == TFLAG:90
	C -= MARK:MASTER:4 * 2

SIF TALENT:MASTER:28 && (TALENT:MASTER:78 == 0 || TALENT:MASTER:79 == 0)
	A -= 5

;媚薬
SIF TEQUIP:11
	B += 5

;調教レベルを見る
IF C > 0
	IF CFLAG:MASTER:0 < 1
		TIMES C , 0.40
	ELSEIF CFLAG:MASTER:0 < 2
		TIMES C , 0.50
	ELSEIF CFLAG:MASTER:0 < 3
		TIMES C , 0.60
	ELSEIF CFLAG:MASTER:0 < 4
		TIMES C , 0.70
	ELSEIF CFLAG:MASTER:0 < 5
		TIMES C , 0.80
	ELSEIF CFLAG:MASTER:0 < 10
		TIMES C , 0.90
	ENDIF
ENDIF

SIF TFLAG:61 == 1 || TFLAG:61 == 2
	C -= TFLAG:61 * 3
SIF TFLAG:61 == 3
	C -= 3
SIF TFLAG:61 == 4
	C += 8
SIF TFLAG:61 == 5
	C += 8
SIF TFLAG:61 == 6
	C -= 5
SIF TFLAG:61 == 8
	C += 7

;調教者の行動による実行難易度
IF TFLAG:90 == 0
	D = 15
ELSEIF TFLAG:90 == 1
	D = 15
ELSEIF TFLAG:90 == 3
	D = 30
ELSEIF TFLAG:90 == 6
	D = 35
ELSEIF TFLAG:90 == 27
	D = 30
ELSEIF TFLAG:90 == 44
	D = 10
ELSEIF TFLAG:90 == 73
	D = 10
ELSE
	D = 20
ENDIF

SIF TFLAG:64 < 2
	D -= 2

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0

;ボールギャグ
SIF TEQUIP:42
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;恥ずかしがる
;────────────────────────────────────
@COM_ABLE10
;一括管理
SIF GLOBAL_COMABLE(10)
	RETURN RESULT
A = 0
B = 0
U = 0

;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;快感のソースが入って、強すぎるでもない場合
IF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 0 || TFLAG:90 == 15
	CALL ABLE_KAIKAN
	IF U < 250
		A = 10
	ELSEIF U < 500
		A = 8
	ELSEIF U < 750
		A = 5
	ELSEIF U < 1000
		A = 2
	ENDIF
	;媚薬
	SIF TEQUIP:11
		A -= 3
	A -= PALAM:5 / 2000
	A -= PALAM:10 / 2000
	A += PALAM:11 / 1000
	A -= MARK:MASTER:1 / 3
ENDIF

;恥情
IF PALAM:11 > 7499
	B = 10
ELSEIF PALAM:11 > 4999
	B = 7
ELSEIF PALAM:11 > 2499
	B = 4
ELSEIF PALAM:11 > 999
	B = 2
ELSEIF PALAM:11 > 499
	B = 1
ENDIF

;調教者行動は羞恥
SIF TFLAG:80 == 4
	B += 5
;調教者行動は奉仕
SIF TFLAG:80 == 5
	B += 3
;調教者行動は自慰/秘貝開帳/自慰みせつけ
SIF TFLAG:90 == 40 || TFLAG:90 == 41 || TFLAG:90 == 42
	B += 2
;調教者行動は素股/足コキ
SIF TFLAG:90 == 53 || TFLAG:90 == 54
	B += 1
;調教者行動は性知識の話
SIF TFLAG:90 == 2
	B += 3
;調教者行動は脱衣
SIF TFLAG:90 == 5 && TFLAG:93 == 0
	B += 4
;調教者行動は放置プレイ
SIF TFLAG:90 == 73
	B += 5
;アイマスク
SIF TEQUIP:41
	B += 2
;野外プレイ
SIF TEQUIP:52
	B += 4
;羞恥プレイ
SIF TEQUIP:56
	B += 4
;自慰中
SIF TEQUIP:69
	B += 5

IF B > 0
	;素質による変動
	SIF TALENT:MASTER:34 && B > 0
		B += 2
	SIF TALENT:MASTER:35 && B > 0
		B -= 2
	;羞恥よりも苦痛
	SIF PALAM:10 > PALAM:11
		B -= 3
	;快感が強すぎる
	IF U > 10000
		B -= 25
	ELSEIF U > 7000
		B -= 18
	ELSEIF U > 5000
		B -= 13
	ELSEIF U > 3500
		B -= 9
	ELSEIF U > 2000
		B -= 5
	ELSEIF U > 1000
		B -= 1
	ENDIF
	;理性
	IF BASE:MASTER:5 > 800
		B += 3
	ELSEIF BASE:MASTER:5 > 600
		B += 1
	ELSEIF BASE:MASTER:5 > 400
		B += 0
	ELSEIF BASE:MASTER:5 > 200
		B -= 1
	ELSEIF BASE:MASTER:5 > 100
		B -= 2
	ELSE
		B -= 5
	ENDIF
	;状態
	SIF TFLAG:61 == 4
		B -= 2
	SIF TFLAG:61 == 5
		B -= 2
ENDIF

;快感か恥情どちらでも条件満足できたら実行可能
SIF A < 1 && B < 1
	RETURN 0
;性交実行や挿入中はなし
SIF TFLAG:80 == 3 || TEQUIP:70
	RETURN 0
;行動加虐はなし
SIF TFLAG:80 == 6
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;嫌がる
;────────────────────────────────────
@COM_ABLE11
;一括管理
SIF GLOBAL_COMABLE(11)
	RETURN RESULT
A = 0
B = 0
U = 0

;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;快感のソースが入って、強すぎるでもない場合
IF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 0 || TFLAG:90 == 15
	CALL ABLE_KAIKAN
	IF U < 400
		A = 10
	ELSEIF U < 850
		A = 8
	ELSEIF U < 1400
		A = 5
	ELSEIF U < 2000
		A = 2
	ELSEIF U < 2600
		A = -1
	ELSEIF U < 3300
		A = -2
	ELSEIF U < 4100
		A = -3
	ELSEIF U < 5000
		A = -5
	ELSE
		A = -8
	ENDIF
	;媚薬
	SIF TEQUIP:11
		A -= 3
	A -= PALAM:5 / 1500
	A += PALAM:9 / 800
	A -= PALAM:8 / 1000
	A -= PALAM:6 / 1200
	A -= MARK:2 / 3
	A += MARK:3 / 3
	A -= SOURCE:23 / 500
ENDIF

;恥情
;調教者行動は羞恥
SIF TFLAG:80 == 4
	B += 3
;調教者行動は奉仕
SIF TFLAG:80 == 5
	B += 4
;調教者行動は自慰/秘貝開帳
SIF TFLAG:90 == 40 || TFLAG:90 == 41
	B += 4
;調教者行動は素股/足コキ
SIF TFLAG:90 == 53 || TFLAG:90 == 54
	B += 1
;調教者行動は脱衣
SIF TFLAG:90 == 5 && TFLAG:93 == 0
	B += 5

IF B > 0
	IF PALAM:11 > 7499
		B = 10
	ELSEIF PALAM:11 > 4999
		B = 7
	ELSEIF PALAM:11 > 2499
		B = 4
	ELSEIF PALAM:11 > 999
		B = 2
	ELSEIF PALAM:11 > 499
		B = 1
	ENDIF
	;素質による変動
	SIF TALENT:MASTER:35 && B > 0
		B -= 2
	SIF TALENT:MASTER:11 && B > 0
		B += 2
	SIF TALENT:MASTER:16 && B > 0
		B += 2
	;羞恥よりも苦痛
	SIF PALAM:10 > PALAM:11
		B -= 3
	;屈服
	SIF PALAM:6 > PALAM:11
		B -= 3
	;恭順
	SIF PALAM:8 > PALAM:11
		B -= 4
	;恥ずかしいより優先な要因
	SIF SOURCE:13
		B -= 3
	SIF SOURCE:24
		B -= 5
	;快感が強すぎる
	IF U > 10000
		B -= 20
	ELSEIF U > 7000
		B -= 14
	ELSEIF U > 5000
		B -= 9
	ELSEIF U > 3500
		B -= 6
	ELSEIF U > 2000
		B -= 3
	ELSEIF U > 1000
		B -= 1
	ENDIF
	;理性
	IF BASE:MASTER:5 > 800
		B += 4
	ELSEIF BASE:MASTER:5 > 600
		B += 2
	ELSEIF BASE:MASTER:5 > 400
		B += 0
	ELSEIF BASE:MASTER:5 > 200
		B -= 2
	ELSEIF BASE:MASTER:5 > 100
		B -= 4
	ELSE
		B -= 7
	ENDIF
	;状態
	SIF TFLAG:61 == 4
		B -= 5
	SIF TFLAG:61 == 5
		B -= 2
ENDIF

;不潔のソース
IF TALENT:MASTER:64 == 0
	IF SOURCE:21 > 1000
		A += 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
		B += 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 600
		A += 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
		B += 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 250
		A += 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
		B += 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 50
		A += 1
		B += 1
	ENDIF
ENDIF

;快感か恥情どちらでも条件満足できたら実行可能
SIF A < 1 && B < 1
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;下手だと罵る
;────────────────────────────────────
@COM_ABLE12
;一括管理
SIF GLOBAL_COMABLE(12)
	RETURN RESULT
A = 0
B = 0
U = 0

;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;快感のソースが入って、強すぎるでもない場合
IF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 0 || TFLAG:90 == 15
	CALL ABLE_KAIKAN
	IF U < 200
		A = 14
	ELSEIF U < 400
		A = 10
	ELSEIF U < 900
		A = 7
	ELSEIF U < 1400
		A = 4
	ELSEIF U < 2000
		A = 1
	ELSEIF U < 2600
		A = -3
	ELSEIF U < 3500
		A = -6
	ELSEIF U < 5000
		A = -10
	ELSE
		A = -15
	ENDIF
	;媚薬
	SIF TEQUIP:11
		A -= 3
	;今の状況
	A -= PALAM:5 / 2000
	A += PALAM:9 / 800
	A += PALAM:12 / 1000
	A -= PALAM:8 / 1000
	A -= PALAM:6 / 1000
	A -= SOURCE:23 / 500
	;過去の経験
	A -= MARK:2 / 3
	A -= MARK:MASTER:1 / 3
	A += MARK:3 / 2
	;性格
	A += TALENT:MASTER:15 * 3 - TALENT:MASTER:17 * 2 + TALENT:MASTER:11 * 3 - TALENT:MASTER:13 * 2 + TALENT:MASTER:16 * 3 - TALENT:MASTER:14 * 2 + TALENT:MASTER:25 * 2 - TALENT:MASTER:24 - TALENT:MASTER:70 * 5 + TALENT:MASTER:71 * 3
	;疲れ
	SIF TFLAG:61 > 0 && TFLAG:61 < 4
		A -= 3
	;朦朧
	SIF TFLAG:61 == 4
		A -= 3
	;欲情
	SIF TFLAG:61 == 5
		A -= 5
	;怒り
	SIF TFLAG:61 == 6
		A += 5
	;退屈
	SIF TFLAG:61 == 7
		A += 3
	;狂気
	SIF TFLAG:61 == 8
		A += 7
ENDIF

;調教者による快感じゃないと対象外
SIF TFLAG:80 != 1 && TFLAG:80 != 3 
	RETURN 0

;ボールギャグ
SIF TEQUIP:42
	RETURN 0

;快感が条件満足できたら実行可能
SIF A < 1
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;不敵に笑う
;────────────────────────────────────
@COM_ABLE13
;一括管理
SIF GLOBAL_COMABLE(13)
	RETURN RESULT
A = 0
B = 0
S = 0
U = 0

;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;快感のソースが入って、強すぎるでもない場合
IF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 0 || TFLAG:90 == 15
	CALL ABLE_KAIKAN
	IF U < 300
		A = 12
	ELSEIF U < 650
		A = 9
	ELSEIF U < 1000
		A = 6
	ELSEIF U < 1450
		A = 3
	ELSEIF U < 2000
		A = 1
	ELSEIF U < 2600
		A = -2
	ELSEIF U < 3300
		A = -5
	ELSEIF U < 4000
		A = -8
	ELSE
		A = -12
	ENDIF
	;媚薬
	SIF TEQUIP:11
		A -= 3
	;今の状況
	A -= PALAM:7 / 500
	A -= PALAM:10 / 1000
	A -= PALAM:12 / 1200
	A += PALAM:9 / 1200
	A -= PALAM:13 / 1000
	A -= PALAM:11 / 1000
	A -= SOURCE:23 / 500
	;過去の経験
	A -= MARK:MASTER:4 / 2
	SIF CFLAG:MASTER:8 == TFLAG:90
		A -= MARK:MASTER:4 * 2
	A += MARK:3 / 2
	;性格
	A += TALENT:MASTER:12 * 5 - TALENT:MASTER:10 * 3 + TALENT:MASTER:16 * 2 - TALENT:MASTER:14 + TALENT:MASTER:15 * 3 - TALENT:MASTER:17 * 2 + TALENT:MASTER:26 * 2 - TALENT:MASTER:25 - TALENT:MASTER:34 * 5 + TALENT:MASTER:71 * 3
	;疲れ
	SIF TFLAG:61 > 0 && TFLAG:61 < 3
		A -= 1
	;無気力
	SIF TFLAG:61 == 3
		A -= 3
	;怒り
	SIF TFLAG:61 == 6
		A += 2
	;狂気
	SIF TFLAG:61 == 8
		A += 3
ENDIF

;調教者の行動は加虐、もしくは痛みや拘束のソースが入る状況
IF TFLAG:80 == 6 || SOURCE:13 + SOURCE:14 > 0
	CALL ABLE_GYAKU
	IF S < 250
		B = 12
	ELSEIF S < 600
		B = 8
	ELSEIF S < 1000
		B = 5
	ELSEIF S < 1600
		B = 3
	ELSEIF S < 2400
		B = 2
	ELSEIF S < 3600
		B = 1
	ENDIF
ENDIF

SIF TFLAG:90 == 56 && B == 0
	B = 3

IF B > 0
	B += TALENT:MASTER:12 * 7 - TALENT:MASTER:10 * 5 + TALENT:MASTER:15 - TALENT:MASTER:17 * 2 + TALENT:MASTER:20 - TALENT:MASTER:21 * 2 - TALENT:MASTER:77 * 10 + TALENT:MASTER:115 * 5 + TALENT:MASTER:111 * 3 - TALENT:MASTER:110 * 3
	B += MARK:3 * 2 - MARK:MASTER:0 * 2 - MARK:MASTER:4
	SIF CFLAG:MASTER:8 == TFLAG:90
		B -= MARK:MASTER:4
ENDIF

;調教者の行動は奉仕じゃだめ
SIF TFLAG:80 == 5
	RETURN 0

;快感か苦痛どちらでも条件満足できたら実行可能
SIF A < 1 && B < 1
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;快感を我慢する
;────────────────────────────────────
@COM_ABLE14
;一括管理
SIF GLOBAL_COMABLE(14)
	RETURN RESULT
A = 0
U = 0
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;イカせて選択後
SIF TFLAG:167
	RETURN 0
;快感のソースが入って、かなり強い場合
IF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 0 || TFLAG:90 == 15
	CALL ABLE_KAIKAN
	IF U > 12000
		A = 40
	ELSEIF U > 11000
		A = 33
	ELSEIF U > 10000
		A = 26
	ELSEIF U > 9000
		A = 20
	ELSEIF U > 8000
		A = 14
	ELSEIF U > 7000
		A = 9
	ELSEIF U > 6000
		A = 5
	ELSEIF U > 5000
		A = 2
	ELSEIF U > 4000
		A = 1
	ELSEIF FLAG:2004
		A = 1
	ENDIF
	;今の状況
	IF PALAM:5 > 8000
		TIMES A , 1.35
	ELSEIF PALAM:5 > 7000
		TIMES A , 1.25
	ELSEIF PALAM:5 > 6000
		TIMES A , 1.15
	ELSEIF PALAM:5 > 5000
		TIMES A , 1.05
	ENDIF
	;媚薬
	SIF TEQUIP:11
		A += 3
	;朦朧
	SIF TFLAG:61 == 4
		TIMES A , 1.10
	;情欲
	IF TFLAG:61 == 5
		A += 1
		TIMES A , 1.20
	ENDIF
	;過去の経験
	A += MARK:MASTER:1
	;我慢できるかの判定
	IF A > 10
		SIF TALENT:MASTER:15
			A -= 3
		SIF TALENT:MASTER:17
			A += 3
		SIF TALENT:MASTER:20
			A -= 5
		SIF TALENT:MASTER:17
			A += 5
		SIF TALENT:MASTER:32
			A -= 4
		SIF TALENT:MASTER:33
			A += 4
		A -= BASE:MASTER:5 / 1000
		SIF A < 1
			A = 1
	ENDIF
ENDIF

;合意なしの騎乗位は受け入れない
SIF CFLAG:3 == 0 && TFLAG:90 == 32
	RETURN 0

;快感が条件満足できたら実行可能
SIF A < 1 || A > 20
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;快感を受け入れる
;────────────────────────────────────
@COM_ABLE15
;一括管理
SIF GLOBAL_COMABLE(15)
	RETURN RESULT
A = 0
U = 0
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;快感のソースが入って、とても強い場合
IF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 0 || TFLAG:90 == 15
	CALL ABLE_KAIKAN
	IF U > 10000
		A = 30
	ELSEIF U > 8500
		A = 25
	ELSEIF U > 7000
		A = 20
	ELSEIF U > 5500
		A = 16
	ELSEIF U > 4000
		A = 12
	ELSEIF U > 3000
		A = 8
	ELSEIF U > 2000
		A = 5
	ELSEIF U > 1000 || FLAG:2004
		A = 2
	ELSE
		A = 1
	ENDIF
	;今の状況
	IF PALAM:5 > 8000
		TIMES A , 1.35
	ELSEIF PALAM:5 > 7000
		TIMES A , 1.25
	ELSEIF PALAM:5 > 6000
		TIMES A , 1.15
	ELSEIF PALAM:5 > 5000
		TIMES A , 1.05
	ENDIF
	;媚薬
	SIF TEQUIP:11
		A += 3
	;朦朧
	SIF TFLAG:61 == 4
		TIMES A , 1.10
	;情欲
	IF TFLAG:61 == 5
		A += 1
		TIMES A , 1.20
	ENDIF
	;過去の経験
	A += MARK:MASTER:1 - MARK:3 - MARK:MASTER:4
	SIF CFLAG:MASTER:8 == TFLAG:90
		A -= MARK:MASTER:4
	;我慢に関連する性格
	A += TALENT:MASTER:17 * 3 - TALENT:MASTER:15 * 3 - TALENT:MASTER:20 * 5 + TALENT:MASTER:21 * 5 - TALENT:MASTER:32 * 4 + TALENT:MASTER:33 * 4

	;調教のプログレス
	IF FLAG:2004 == 0
		IF CFLAG:MASTER:0 < 1
			A -= 6
		ELSEIF CFLAG:MASTER:0 < 2
			A -= 5
		ELSEIF CFLAG:MASTER:0 < 4
			A -= 4
		ELSEIF CFLAG:MASTER:0 < 6
			A -= 3
		ELSEIF CFLAG:MASTER:0 < 8
			A -= 2
		ELSEIF CFLAG:MASTER:0 < 11
			A -= 1
		ENDIF
	ENDIF
	
	SIF TALENT:MASTER:28 && (TALENT:MASTER:78 == 0 || TALENT:MASTER:79 == 0)
		A -= 5
	SIF FLAG:2004 == 0
		A -= BASE:MASTER:5 / 1000
ENDIF

;合意なしの騎乗位は受け入れない
SIF CFLAG:3 == 0 && TFLAG:90 == 32
	RETURN 0

;快感が条件満足できたら実行可能
SIF A < 1
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;腰を振るの実行判定
;────────────────────────────────────
@COM_ABLE20
;一括管理
SIF GLOBAL_COMABLE(20)
	RETURN RESULT
A = 0

;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;焦らし
SIF TFLAG:126 > 1
	RETURN 0
SIF TFLAG:90 == 17 && TALENT:108
	RETURN 0
;挿入してる/されてる/しようとしてるなど、追加;パイズリ,素股
IF TFLAG:80 == 3 || TEQUIP:70 || TEQUIP:71 || TFLAG:90 == 17 || TFLAG:90 == 16
	IF (TFLAG:90 > 29 && TFLAG:90 < 35) || TEQUIP:70 < 6
		SIF TALENT:MASTER:73
			A += 3
		IF TALENT:MASTER:102
			A += 2
		ELSEIF TALENT:MASTER:103
			A -= 2
		ENDIF
		A += ABL:MASTER:4
	ELSEIF TFLAG:90 == 35 || TEQUIP:70 == 6
		SIF TALENT:MASTER:74
			A += 3
		IF TALENT:MASTER:104
			A += 2
		ELSEIF TALENT:MASTER:105
			A -= 2
		ENDIF
		A += ABL:MASTER:5
	ELSEIF TFLAG:90 == 36 || (TFLAG:90 >= 95 && TFLAG:90 <= 99) || TEQUIP:70 == 7 || TEQUIP:71 || TFLAG:90 == 17 || TFLAG:90 == 16
		SIF TALENT:MASTER:72
			A += 3
		IF TALENT:MASTER:100
			A += 2
		ELSEIF TALENT:MASTER:101
			A -= 2
		ENDIF
		A += ABL:MASTER:3
	ENDIF
	;欲望
	IF PALAM:5 > 8000
		A += 5
	ELSEIF PALAM:5 > 7000
		A += 4
	ELSEIF PALAM:5 > 6000
		A += 3
	ELSEIF PALAM:5 > 5000
		A += 2
	ELSEIF PALAM:5 > 4000
		A += 1
	ENDIF
	;媚薬
	SIF TEQUIP:11
		A += 3
	;朦朧
	SIF TFLAG:61 == 4
		TIMES A , 1.10
	;情欲
	IF TFLAG:61 == 5
		A += 1
		TIMES A , 1.20
	ENDIF
	A += TALENT:MASTER:70 * 5 - TALENT:MASTER:71 * 5 + TALENT:MASTER:13 * 3 - TALENT:MASTER:11 * 3 - TALENT:MASTER:32 * 3 + TALENT:MASTER:33 * 3
	A += MARK:MASTER:1 - MARK:3
ELSE
	A = -99
ENDIF

;合意なしの騎乗位は受け入れない
SIF CFLAG:3 == 0 && TFLAG:90 == 32
	RETURN 0

;体位によって難易度が分かれる
SIF A < TEQUIP:70
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;中に出して！の実行判定
;────────────────────────────────────
@COM_ABLE21
;一括管理
SIF GLOBAL_COMABLE(21)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;挿入しないとだめ
SIF TFLAG:80 != 3 && (TEQUIP:70 == 0 || TEQUIP:70 == 6)
	RETURN 0
;オトコはだめ
SIF TALENT:MASTER:122
	RETURN 0
;逆レイプはだめ
SIF TFLAG:90 == 36
	RETURN 0
;ある程度の性知識がないとだめ
SIF EXP:MASTER:8 < 10
	RETURN 0

;愛情が必要
A = CFLAG:0 / 10 + CFLAG:2 / 200 + TALENT:MASTER:78 * 7 + TALENT:MASTER:79 * 3 - MARK:3 * 2

;合意なし
SIF CFLAG:3 == 0
	A -= 2 + TALENT:MASTER:30 * 2 - TALENT:MASTER:31 * 2 + TALENT:MASTER:28 * 2

;合意なしの騎乗位は受け入れない
SIF CFLAG:3 == 0 && TFLAG:90 == 32
	RETURN 0

SIF A < 10
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;中に出さないで！の実行判定
;────────────────────────────────────
@COM_ABLE22
;一括管理
SIF GLOBAL_COMABLE(22)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;挿入しないとだめ
SIF TFLAG:80 != 3 && (TEQUIP:70 == 0 || TEQUIP:70 == 6)
	RETURN 0
;オトコはだめ
SIF TALENT:MASTER:122
	RETURN 0
;逆レイプはだめ
SIF TFLAG:90 == 36
	RETURN 0
;ある程度の性知識がないとだめ
SIF EXP:MASTER:8 < 10
	RETURN 0

;理性や嫌悪感が必要
A = 0

IF BASE:MASTER:5 < 200
	A -= 1
ELSEIF BASE:MASTER:5 < 400
	A += 1
ELSEIF BASE:MASTER:5 < 600
	A += 2
ELSEIF BASE:MASTER:5 < 800
	A += 3
ELSEIF BASE:MASTER:5 < 1000
	A += 5
ELSE
	A += 8
ENDIF

IF CFLAG:2 < 250
	A += 8
ELSEIF CFLAG:2 < 500
	A += 6
ELSEIF CFLAG:2 < 800
	A += 4
ELSEIF CFLAG:2 < 1150
	A += 2
ELSEIF CFLAG:2 < 1600
	A += 1
ELSE
	A -= 1
ENDIF

;性格による変動
A += TALENT:MASTER:20 * 3 - TALENT:MASTER:20 * 3 - TALENT:MASTER:17 * 2 + TALENT:MASTER:15 * 2
A += MARK:3 - MARK:MASTER:1 / 2

;合意なし
SIF CFLAG:3 == 0
	A += 2 + TALENT:MASTER:30 * 2 - TALENT:MASTER:31 * 2 + TALENT:MASTER:28 * 2

;朦朧か情欲
SIF TFLAG:61 == 4 || TFLAG:61 == 5
	A -= 3

;合意なしの騎乗位は受け入れない
SIF CFLAG:3 == 0 && TFLAG:90 == 32
	RETURN 0

SIF A < 1
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;痛みを我慢するの実行判定
;────────────────────────────────────
@COM_ABLE30
;一括管理
SIF GLOBAL_COMABLE(30)
	RETURN RESULT
B = 0
S = 0

;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;痛みや拘束のソースが入る状況
IF SOURCE:13 + SOURCE:14 > 0
	CALL ABLE_GYAKU
	IF S < 50
		B = -6
	ELSEIF S < 100
		B = -3
	ELSEIF S < 200
		B = -1
	ELSEIF S < 300
		B = 1
	ELSEIF S < 600
		B = 4
	ELSEIF S < 1000
		B = 8
	ELSEIF S < 1500
		B = 10
	ELSEIF S < 2200
		B = 14
	ELSEIF S < 3000
		B = 18
	ELSEIF S < 4000
		B = 22
	ELSEIF S < 5000
		B = 28
	ELSEIF S < 6000
		B = 34
	ELSE
		B = 40
	ENDIF

	;我慢できるかの判定
	B += TALENT:MASTER:10 * 7 - TALENT:MASTER:12 * 7 - TALENT:MASTER:15 * 5 + TALENT:MASTER:17 * 3 - TALENT:MASTER:20 * 2 + TALENT:MASTER:21 * 2 + TALENT:MASTER:77 * 8 - TALENT:MASTER:115 * 5 - TALENT:MASTER:111 * 3 + TALENT:MASTER:110 * 3
	B -= MARK:3 * 2 - MARK:MASTER:0 * 2 - MARK:MASTER:4
	IF B > 10
		B += PALAM:10 / 500 + PALAM:12 / 1000
		SIF CFLAG:MASTER:8 == TFLAG:90
			B += MARK:MASTER:4
		;朦朧
		SIF TFLAG:61 == 4
			B += 5
		;怒り
		SIF TFLAG:61 == 5
			B += 5
	ENDIF
ENDIF

;苦痛が条件満足できたら実行可能
SIF B < 1 || B > 20
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;悲鳴を上げるの実行判定
;────────────────────────────────────
@COM_ABLE31
;一括管理
SIF GLOBAL_COMABLE(31)
	RETURN RESULT
A = 0
B = 0
S = 0

;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;恐怖を感じる状況（トリガーイベント制）
IF TFLAG:90 == 4 || TFLAG:90 == 7 || TFLAG:90 == 8 || TFLAG:90 == 9 || TFLAG:90 == 66 || TFLAG:90 == 74
	A = 1 + PALAM:7 / 1000 + PALAM:10 / 1800
	SIF TFLAG:90 == 9
		A += 2
	SIF TFLAG:90 == 74 && TALENT:MASTER:77 == 0 && TALENT:MASTER:76 == 0
		A += 5
	A += TALENT:MASTER:10 * 3 - TALENT:MASTER:12 * 3 - TALENT:MASTER:15 * 2 + TALENT:MASTER:17 * 2 + TALENT:MASTER:14 - TALENT:MASTER:16 - TALENT:MASTER:77 * 10
	A -= MARK:3 * 2 - MARK:MASTER:4 * 2 + BASE:MASTER:5 / 100
	SIF CFLAG:MASTER:8 == TFLAG:90
		A += MARK:MASTER:4 * 2
ENDIF

;痛みや拘束のソースが入る状況、Bは抵抗の強度を表します
IF SOURCE:13 + SOURCE:14 > 0
	CALL ABLE_GYAKU
	IF S < 100
		B = 12
	ELSEIF S < 250
		B = 7
	ELSEIF S < 500
		B = 3
	ELSEIF S < 1000
		B = 2
	ELSEIF S < 1800
		B = 1
	ELSEIF S < 3200
		B = -1
	ELSEIF S < 5000
		B = -3
	ELSEIF S < 6600
		B = -6
	ELSEIF S < 8600
		B = -10
	ELSEIF S < 10500
		B = -16
	ELSE
		B = -25
	ENDIF
ENDIF

IF B > 0
	B += TALENT:MASTER:12 * 7 - TALENT:MASTER:10 * 5 + TALENT:MASTER:15 - TALENT:MASTER:17 * 2 + TALENT:MASTER:20 - TALENT:MASTER:21 * 2 - TALENT:MASTER:77 * 10 + TALENT:MASTER:115 * 5 + TALENT:MASTER:111 * 3 - TALENT:MASTER:110 * 3
	B += MARK:3 * 2 - MARK:MASTER:0 * 2 - MARK:MASTER:4
	SIF CFLAG:MASTER:8 == TFLAG:90
		B -= MARK:MASTER:4
ENDIF

;恐怖か苦痛どちらでも条件満足できたら実行可能
SIF A < 1 && B > -1
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;暴れるの実行判定
;────────────────────────────────────
@COM_ABLE32
;一括管理
SIF GLOBAL_COMABLE(32)
	RETURN RESULT
A = 0
B = 0
C = 0

;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;反抗しようとする状況を作る（トリガーイベント制）
;愛撫＆道具、Aは反抗の強度を表す
IF TFLAG:80 == 1 || TFLAG:80 == 2
	CALL ABLE_KAIKAN
	IF U > 10000
		A = -15
	ELSEIF U > 9000
		A = -11
	ELSEIF U > 8000
		A = -7
	ELSEIF U > 7000
		A = -3
	ELSEIF U > 6000
		A = 1
	ELSEIF U > 5000
		A = 3
	ELSEIF U > 4000
		A = 6
	ELSEIF U > 3000
		A = 9
	ELSEIF U > 2000
		A = 12
	ELSEIF U > 1000
		A = 8
	ELSE
		A = 3
	ENDIF
	A += TALENT:MASTER:12 + TALENT:MASTER:16 * 2 - TALENT:MASTER:14 * 4 + TALENT:MASTER:11 * 5 - TALENT:MASTER:13 * 2 + TALENT:MASTER:25 - TALENT:MASTER:24 - TALENT:MASTER:76 * 5
	A += PALAM:9 / 1000 + PALAM:12 / 1000 + BASE:MASTER:5 / 100 - PALAM:5 / 1000
	A += MARK:3 - MARK:MASTER:1
	;媚薬
	SIF TEQUIP:11
		A -= 3
	;縛り
	A -= TEQUIP:40 * 3 + TEQUIP:41 * 2 + TEQUIP:42
	;朦朧
	SIF TFLAG:61 == 4
		A -= 2
	;情欲
	SIF TFLAG:61 == 5
		A -= 3
	;装着してる道具の解除は対象にならない
	SIF TFLAG:80 == 3 && TFLAG:93 == 1
		A = 0
ENDIF

;性交
IF TFLAG:80 == 3 || TEQUIP:70 > 0
	IF (TFLAG:90 > 29 && TFLAG:90 < 35) || TEQUIP:70 < 6
		SIF TALENT:MASTER:73
			B -= 3
		IF TALENT:MASTER:102
			B -= 2
		ELSEIF TALENT:MASTER:103
			B += 2
		ENDIF
		B -= ABL:MASTER:4
	ELSEIF TFLAG:90 == 35 || TEQUIP:70 == 6
		SIF TALENT:MASTER:74
			B -= 3
		IF TALENT:MASTER:104
			B -= 2
		ELSEIF TALENT:MASTER:105
			B += 2
		ENDIF
		B -= ABL:MASTER:5
	ELSEIF TFLAG:90 == 36 || TEQUIP:70 == 7
		SIF TALENT:MASTER:72
			B -= 3
		IF TALENT:MASTER:100
			B -= 2
		ELSEIF TALENT:MASTER:101
			B += 2
		ENDIF
		B -= ABL:MASTER:3
	ENDIF
	;欲望
	IF PALAM:5 > 8000
		B -= 5
	ELSEIF PALAM:5 > 7000
		B -= 4
	ELSEIF PALAM:5 > 6000
		B -= 3
	ELSEIF PALAM:5 > 5000
		B -= 2
	ELSEIF PALAM:5 > 4000
		B -= 1
	ENDIF
	;媚薬
	SIF TEQUIP:11
		B -= 3
	;朦朧
	SIF TFLAG:61 == 4
		TIMES B , 1.10
	;情欲
	IF TFLAG:61 == 5
		B -= 1
		TIMES B , 1.20
	ENDIF
	;怒り
	SIF TFLAG:61 == 6
		B += 5
	;縛り
	B -= TEQUIP:40 * 5 + TEQUIP:41 * 2
	B += TALENT:MASTER:71 * 5 - TALENT:MASTER:70 * 5 + TALENT:MASTER:11 * 3 - TALENT:MASTER:13 * 3 + TALENT:MASTER:32 * 3 - TALENT:MASTER:33 * 3 + TALENT:MASTER:0 * 10 + TALENT:MASTER:1 * 5
	B += MARK:3 * 2 - MARK:MASTER:1 + BASE:MASTER:5 / 100
ENDIF

;加虐＆異常
IF TFLAG:80 == 6 || TFLAG:80 == 7 || TFLAG:90 == 56 || TFLAG:90 == 92
	;恐怖を見る。恐怖によって抵抗したい傾向が高まるが、あまりに恐怖しすぎると動けなくなる。気丈と臆病の処理もここで行う
	IF PALAM:7 > 8000
		IF TALENT:MASTER:10
			C = -10
		ELSEIF TALENT:MASTER:12
			C = 3
		ELSE
			C = -5
		ENDIF
	ELSEIF PALAM:7 > 6500
		IF TALENT:MASTER:10
			C = -6
		ELSEIF TALENT:MASTER:12
			C = 5
		ELSE
			C = -1
		ENDIF
	ELSEIF PALAM:7 > 5000
		IF TALENT:MASTER:10
			C = -2
		ELSEIF TALENT:MASTER:12
			C = 7
		ELSE
			C = 2
		ENDIF
	ELSEIF PALAM:7 > 3500
		IF TALENT:MASTER:10
			C = 1
		ELSEIF TALENT:MASTER:12
			C = 8
		ELSE
			C = 4
		ENDIF
	ELSEIF PALAM:7 > 2000
		IF TALENT:MASTER:10
			C = 2
		ELSEIF TALENT:MASTER:12
			C = 9
		ELSE
			C = 3
		ENDIF
	ELSE
		IF TALENT:MASTER:10
			C = 0
		ELSEIF TALENT:MASTER:12
			C = 5
		ELSE
			C = 2
		ENDIF
	ENDIF
	;行動のエキストラ補正
	SIF TFLAG:90 == 67
		C += 3
	SIF TFLAG:90 == 70
		C += 2
	SIF TFLAG:90 == 71
		C += 3
	SIF TFLAG:90 == 72
		C += 5
	SIF TFLAG:90 == 74
		C += 5
	C += TALENT:MASTER:11 * 6 - TALENT:MASTER:13 * 2 - TALENT:MASTER:77 * 5
	C += MARK:3 * 2 + MARK:MASTER:4 - ABL:MASTER:15 - ABL:MASTER:16
	SIF CFLAG:MASTER:8 == TFLAG:90 && C < 5
		C -= MARK:MASTER:4 * 2
	;無気力
	SIF TFLAG:61 == 3
		C -= 3
	;朦朧
	SIF TFLAG:61 == 4
		C -= 3
	;怒り
	SIF TFLAG:61 == 6
		C += 5
	;縛り
	C -= TEQUIP:40 * 4 + TEQUIP:41 * 4 + TEQUIP:42
	;例外の排除（体の接触がない行動は対象外）
	SIF TFLAG:90 == 66 || TFLAG:90 == 73
		C = 0
ENDIF

;不潔のソース
IF TALENT:MASTER:64 == 0 && A > 0
	IF SOURCE:21 > 1000
		A += 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 600
		A += 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 250
		A += 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 50
		A += 1
	ENDIF
ENDIF

;体力/気力/理性の残量を見る
IF BASE:MASTER:0 * 2 + BASE:MASTER:1 * 3 + BASE:MASTER:5 < 1500
	A -= 8
	B -= 9
	C -= 10
ELSEIF BASE:MASTER:0 * 2 + BASE:MASTER:1 * 3 + BASE:MASTER:5 < 2000
	A -= 6
	B -= 7
	C -= 9
ELSEIF BASE:MASTER:0 * 2 + BASE:MASTER:1 * 3 + BASE:MASTER:5 < 2500
	A -= 4
	B -= 5
	C -= 7
ELSEIF BASE:MASTER:0 * 2 + BASE:MASTER:1 * 3 + BASE:MASTER:5 < 3000
	A -= 2
	B -= 3
	C -= 5
ELSEIF BASE:MASTER:0 * 2 + BASE:MASTER:1 * 3 + BASE:MASTER:5 < 3500
	B -= 2
	C -= 3
ELSEIF BASE:MASTER:0 * 2 + BASE:MASTER:1 * 3 + BASE:MASTER:5 < 4000
	B -= 1
	C -= 2
ENDIF

;条件ひとつも満足できないとだめ
SIF A < 1 && B < 1 && C < 1
	RETURN 0
;木馬。実はこれでも暴れられるじゃないかなと思ったが追加の痛みなど処理が面倒なので
SIF TEQUIP:43
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;拒否するの実行判定
;────────────────────────────────────
@COM_ABLE40
;一括管理
SIF GLOBAL_COMABLE(40)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動
SIF TFLAG:80 != 5 && TFLAG:90 != 91 && TFLAG:90 != 45 && TFLAG:90 != 40 && TFLAG:90 != 32 && TFLAG:90 != 5 && TFLAG:90 != 6
	RETURN 0
;イラマチオ ぱふぱふ
SIF TFLAG:90 == 56 || (TFLAG:90 == 57 && TFLAG:93 == 1)
	RETURN 0

;拒否できない判定
A = TALENT:MASTER:15 * 3 - TALENT:MASTER:17 * 3 + TALENT:MASTER:11 * 4 - TALENT:MASTER:13 * 4 - TALENT:MASTER:33 * 5 + TALENT:MASTER:32 * 5 + TALENT:MASTER:34 * 3 + TALENT:MASTER:65 * 3 - TALENT:MASTER:63 * 5
B = PALAM:9 / 800 + PALAM:13 / 1000 - PALAM:8 / 500 - PALAM:6 / 500 - PALAM:10 / 500 + BASE:MASTER:5 / 100
C = MARK:3 * 2 - ABL:MASTER:0 * 2 - ABL:MASTER:7 * 2 - TALENT:90 * 5 - TALENT:91 * 5

SIF TFLAG:61 == 3
	C += 3
SIF TFLAG:61 == 4
	C -= 3
SIF TFLAG:61 == 6
	C += 5
SIF TFLAG:61 == 7
	C += 3

;調教者の行動による実行難易度
IF TFLAG:90 == 5
	IF TFLAG:93 == 1
		D = 40 - ABL:MASTER:8 * 10
	ELSE
		D = -1
	ENDIF
ELSEIF TFLAG:90 == 6
	D = 5
ELSEIF TFLAG:90 == 32
	D = 1
ELSEIF TFLAG:90 == 40
	D = ABL:MASTER:12 * 5
ELSEIF TFLAG:90 == 45
	D = -5
ELSEIF TFLAG:90 == 50
	D = 10
ELSEIF TFLAG:90 == 52 && TALENT:MASTER:108
	D = -5
ELSEIF TFLAG:90 == 54
	D = TALENT:MASTER:80 * 10 - 10
ELSEIF TFLAG:90 == 55
	D = TALENT:MASTER:80 * 10 - 15
ELSE
	D = 0
ENDIF

;不潔のソース
IF TALENT:MASTER:64 == 0
	IF SOURCE:21 > 1000
		A -= 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 600
		A -= 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 250
		A -= 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 50
		A -= 1
	ENDIF
ENDIF

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;消極的やるの実行判定
;────────────────────────────────────
@COM_ABLE41
;一括管理
SIF GLOBAL_COMABLE(41)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動
SIF TFLAG:80 != 5 && TFLAG:90 != 45 && TFLAG:90 != 40 && TFLAG:90 != 32 && TFLAG:90 != 5
	RETURN 0

A = 0
B = 0

;消極的にいられない判定
A += TALENT:MASTER:23 * 5 - TALENT:MASTER:22 * 5 + TALENT:MASTER:25 * 3 - TALENT:MASTER:24 * 3 + TALENT:MASTER:33 * 5 - TALENT:MASTER:32 * 5 - TALENT:MASTER:65 * 5 + TALENT:MASTER:63 * 7
A += PALAM:5 / 1200 + PALAM:8 / 800 - PALAM:9 / 1000 - PALAM:13 / 1000
A += MARK:2 + ABL:MASTER:0 + ABL:MASTER:7 * 2 - MARK:3 * 2 + TALENT:91 * 5

;状態による変動
SIF TFLAG:61 == 3
	A -= 5
SIF TFLAG:61 == 5
	A += 3
SIF TFLAG:61 == 6
	A -= 5
SIF TFLAG:61 == 7
	A -= 3

;了承できない判定
B += TALENT:MASTER:12 * 2 + TALENT:MASTER:15 * 7 - TALENT:MASTER:17 * 7 + TALENT:MASTER:11 * 2 + TALENT:MASTER:34 * 2 + TALENT:MASTER:32 * 3 - TALENT:MASTER:33 * 3 + TALENT:MASTER:63 * 7 - TALENT:MASTER:65 * 7
B += PALAM:9 / 1000 + PALAM:13 / 1500 + PALAM:11 / 1500 - PALAM:6 / 1200 - PALAM:8 / 1000
B += MARK:3 * 2 - MARK:2 * 2 - TALENT:90 * 5

;調教者の行動による実行難易度
SIF TFLAG:90 == 5 && TFLAG:93 == 0
	B -= 5

IF TFLAG:90 == 56
	A -= 5
	B = 0
ENDIF

SIF TFLAG:90 == 32 && CFLAG:3 == 0
	B += 10
SIF TFLAG:90 == 40
	B += 5 - ABL:MASTER:12 * 5

;不潔のソース
IF TALENT:MASTER:64 == 0
	IF SOURCE:21 > 1000
		A -= 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
		B += 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 600
		A -= 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
		B += 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 250
		A -= 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
		B += 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 50
		A -= 1
		B += 1
	ENDIF
ENDIF

;どちらでもチェックされたらアウト
SIF (A > 0 || B > 0) && FLAG:2004 == 0
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;積極的やるの実行判定
;────────────────────────────────────
@COM_ABLE42
;一括管理
SIF GLOBAL_COMABLE(42)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動
SIF TFLAG:80 != 5 && TFLAG:90 != 45 && TFLAG:90 != 40 && TFLAG:90 != 32 && TFLAG:90 != 5
	RETURN 0

;積極になれない判定
A = 5

A += TALENT:MASTER:22 * 5 - TALENT:MASTER:23 * 5 + TALENT:MASTER:24 * 3 - TALENT:MASTER:25 * 3 + TALENT:MASTER:32 * 5 - TALENT:MASTER:33 * 5 + TALENT:MASTER:65 * 5 - TALENT:MASTER:63 * 7
A += PALAM:12 / 1200 + PALAM:13 / 800 + PALAM:9 / 1000 - PALAM:8 / 800 - PALAM:6 / 1200
A += MARK:3 * 2 - ABL:MASTER:7 * 2 - MARK:2 * 2 - TALENT:91 * 5

SIF TFLAG:90 == 40
	A += 2 + TALENT:MASTER:34 * 3 - TALENT:MASTER:35 * 3 - TALENT:MASTER:60 * 5

;状態による変動
SIF TFLAG:61 == 3
	A -= 5
SIF TFLAG:61 == 5
	A += 3
SIF TFLAG:61 == 6
	A -= 5
SIF TFLAG:61 == 7
	A -= 3

;不潔のソース
IF TALENT:MASTER:64 == 0
	IF SOURCE:21 > 1000
		A += 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 600
		A += 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 250
		A += 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 50
		A += 1
	ENDIF
ENDIF

;チェックされたらアウト
SIF A > 0 && FLAG:2004 == 0
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;愛情を込めてやるの実行判定
;────────────────────────────────────
@COM_ABLE43
;一括管理
SIF GLOBAL_COMABLE(43)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動
SIF TFLAG:80 != 5 && TFLAG:90 != 32
	RETURN 0

;好感度と調教レベルなどが必要
SIF CFLAG:2 < 4000 - CFLAG:MASTER:0 * 100 - TALENT:MASTER:78 * 500
	RETURN 0

;反発刻印があるとだめ
SIF MARK:3 > 0
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;自慰し始めるの実行判定
;────────────────────────────────────
@COM_ABLE50
;一括管理
SIF GLOBAL_COMABLE(50)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 2 && TFLAG:90 != 15 && TFLAG:90 != 42 && TFLAG:90 != 43 && TFLAG:90 != 44 && TFLAG:90 != 50 && TFLAG:90 != 51 && TFLAG:90 != 55 && TFLAG:90 != 57 && TFLAG:90 != 66 && TFLAG:90 != 73 && TFLAG:90 != 90
	RETURN 0
;シックスナイン
SIF TFLAG:120 == 202
	RETURN 0
A = 0
B = 0

;欲望を抑えきれない判定
A += TALENT:MASTER:21 * 5 - TALENT:MASTER:15 * 3 - TALENT:MASTER:20 * 5 + TALENT:MASTER:60 * 5 + TALENT:MASTER:33 * 3 - TALENT:MASTER:32 * 3
A += PALAM:5 / 1000 - PALAM:10 / 1000 - PALAM:12 / 1500
A += MARK:MASTER:1 / 2 + ABL:MASTER:12 * 2 + ABL:MASTER:1

;レベル不足
IF CFLAG:MASTER:0 < 2
	A -= 7
ELSEIF CFLAG:MASTER:0 < 4
	A -= 5
ELSEIF CFLAG:MASTER:0 < 6
	A -= 3
ELSEIF CFLAG:MASTER:0 < 8
	A -= 2
ELSEIF CFLAG:MASTER:0 < 11
	A -= 1
ENDIF

;理性
IF BASE:MASTER:5 > 800
	A -= 5
ELSEIF BASE:MASTER:5 > 600
	A -= 3
ELSEIF BASE:MASTER:5 > 400
	A -= 2
ELSEIF BASE:MASTER:5 > 200
	A -= 1
ELSEIF BASE:MASTER:5 > 100
	A += 2
ELSE
	A += 5
ENDIF

;従順しすぎで勝手に自慰できない判定
B += TALENT:MASTER:79 * 5
B += PALAM:6 / 1200 + PALAM:8 / 1000 - PALAM:9 / 1000
B += ABL:MASTER:0 * 2 + MARK:2

;媚薬
IF TEQUIP:11
	A += 5
	B -= 5
ENDIF

;一線越えない
SIF TALENT:MASTER:28 && ABL:MASTER:12 == 0
	A = 0

;縄
SIF TEQUIP:40
	RETURN 0
;すでに自慰してる、性行中
SIF TEQUIP:69 == 1 || TEQUIP:69 == 3 || TEQUIP:70 || TEQUIP:71
	RETURN 0
;どちらでもチェックされたらアウト
SIF A < 1 || B > A
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;道具を外すの実行判定
;────────────────────────────────────
@COM_ABLE51
;一括管理
SIF GLOBAL_COMABLE(51)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;装備をつけられていないとだめ
SIF TEQUIP:20 == 0 && TEQUIP:25 == 0 && TEQUIP:26 == 0 && TEQUIP:27 == 0 && TEQUIP:30 == 0 && TEQUIP:31 == 0 && TEQUIP:35 == 0 && TEQUIP:36 == 0 && TEQUIP:41 == 0 && TEQUIP:42 == 0
	RETURN 0
A = 0

;反抗できるかの判定
A += TALENT:MASTER:12 * 2 + TALENT:MASTER:11 * 5 - TALENT:MASTER:13 * 3 - TALENT:MASTER:14 * 3 + TALENT:MASTER:16 * 3 - TALENT:MASTER:79 * 10
A += PALAM:9 / 1000 - PALAM:6 / 1200 - PALAM:8 / 1200
A += MARK:3 - MARK:2 - ABL:MASTER:0 - MARK:MASTER:4
SIF CFLAG:MASTER:8 == TFLAG:90
	A -= MARK:MASTER:4

;理性
IF BASE:MASTER:5 > 800
	A += 5
ELSEIF BASE:MASTER:5 > 600
	A += 3
ELSEIF BASE:MASTER:5 > 400
	A += 2
ELSEIF BASE:MASTER:5 > 200
	A += 1
ELSEIF BASE:MASTER:5 > 100
	A -= 2
ELSE
	A -= 5
ENDIF

;縄
SIF TEQUIP:40
	RETURN 0

;挿入中
SIF TEQUIP:70
	RETURN 0
;条件を満たさないとだめ
SIF A < 1
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1
;────────────────────────────────────
;逃げる
;────────────────────────────────────
@COM_ABLE60
;一括管理
SIF GLOBAL_COMABLE(60)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
SIF FLAG:1700 == 0
	RETURN 0
;もがく成功.夢魔が絶頂の次のターン
SIF TFLAG:404 || TFLAG:403
	RETURN 1
SIF TEQUIP:37 || TEQUIP:38 || TEQUIP:40 || TEQUIP:70 || TEQUIP:71 || TFLAG:112
	RETURN 0
RETURN 1
;────────────────────────────────────
;なすがまま
;────────────────────────────────────
@COM_ABLE61
;一括管理
SIF GLOBAL_COMABLE(61)
	RETURN RESULT

SIF FLAG:1700 == 0 && !TFLAG:370 && !GETBIT(CFLAG:MASTER:15,5)
	RETURN 0


RETURN 1
;────────────────────────────────────
;もがく
;────────────────────────────────────
@COM_ABLE62
;一括管理
SIF GLOBAL_COMABLE(62)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
SIF FLAG:1700 == 0
	RETURN 0
;逃げられない状況を解除する
SIF TEQUIP:37 == 0 && TEQUIP:38 == 0 && TEQUIP:40 == 0 && TEQUIP:70 == 0 && TEQUIP:71 == 0 && TFLAG:112 == 0
	RETURN 0
;状態異常中
SIF TFLAG:61 == 5
	RETURN 0
SIF BASE:MASTER:0 <= 0
	RETURN 0
RETURN 1
;────────────────────────────────────
;アイテム
;────────────────────────────────────
@COM_ABLE63
;一括管理
SIF GLOBAL_COMABLE(63)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
SIF FLAG:1700 == 0
	RETURN 0
;アイテムが無い
SIF ITEM:500 == 0 && ITEM:501 == 0 && ITEM:506 == 0
	RETURN 0
;状態異常中
SIF TEQUIP:40
	RETURN 0
RETURN 1
;────────────────────────────────────
;様子をみる
;────────────────────────────────────
@COM_ABLE64
;一括管理
SIF GLOBAL_COMABLE(64)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
SIF FLAG:1700 == 0
	RETURN 0
SIF TFLAG:61 == 5
	RETURN 0
RETURN 1
;────────────────────────────────────
;アナライズ
;────────────────────────────────────
@COM_ABLE65
;一括管理
SIF GLOBAL_COMABLE(65)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
SIF FLAG:1700 == 0
	RETURN 0
RETURN 1
;────────────────────────────────────
;イかせて！実行判定
;────────────────────────────────────
@COM_ABLE70
;一括管理
SIF GLOBAL_COMABLE(70)
	RETURN RESULT
A = 0
U = 0
;状態異常
SIF TFLAG:370
	RETURN 0

;遭遇時
SIF FLAG:1700
	RETURN 0
SIF TFLAG:126
	RETURN 0
;イキそうならいろいろすっとばす
IF TFLAG:170
	TFLAG:92 += 1
	RETURN 1
ENDIF

SIF BASE:MASTER:2 <= 0
	RETURN 0


SIF TFLAG:80 != 1 || TFLAG:90 == 15 || TFLAG:90 == 27
	RETURN 0
IF TFLAG:90 == 10 || TFLAG:90 == 12 || TFLAG:90 == 16 || TFLAG:90 == 17 || TFLAG:90 == 18
	SIF PALAM:0 < 5000
		RETURN 0
ELSEIF TFLAG:90 == 11
	SIF PALAM:3 < 5000
		RETURN 0
ELSEIF TFLAG:90 == 13 || TFLAG:90 == 14
	SIF PALAM:2 < 5000
		RETURN 0
ENDIF
;快感のソースが入って、とても強い場合
IF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 0 || TFLAG:90 == 15
	CALL ABLE_KAIKAN
	IF U > 10000
		A = 30
	ELSEIF U > 8500
		A = 25
	ELSEIF U > 7000
		A = 20
	ELSEIF U > 5500
		A = 16
	ELSEIF U > 4000
		A = 12
	ELSEIF U > 3000
		A = 8
	ELSEIF U > 2000
		A = 5
	ELSEIF U > 1000 || FLAG:2004
		A = 2
	ELSE
		A = 1
	ENDIF
	;今の状況
	IF PALAM:5 > 8000
		TIMES A , 1.35
	ELSEIF PALAM:5 > 7000
		TIMES A , 1.25
	ELSEIF PALAM:5 > 6000
		TIMES A , 1.15
	ELSEIF PALAM:5 > 5000
		TIMES A , 1.05
	ENDIF
	;媚薬
	SIF TEQUIP:11
		A += 3
	;朦朧
	SIF TFLAG:61 == 4
		TIMES A , 1.10
	;情欲
	IF TFLAG:61 == 5
		A += 1
		TIMES A , 1.20
	ENDIF
	;過去の経験
	A += MARK:MASTER:1 - MARK:3 - MARK:MASTER:4
	SIF CFLAG:MASTER:8 == TFLAG:90
		A -= MARK:MASTER:4
	;我慢に関連する性格
	A += TALENT:MASTER:17 * 3 - TALENT:MASTER:15 * 3 - TALENT:MASTER:20 * 5 + TALENT:MASTER:21 * 5 - TALENT:MASTER:32 * 4 + TALENT:MASTER:33 * 4

	;調教のプログレス
	IF FLAG:2004 == 0
		IF CFLAG:MASTER:0 < 1
			A -= 6
		ELSEIF CFLAG:MASTER:0 < 2
			A -= 5
		ELSEIF CFLAG:MASTER:0 < 4
			A -= 4
		ELSEIF CFLAG:MASTER:0 < 6
			A -= 3
		ELSEIF CFLAG:MASTER:0 < 8
			A -= 2
		ELSEIF CFLAG:MASTER:0 < 11
			A -= 1
		ENDIF
	ENDIF
	
	SIF TALENT:MASTER:28 && (TALENT:MASTER:78 == 0 || TALENT:MASTER:79 == 0)
		A -= 5
	SIF FLAG:2004 == 0
		A -= BASE:MASTER:5 / 1000
ENDIF


;快感が条件満足できたら実行可能
SIF A < 1
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1
;────────────────────────────────────
;怯えるの実行判定
;────────────────────────────────────
@COM_ABLE33
;一括管理
SIF GLOBAL_COMABLE(33)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;遭遇時
SIF FLAG:1700
	RETURN 0
;トリガーする行動や状況
SIF TFLAG:90 != 0 && TFLAG:90 != 1 && TFLAG:90 != 2 && TFLAG:90 != 4 && TFLAG:90 != 7 && TFLAG:90 != 8 && TFLAG:90 != 9 && TFLAG:90 != 18 && TFLAG:90 != 22 && TFLAG:90 != 23 && TFLAG:90 != 26 && TFLAG:90 != 27 && TFLAG:90 != 36 && TFLAG:90 != 43 && TFLAG:90 != 44 && TFLAG:90 != 63 && TFLAG:90 != 64 && TFLAG:90 != 65 && TFLAG:90 != 66 && TFLAG:90 != 68 && TFLAG:90 != 73 && TFLAG:90 != 74
	RETURN 0
;判定
A = TALENT:MASTER:13 * 2 - TALENT:MASTER:11 * 2 + TALENT:MASTER:17 * 5 - TALENT:MASTER:15 * 5 - TALENT:MASTER:20 * 3 + TALENT:MASTER:21 * 3 - TALENT:MASTER:41 * 5 + TALENT:MASTER:40 * 5
B = PALAM:10 / 800 + PALAM:11 / 1500 + PALAM:12 / 1200 + PALAM:13 / 1500 - PALAM:9 / 1000
C = ABL:MASTER:0 * 3 - ABL:MASTER:11 * 3 - ABL:MASTER:15 * 2 - ABL:MASTER:16 * 2 - MARK:MASTER:0 * 2

;媚薬
SIF TEQUIP:11
	B += 5

SIF TFLAG:61 == 1 || TFLAG:61 == 2
	C += TFLAG:61 * 3
SIF TFLAG:61 == 4
	C += 3
SIF TFLAG:61 == 5
	C -= 5
SIF TFLAG:61 == 6
	C -= 8
SIF TFLAG:61 == 8
	C -= 6

;調教者の行動による実行難易度
IF TFLAG:90 == 0
	D = 18
ELSEIF TFLAG:90 == 1
	D = 17
ELSEIF TFLAG:90 == 2
	D = 22
ELSEIF TFLAG:90 == 4
	D = 17
ELSEIF TFLAG:90 == 7
	D = 15
ELSEIF TFLAG:90 == 8
	D = 17
ELSEIF TFLAG:90 == 9
	D = 17
ELSEIF TFLAG:90 == 18
	D = 20
ELSEIF TFLAG:90 == 22
	D = 15
ELSEIF TFLAG:90 == 23
	D = 15
ELSEIF TFLAG:90 == 26 && TFLAG:93 != 1
	D = 15
ELSEIF TFLAG:90 == 27 && TFLAG:93 != 1
	D = 10
ELSEIF TFLAG:90 == 36
	D = 15
ELSEIF TFLAG:90 == 43
	D = 10
ELSEIF TFLAG:90 == 44
	D = 10
ELSEIF TFLAG:90 == 63
	D = 10
ELSEIF TFLAG:90 == 64
	D = 10
ELSEIF TFLAG:90 == 65
	D = 10
ELSEIF TFLAG:90 == 66
	D = 17
ELSEIF TFLAG:90 == 68 && TFLAG:93 != 1
	D = 10
ELSEIF TFLAG:90 == 73
	D = 15
ELSEIF TFLAG:90 == 74
	D = 5
ELSE
	D = 20
ENDIF

SIF TFLAG:64 < 2
	D -= 2
	
SIF TEQUIP:42
	D -= 8

SIF A + B + C < D && FLAG:2004 == 0
	RETURN 0

;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;クンニの実行判定
;────────────────────────────────────
@COM_ABLE71
;一括管理
SIF GLOBAL_COMABLE(71)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;オトコ
SIF TALENT:122
	RETURN 0
;積極になれない判定
LOCAL = ABL:MASTER:2 + 2 * ABL:MASTER:7 + MARK:2 + TALENT:91 * 5 - MARK:3 * 6
LOCAL += TALENT:MASTER:23 * 2 - TALENT:MASTER:22 * 2 + TALENT:MASTER:33 * 3 - TALENT:MASTER:32 * 3 - TALENT:MASTER:65 * 3 + TALENT:MASTER:63 * 5
LOCAL -= PALAM:12 / 1200 + PALAM:13 / 800 + PALAM:9 / 1000 - PALAM:8 / 800 - PALAM:6 / 1200


;状態による変動
SIF TFLAG:61 == 3
	LOCAL += 5
SIF TFLAG:61 == 5
	LOCAL -= 3
SIF TFLAG:61 == 6
	LOCAL += 5
SIF TFLAG:61 == 7
	LOCAL += 3

;不潔のソース
IF TALENT:MASTER:64 == 0
	IF SOURCE:21 > 1000
		LOCAL -= 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 600
		LOCAL -= 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 250
		LOCAL -= 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 50
		LOCAL -= 1
	ENDIF
ENDIF

;チェックされたらアウト
SIF LOCAL < 15 && FLAG:2004 == 0
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
SIF TFLAG:170
	RETURN 0
;会話
SIF SELECTCOM <= 10
	RETURN 0
SIF TEQUIP:70
	RETURN 0
;トリガーする行動
IF TOUCH(2,4) || TOUCH(1,4) || TOUCH(7,4)
	TFLAG:92 += 1
	RETURN 1
ELSEIF (TFLAG:90 == 10 || TFLAG:90 == 12 || TFLAG:90 == 13 || TFLAG:90 == 17 || TFLAG:90 == 40) && !TFLAG:177
	TFLAG:92 += 1
	RETURN 1
ENDIF

;────────────────────────────────────
;アナル舐めの実行判定
;────────────────────────────────────
@COM_ABLE72
;一括管理
SIF GLOBAL_COMABLE(72)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0

;積極になれない判定
LOCAL = ABL:MASTER:2 + 2 * ABL:MASTER:7 + MARK:2 + TALENT:91 * 5 - MARK:3 * 6
LOCAL += TALENT:MASTER:23 * 2 - TALENT:MASTER:22 * 2 + TALENT:MASTER:33 * 3 - TALENT:MASTER:32 * 3 - TALENT:MASTER:65 * 3 + TALENT:MASTER:63 * 5
LOCAL -= PALAM:12 / 1200 + PALAM:13 / 800 + PALAM:9 / 1000 - PALAM:8 / 800 - PALAM:6 / 1200


;状態による変動
SIF TFLAG:61 == 3
	LOCAL += 5
SIF TFLAG:61 == 5
	LOCAL -= 3
SIF TFLAG:61 == 6
	LOCAL += 5
SIF TFLAG:61 == 7
	LOCAL += 3

;不潔のソース
IF TALENT:MASTER:64 == 0
	IF SOURCE:21 > 1000
		LOCAL -= 7 - TALENT:MASTER:61 * 3 + TALENT:MASTER:62 * 3
	ELSEIF SOURCE:21 > 600
		LOCAL -= 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 250
		LOCAL -= 4 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 50
		LOCAL -= 3
	ENDIF
ENDIF
SIF SELECTCOM == 51
	RETURN 0
;チェックされたらアウト
SIF LOCAL < 15 && FLAG:2004 == 0
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
SIF TFLAG:170
	RETURN 0
;会話
SIF SELECTCOM <= 10
	RETURN 0
SIF TEQUIP:70
	RETURN 0
;トリガーする行動
IF TOUCH(2,4) || TOUCH(1,4) || TOUCH(7,4)
	TFLAG:92 += 1
	RETURN 1
ELSEIF (TFLAG:90 == 10 || TFLAG:90 == 12 || TFLAG:90 == 13 || TFLAG:90 == 17) && !TFLAG:177
	TFLAG:92 += 1
	RETURN 1
ENDIF

;────────────────────────────────────
;乳首吸いの実行判定
;────────────────────────────────────
@COM_ABLE73
;一括管理
SIF GLOBAL_COMABLE(73)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;トリガーする行動
SIF !TOUCH(5,4) && TEQUIP:70 != 1 && TEQUIP:70 != 4 && TEQUIP:70 != 3 && TEQUIP:71 != 1 && TEQUIP:71 != 4
	RETURN 0

;積極になれない判定
A = 5

A += TALENT:MASTER:22 * 5 - TALENT:MASTER:23 * 5 + TALENT:MASTER:24 * 3 - TALENT:MASTER:25 * 3 + TALENT:MASTER:32 * 5 - TALENT:MASTER:33 * 5 + TALENT:MASTER:65 * 5 - TALENT:MASTER:63 * 7
A += PALAM:12 / 1200 + PALAM:13 / 800 + PALAM:9 / 1000 - PALAM:8 / 800 - PALAM:6 / 1200
A += MARK:3 * 2 - ABL:MASTER:7 * 2 - MARK:2 * 2 - TALENT:91 * 5

SIF TFLAG:90 == 40
	A += 2 + TALENT:MASTER:34 * 3 - TALENT:MASTER:35 * 3 - TALENT:MASTER:60 * 5

;状態による変動
SIF TFLAG:61 == 3
	A -= 5
SIF TFLAG:61 == 5
	A += 3
SIF TFLAG:61 == 6
	A -= 5
SIF TFLAG:61 == 7
	A -= 3

;不潔のソース
IF TALENT:MASTER:64 == 0
	IF SOURCE:21 > 1000
		A += 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 600
		A += 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 250
		A += 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 50
		A += 1
	ENDIF
ENDIF

;チェックされたらアウト
SIF A > 0 && FLAG:2004 == 0
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;キスの実行判定
;────────────────────────────────────
@COM_ABLE74
;一括管理
SIF GLOBAL_COMABLE(74)
	RETURN RESULT
;状態異常
SIF TFLAG:370
	RETURN 0
;トリガーする行動
SIF !(TFLAG:90 == 33 || TFLAG:90 == 38 || TFLAG:90 == 97 || TFLAG:90 == 101)
	RETURN 0

;積極になれない判定
A = 5

A += TALENT:MASTER:22 * 5 - TALENT:MASTER:23 * 5 + TALENT:MASTER:24 * 3 - TALENT:MASTER:25 * 3 + TALENT:MASTER:32 * 5 - TALENT:MASTER:33 * 5 + TALENT:MASTER:65 * 5 - TALENT:MASTER:63 * 7
A += PALAM:12 / 1200 + PALAM:13 / 800 + PALAM:9 / 1000 - PALAM:8 / 800 - PALAM:6 / 1200
A += MARK:3 * 2 - ABL:MASTER:7 * 2 - MARK:2 * 2 - TALENT:91 * 5

SIF TFLAG:90 == 40
	A += 2 + TALENT:MASTER:34 * 3 - TALENT:MASTER:35 * 3 - TALENT:MASTER:60 * 5

;状態による変動
SIF TFLAG:61 == 3
	A -= 5
SIF TFLAG:61 == 5
	A += 3
SIF TFLAG:61 == 6
	A -= 5
SIF TFLAG:61 == 7
	A -= 3

;不潔のソース
IF TALENT:MASTER:64 == 0
	IF SOURCE:21 > 1000
		A += 5 - TALENT:MASTER:61 * 2 + TALENT:MASTER:62 * 2
	ELSEIF SOURCE:21 > 600
		A += 3 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 250
		A += 2 - TALENT:MASTER:61 * 1 + TALENT:MASTER:62 * 1
	ELSEIF SOURCE:21 > 50
		A += 1
	ENDIF
ENDIF

;チェックされたらアウト
SIF A > 0 && FLAG:2004 == 0
	RETURN 0
;一回休みのときはだめ
SIF TFLAG:101 == 1
	RETURN 0
TFLAG:92 += 1
RETURN 1

;────────────────────────────────────
;ぼっとするの実行判定
;────────────────────────────────────
@COM_ABLE52
;一括管理
SIF GLOBAL_COMABLE(52)
	RETURN RESULT
SIF FLAG:1700
	RETURN 0
SIF TFLAG:370
	RETURN 0
;他の行動が全部できないとだめ
SIF TFLAG:92 > 0
	RETURN 0
RETURN 1


;────────────────────────────────────
;快感関連の計算
;────────────────────────────────────
@ABLE_KAIKAN
V = SOURCE:0
X = SOURCE:1
Y = SOURCE:2
Z = SOURCE:3

;敏感や鈍感
SIF TALENT:MASTER:100
	TIMES V , 1.20
SIF TALENT:MASTER:101
	TIMES V , 0.80
SIF TALENT:MASTER:102
	TIMES X , 1.20
SIF TALENT:MASTER:103
	TIMES X , 0.80
SIF TALENT:MASTER:104
	TIMES Y , 1.20
SIF TALENT:MASTER:105
	TIMES Y , 0.80
SIF TALENT:MASTER:106
	TIMES Z , 1.20
SIF TALENT:MASTER:107
	TIMES Z , 0.80

;淫系素質
SIF TALENT:MASTER:72
	TIMES V , 1.30
SIF TALENT:MASTER:73
	TIMES X , 1.30
SIF TALENT:MASTER:74
	TIMES Y , 1.30
SIF TALENT:MASTER:75
	TIMES Z , 1.30

;各感覚
IF ABL:MASTER:3 > 4
	TIMES V , 1.50
ELSEIF ABL:MASTER:3 > 3
	TIMES V , 1.40
ELSEIF ABL:MASTER:3 > 2
	TIMES V , 1.30
ELSEIF ABL:MASTER:3 > 1
	TIMES V , 1.20
ELSEIF ABL:MASTER:3 > 0
	TIMES V , 1.10
ENDIF

IF ABL:MASTER:4 > 4
	TIMES X , 1.50
ELSEIF ABL:MASTER:4 > 3
	TIMES X , 1.40
ELSEIF ABL:MASTER:4 > 2
	TIMES X , 1.30
ELSEIF ABL:MASTER:4 > 1
	TIMES X , 1.20
ELSEIF ABL:MASTER:4 > 0
	TIMES X , 1.10
ENDIF

IF ABL:MASTER:5 > 4
	TIMES Y , 1.50
ELSEIF ABL:MASTER:5 > 3
	TIMES Y , 1.40
ELSEIF ABL:MASTER:5 > 2
	TIMES Y , 1.30
ELSEIF ABL:MASTER:5 > 1
	TIMES Y , 1.20
ELSEIF ABL:MASTER:5 > 0
	TIMES Y , 1.10
ENDIF

IF ABL:MASTER:6 > 4
	TIMES Z , 1.50
ELSEIF ABL:MASTER:6 > 3
	TIMES Z , 1.40
ELSEIF ABL:MASTER:6 > 2
	TIMES Z , 1.30
ELSEIF ABL:MASTER:6 > 1
	TIMES Z , 1.20
ELSEIF ABL:MASTER:6 > 0
	TIMES Z , 1.10
ENDIF

U = V + X + Y + Z

;キスはＣＶＡＢのソースが入らないので例外として処理します
SIF TFLAG:90 == 15
	U = SOURCE:11 + SOURCE:12

;快感に素直/否定
SIF TALENT:MASTER:70
	TIMES U , 1.20
SIF TALENT:MASTER:71
	TIMES U , 0.80

;────────────────────────────────────
;加虐関連の計算
;────────────────────────────────────
@ABLE_GYAKU
Q = SOURCE:13
R = SOURCE:14

;痛みに弱い/強い
SIF TALENT:MASTER:40
	TIMES Q , 1.50
SIF TALENT:MASTER:41
	TIMES Q , 0.60

S = Q + R

;痛みが重ねたらしびれる？
IF PALAM:10 > 8500
	TIMES S , 0.75
ELSEIF PALAM:10 > 7000
	TIMES S , 0.85
ELSEIF PALAM:10 > 5500
	TIMES S , 0.95
ENDIF

;不自由な状態
IF PALAM:12 > 8500 || R > 1500
	TIMES S , 1.30
ELSEIF PALAM:12 > 7000 || R > 1000
	TIMES S , 1.20
ELSEIF PALAM:12 > 5500 || R > 500
	TIMES S , 1.10
ENDIF

;COMABLE一括管理
@GLOBAL_COMABLE(ARG)
#FUNCTION
IF !TFLAG:3
	RESULT = 0
	RETURNF 1
ENDIF
