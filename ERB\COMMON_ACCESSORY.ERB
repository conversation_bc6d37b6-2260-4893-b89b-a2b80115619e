﻿;-------------------------------------------------
;汎用関数置き場
;アクセサリ関連
;-------------------------------------------------

;-------------------------------------------------
;関数名:ACCESSORY_CALC
;概　要:アクセサリUP (0= 5%, 1= 10%, 2 =20%, 3= 50%)の計算用
;引　数:ARG…UP種別
;戻り値:増加％値
;備　考:式中関数
;-------------------------------------------------
@ACCESSORY_CALC(ARG)
#FUNCTION
SELECTCASE ARG
	CASE 1
		RETURNF 5
	CASE 2
		RETURNF 10
	CASE 3
		RETURNF 20
	CASE 4
		RETURNF 50
ENDSELECT


;-------------------------------------------------
;関数名:ACCESSORY_COUNT
;概　要:アクセサリ所持数取得関数
;引　数:ARG…場所(0.所持品/1.倉庫)
;戻り値:アクセサリ所持数
;備　考:式中関数
;-------------------------------------------------
@ACCESSORY_COUNT(ARG)
#FUNCTION
LOCAL:1 = 0
SELECTCASE ARG
	CASE 0
		FOR LOCAL, 0, 10
			LOCAL:1 += DA:LOCAL:0 != 0
		NEXT
	CASE 1
		FOR LOCAL, 0, 30
			LOCAL:1 += DB:LOCAL:0 != 0
		NEXT
ENDSELECT
RETURNF LOCAL:1


;-------------------------------------------------
;関数名:ACCESSORY_DATA
;概　要:アクセサリ情報表示関数(所持品)
;引　数:ARG…所持アクセサリのインデックス
;戻り値:なし
;-------------------------------------------------
@ACCESSORY_DATA(ARG)
PRINTFORML %STR:(905 + 4 * DA:ARG:1 + DA:ARG:2)%%STR:(900 + DA:ARG:1)%
PRINTFORML 装备Lv{DA:ARG:4}
FOR LOCAL, 5, 11
	SIF DA:ARG:LOCAL
		PRINTFORM %ACCESSORY_PALAMNAME(LOCAL)%+{DA:ARG:LOCAL} 
NEXT
PRINTL 
FOR LOCAL, 11, 23, 2
	SIF DA:ARG:LOCAL
		PRINTFORML [%ACCESSORY_ENCHANT(DA:ARG:LOCAL, DA:ARG:(LOCAL + 1))%]
NEXT


;-------------------------------------------------
;関数名:ACCESSORY_DATA_EQUIP
;概　要:アクセサリ情報表示関数(装备中)
;引　数:なし
;戻り値:なし
;-------------------------------------------------
@ACCESSORY_DATA_EQUIP
PRINTFORML %STR:(905 + 4 * DC:0:1 + DC:0:2)%%STR:(900 + DC:0:1)%
PRINTFORML 装备Lv{DC:0:4}
FOR LOCAL, 5, 11
	SIF DC:0:LOCAL
		PRINTFORM %ACCESSORY_PALAMNAME(LOCAL)%+{DC:0:LOCAL} 
NEXT
PRINTL 
FOR LOCAL, 11, 23, 2
	SIF DC:0:LOCAL
		PRINTFORML [%ACCESSORY_ENCHANT(DC:0:LOCAL, DC:0:(LOCAL + 1))%]
NEXT


;-------------------------------------------------
;関数名:ACCESSORY_DATA_STORAGE
;概　要:アクセサリ情報表示関数(倉庫)
;引　数:ARG…倉庫のアクセサリのインデックス
;戻り値:なし
;-------------------------------------------------
@ACCESSORY_DATA_STORAGE(ARG)
PRINTFORML %STR:(905 + 4 * DB:ARG:1 + DB:ARG:2)%%STR:(900 + DB:ARG:1)%
PRINTFORML 装备Lv{DB:ARG:4}
FOR LOCAL, 5, 11
	SIF DB:ARG:LOCAL
		PRINTFORM %ACCESSORY_PALAMNAME(LOCAL)%+{DB:ARG:LOCAL} 
NEXT
PRINTL 
FOR LOCAL, 11, 23, 2
	SIF DB:ARG:LOCAL
		PRINTFORML [%ACCESSORY_ENCHANT(DB:ARG:LOCAL, DB:ARG:(LOCAL + 1))%]
NEXT


;-------------------------------------------------
;関数名:ACCESSORY_ENCHANT
;概　要:アクセサリエンチャント効果取得関数
;引　数:ARG:0…エンチャント種別
;      :ARG:1…エンチャント効果量
;戻り値:アクセサリ所持数
;備　考:式中関数
;-------------------------------------------------
@ACCESSORY_ENCHANT(ARG:0, ARG:1)
#FUNCTIONS
SIF !STRLENS(LOCALS:4)
	SPLIT "////5％UP/10％UP/20％UP/50％UP/5％UP/7％UP/10％UP//20％UP/50％UP/無効", "/", LOCALS
LOCALS = %STR:(922 + ARG:0)%
SELECTCASE ARG:0
	CASE 1 TO 7
		LOCALS += LOCALS:(4 + ARG:1)
	CASE 8
		LOCALS += LOCALS:(8 + ARG:1)
	CASE 9 TO 13
		LOCALS += LOCALS:(12 + ARG:1)
	CASE 14
		LOCALS += @"{1 + ARG:1}0％UP"
	CASE 15 TO 24
		LOCALS += @"\@ ARG:1 ? 强 # 弱 \@い"
	CASE 25 TO 27, 29
		LOCALS += @"+{1 + ARG:1}"
	CASE 28
		LOCALS += @"-{1 + ARG:1}"
	CASE 30
		LOCALS += @"-{1 + ARG:1}0％"
	CASE 31
		LOCALS += @"+{1 + ARG:1}0％"
	CASE 34
		LOCALS += @" * \@ ARG:1 ? 2 # 1.5 \@"
ENDSELECT
RETURNF LOCALS


;-------------------------------------------------
;関数名:ACCESSORY_LIST_EQUIP
;概　要:アクセサリ一覧表示関数(装备中・所持品)
;引　数:なし
;戻り値:なし
;-------------------------------------------------
@ACCESSORY_LIST_EQUIP
SIF DC:0:0
	PRINTFORML [  0] - %STR:(905 + 4 * DC:0:1 + DC:0:2)%%STR:(900 + DC:0:1)% 装备中
FOR LOCAL, 0, 5
	SIF DA:LOCAL:0
		PRINTFORML [{LOCAL + 1, 3}] - %STR:(905 + 4 * DA:LOCAL:1 + DA:LOCAL:2)%%STR:(900 + DA:LOCAL:1)%
NEXT
PRINTL [100] - 返回


;-------------------------------------------------
;関数名:ACCESSORY_LIST_STORAGE
;概　要:アクセサリ一覧表示関数(所持品・倉庫)
;引　数:なし
;戻り値:なし
;-------------------------------------------------
@ACCESSORY_LIST_STORAGE
DRAWLINE
PRINTFORML 持有物\@ ACCESSORY_COUNT(0) >= 5 ? (持有物太多了) # \@
FOR LOCAL, 0, 10
	SIF DA:LOCAL:0
		PRINTFORML [{LOCAL, 3}] - %STR:(905 + 4 * DA:LOCAL:1 + DA:LOCAL:2)%%STR:(900 + DA:LOCAL:1)%
NEXT
PRINTFORML 仓库\@ ACCESSORY_COUNT(1) >= 20 ? (仓库满了) # \@
FOR LOCAL, 0, 30
	SIF DB:LOCAL:0
		PRINTFORML [{LOCAL + 100}] - %STR:(905 + 4 * DB:LOCAL:1 + DB:LOCAL:2)%%STR:(900 + DB:LOCAL:1)%
NEXT
PRINTL [200] - 返回


;-------------------------------------------------
;関数名:ACCESSORY_MAXBASE
;概　要:アクセサリ補正MAXBASE値取得関数
;引　数:ARG…MAXBASE番号
;戻り値:MAXBASE値
;備　考:式中関数
;アクセサリの補正を受けたMASTERのMAXBASE値を返す
;-------------------------------------------------
@ACCESSORY_MAXBASE(ARG)
#FUNCTION
;FLAG:3188 体力UP	FLAG:3189 気力UP	FLAG:3190 精力UP
RETURNF ARG < 3 ? MAXBASE:MASTER:ARG * (100 + FLAG:(3188 + ARG)) / 100 # MAXBASE:MASTER:ARG


;-------------------------------------------------
;関数名:ACCESSORY_PALAMNAME
;概　要:アクセサリパラメータ名取得関数
;引　数:ARG…パラメータ番号
;戻り値:パラメータ名
;備　考:式中関数
;-------------------------------------------------
@ACCESSORY_PALAMNAME(ARG)
#FUNCTIONS
SIF !STRLENS(LOCALS:5)
	SPLIT "/////技巧/魅力/快速度/体力/气力/精力", "/", LOCALS
RETURNF LOCALS:ARG


;-------------------------------------------------
;関数名:ACCESSORY_PALAMRESET
;概　要:アクセサリパラメータ再設定関数(所持品)
;引　数:ARG…所持アクセサリのインデックス
;戻り値:なし
;ユニークは未対応？
;-------------------------------------------------
@ACCESSORY_PALAMRESET(ARG)
SELECTCASE DA:ARG:1
	;ブレスレット(魅力+n 気力+2n+3)
	CASE 0
		DA:ARG:6  = DA:ARG:4
		DA:ARG:9  = DA:ARG:4 * 2 + 3
	;カフス(魅力+2n+3 気力+n)
	CASE 1
		DA:ARG:6  = DA:ARG:4 * 2 + 3
		DA:ARG:9  = DA:ARG:4
	;ガントレット(技巧+n+2 魅力+n+2 気力+n)
	CASE 2
		DA:ARG:5  = DA:ARG:4 + 2
		DA:ARG:6  = DA:ARG:4 + 2
		DA:ARG:9  = DA:ARG:4
	;サッシュ(魅力+n+2 気力+n 精力+n+2)
	CASE 3
		DA:ARG:6  = DA:ARG:4 + 2
		DA:ARG:9  = DA:ARG:4
		DA:ARG:10 = DA:ARG:4 + 2
	;リング(技巧+n 魅力+n 素早さ+n 体力+n 気力+n 精力+n)
	CASE 4
		DA:ARG:5  = DA:ARG:4
		DA:ARG:6  = DA:ARG:4
		DA:ARG:7  = DA:ARG:4
		DA:ARG:8  = DA:ARG:4
		DA:ARG:9  = DA:ARG:4
		DA:ARG:10 = DA:ARG:4
ENDSELECT


;-------------------------------------------------
;関数名:ACCESSORY_PALAMRESET_EQUIP
;概　要:アクセサリパラメータ再設定関数(装备中)
;引　数:ARG…所持アクセサリのインデックス
;戻り値:なし
;ユニークは未対応？
;-------------------------------------------------
@ACCESSORY_PALAMRESET_EQUIP
SELECTCASE DC:0:1
	;ブレスレット(魅力+n 気力+2n+3)
	CASE 0
		DC:0:6  = DC:0:4
		DC:0:9  = DC:0:4 * 2 + 3
	;カフス(魅力+2n+3 気力+n)
	CASE 1
		DC:0:6  = DC:0:4 * 2 + 3
		DC:0:9  = DC:0:4
	;ガントレット(技巧+n+2 魅力+n+2 気力+n)
	CASE 2
		DC:0:5  = DC:0:4 + 2
		DC:0:6  = DC:0:4 + 2
		DC:0:9  = DC:0:4
	;サッシュ(魅力+n+2 気力+n 精力+n+2)
	CASE 3
		DC:0:6  = DC:0:4 + 2
		DC:0:9  = DC:0:4
		DC:0:10 = DC:0:4 + 2
	;リング(技巧+n 魅力+n 素早さ+n 体力+n 気力+n 精力+n)
	CASE 4
		DC:0:5  = DC:0:4
		DC:0:6  = DC:0:4
		DC:0:7  = DC:0:4
		DC:0:8  = DC:0:4
		DC:0:9  = DC:0:4
		DC:0:10 = DC:0:4
ENDSELECT


;-------------------------------------------------
;関数名:ACCESSORY_SET
;概　要:FLAGにアクセサリ情報をセット
;引　数:なし
;戻り値:なし
;-------------------------------------------------
@ACCESSORY_SET
FOR LOCAL, 0, 100
	FLAG:(3100 + LOCAL) = 0
NEXT
;アクセサリエンチャント番号と対応する3100以降のFLAGにエンチャント強度を書き込む
FOR LOCAL, 11, 23, 2
	FLAG:(3100 + DC:0:LOCAL) = DC:0:LOCAL ? 1 + DC:0:(1 + LOCAL) # 0
NEXT
;FLAG:3185 技巧UP	FLAG:3186 魅力UP	FLAG:3187 素早さUP
;FLAG:3188 体力UP	FLAG:3189 気力UP	FLAG:3190 精力UP
FOR LOCAL, 0, 6
	FLAG:(3185 + LOCAL) = DC:0:(LOCAL + 5) + ACCESSORY_CALC(FLAG:(3101 + LOCAL)) + ACCESSORY_CALC(FLAG:3107)
NEXT


;-------------------------------------------------
;関数名:ACCESSORY_TRIM
;概　要:アクセサリ位置詰め関数
;引　数:なし
;戻り値:なし
;-------------------------------------------------
@ACCESSORY_TRIM
;所持品
LOCAL:0 = ACCESSORY_COUNT(0)
FOR LOCAL:1, 0, LOCAL:0
	;空白じゃない→次のindexへ
	SIF DA:(LOCAL:1):0
		CONTINUE
	;空白だった→次の空白でないindexをサーチ
	FOR LOCAL:2, LOCAL:1 + 1, 10
		SIF DA:(LOCAL:2):0
			BREAK
	NEXT
	;空白indexと埋まってるindexをswap
	FOR LOCAL:3, 0, 100
		SWAP DA:(LOCAL:1):(LOCAL:3), DA:(LOCAL:2 - 1):(LOCAL:3)
	NEXT
NEXT
;倉庫
LOCAL:0 = ACCESSORY_COUNT(1)
FOR LOCAL:1, 0, LOCAL:0
	SIF DB:(LOCAL:1):0
		CONTINUE
	FOR LOCAL:2, LOCAL:1 + 1, 30
		SIF DB:(LOCAL:2):0
			BREAK
	NEXT
	FOR LOCAL:3, 0, 100
		SWAP DB:(LOCAL:1):(LOCAL:3), DB:(LOCAL:2 - 1):(LOCAL:3)
	NEXT
NEXT
