﻿@ABL_REVISION

IF TARGET > 0
	REPEAT 30
		A:(COUNT + 1) = (100 - 500/(ABL:(COUNT + 1) + 5))
	REND
	A:30 = 50 - 2000 / (CFLAG:0 + 40)
ENDIF
REPEAT 30
B:(COUNT + 1) = (100 - 500/(ABL:MASTER:(COUNT + 1) + 5))
REND
B:30 = (100 - 500/(ABL:MASTER:0 + 5))
IF ASSI > 0
	REPEAT 30
	C:(COUNT + 1) = (100 - 500/(ABL:ASSI:(COUNT + 1) + 5))
	REND
	C:30 = 50 - 2000 / (CFLAG:ASSI:0 + 40)
ENDIF
;TARGET愛撫
IF TARGET > 0
	
	LOCAL:1 = MAX(CFLAG:0  * 10 / (CFLAG:MASTER:0 + 1) , MIN(CFLAG:2/ 200 , 30), 10)
	SIF LOCAL:1 > 1000
		LOCAL:1 = 1000
	;C愛撫
	LOCAL = MAX(ABL:2 + ABL:21 - ABL:MASTER:3 , MARK:2)
	S:1 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;V愛撫
	LOCAL = MAX(ABL:2 + ABL:21 - ABL:MASTER:4 , MARK:2)
	S:2 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;A愛撫
	LOCAL = MAX(ABL:2 + ABL:21 - ABL:MASTER:5 , MARK:2)
	S:3 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;B愛撫
	LOCAL = MAX(ABL:2 + ABL:21 - ABL:MASTER:6 , MARK:2)
	S:4 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10

	;V性交
	LOCAL = MAX(ABL:2 + ABL:23- ABL:MASTER:4 , MARK:2)
	S:5 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;A性交
	LOCAL = MAX(ABL:2 + ABL:23 - ABL:MASTER:5 , MARK:2)
	S:6 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;V性交奉仕
	LOCAL = MAX(ABL:2 + ABL:23 + ABL:4 - ABL:MASTER:3 , MARK:2)
	S:7 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;A性交奉仕
	LOCAL = MAX(ABL:2 + ABL:23 + ABL:5 - ABL:MASTER:3 , MARK:2)
	S:8 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	
	;C道具
	LOCAL = MAX(ABL:2 + ABL:22 - ABL:MASTER:3 , MARK:2)
	S:11 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;V道具
	LOCAL = MAX(ABL:2 + ABL:22 - ABL:MASTER:4 , MARK:2)
	S:12 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;A道具
	LOCAL = MAX(ABL:2 + ABL:22 - ABL:MASTER:5 , MARK:2)
	S:13 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;B道具
	LOCAL = MAX(ABL:2 + ABL:22 - ABL:MASTER:6 , MARK:2)
	S:14 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
ENDIF
;ASSI愛撫
IF ASSI > 0
	LOCAL:1 = MAX(CFLAG:ASSI:0  * 10 / (CFLAG:MASTER:0 + 1) ,MIN(CFLAG:ASSI:2/ 200 , 30), 10)
	SIF LOCAL:1 > 1000
		LOCAL:1 = 1000
	;C愛撫
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:21 - ABL:MASTER:3 , MARK:ASSI:2)
	S:21 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;V愛撫
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:21 - ABL:MASTER:4 , MARK:ASSI:2)
	S:22 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;A愛撫
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:21 - ABL:MASTER:5 , MARK:ASSI:2)
	S:23 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;B愛撫
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:21 - ABL:MASTER:6 , MARK:ASSI:2)
	S:24 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10

	;V性交
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:23 - ABL:MASTER:4 , MARK:ASSI:2)
	S:25 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;A性交
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:23 - ABL:MASTER:5 , MARK:ASSI:2)
	S:26 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;V性交奉仕
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:23 + ABL:ASSI:4 - ABL:MASTER:3 , MARK:ASSI:2)
	S:27 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;A性交奉仕
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:23 + ABL:ASSI:5 - ABL:MASTER:3 , MARK:ASSI:2)
	S:27 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10

	;C道具
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:22 - ABL:MASTER:3 , MARK:2)
	S:31 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;V道具
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:22 - ABL:MASTER:4 , MARK:2)
	S:32 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;A道具
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:22 - ABL:MASTER:5 , MARK:2)
	S:33 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
	;B道具
	LOCAL = MAX(ABL:ASSI:2 + ABL:ASSI:22 - ABL:MASTER:6 , MARK:2)
	S:34 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 / 10
ENDIF
IF TARGET > 0
	LOCAL:1 = MAX(CFLAG:MASTER:0  * 10 / (CFLAG:0 + 1) , MIN(CFLAG:9/ 200 , 30), 10)
	SIF LOCAL:1 > 1000
		LOCAL:1 = 1000
	;アクセサリ効果
	LOCAL:2 = FLAG:3185
	;MASTER→TARGET愛撫
	;C愛撫
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:3 , MARK:2)
	S:41 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;A愛撫
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:5 , MARK:2)
	S:43 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;B愛撫
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:6 , MARK:2)
	S:44 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;V性交
	LOCAL = MAX(ABL:MASTER:2 + ABL:MASTER:4 - ABL:3 , MARK:2)
	S:45 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;A性交
	LOCAL = MAX(ABL:MASTER:2 + ABL:MASTER:5 - ABL:3 , MARK:2)
	S:46 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;V性交奉仕
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:4 , MARK:2)
	S:47 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;A性交奉仕
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:5 , MARK:2)
	S:48 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
ENDIF
;MASTER→ASSI愛撫
IF ASSI > 0
	LOCAL:1 = MAX(CFLAG:MASTER:0  * 10 / (CFLAG:ASSI:0 + 1) , MIN(CFLAG:9/ 200 , 30), 10)
	SIF LOCAL:1 > 1000
		LOCAL:1 = 1000
	;アクセサリ効果
	LOCAL:2 = FLAG:3185
	;C愛撫
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:ASSI:3 , MARK:ASSI:2)
	S:51 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;A愛撫
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:ASSI:5 , MARK:ASSI:2)
	S:53 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;B愛撫
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:ASSI:6 , MARK:ASSI:2)
	S:54 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;V性交
	LOCAL = MAX(ABL:MASTER:2 + ABL:MASTER:4 - ABL:ASSI:3 , MARK:ASSI:2)
	S:55 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;A性交
	LOCAL = MAX(ABL:MASTER:2 + ABL:MASTER:5 - ABL:ASSI:3 , MARK:ASSI:2)
	S:56 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;V性交奉仕
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:ASSI:4 , MARK:ASSI:2)
	S:57 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
	;A性交奉仕
	LOCAL = MAX(2 * ABL:MASTER:2 - ABL:ASSI:5 , MARK:ASSI:2)
	S:58 = (LOCAL + 10) * (LOCAL + 10) * LOCAL:1 * (FLAG:3185 + 100) / 1000
ENDIF
;基本値		技巧－感覚
;100		0
;121		1
;144		2
;169		3
;196		4
;225		5
;256		6
;289		7
;324		8
;361		9
;400		10

@ACTION_APPLY
CALL ABL_REVISION
;────────────────────────────────────
;000,今の気分を聞く（Ｓ/Ｎ/Ｈ）[罰]（ソースなし、調教対象の反応だけを見る）
;────────────────────────────────────
IF TFLAG:90 == 0
	TFLAG:64 += 1
;────────────────────────────────────
;001,自分の事をどう思うかを聞く（Ｓ/Ｎ/Ｈ）（情愛）
;────────────────────────────────────
ELSEIF TFLAG:90 == 1
	SOURCE:11 = 50 + ABL:MASTER:0 * 2 + ABL:20
	TFLAG:64 += 1
	TFLAG:100 |= 1
;────────────────────────────────────
;002,性についての話をする（Ｓ/Ｎ/Ｈ）（調教対象の性知識）
;────────────────────────────────────
ELSEIF TFLAG:90 == 2
	TFLAG:130 += 50
	TFLAG:64 += 1
	TFLAG:100 |= 2
;────────────────────────────────────
;003,優しく慰める（Ｓ/Ｎ）（トラウマ逆）
;────────────────────────────────────
ELSEIF TFLAG:90 == 3
	SOURCE:24 = -50 - ABL:20 * 5
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1
	TFLAG:64 += 1
	TFLAG:100 |= 4
;────────────────────────────────────
;004,厳しく脅す（Ｎ/Ｈ）(トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 4
	SOURCE:24 = 80 + ABL:26 * 5
	SIF CFLAG:MASTER:8 == 4
		TIMES SOURCE:24 , 1.50
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1
	TFLAG:64 += 1
	TFLAG:100 |= 8
;────────────────────────────────────
;005,服を脱いで/服を着てと命じる（Ｓ/Ｎ/Ｈ/Ａ）[罰]（露出）
;────────────────────────────────────
ELSEIF TFLAG:90 == 5
	;自主脱衣の処理
	IF TALENT:32 == 0 && TALENT:34 == 0 && TALENT:140 == 0
		IF TALENT:33 * 4 + A:1 / 10 + A:30 / 5 > 3 + RAND:3
			TFLAG:103 = 7
		ELSEIF RAND:3 > 1
			TFLAG:103 = 3
		ENDIF
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TFLAG:93 == 0
		SOURCE:20 = 50
	SIF TFLAG:93 == 1
		SOURCE:20 = -50
	SIF TEQUIP:69 == 1
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 2
	TEQUIP:100 = 0
	TFLAG:64 += 1
;────────────────────────────────────
;006,合意を求める（Ｓ/Ｎ/Ｈ）（情愛）
;────────────────────────────────────
ELSEIF TFLAG:90 == 6
	SOURCE:11 = 200
	TFLAG:64 += 1
	TFLAG:100 |= 16
;────────────────────────────────────
;007,お仕置きと宣言する（Ｎ/Ｈ）[罰]（トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 7
	SOURCE:24 = 100 + MARK:MASTER:4 * 50
	SIF CFLAG:MASTER:8 == 7
		TIMES SOURCE:24 , 1.50
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1

	TEQUIP:100 = 0
	TFLAG:64 += 1
;────────────────────────────────────
;008,怒鳴る（Ｈ/Ａ）（情愛逆、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 8
	SOURCE:11 = -150
	SOURCE:24 = 150
	SIF CFLAG:MASTER:8 == 8
		TIMES SOURCE:24 , 1.50
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1
	TFLAG:64 += 1
;────────────────────────────────────
;009,不気味に笑う（Ａ）[罰]（逸脱、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 9
	SIF RAND:6 > 2
		TFLAG:102 = 6
	SIF RAND:6 > 2
		TFLAG:103 = 6
	SOURCE:22 = RAND:7 * 100
	SOURCE:24 = 250 + RAND:5 * 100
	SIF CFLAG:MASTER:8 == 9
		TIMES SOURCE:24 , 1.50
	TFLAG:64 += 1
	TFLAG:100 |= 32
;────────────────────────────────────
;010,愛撫/手淫する（Ｓ/Ｎ/Ｈ）（快Ｃ、快Ｂ、接触、情愛、性行動、不潔）
;────────────────────────────────────
ELSEIF TFLAG:90 == 10
	SOURCE:0 = 75 + S:1 * (2 + TALENT:57) / 4
	;脱衣の処理
	IF TALENT:32 == 0 && TALENT:34 == 0 && TALENT:140 == 0
		IF TALENT:33 * 4 + A:1 / 10 + A:30 / 5 > 3 + RAND:3
			TFLAG:102 = 8
			SIF RAND:3 > 0
				TFLAG:102 = 7
		ELSEIF RAND:3 > 1
			TFLAG:102 = 3
		ENDIF
	ENDIF
	SIF TEQUIP:0 && (RAND:3 == 0 || (TFLAG:93 == 1 && STAIN:MASTER:2 > 2) || (TFLAG:93 == 0 && STAIN:MASTER:3 > 3))
		TFLAG:103 = 6
	CALL MIYAKU_USE
	IF TFLAG:93
		TIMES SOURCE:0 , 2
	ELSE
		SOURCE:3 = SOURCE:0
	ENDIF
	SOURCE:10 = 100 + A:30 * 5
	SOURCE:12 = 50 + SOURCE:0 / 10 + SOURCE:3 / 10
	SOURCE:11 = SOURCE:10 / 2 + A:7 * 5
	SIF STAIN:1 & 1 || STAIN:1 & 2 || STAIN:1 & 16
		SOURCE:21 += 50
	SIF STAIN:1 & 4 || STAIN:1 & 8
		SOURCE:21 += 100
	SIF STAIN:1 & 32 || STAIN:1 & 64
		SOURCE:21 += 300
	IF TFLAG:93 == 1
		STAIN:MASTER:2 |= STAIN:1
		STAIN:1 |= STAIN:MASTER:2
	ELSE
		STAIN:MASTER:3 |= STAIN:1
		STAIN:1 |= STAIN:MASTER:3
	ENDIF
	SIF TEQUIP:69 == 1
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 2
	TFLAG:130 += 100 + (A:2 + A:21 + TALENT:57 * 50)
	TFLAG:64 += 2
;────────────────────────────────────
;011,胸愛撫/乳首吸い/乳の揉み合い（Ｓ/Ｎ/Ｈ）（快Ｂ、接触、情愛、性行動、痛み、不潔）
;────────────────────────────────────
ELSEIF TFLAG:90 == 11
	;脱衣の処理
	IF TALENT:32 == 0 && TALENT:34 == 0 && TALENT:140 == 0
		IF TALENT:33 * 2 + A:1 / 10 + A:30 / 5 > 1 + RAND:3
			TFLAG:102 = 4
		ELSEIF RAND:3 > 1
			TFLAG:102 = 1
		ENDIF
	ENDIF
	SIF TEQUIP:0 && TFLAG:93 != 1 && (RAND:3 == 0 || STAIN:MASTER:5 > 3)
		TFLAG:103 = 6
	CALL MIYAKU_USE
	IF TFLAG:93 == 0
		SOURCE:3 = 75 + S:4 * (2 + TALENT:57) / 3
		CALL MIYAKU_USE
		SOURCE:10 = 100 + A:30 * 5
		SOURCE:11 = SOURCE:10 / 2 + A:7 * 5
		SOURCE:12 = 100 + SOURCE:3 / 10
		SIF STAIN:1 & 1 || STAIN:1 & 2 || STAIN:1 & 16
			SOURCE:21 += 80
		SIF STAIN:1 & 4 || STAIN:1 & 8
			SOURCE:21 += 160
		SIF STAIN:1 & 32 || STAIN:1 & 64
			SOURCE:21 += 500
		STAIN:MASTER:5 |= STAIN:1
		STAIN:1 |= STAIN:MASTER:5
	ELSEIF TFLAG:93 == 1
		SOURCE:3 = 150 + S:4 * (3 + TALENT:52 + TALENT:161) / 3
		CALL MIYAKU_USE
		;猫舌の処理
		IF TALENT:54
			SOURCE:3 *= 12 / 10
		ENDIF
		SOURCE:10 = 75 + A:30 * 10
		SOURCE:11 = SOURCE:10 / 2 + A:7 * 5
		SOURCE:12 = 50 + SOURCE:3 / 10
		SIF STAIN:0 & 1 || STAIN:0 & 2 || STAIN:0 & 16
			SOURCE:21 += 100
		SIF STAIN:0 & 4 || STAIN:0 & 8
			SOURCE:21 += 200
		SIF STAIN:0 & 32 || STAIN:0 & 64
			SOURCE:21 += 600
		STAIN:MASTER:5 |= STAIN:0
		STAIN:0 |= STAIN:MASTER:5
	ELSE
		;調教対象の反応によって要求に応じない可能性がありますから、ここは調教者が調教対象の胸を揉むまで処理し、調教対象の動きはCOMの方で処理します。この類の行動は全部このように処理します。
		TFLAG:103 = 4
		SOURCE:3 = S:4 * (2 + TALENT:57) / 2
		SOURCE:10 = 50 + A:30 * 5 + CFLAG:MASTER:0 * 5
		CALL MIYAKU_USE
		SOURCE:11 = 50 + CFLAG:2 / 50 + A:7 * 5
		SOURCE:12 = 50 + SOURCE:3 / 8
		SIF STAIN:1 & 1 || STAIN:1 & 2 || STAIN:1 & 16
			SOURCE:21 += 85
		SIF STAIN:1 & 4 || STAIN:1 & 8
			SOURCE:21 += 180
		SIF STAIN:1 & 32 || STAIN:1 & 64
			SOURCE:21 += 550
		STAIN:MASTER:5 |= STAIN:1
		STAIN:1 |= STAIN:MASTER:5
		SIF TEQUIP:69
			TEQUIP:69 = 0
		TEQUIP:93 = 0
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TFLAG:130 += 80 + (A:2 + A:21 + TALENT:57 * 50) / 3
	TFLAG:64 += 2
;────────────────────────────────────
;012,クンニ/フェラする（Ｓ/Ｎ/Ｈ）（快Ｃ、接触、情愛、性行動、痛み、不潔）
;────────────────────────────────────
ELSEIF TFLAG:90 == 12
	SOURCE:0 = 200 + S:1 * (3 + TALENT:52 + TALENT:161) / 3
	;脱衣の処理
	IF TALENT:32 == 0 && TALENT:34 == 0 && TALENT:140 == 0
		IF TALENT:33 * 2 + A:1 / 10 + A:30 / 5 > 1 + RAND:3
			TFLAG:102 = 5
		ELSEIF RAND:3 > 1
			TFLAG:102 = 2
		ENDIF
	ENDIF
	CALL MIYAKU_USE
	PALAM:4 += 20 + TFLAG:93 * 20
	;猫舌の処理
	IF TALENT:54
		SOURCE:0 *= 12 / 10
	ENDIF
	SOURCE:10 = 150 + A:30 * 5
	SOURCE:11 = SOURCE:10 / 2 + A:7 * 5
	SOURCE:12 = 80 + SOURCE:0 / 10
	SIF STAIN:0 & 1 || STAIN:0 & 2 || STAIN:0 & 16
		SOURCE:21 += 60
	SIF STAIN:0 & 4 || STAIN:0 & 8
		SOURCE:21 += 120
	SIF STAIN:0 & 32 || STAIN:0 & 64
		SOURCE:21 += 360
	IF TFLAG:93 == 1
		STAIN:MASTER:2 |= STAIN:0
		STAIN:0 |= STAIN:MASTER:2
	ELSE
		STAIN:MASTER:3 |= STAIN:0
		STAIN:0 |= STAIN:MASTER:3
	ENDIF
	SIF TEQUIP:69 == 1
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 2

	TEQUIP:91 = 0
	TFLAG:130 += 150 + (A:2 + A:21 + TALENT:52 * 50 + TALENT:54 * 50)
	TFLAG:64 += 2
;────────────────────────────────────
;013,アナル愛撫（Ｎ/Ｈ）[罰]（快Ａ、接触、性行動、痛み、不潔、逸脱）
;────────────────────────────────────
ELSEIF TFLAG:90 == 13
	SOURCE:2 = 60 + S:3 * (2 + TALENT:57) / 2
	;脱衣の処理
	IF TALENT:32 == 0 && TALENT:34 == 0 && TALENT:140 == 0
		IF TALENT:33 * 2 + A:1 / 10 + A:30 / 5 > 1 + RAND:3
			TFLAG:102 = 5
		ELSE
			TFLAG:102 = 2
		ENDIF
	ENDIF
	SIF TEQUIP:0 && (RAND:3 || STAIN:MASTER:4 > 8)
		TFLAG:103 = 6
	CALL LOTION_USE
	CALL MIYAKU_USE
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:2 , 0.50
		SOURCE:13 += 500
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:2 , 0.70
		SOURCE:13 += 200
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:2 , 0.90
		SOURCE:13 += 100
	ENDIF
	;経験不足
	IF EXP:MASTER:2 < 1
		TIMES SOURCE:2 , 0.60
		SOURCE:13 += 600
		SOURCE:22 += 1200
	ELSEIF EXP:MASTER:2 < 6
		TIMES SOURCE:2 , 0.75
		SOURCE:13 += 200
		SOURCE:22 += 700 - EXP:MASTER:2 * 100
	ELSEIF EXP:MASTER:2 < 11
		TIMES SOURCE:2 , 0.90
		SOURCE:13 += 100
		SOURCE:22 += 100 - EXP:MASTER:2 * 10
	ELSEIF EXP:MASTER:2 < 21
		SOURCE:22 += 5
	ENDIF
	SOURCE:10 = 80 + A:30 * 5
	SOURCE:12 = 80 + SOURCE:2 / 10
	SIF STAIN:1 & 1 || STAIN:1 & 2 || STAIN:1 & 16
		SOURCE:21 += 10
	SIF STAIN:1 & 4 || STAIN:1 & 8
		SOURCE:21 += 30
	SIF STAIN:1 & 32 || STAIN:1 & 64
		SOURCE:21 += 100
	STAIN:MASTER:4 |= STAIN:1
	STAIN:1 |= STAIN:MASTER:4

	TEQUIP:92 = 0
	IF TFLAG:132 == 0
		TFLAG:130 += 100 + (B:5 + B:11 * A:2 / 50)
	ELSE
		TFLAG:131 += (1 + A:2 / 10 + TALENT:57) * 500
	ENDIF
	TFLAG:64 += 2
;────────────────────────────────────
;014,アナル舐め（Ｎ/Ｈ）[罰]（快Ａ、接触、性行動、痛み、不潔、逸脱）
;────────────────────────────────────
ELSEIF TFLAG:90 == 14
	SOURCE:2 = 60 + S:3 * (3 + TALENT:52 + TALENT:161) / 3
	;脱衣の処理
	IF TALENT:32 == 0 && TALENT:34 == 0 && TALENT:140 == 0
		IF TALENT:33 * 2 + A:1 / 10 + A:30 / 5 > 1 + RAND:3
			TFLAG:102 = 5
		ELSE
			TFLAG:102 = 2
		ENDIF
	ENDIF
	CALL MIYAKU_USE
	PALAM:4 += 20
	;猫舌の処理
	IF TALENT:54
		SOURCE:2 *= 12 / 10
	ENDIF
	;経験不足
	IF EXP:MASTER:2 < 1
		TIMES SOURCE:2 , 0.80
		SOURCE:22 += 1000
	ELSEIF EXP:MASTER:2 < 6
		TIMES SOURCE:2 , 0.85
		SOURCE:22 += 500 - EXP:MASTER:2 * 50
	ELSEIF EXP:MASTER:2 < 11
		TIMES SOURCE:2 , 0.95
		SOURCE:22 += 150 - EXP:MASTER:2 * 12
	ELSEIF EXP:MASTER:2 < 21
		SOURCE:22 += 10
	ENDIF
	SOURCE:10 = 120 + A:30 * 5
	SOURCE:12 = 80 + SOURCE:2 / 10
	SIF STAIN:0 & 1 || STAIN:0 & 2 || STAIN:0 & 16
		SOURCE:21 += 10
	SIF STAIN:0 & 4 || STAIN:0 & 8
		SOURCE:21 += 30
	SIF STAIN:0 & 32 || STAIN:0 & 64
		SOURCE:21 += 100
	STAIN:MASTER:4 |= STAIN:0
	STAIN:0 |= STAIN:MASTER:4

	TEQUIP:92 = 0
	IF TFLAG:132 == 0
		TFLAG:130 += 80 + B:5
	ELSE
		TFLAG:131 += (1 + A:2 / 10 + TALENT:52) * 500
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TFLAG:64 += 2
;────────────────────────────────────
;015,キス（Ｓ/Ｎ）（接触、情愛、性行動、痛み、不潔）
;────────────────────────────────────
ELSEIF TFLAG:90 == 15
	SOURCE:10 = 150 + A:30 * 5
	SOURCE:11 = 100 + CFLAG:2 / 50 + TALENT:52 * A:21 * 2 + A:21 * A:2 / 10 + A:7 * 5
	;脱衣の処理
	SIF RAND:3 > 1
		TFLAG:102 = 6
	CALL MIYAKU_USE

	SOURCE:12 = 50 + SOURCE:11 / 15
	SIF STAIN:0 & 1 || STAIN:0 & 2 || STAIN:0 & 16
		SOURCE:21 += 180
	SIF STAIN:0 & 4 || STAIN:0 & 8
		SOURCE:21 += 350
	SIF STAIN:0 & 32 || STAIN:0 & 64
		SOURCE:21 += 700
	STAIN:MASTER:0 |= STAIN:0
	STAIN:0 |= STAIN:MASTER:0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TFLAG:130 += 50 + (A:2 + A:21 + TALENT:52 * 50 + TALENT:54 * 50) / 2
	TFLAG:64 += 1
;────────────────────────────────────
;016,貝あわせ/素股する（Ｓ/Ｎ）（快Ｃ、接触、情愛、性行動、不潔、快ｃ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 16
	SOURCE:0 = 80 + S:1 * (3 + TALENT:42 - TALENT:43) / 3
	SOURCE:40 = 30 + S:41 * (3 + TALENT:42 - TALENT:43) / 3
	;脱衣の処理
	IF TALENT:32 == 0 && TALENT:34 == 0 && TALENT:140 == 0
		IF TALENT:33 * 2 + A:1 / 10 + A:30 / 5 > 1 + RAND:3
			TFLAG:102 = 5
		ELSE
			TFLAG:102 = 2
		ENDIF
	ENDIF
	TFLAG:103 = 5
	CALL LOTION_USE
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:0 , 0.75
		TIMES SOURCE:40 , 0.75
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:0 , 0.85
		TIMES SOURCE:40 , 0.85
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:0 , 0.95
		TIMES SOURCE:40 , 0.95
	ENDIF
	SOURCE:10 = 200 + A:30 * 5 - TFLAG:93 * 50
	SOURCE:11 = SOURCE:10 / 2 + A:7 * 5
	SOURCE:12 = 80 + SOURCE:0 / 10
	SIF STAIN:3 & 1 || STAIN:3 & 2 || STAIN:3 & 16
		SOURCE:21 += 50
	SIF STAIN:3 & 4 || STAIN:3 & 8
		SOURCE:21 += 100
	SIF STAIN:3 & 32 || STAIN:3 & 64
		SOURCE:21 += 300
	IF TFLAG:93 == 1
		STAIN:MASTER:2 |= STAIN:3
		STAIN:3 |= STAIN:MASTER:2
	ELSE
		STAIN:MASTER:3 |= STAIN:3
		STAIN:3 |= STAIN:MASTER:3
	ENDIF
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TEQUIP:91 = 0
	TEQUIP:94 = 0
	TFLAG:130 += 150 + (A:2 + A:21 )
	TFLAG:64 += 3
;────────────────────────────────────
;017,パイズリする（Ｓ/Ｎ）（快Ｃ、接触、情愛、痛み、性行動、不潔、快ｂ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 17
	SOURCE:0 = 70 + S:1 * (4 + TALENT:109 - TALENT:108) / 4
	SOURCE:43 = 20 + S:44 / 2
	;脱衣の処理
	IF TALENT:32 == 0 && TALENT:34 == 0 && TALENT:140 == 0
		IF TALENT:33 * 2 + A:1 / 10 + A:30 / 5 > 1 + RAND:3
			TFLAG:102 = 5
			SIF A:30 + A:0 / 10 > 4 + RAND:5
				TFLAG:102 = 7
		ELSE
			TFLAG:102 = 2
		ENDIF
	ENDIF
	TFLAG:103 = 4
	CALL MIYAKU_USE
	CALL LOTION_USE
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:0 , 0.75
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:0 , 0.85
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:0 , 0.95
	ENDIF
	SIF TALENT:108
		TIMES SOURCE:0 , 0.80
	;猫舌の処理
	IF TALENT:54
		SOURCE:0 *= 11 / 10
	ENDIF
	SOURCE:10 = 250 + A:30 * 5
	SOURCE:11 = SOURCE:10 / 2 + A:7 * 5
	SOURCE:12 = 40 + SOURCE:0 / 10
	SIF STAIN:0 & 1 || STAIN:0 & 2 || STAIN:0 & 16
		SOURCE:21 += 20
	SIF STAIN:0 & 4 || STAIN:0 & 8
		SOURCE:21 += 50
	SIF STAIN:0 & 32 || STAIN:0 & 64
		SOURCE:21 += 150
	SIF STAIN:5 & 1 || STAIN:5 & 2 || STAIN:5 & 16
		SOURCE:21 += 50
	SIF STAIN:5 & 4 || STAIN:5 & 8
		SOURCE:21 += 100
	SIF STAIN:5 & 32 || STAIN:5 & 64
		SOURCE:21 += 300
	STAIN:MASTER:2 |= STAIN:0
	STAIN:MASTER:2 |= STAIN:5
	STAIN:0 |= STAIN:MASTER:2
	STAIN:5 |= STAIN:MASTER:2
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:91 = 0
	TFLAG:130 += 150 + (A:2 + A:21 + TALENT:109 * 50 - TALENT:108 * 50 + TALENT:75 * 50)
	TFLAG:64 += 3
;────────────────────────────────────
;018,足コキする（Ｓ/Ｎ/Ｈ）[罰]（快Ｃ、接触、情愛、性行動、不潔、逸脱）
;────────────────────────────────────
ELSEIF TFLAG:90 == 18
	SOURCE:0 = 40 + S:1 * (3 + TALENT:118) / 3
	SOURCE:10 = 60 + A:30 * 10
	SOURCE:11 = SOURCE:10 / 2 + A:7 * 5
	SOURCE:12 = SOURCE:0 / 10
	SIF TALENT:83
		SOURCE:13 = SOURCE:0 / 4
	SOURCE:22 = 200 - A:30 * 10
	;脱衣の処理
	SIF TALENT:32 == 0 && TALENT:34 == 0 && TALENT:140 == 0
		TFLAG:102 = 2
	SIF TEQUIP:1 && STAIN:MASTER:2 > 2
		TFLAG:103 = 6
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	SIF STAIN:6 & 1 || STAIN:6 & 2 || STAIN:6 & 16
		SOURCE:21 += 35
	SIF STAIN:6 & 4 || STAIN:6 & 8
		SOURCE:21 += 80
	SIF STAIN:6 & 32 || STAIN:6 & 64
		SOURCE:21 += 200
	STAIN:MASTER:2 |= STAIN:6
	STAIN:6 |= STAIN:MASTER:2
	IF TEQUIP:44
		TEQUIP:44 = 0
		TEQUIP:45 = 1
	ENDIF
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TFLAG:130 += 100 + (A:2 + A:21)
	TFLAG:64 += 2
;────────────────────────────────────
;020,ローター（Ｓ/Ｎ）（快Ｃ、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 20
	SOURCE:0 = 150 + S:11 * (3 + TALENT:59) / 3
	SOURCE:23 = B:3 * (B:3 - 10) * (ITEM:9 + 1) / 10
	;脱衣の処理
	IF TALENT:140
		SIF A:30 + TALENT:33 * 3 > 2 + RAND:3
			TFLAG:102 = 9
	ELSEIF TALENT:32 == 0 && TALENT:34 == 0
		TFLAG:102 = 5
	ENDIF
	SIF TEQUIP:MASTER:4 > 1 && TFLAG:102 != 5
		TFLAG:102 = 2
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:0 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	TEQUIP:91 = 0
	TFLAG:130 += 150 + (A:2 + A:22 )
	TFLAG:64 += 1
;────────────────────────────────────
;021,バイブ（Ｓ/Ｎ/Ｈ）[罰]（快Ｖ、痛み、逸脱、中毒充足、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 21
	IF TFLAG:93 == 0
		SOURCE:1 = 150 + ITEM:9 * 200 + S:12 * (TALENT:59 + 3) / 3
		;脱衣の処理
		IF TALENT:140
			SIF A:30 + TALENT:33 * 3 > 2 + RAND:3
				TFLAG:102 = 9
		ELSEIF TALENT:32 == 0 && TALENT:34 == 0
			TFLAG:102 = 5
		ENDIF
		SIF TEQUIP:MASTER:4 > 1 && TFLAG:102 != 5
			TFLAG:102 = 2
		CALL MIYAKU_USE
		CALL LOTION_USE
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES SOURCE:1 , 0.60
			SOURCE:13 += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES SOURCE:1 , 0.75
			SOURCE:13 += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES SOURCE:1 , 0.90
			SOURCE:13 += 100
		ENDIF
		;経験不足
		IF EXP:MASTER:1 < 1
			TIMES SOURCE:1 , 0.30
			SOURCE:13 += 1000
			SOURCE:22 += 1000
			;合意なしで処女を奪おうとすると…
			SIF CFLAG:3 == 0 && EXP:MASTER:8 > 4
				SOURCE:24 += 2500
		ELSEIF EXP:MASTER:1 < 4
			TIMES SOURCE:1 , 0.80
			SOURCE:13 += 100
			SOURCE:22 += 250
		ELSEIF EXP:MASTER:1 < 6
			TIMES SOURCE:1 , 0.95
			SOURCE:13 += 10
			SOURCE:22 += 50
		ENDIF
		TEQUIP:20 = 1 + ITEM:9
	ELSE
		SOURCE:1 = 10 + (TALENT:59 + 3) * S:12 / 3
		TEQUIP:20 = 0
	ENDIF
	SOURCE:23 = B:4 * (B:4 - 20) * (ITEM:9 + 1) / 5
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:1 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	IF CFLAG:MASTER:8 == 21
		SOURCE:24 += SOURCE:22
		TIMES SOURCE:24 , 1.50
	ENDIF
	TEQUIP:94 = 0
	TFLAG:130 += 100
	TFLAG:64 += 2
;────────────────────────────────────
;022,アナルバイブ（Ｎ/Ｈ）[罰]（快Ａ、痛み、逸脱、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 22
	IF TFLAG:93 == 0
		SOURCE:2 = 100 + ITEM:9 * 200 + (TALENT:59 + 3) * S:13 / 3
		;脱衣の処理
		IF TALENT:140
			SIF A:30 + TALENT:33 * 3 > 2 + RAND:3
				TFLAG:102 = 9
		ELSEIF TALENT:32 == 0 && TALENT:34 == 0
			TFLAG:102 = 5
		ENDIF
		SIF TEQUIP:MASTER:4 > 1 && TFLAG:102 != 5
			TFLAG:102 = 2
		CALL MIYAKU_USE
		CALL LOTION_USE
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES SOURCE:2 , 0.50
			SOURCE:13 += 600
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES SOURCE:2 , 0.70
			SOURCE:13 += 250
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES SOURCE:2 , 0.90
			SOURCE:13 += 100
		ENDIF
		;経験不足
		IF EXP:MASTER:2 < 1
			TIMES SOURCE:2 , 0.50
			SOURCE:13 += 800
			SOURCE:22 += 1500
		ELSEIF EXP:MASTER:2 < 6
			TIMES SOURCE:2 , 0.75
			SOURCE:13 += 300
			SOURCE:22 += 800 - EXP:MASTER:2 * 100
		ELSEIF EXP:MASTER:2 < 11
			TIMES SOURCE:2 , 0.90
			SOURCE:13 += 120
			SOURCE:22 += 200 - EXP:MASTER:2 * 10
		ELSEIF EXP:MASTER:2 < 21
			SOURCE:22 += 60
		ENDIF
		TEQUIP:25 = 1 + ITEM:9
	ELSE
		SOURCE:2 = 10 + (TALENT:59 + 3) * S:13 / 3
		TEQUIP:25 = 0
	ENDIF
	SOURCE:23 = (B:5 + 10) * (B:5 - 20) * (ITEM:9 + 1) / 5
		SOURCE:23 += SOURCE:2 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	TEQUIP:92 = 0
	TFLAG:130 += 150
	TFLAG:64 += 2
;────────────────────────────────────
;023,アナルビーズ（Ｓ/Ｎ/Ｈ）（快Ａ、痛み、逸脱、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 23
	IF TFLAG:93 == 0
		TEQUIP:26 = 1 + RAND:2
		SOURCE:2 = 50 + S:13 * (3 + TEQUIP:26 + TALENT:59) / 5
		;脱衣の処理
		IF TALENT:140
			SIF A:30 + TALENT:33 * 3 > 2 + RAND:3
				TFLAG:102 = 9
		ELSEIF TALENT:32 == 0 && TALENT:34 == 0
			TFLAG:102 = 5
		ENDIF
		SIF TEQUIP:MASTER:4 > 1 && TFLAG:102 != 5
			TFLAG:102 = 2
		CALL MIYAKU_USE
		CALL LOTION_USE
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES SOURCE:2 , 0.60
			SOURCE:13 += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES SOURCE:2 , 0.75
			SOURCE:13 += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES SOURCE:2 , 0.90
			SOURCE:13 += 50
		ENDIF
		;経験不足
		IF EXP:MASTER:2 < 1
			TIMES SOURCE:2 , 0.80
			SOURCE:13 += 450
			SOURCE:22 += 1200
		ELSEIF EXP:MASTER:2 < 6
			TIMES SOURCE:2 , 0.90
			SOURCE:13 += 200
			SOURCE:22 += 700 - EXP:MASTER:2 * 100
		ELSEIF EXP:MASTER:2 < 11
			SOURCE:13 += 60
			SOURCE:22 += 150 - EXP:MASTER:2 * 10
		ELSEIF EXP:MASTER:2 < 21
			SOURCE:22 += 20
		ENDIF
	ELSEIF TFLAG:93 == 2
		TEQUIP:26 += 1 + RAND:3
		SOURCE:2 = 50 + S:13 * (3 + TEQUIP:26 + TALENT:59) / 6
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES SOURCE:2 , 0.70
			SOURCE:13 += 250
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES SOURCE:2 , 0.85
			SOURCE:13 += 100
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			SOURCE:13 += 20
		ENDIF
		;経験不足
		IF EXP:MASTER:2 < 1
			TIMES SOURCE:2 , 0.80
			SOURCE:13 += 250
			SOURCE:22 += 500
		ELSEIF EXP:MASTER:2 < 6
			TIMES SOURCE:2 , 0.90
			SOURCE:13 += 50
			SOURCE:22 += 300 - EXP:MASTER:2 * 50
		ELSEIF EXP:MASTER:2 < 11
			SOURCE:22 += 40 - EXP:MASTER:2 * 3
		ELSEIF EXP:MASTER:2 < 21
			SOURCE:22 += 5
		ENDIF
	ELSE
		SOURCE:2 = S:13 * (3 + TEQUIP:26 + TALENT:59) / 3
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES SOURCE:2 , 0.80
			SOURCE:13 += 1000
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES SOURCE:2 , 0.90
			SOURCE:13 += 800
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			SOURCE:13 += 500
		ENDIF
		;経験不足
		IF EXP:MASTER:2 < 1
			TIMES SOURCE:2 , 0.80
			SOURCE:13 += 750
			SOURCE:22 += 700
		ELSEIF EXP:MASTER:2 < 6
			TIMES SOURCE:2 , 0.90
			SOURCE:13 += 350
			SOURCE:22 += 500 - EXP:MASTER:2 * 20
		ELSEIF EXP:MASTER:2 < 11
			SOURCE:13 += 150
			SOURCE:22 += 200 - EXP:MASTER:2 * 10
		ELSEIF EXP:MASTER:2 < 21
			SOURCE:22 += 80 - EXP:MASTER:2 * 5
		ENDIF
		TEQUIP:26 = 0
	ENDIF
	SOURCE:23 = (B:5 + 10) * (B:5 - 20) / 5
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:2 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	TEQUIP:92 = 0
	TFLAG:130 += 100
	TFLAG:64 += 1
;────────────────────────────────────
;024,クリキャップ/オナホール（Ｓ/Ｎ）（快Ｃ、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 24
	IF TFLAG:93 == 0
		;脱衣の処理
		IF TALENT:140
			SIF A:30 + TALENT:33 * 3 > 2 + RAND:3
				TFLAG:102 = 9
		ELSEIF TALENT:32 == 0 && TALENT:34 == 0
			TFLAG:102 = 5
		ENDIF
		SIF TEQUIP:MASTER:4 > 1 && TFLAG:102 != 5
			TFLAG:102 = 2
		SOURCE:0 = 150 + ITEM:9 * 100 + (TALENT:59 + 3) * S:11 / 3
		TEQUIP:30 = 1 + ITEM:9
	ELSEIF TFLAG:93 == 1
		SOURCE:0 = 50
		TEQUIP:30 = 0
	ELSEIF TFLAG:93 == 2
		;脱衣の処理
		IF TALENT:140
			SIF A:30 + TALENT:33 * 3 > 2 + RAND:3
				TFLAG:102 = 9
		ELSEIF TALENT:32 == 0 && TALENT:34 == 0
			TFLAG:102 = 5
		ENDIF
		SIF TEQUIP:MASTER:4 > 1 && TFLAG:102 != 5
			TFLAG:102 = 2
		SOURCE:0 = 150 + ITEM:9 * 100 + (TALENT:59 + 3) * S:11 / 3
		TEQUIP:31 = 1 + ITEM:9
	ELSE
		SOURCE:0 = 50
		TEQUIP:31 = 0
	ENDIF
	SOURCE:23 = B:3 * (B:3 - 10) * (ITEM:9 + 3) / 20
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:0 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	TEQUIP:91 = 0
	TFLAG:130 += 200
	TFLAG:64 += 1
;────────────────────────────────────
;025,ニプルキャップ（Ｓ/Ｎ）（快Ｂ、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 25
	IF TFLAG:93 == 0
		;脱衣の処理
		IF TALENT:140 == 0 && TALENT:32 == 0 && TALENT:34 == 0
			TFLAG:102 = 4
			SIF RAND:3 > 1
				TFLAG:102 = 1
		ENDIF
		SOURCE:3 = 150 + ITEM:9 * 100 + S:14 * (TALENT:59 + 3) / 3
		TEQUIP:35 = 1
	ELSE
		SOURCE:3 = 50
		TEQUIP:35 = 0
	ENDIF
	SOURCE:23 = (B:6 - 10) * (B:6 - 20) * (ITEM:9 + 1) / 4
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:3 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	TEQUIP:93 = 0
	TFLAG:130 += 30
	TFLAG:64 += 1
;────────────────────────────────────
;026,搾乳器（Ｎ/Ｈ）[罰]（快Ｂ、痛み、逸脱、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 26
	IF TFLAG:93 == 0
		TFLAG:102 = 4
		;経験を見る
		IF EXP:MASTER:6 < 1
			SOURCE:3 = S:14 * (TALENT:59 + 3) / 10
			SOURCE:13 = 800
			SOURCE:22 = 1200
		ELSEIF EXP:MASTER:6 < 6
			SOURCE:3 = 25 + S:14 * (TALENT:59 + 3) / 8
			SOURCE:13 = 650
			SOURCE:22 = 900
		ELSEIF EXP:MASTER:6 < 13
			SOURCE:3 = 50 + S:14 * (TALENT:59 + 3) / 5
			SOURCE:13 = 450
			SOURCE:22 = 600
		ELSEIF EXP:MASTER:6 < 21
			SOURCE:3 = 75 + S:14 * (TALENT:59 + 3) / 3
			SOURCE:13 = 300
			SOURCE:22 = 300
		ELSE
			SOURCE:3 = 100 + S:14 * (TALENT:59 + 3) / 2
			SOURCE:13 = 150
			SOURCE:23 = (B:6 + 10) * (B:6 - 10) * (ITEM:9 + 1) / 5
			SIF SOURCE:23 > 0
				SOURCE:23 += SOURCE:3 / 5
			SIF SOURCE:23 < 0
				SOURCE:23 = 0
		ENDIF
		TEQUIP:36 = 1
		TFLAG:50 = 1
	ELSE
		SOURCE:3 = 100
		TFLAG:50 = 0
		TEQUIP:36 = 0
	ENDIF
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:93 = 0
	TFLAG:130 += 50
	TFLAG:64 += 2
;────────────────────────────────────
;027,押し倒す(Ｓ/Ｎ）（快Ｂ、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 27
	IF TEQUIP:37 == 0
		TFLAG:160 = TFLAG:64
		TEQUIP:37 = 1
	ELSE
		TFLAG:160 = 0
		TEQUIP:37 = 0
	ENDIF

	SIF TEQUIP:69
		TEQUIP:69 = 0

	TFLAG:130 += 100 + B:1 * 3
	TFLAG:64 += 1
;────────────────────────────────────
;030,正常位（Ｓ/Ｎ）（快Ｖ、接触、情愛、性行動、痛み、中毒充足、トラウマ、快ｃ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 30
	IF TALENT:140 && TEQUIP:MASTER:4 < 1
		TFLAG:102 = 9
		TFLAG:103 = 9
	ELSEIF TALENT:33 || (A:30 + A:1 / 10 + TALENT:MASTER:91 * 3 > TALENT:32 * 5 + TALENT:34 * 3 + RAND:3)
		TFLAG:102 = 7 + RAND:2
		TFLAG:103 = 7 + RAND:2
	ELSE
		TFLAG:102 = 5
		TFLAG:103 = 5
	ENDIF
	SOURCE:1 = 300 + S:5 * 3 / 2
	SIF TALENT:121 || TALENT:122
		SOURCE:40 = 200 + S:45 * 3 / 2
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:1 , 0.60
		TIMES SOURCE:40 , 0.80
		SOURCE:13 += 500
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:1 , 0.75
		TIMES SOURCE:40 , 0.90
		SOURCE:13 += 200
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:1 , 0.90
		TIMES SOURCE:40 , 0.95
		SOURCE:13 += 100
	ENDIF
	;経験不足
	IF EXP:MASTER:1 < 1
		TIMES SOURCE:1 , 0.30
		SOURCE:13 += 1000
		SOURCE:22 += 1000
		;合意なしで処女を奪おうとすると…
		SIF CFLAG:3 == 0 && EXP:MASTER:8 > 4
			SOURCE:24 += 2500
	ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
		TIMES SOURCE:1 , 0.80
		SOURCE:13 += 150
		SOURCE:22 += 250
	ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
		TIMES SOURCE:1 , 0.95
		SOURCE:13 += 50
		SOURCE:22 += 50
	ENDIF
	SOURCE:10 = 250 + A:30 * 10
	SOURCE:12 = 50 + SOURCE:10 / 5
	IF CFLAG:MASTER:8 == 30
		SOURCE:24 += SOURCE:10
		TIMES SOURCE:24 , 1.50
	ENDIF
	SOURCE:11 += SOURCE:1 / 5 - SOURCE:24 - SOURCE:13
	SIF SOURCE:11 > 0
		SOURCE:23 = B:4 * (B:4 - 20) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:1 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 5
	STAIN:MASTER:3 |= STAIN:2
	STAIN:2 |= STAIN:MASTER:3
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:94 = 0
	TFLAG:130 += 100
	TFLAG:64 += 3
	TEQUIP:20 = 0

;────────────────────────────────────
;031,後背位（Ｎ/Ｈ）[罰]（快Ｖ、接触、情愛、性行動、痛み、中毒充足、トラウマ、快ｃ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 31
	IF TALENT:140 && TEQUIP:MASTER:4 < 1
		TFLAG:102 = 9
		TFLAG:103 = 9
	ELSEIF TALENT:33 || (A:30 + A:1 / 10 + TALENT:MASTER:91 * 3 > TALENT:32 * 5 + TALENT:34 * 3 + RAND:3)
		TFLAG:102 = 7 + RAND:2
		TFLAG:103 = 7 + RAND:2
	ELSE
		TFLAG:102 = 5
		TFLAG:103 = 5
	ENDIF
	SOURCE:1 = 300 + S:5 * 3 / 2
	SIF TALENT:121 || TALENT:122
		SOURCE:40 = 300 + S:45
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:1 , 0.50
		TIMES SOURCE:40 , 0.60
		SOURCE:13 += 750
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:1 , 0.70
		TIMES SOURCE:40 , 0.80
		SOURCE:13 += 400
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:1 , 0.85
		TIMES SOURCE:40 , 0.95
		SOURCE:13 += 150
	ENDIF
	;経験不足
	IF EXP:MASTER:1 < 1
		TIMES SOURCE:1 , 0.30
		SOURCE:13 += 1200
		SOURCE:22 += 1500
		;合意なしで処女を奪おうとすると…
		SIF CFLAG:3 == 0 && EXP:MASTER:8 > 4
			SOURCE:24 += 3500
	ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
		TIMES SOURCE:1 , 0.70
		SOURCE:13 += 350
		SOURCE:22 += 750
	ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
		TIMES SOURCE:1 , 0.90
		SOURCE:13 += 90
		SOURCE:22 += 250
	ENDIF
	SOURCE:10 = 200 + A:30 * 10
	SOURCE:12 = 50 + SOURCE:10 / 5
	IF CFLAG:MASTER:8 == 31
		SOURCE:24 += SOURCE:10
		TIMES SOURCE:24 , 1.50
	ENDIF
	SOURCE:11 += SOURCE:1 / 7 - SOURCE:24
	SIF SOURCE:11 > 0
		SOURCE:23 = B:4 * (B:4 - 20) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:1 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 7
	STAIN:MASTER:3 |= STAIN:2
	STAIN:2 |= STAIN:MASTER:3
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:94 = 0
	TFLAG:130 += 100
	TFLAG:64 += 3
	TEQUIP:20 = 0
;────────────────────────────────────
;032,騎乗位（Ｓ/Ｎ）（快Ｖ、接触、情愛、性行動、痛み、中毒充足、トラウマ、快ｃ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 32
	IF TALENT:140 && TEQUIP:MASTER:4 < 1
		TFLAG:102 = 9
		TFLAG:103 = 9
	ELSEIF TALENT:33 || (A:30 + A:1 / 10 + TALENT:MASTER:91 * 3 > TALENT:32 * 5 + TALENT:34 * 3 + RAND:3)
		TFLAG:102 = 7 + RAND:2
		TFLAG:103 = 7 + RAND:2
	ELSE
		TFLAG:102 = 5
		TFLAG:103 = 5
	ENDIF
	SOURCE:1 = 100 + S:5 * 2
	SIF TALENT:121 || TALENT:122
		SOURCE:40 = 100 + S:45 * 2
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:1 , 0.60
		TIMES SOURCE:40 , 0.65
		SOURCE:13 += 500
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:1 , 0.80
		TIMES SOURCE:40 , 0.80
		SOURCE:13 += 200
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:1 , 0.95
		TIMES SOURCE:40 , 0.90
		SOURCE:13 += 50
	ENDIF
	;経験不足
	IF EXP:MASTER:1 < 1
		TIMES SOURCE:1 , 0.35
		SOURCE:13 += 700
		SOURCE:22 += 1200
		;合意なしで処女を奪おうとすると…
		SIF CFLAG:3 == 0 && EXP:MASTER:8 > 4
			SOURCE:24 += 3200
	ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
		TIMES SOURCE:1 , 0.75
		SOURCE:13 += 200
		SOURCE:22 += 700
	ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
		TIMES SOURCE:1 , 0.95
		SOURCE:13 += 30
		SOURCE:22 += 150
	ENDIF
	SOURCE:10 = 200 + CFLAG:MASTER:0 * 10
	SOURCE:12 = 50 + SOURCE:10 / 5
	IF CFLAG:MASTER:8 == 32
		SOURCE:24 += SOURCE:10
		TIMES SOURCE:24 , 1.50
	ENDIF
	SOURCE:11 = SOURCE:1 / 5 - SOURCE:24 - SOURCE:13
	SIF SOURCE:11 > 0
		SOURCE:23 = B:4 * (B:4 - 20) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:1 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 7 + SOURCE:12 / 5
	STAIN:MASTER:3 |= STAIN:2
	STAIN:2 |= STAIN:MASTER:3
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0
	TEQUIP:94 = 0
	TFLAG:130 += 100
	TFLAG:64 += 3
	TEQUIP:20 = 0
;────────────────────────────────────
;033,対面座位（Ｓ/Ｎ/Ｈ）（快Ｖ、接触、情愛、性行動、痛み、中毒充足、トラウマ、快ｃ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 33
	IF TALENT:140 && TEQUIP:MASTER:4 < 1
		TFLAG:102 = 9
		TFLAG:103 = 9
	ELSEIF TALENT:33 || (A:30 + A:1 /10 + TALENT:MASTER:91 * 3 > TALENT:32 * 5 + TALENT:34 * 3 + RAND:3)
		TFLAG:102 = 7 + RAND:2
		TFLAG:103 = 7 + RAND:2
	ELSE
		TFLAG:102 = 5
		TFLAG:103 = 5
	ENDIF
	SOURCE:1 = 100 + S:5
	SOURCE:0 = S:1 / 2
	SIF TALENT:121 || TALENT:122
		SOURCE:40 = 100 + S:45 * 2 / 3
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:1 , 0.60
		TIMES SOURCE:40 , 0.60
		SOURCE:13 += 500
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:1 , 0.75
		TIMES SOURCE:40 , 0.75
		SOURCE:13 += 200
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:1 , 0.90
		TIMES SOURCE:40 , 0.90
		SOURCE:13 += 100
	ENDIF
	;経験不足
	IF EXP:MASTER:1 < 1
		TIMES SOURCE:1 , 0.30
		SOURCE:13 += 1200
		SOURCE:22 += 1200
		;合意なしで処女を奪おうとすると…
		SIF CFLAG:3 == 0 && EXP:MASTER:8 > 4
			SOURCE:24 += 2750
	ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
		TIMES SOURCE:1 , 0.80
		SOURCE:13 += 250
		SOURCE:22 += 350
	ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
		TIMES SOURCE:1 , 0.95
		SOURCE:13 += 50
		SOURCE:22 += 100
	ENDIF
	SOURCE:10 = 400 + A:30 * 8 + CFLAG:MASTER:0 * 8
	SOURCE:12 = 50 + SOURCE:10 / 5
	IF CFLAG:MASTER:8 == 33
		SOURCE:24 += SOURCE:10
		TIMES SOURCE:24 , 1.50
	ENDIF
	SOURCE:11 = SOURCE:1 / 4 - SOURCE:24 - SOURCE:13 * 2
	SIF SOURCE:11 > 0
		SOURCE:23 = B:4 * (B:4 - 20) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:1 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 5
	STAIN:MASTER:3 |= STAIN:2
	STAIN:2 |= STAIN:MASTER:3
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:94 = 0
	TFLAG:130 += 150
	TFLAG:64 += 3
	TEQUIP:20 = 0
;────────────────────────────────────
;034,背面座位（Ｎ/Ｈ）（快Ｖ、接触、情愛、性行動、痛み、露出、中毒充足、トラウマ、快ｃ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 34
	IF TALENT:140 && TEQUIP:MASTER:4 < 1
		TFLAG:102 = 9
		TFLAG:103 = 9
	ELSEIF TALENT:33 || (A:30 + A:1 / 10 + TALENT:MASTER:91 * 3 > TALENT:32 * 5 + TALENT:34 * 3 + RAND:3)
		TFLAG:102 = 7 + RAND:2
		TFLAG:103 = 7 + RAND:2
	ELSE
		TFLAG:102 = 5
		TFLAG:103 = 5
	ENDIF
	SOURCE:1 = 50 + S:5
	SOURCE:0 = S:1 / 2
	SOURCE:3 = S:4 / 2
	SIF TALENT:121 || TALENT:122
		SOURCE:40 = 150 + S:45 / 2
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:1 , 0.50
		TIMES SOURCE:40 , 0.55
		SOURCE:13 += 750
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:1 , 0.70
		TIMES SOURCE:40 , 0.75
		SOURCE:13 += 400
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:1 , 0.85
		TIMES SOURCE:40 , 0.90
		SOURCE:13 += 150
	ENDIF
	;経験不足
	IF EXP:MASTER:1 < 1
		TIMES SOURCE:1 , 0.30
		SOURCE:13 += 1250
		SOURCE:22 += 1600
		;合意なしで処女を奪おうとすると…
		SIF CFLAG:3 == 0 && EXP:MASTER:8 > 4
			SOURCE:24 += 3500
	ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
		TIMES SOURCE:1 , 0.70
		SOURCE:13 += 380
		SOURCE:22 += 800
	ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
		TIMES SOURCE:1 , 0.90
		SOURCE:13 += 100
		SOURCE:22 += 350
	ENDIF
	SOURCE:10 = 500 + A:30 * 10
	SOURCE:12 = 50 + SOURCE:10 / 5
	;乱れ牡丹の処理
	SIF TEQUIP:56
		SOURCE:20 = 200 + SOURCE:1 / 5
	IF CFLAG:MASTER:8 == 34
		SOURCE:24 += SOURCE:10
		TIMES SOURCE:24 , 1.50
	ENDIF
	SOURCE:11 = SOURCE:1 / 7 - SOURCE:24
	SIF SOURCE:11 > 0
		SOURCE:23 = B:4 * (B:4 - 20) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:1 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 7
	STAIN:MASTER:3 |= STAIN:2
	STAIN:2 |= STAIN:MASTER:3
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:94 = 0
	TFLAG:130 += 150
	TFLAG:64 += 4
	TEQUIP:20 = 0
;────────────────────────────────────
;035,アナルセックス（Ｎ/Ｈ/Ａ）[罰]（快Ａ、接触、情愛、性行動、痛み、逸脱、中毒充足、トラウマ、快ｃ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 35
	IF TALENT:140 && TEQUIP:MASTER:4 < 1
		TFLAG:102 = 9
		TFLAG:103 = 9
	ELSEIF TALENT:33 || (A:30 + A:1 / 10 + TALENT:MASTER:91 * 3 > TALENT:32 * 5 + TALENT:34 * 3 + RAND:3)
		TFLAG:102 = 7 + RAND:2
		TFLAG:103 = 7 + RAND:2
	ELSE
		TFLAG:102 = 5
		TFLAG:103 = 5
	ENDIF
	SOURCE:2 = 300 + S:6 * 2
	SIF TALENT:121 || TALENT:122
		SOURCE:40 = 500 + S:46 * 2
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:2 , 0.40
		TIMES SOURCE:40 , 0.70
		SOURCE:13 += 1500
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:2 , 0.65
		TIMES SOURCE:40 , 0.80
		SOURCE:13 += 900
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:2 , 0.80
		TIMES SOURCE:40 , 0.90
		SOURCE:13 += 250
	ENDIF
	;経験不足
	IF EXP:MASTER:2 < 1
		TIMES SOURCE:2 , 0.40
		SOURCE:13 += 1500
		SOURCE:22 += 2500
		SOURCE:24 += 1000
	ELSEIF EXP:MASTER:2 < 6 && EXP:MASTER:7 < 11
		TIMES SOURCE:2 , 0.55
		SOURCE:13 += 1000
		SOURCE:22 += 1500
		SOURCE:24 += 500
	ELSEIF EXP:MASTER:2 < 13 && EXP:MASTER:7 < 21
		TIMES SOURCE:2 , 0.70
		SOURCE:13 += 600
		SOURCE:22 += 500
		SOURCE:24 += 100
	ELSEIF EXP:MASTER:2 < 26 && EXP:MASTER:7 < 31
		TIMES SOURCE:2 , 0.85
		SOURCE:13 += 200
		SOURCE:22 += 150
	ENDIF
	SOURCE:10 = 200 + A:30 * 10
	SOURCE:12 = SOURCE:10 / 5
	IF CFLAG:MASTER:8 == 35
		SOURCE:24 += SOURCE:10 + SOURCE:22
		TIMES SOURCE:24 , 1.50
	ENDIF
	SOURCE:11 = SOURCE:2 / 5 - SOURCE:13 / 5 - SOURCE:24 / 5
	SIF SOURCE:11 > 0
		SOURCE:23 = (B:5 + 10) * (B:5 - 30) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:1 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 5
	STAIN:MASTER:4 |= STAIN:2
	STAIN:2 |= STAIN:MASTER:4
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:92 = 0
	TFLAG:130 += 100 + (B:5 + B:11)
	TFLAG:64 += 4
	TEQUIP:25 = 0
	TEQUIP:26 = 0
;────────────────────────────────────
;036,逆レイプ（Ｎ/Ｈ/Ａ）[罰]（快Ｃ、接触、情愛、性行動、痛み、逸脱、中毒充足、トラウマ、快ｖ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 36
	IF TALENT:140 && TEQUIP:MASTER:4 < 1
		TFLAG:102 = 9
		TFLAG:103 = 9
	ELSEIF TALENT:33 || (A:30 + A:1 / 10 + TALENT:MASTER:91 * 3 > TALENT:32 * 5 + TALENT:34 * 3 + RAND:3)
		TFLAG:102 = 7 + RAND:2
		TFLAG:103 = 7 + RAND:2
	ELSE
		TFLAG:102 = 5
		TFLAG:103 = 5
	ENDIF
	SOURCE:0 = 300 + 2 * S:7
	SOURCE:41 = 100 + S:47 * 2
	;潤滑不足
	IF PALAM:4 < 100 && TEQUIP:10 < 3
		TIMES SOURCE:0 , 0.80
		TIMES SOURCE:41 , 0.70
	ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
		TIMES SOURCE:0 , 0.90
		TIMES SOURCE:41 , 0.80
	ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
		TIMES SOURCE:0 , 0.95
		TIMES SOURCE:41 , 0.90
	ENDIF
	;経験不足
	IF EXP:MASTER:7 < 1 && CFLAG:MASTER:0 < 3
		TIMES SOURCE:0 , 0.80
		TIMES SOURCE:41 , 0.70
		SOURCE:13 += 150
		SOURCE:22 += 2500
		SOURCE:24 += 2000
	ELSEIF EXP:MASTER:7 < 6 && CFLAG:MASTER:0 < 5
		TIMES SOURCE:0 , 0.90
		TIMES SOURCE:41 , 0.80
		SOURCE:22 += 1800
		SOURCE:24 += 1000
	ELSEIF EXP:MASTER:7 < 13 && CFLAG:MASTER:0 < 8
		SOURCE:22 += 1000
		SOURCE:24 += 400
	ELSEIF EXP:MASTER:7 < 26 && CFLAG:MASTER:0 < 11
		SOURCE:22 += 150
		SOURCE:24 += 50
	ENDIF
	SOURCE:10 = 200 + A:30 * 10
	SOURCE:12 = SOURCE:10 / 5
	;合意なしで童貞を奪おうとすると…
	SIF CFLAG:3 == 0 && EXP:MASTER:8 > 4 && TALENT:MASTER:1
		SOURCE:24 += 3500
	;調教者が処女
	SIF TALENT:0 && CFLAG:MASTER:0 < 5 && CFLAG:2 < 1000
		SOURCE:22 += 3000 - CFLAG:2 * 2
	IF CFLAG:MASTER:8 == 36
		SOURCE:24 += SOURCE:10 + SOURCE:22
		TIMES SOURCE:24 , 1.50
	ENDIF
	SOURCE:11 = SOURCE:0 / 5 - SOURCE:22 / 5 - SOURCE:24 / 5
	SIF SOURCE:11 > 0
		SOURCE:23 = B:3 * (B:3 - 10) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:0 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 5
	STAIN:MASTER:2 |= STAIN:3
	STAIN:3 |= STAIN:MASTER:2
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:37
		TEQUIP:37 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:91 = 0
	TFLAG:64 += 4
	TEQUIP:31 = 0
;────────────────────────────────────
;040,自慰（Ｓ/Ｎ/Ｈ）[罰]（逸脱、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 40
	;拒否される可能性があるのでここは命令を出すところまで処理します
	SOURCE:22 = 1200 - B:8 * 8 - B:12 * 20 - CFLAG:MASTER:0 * 20
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	SOURCE:23 = B:12 * (B:12 + 10) * (B:2 + 10) / 1000

	TFLAG:131 += 100 + B:12 * 5
	TFLAG:64 += 3
;────────────────────────────────────
;041,秘貝開帳（Ｓ/Ｎ/Ｈ）（快Ｃ、露出、逸脱）
;────────────────────────────────────
ELSEIF TFLAG:90 == 41
	TFLAG:102 = 5
	SOURCE:0 = 30 + (TALENT:57 + 1) * A:2
	SOURCE:20 = SOURCE:0 / 3 + (TEQUIP:56 + 1) * 250
	SOURCE:22 = SOURCE:20 - B:8 * 2 - CFLAG:MASTER:0 * 5
	STAIN:MASTER:3 |= STAIN:1
	STAIN:1 |= STAIN:MASTER:3
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TFLAG:64 += 1
;────────────────────────────────────
;042,自慰みせつけ（Ｓ/Ｎ/Ｈ/Ａ）（逸脱、欲情追加、快ｃ、快ｖ、快ｂ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 42
	TFLAG:103 = 3 + RAND:3
	SIF TALENT:33
		TFLAG:103 = 7 + RAND:2
	SIF TALENT:32 || TALENT:34
		TFLAG:103 = RAND:3
	SIF TALENT:140
		TFLAG:103 = 9
	SOURCE:40 = 50 + A:1 * A:2 * A:30 * 2 / 100 + B:2 * 2 * (TALENT:MASTER:57 + 1)
	SIF TALENT:122 == 0
		SOURCE:43 = 50 + A:1 * A:2 * A:30 * 2 / 100 + B:2 * 2 * (TALENT:MASTER:57 + 1)
	;バイブ使用
	IF ITEM:0 && TALENT:0 == 0 && TALENT:122 == 0
		SOURCE:41 = 50 + A:1 * (A:30 + 1) * 5 / 10 + (TALENT:59 + 1) * A:2 * (ITEM:9 + 1) * 3
		TIMES SOURCE:40 , 0.80
	ENDIF
	A = SOURCE:40 + SOURCE:41 + SOURCE:43
	;調教レベルと欲望を見る
	IF CFLAG:MASTER:0 < 1 || ABL:MASTER:1 < 1
		TIMES A , 0.10
	ELSEIF CFLAG:MASTER:0 < 3 || ABL:MASTER:1 < 2
		TIMES A , 0.25
	ELSEIF CFLAG:MASTER:0 < 5 || ABL:MASTER:1 < 3
		TIMES A , 0.40
	ELSEIF CFLAG:MASTER:0 < 8 || ABL:MASTER:1 < 4
		TIMES A , 0.60
	ELSEIF CFLAG:MASTER:0 < 11 || ABL:MASTER:1 < 5
		TIMES A , 0.85
	ENDIF
	UP:5 += A / 5
	SOURCE:22 = A - B:8 * 2 - CFLAG:MASTER:0 * 5
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	IF TEQUIP:69 == 1
		TEQUIP:69 = 3
	ELSE
		TEQUIP:69 = 2
	ENDIF

	TFLAG:130 += 100 + A:1
	TFLAG:64 += 3
;────────────────────────────────────
;043,羞恥プレイ（Ｓ/Ｎ/Ｈ）（露出）
;────────────────────────────────────
ELSEIF TFLAG:90 == 43
	IF TFLAG:93 == 1
		TEQUIP:56 = 0
	ELSE
		SOURCE:20 = 200
		TEQUIP:56 = 1
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0

	TFLAG:64 += 2
;────────────────────────────────────
;044,野外プレイ（Ｈ/Ａ）[罰]（拘束<=首輪のこと、露出、逸脱、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 44
	IF TFLAG:93 == 1
		TEQUIP:52 = 0
	ELSE
		SOURCE:14 = 50 + MARK:3 * 15
		SOURCE:20 = 300
		SOURCE:22 = 150 + SOURCE:14 - B:8 * 3 - CFLAG:MASTER:0 * 10
		SIF SOURCE:22 < 0
			SOURCE:22 = 0
		SOURCE:24 = TALENT:MASTER:10 * 150 - TALENT:MASTER:22 * 50 + TALENT:MASTER:32 * 150 - TALENT:MASTER:33 * 50 + TALENT:MASTER:34 * 250 - TALENT:MASTER:35 * 150 + SOURCE:22
		SIF SOURCE:22 == 0
			SOURCE:24 = 0
		SIF SOURCE:24 < 0
			SOURCE:24 = 0
		SIF CFLAG:MASTER:8 == 44
			TIMES SOURCE:24 , 1.50
		TEQUIP:52 = 1
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TFLAG:64 += 5
;────────────────────────────────────
;045,放尿（Ｎ/Ｈ/Ａ）[罰]（露出、逸脱、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 45
	SOURCE:20 = 350
	SOURCE:22 = 750 - B:8 * 15 - CFLAG:MASTER:0 * 30
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	SOURCE:24 = TALENT:MASTER:10 * 150 - TALENT:MASTER:22 * 50 + TALENT:MASTER:32 * 150 - TALENT:MASTER:33 * 50 + TALENT:MASTER:34 * 250 - TALENT:MASTER:35 * 150 + SOURCE:22
	SIF SOURCE:22 == 0
		SOURCE:24 = 0
	SIF SOURCE:24 < 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 45
		TIMES SOURCE:24 , 1.50
	SIF TEQUIP:69 == 1
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 2

	TFLAG:64 += 2
;────────────────────────────────────
;046,コスプレ（Ｓ/Ｎ/Ｈ/Ａ）（未実装）
;────────────────────────────────────
;ELSEIF TFLAG:90 == 46
;────────────────────────────────────
;050,手淫/愛撫強制（Ｓ/Ｎ）（不潔）
;────────────────────────────────────
;奉仕系の行動は拒否できるので、反応が出るまで身体の接触がないようにしておきます
ELSEIF TFLAG:90 == 50
	;脱衣の処理
	SIF TEQUIP:MASTER:0 && (RAND:3 > 1 || TALENT:101)
		TFLAG:102 = 6
	IF TALENT:140
		TFLAG:103 = 9
	ELSEIF TALENT:33 || (A:30 + A:1 / 10 + TALENT:MASTER:91 * 5 > 4 + RAND:5)
		TFLAG:103 = 7 + RAND:2
	ENDIF
	IF TFLAG:93 == 1
		SOURCE:21 = 50
		SIF STAIN:2 & 4 || STAIN:2 & 8
			SOURCE:21 += 150
		SIF STAIN:2 & 32 || STAIN:2 & 64
			SOURCE:21 += 400
		SIF TFLAG:103 == 0
			TFLAG:103 = 5
	ELSE
		SOURCE:21 = 25
		SIF STAIN:3 & 4 || STAIN:3 & 8
			SOURCE:21 += 150
		SIF STAIN:3 & 32 || STAIN:3 & 64
			SOURCE:21 += 400
		SIF TFLAG:103 == 0
			TFLAG:103 = 3
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1

	TFLAG:130 += 100 + A:1
	TFLAG:64 += 2
;────────────────────────────────────
;051,フェラチオ/クンニ強制（Ｓ/Ｎ/Ｈ）（不潔）
;────────────────────────────────────
ELSEIF TFLAG:90 == 51
	;脱衣の処理
	IF TALENT:140
		TFLAG:103 = 9
	ELSEIF TALENT:32 || TALENT:34
		TFLAG:103 = 2
	ELSE
		TFLAG:103 = 5
	ENDIF
	IF TFLAG:93 == 1
		SOURCE:21 = 150
		SIF STAIN:2 & 4 || STAIN:2 & 8
			SOURCE:21 += 280
		SIF STAIN:2 & 32 || STAIN:2 & 64
			SOURCE:21 += 650
	ELSEIF TFLAG:93 == 3
		SOURCE:21 = 1000
	ELSE
		SOURCE:21 = 100
		SIF STAIN:3 & 4 || STAIN:3 & 8
			SOURCE:21 += 230
		SIF STAIN:3 & 32 || STAIN:3 & 64
			SOURCE:21 += 620
	ENDIF
	IF TFLAG:93 == 2
		TEQUIP:44 = 1
		TEQUIP:45 = 0
	ELSEIF TFLAG:93 == 3
		TEQUIP:44 = 0
		TEQUIP:45 = 1
	ELSE
		TEQUIP:44 = 0
		TEQUIP:45 = 0
	ENDIF
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1

	TFLAG:130 += 100 + A:1
	TFLAG:64 += 2
;────────────────────────────────────
;052,パイズリ（Ｓ/Ｎ/Ｈ）（不潔）
;────────────────────────────────────
ELSEIF TFLAG:90 == 52
	;脱衣の処理
	TFLAG:102 = 1 + RAND:2 * 3
	IF TALENT:140
		TFLAG:103 = 2
	ELSEIF TALENT:32 || TALENT:34
		TFLAG:103 = 5
	ELSE
		TFLAG:103 = 7
	ENDIF
	SOURCE:21 = 75
	SIF STAIN:2 & 4 || STAIN:2 & 8
		SOURCE:21 += 200
	SIF STAIN:2 & 32 || STAIN:2 & 64
		SOURCE:21 += 500
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TFLAG:130 += 100 + A:1
	TFLAG:64 += 3
;────────────────────────────────────
;053,素股（Ｓ/Ｎ/Ｈ）（不潔）
;────────────────────────────────────
ELSEIF TFLAG:90 == 53
	;脱衣の処理
	TFLAG:102 = 2 + RAND:2 * 3
	IF TALENT:140
		TFLAG:103 = 9
	ELSEIF TALENT:32 || TALENT:34
		TFLAG:103 = 2
	ELSE
		TFLAG:103 = 5
	ENDIF
	SOURCE:21 = 60
	SIF STAIN:2 & 4 || STAIN:2 & 8
		SOURCE:21 += 180
	SIF STAIN:2 & 32 || STAIN:2 & 64
		SOURCE:21 += 450
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:94 = 0
	TEQUIP:91 = 0
	TFLAG:130 += 100 + A:1
	TFLAG:64 += 3
;────────────────────────────────────
;054,足コキ（Ｎ/Ｈ）（不潔、逸脱）
;────────────────────────────────────
ELSEIF TFLAG:90 == 54
	SIF TEQUIP:MASTER:1 && (RAND:3 > 1 || TALENT:101)
		TFLAG:102 = 6
	;脱衣の処理
	IF TALENT:140
		TFLAG:103 = 9
	ELSEIF TALENT:32 || TALENT:34
		TFLAG:103 = 2
	ELSE
		TFLAG:103 = 5
	ENDIF
	SOURCE:21 = 40
	SIF STAIN:2 & 4 || STAIN:2 & 8
		SOURCE:21 += 100
	SIF STAIN:2 & 32 || STAIN:2 & 64
		SOURCE:21 += 350
	SOURCE:22 = 500 - B:7 * 10 - CFLAG:MASTER:0 * 20 - TALENT:MASTER:80 * 100
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TFLAG:130 += 100 + A:1
	TFLAG:64 += 2
;────────────────────────────────────
;055,足舐め強制（Ｎ/Ｈ/Ａ）[罰]（不潔、逸脱、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 55
	SIF TEQUIP:1
		TFLAG:103 = 6 * RAND:2
	SOURCE:21 = 180
	SIF STAIN:6 & 4 || STAIN:6 & 8
		SOURCE:21 += 360
	SIF STAIN:6 & 32 || STAIN:6 & 64
		SOURCE:21 += 800
	SOURCE:22 = (35 - B:30 * 4 / 10 - B:11 / 5 - TALENT:MASTER:17 * 5 + TALENT:MASTER:15 * 5) * (50 + CFLAG:MASTER:7 * 3 + MARK:3 * 2 + SOURCE:21 / 50) / (5 + CFLAG:MASTER:0)
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	SOURCE:24 = TALENT:MASTER:15 * 150 - TALENT:MASTER:17 * 50 + TALENT:MASTER:25 * 150 - TALENT:MASTER:24 * 50 + SOURCE:22 - CFLAG:MASTER:0 * ABL:MASTER:0 * 20
	SIF SOURCE:22 == 0
		SOURCE:24 = 0
	SIF SOURCE:24 < 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 55
		TIMES SOURCE:24 , 1.50
	TEQUIP:44 = 0
	TEQUIP:45 = 0

	TFLAG:130 += 50 + (B:11 - 20)
	TFLAG:64 += 2
;────────────────────────────────────
;056,イラマチオ（Ｈ/Ａ）[罰]（接触、情愛逆、性行動、痛み、拘束<=頭を掴む事、不潔、逸脱、トラウマ、快ｃ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 56
	TFLAG:103 = 2 + RAND:2 * 3
	SIF TALENT:140
		TFLAG:103 = 9
	SOURCE:10 = 100 + A:30 * 10
	SOURCE:12 = 50
	SOURCE:13 = 500 + SOURCE:10 / 5
	SOURCE:14 = SOURCE:10 / 2 + 50
	SOURCE:21 = 150
	SIF STAIN:2 & 4 || STAIN:2 & 8
		SOURCE:21 += 300
	SIF STAIN:2 & 32 || STAIN:2 & 64
		SOURCE:21 += 700
	SOURCE:22 = 800 - B:30 * 8 - B:7 * 8 - CFLAG:MASTER:0 * 30 - TALENT:MASTER:80 * 150 + SOURCE:13 + SOURCE:14
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	SOURCE:24 = TALENT:MASTER:10 * 250 - TALENT:MASTER:12 * 200 + TALENT:MASTER:11 * 200 - TALENT:MASTER:13 * 100 + TALENT:MASTER:15 * 200 - TALENT:MASTER:17 * 100 + TALENT:MASTER:25 * 150 - TALENT:MASTER:24 * 50 + SOURCE:22
	SIF SOURCE:22 == 0
		SOURCE:24 = 0
	SIF SOURCE:24 < 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 56
		TIMES SOURCE:24 , 1.50
	SOURCE:11 -= SOURCE:10 / 5 + SOURCE:21 / 5 + SOURCE:24 / 5
	SOURCE:40 += 300 + S:41
	STAIN:MASTER:0 |= STAIN:2
	STAIN:2 |= STAIN:MASTER:0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TFLAG:130 += 50 + (B:11 - 20)
	TFLAG:64 += 3
;────────────────────────────────────
;057,胸愛撫強制（Ｓ/Ｎ/Ｈ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 57
	TFLAG:103 = 4
	SOURCE:10 = 50 + A:30 * 5 + CFLAG:MASTER:0 * 5
	SOURCE:11 = 50 + CFLAG:2 / 50 + A:7 * 5
	SIF STAIN:5 & 1 || STAIN:5 & 2 || STAIN:5 & 16
		SOURCE:21 += 85
	SIF STAIN:5 & 4 || STAIN:5 & 8
		SOURCE:21 += 180
	SIF STAIN:5 & 32 || STAIN:5 & 64
		SOURCE:21 += 550

	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TFLAG:130 += 80 + (A:2 + A:21 + TALENT:57 * 50) / 3
	TFLAG:64 += 2
;────────────────────────────────────
;060,スパンキング（Ｓ/Ｎ/Ｈ/Ａ）[罰]（接触、痛み、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 60
	SOURCE:13 = 50 * (TALENT:83 + 1) + A:2 * (A:26 + 10) * 15 / 100 + (A:2 / 10 + A:30 + 1) * (TALENT:83 * 3 + 1) * (10 + TALENT:83)
	SOURCE:10 = 200 + SOURCE:13 / 10
	SOURCE:23 = B:11 * (B:15 - 10) * (B:15 + 10) * SOURCE:13 / 100000
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1

	TFLAG:130 += 50 + (B:11 - 20) * 2
	TFLAG:64 += 2
;────────────────────────────────────
;061,鞭（Ｎ/Ｈ/Ａ）[罰]（痛み、中毒充足、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 61
	SOURCE:13 = 250 * (TALENT:83 + 1) + A:2 * (A:26 + 20) / 10 + (A:2 + A:2 + A:30 * 10) * (TALENT:83 * 2 + 1) * (10 + TALENT:83) / 10
	SIF TALENT:87
		TIMES SOURCE:13 , 0.90
	SOURCE:23 = B:11 * (B:15 - 10) * (B:15 + 10) * SOURCE:13 / 100000
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:24 = SOURCE:13 / 2 - SOURCE:23
	SIF SOURCE:24 < 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 61
		TIMES SOURCE:24 , 1.50
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1

	TFLAG:130 += 50 + (B:11 - 20) * 3
	TFLAG:64 += 2
;────────────────────────────────────
;062,針（Ｈ/Ａ）[罰]（痛み、逸脱、中毒充足、トラウマ、出血）
;────────────────────────────────────
ELSEIF TFLAG:90 == 62
	SOURCE:13 = 450 * (TALENT:83 + 1) + A:2 * (A:26 + 1) * (2 + TALENT:53 + TALENT:53 + TALENT:53) / 100 + (A:2 + A:2 + A:30 * 10) * (TALENT:83 + TALENT:53 * 2 + 1) * (10 + TALENT:83) / 10
	SIF TALENT:87
		TIMES SOURCE:13 , 0.80
	SOURCE:22 = 200 + SOURCE:13 / 2 - CFLAG:MASTER:0 * 30 - B:11 * 6 - B:15 * 20
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	SOURCE:23 = B:11 * (B:15 - 10) * (B:15 + 10) * SOURCE:13 / 100000
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:24 = SOURCE:13 / 2 - SOURCE:23
	SIF SOURCE:24 < 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 62
		TIMES SOURCE:24 , 1.50
	;出血
	IF TALENT:MASTER:122 || TALENT:MASTER:121
		STAIN:MASTER:2 |= 32
	ELSE
		STAIN:MASTER:3 |= 32
	ENDIF
	STAIN:MASTER:5 |= 32
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TFLAG:130 += 50 + (B:11 - 20) * 4
	TFLAG:64 += 2
;────────────────────────────────────
;063,縄（Ｓ/Ｎ/Ｈ/Ａ）[罰]（拘束、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 63
;本当はいろんな縛り方を作りたいが面倒すぎます…暫定的に縛りの強度だけをTEQUIP:40に記入します
	IF TEQUIP:40 == 0
		SOURCE:14 = 200 + (MARK:3 + A:30) * (A:2 + 10) * 5 * (TALENT:58 * 3 + 2) / 10
		SOURCE:23 = B:11 * (B:16 - 10) * (B:16 + 10) * SOURCE:14 / 100000
		TEQUIP:40 = 1 + SOURCE:14 / 500
	ELSE
		TEQUIP:40 = 0
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TFLAG:130 += 50 + (B:11 - 20)
	TFLAG:64 += 2
;────────────────────────────────────
;064,アイマスク（Ｓ/Ｎ/Ｈ/Ａ）[罰]（拘束、中毒充足、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 64
	IF TEQUIP:41 == 0
		SOURCE:14 = 100 + (A:26 + A:30 * 10) * (A:2 + 10) * 5 * (TALENT:58 + 2) / 10
		SOURCE:23 = B:11 * (B:16 - 10) * (B:16 + 10) * SOURCE:14 / 100000
		SIF SOURCE:23 < 0
			SOURCE:23 = 0
		SOURCE:24 = SOURCE:14 / 2 - SOURCE:23
		SIF SOURCE:24 < 0
			SOURCE:24 = 0
		SIF CFLAG:MASTER:8 == 64
			TIMES SOURCE:24 , 1.50
		TEQUIP:41 = 1
	ELSE
		TEQUIP:41 = 0
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1
	TFLAG:130 += 50 + (B:11 - 20)
	TFLAG:64 += 1
;────────────────────────────────────
;065,ボールギャグ（Ｎ/Ｈ/Ａ）[罰]（拘束、中毒充足、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 65
	IF TEQUIP:42 == 0
		SOURCE:14 = 350 + (A:26 + A:30 * 10) * (TALENT:58 + 1)
		SOURCE:23 = B:11 * (B:16 - 10) * (B:16 + 10) * SOURCE:14 / 100000
		SIF SOURCE:23 < 0
			SOURCE:23 = 0
		SOURCE:24 = SOURCE:14 / 2 - SOURCE:23
		SIF SOURCE:24 < 0
			SOURCE:24 = 0
		SIF CFLAG:MASTER:8 == 65
			TIMES SOURCE:24 , 1.50
		TEQUIP:42 = 1
	ELSE
		TEQUIP:42 = 0
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1
	TFLAG:130 += 50 + (B:11 - 2)
	TFLAG:64 += 1
;────────────────────────────────────
;066,罵倒（Ｎ/Ｈ/Ａ）[罰]（逸脱、中毒充足）
;────────────────────────────────────
ELSEIF TFLAG:90 == 66
	SOURCE:22 = 200 + (A:20 + 10) * (A:26 + 10) * CFLAG:10 * 5 / 100
	SOURCE:23 = B:11 * (B:16 - 10) * (B:16 + 10) * SOURCE:22 / 100000
	SOURCE:22 -= SOURCE:23
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	TFLAG:64 += 1
	TFLAG:100 |= 64
;────────────────────────────────────
;067,三角木馬（Ｈ/Ａ）[罰]（快Ｖ、情愛逆、痛み、拘束、逸脱、中毒充足、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 67
	IF TEQUIP:43 == 0
		SOURCE:1 = (B:11 - 30) * B:15 * (TALENT:MASTER:77 + 2) / 2
		SIF SOURCE:1 < 0
			SOURCE:1 = 0
		SOURCE:13 = 1000 + A:26 * A:2 * 50 / 100 + A:30 * (TALENT:83 * 3 + 2) * 20
		SOURCE:14 = 50 + (MARK:3 + 1) * (A:2 + 10)
		SOURCE:22 = 1800 + SOURCE:13 / 2 - CFLAG:MASTER:0 * 50 - B:11 * 20 - B:15 * 60
		SIF SOURCE:22 < 0
			SOURCE:22 = 0
		SOURCE:23 = B:11 * (B:15 - 30) * (B:15 + 10) * SOURCE:13 / 80000
		SIF SOURCE:23 < 0
			SOURCE:23 = 0
		SOURCE:24 = SOURCE:13 / 2 + SOURCE:14 / 5 - SOURCE:23
		SIF SOURCE:24 < 0
			SOURCE:24 = 0
		SIF CFLAG:MASTER:8 == 67
			TIMES SOURCE:24 , 1.50
		SOURCE:11 += SOURCE:23 - SOURCE:13 - SOURCE:22 - SOURCE:24
		SIF SOURCE:11 > 0
			SOURCE:11 = 0
		TEQUIP:43 = 1
	ELSE
		TEQUIP:43 = 0
	ENDIF
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:92 = 0
	TEQUIP:94 = 0
	TFLAG:130 += 50 + (B:11 - 30) * 4
	TFLAG:64 += 3
;────────────────────────────────────
;068,浣腸器＋プラグ（Ｎ/Ｈ/Ａ）[罰]（快Ａ、痛み、拘束<=プラグのこと、逸脱、中毒充足、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 68
	IF TFLAG:93 == 0
		TEQUIP:27 = 1 + RAND:2
		SOURCE:2 = 20 * (CFLAG:MASTER:0 + 1) + (B:2 - 30) * B:11 * (TALENT:59 + TALENT:MASTER:77 + TEQUIP:27) * 30 / 100
		SIF SOURCE:2 < 0
			SOURCE:2 = 0
		;ここの痛みは腹痛の処理です。経験不足のほうはアナル未開発による痛みです。
		SOURCE:13 = 200 + TEQUIP:27 * 100
		SOURCE:14 = 50 + TEQUIP:27 * 100
		SOURCE:22 = SOURCE:2 / 2 + SOURCE:13 / 2 - CFLAG:MASTER:0 * 20 - B:11 * 10
		SIF SOURCE:22 < 0
			SOURCE:22 = 0
		SOURCE:23 = (B:11 - 30) * B:5 * 25 * SOURCE:2 / 10000
		SIF SOURCE:23 < 0
			SOURCE:23 = 0
		;経験不足
		IF EXP:MASTER:2 < 1
			TIMES SOURCE:2 , 0.80
			SOURCE:13 += 100
			SOURCE:22 += 2000
		ELSEIF EXP:MASTER:2 < 6
			TIMES SOURCE:2 , 0.90
			SOURCE:22 += 1500
		ELSEIF EXP:MASTER:2 < 11
			SOURCE:22 += 1000
		ELSEIF EXP:MASTER:2 < 21
			SOURCE:22 += 500
		ENDIF
	ELSEIF TFLAG:93 == 2
		TEQUIP:27 += 1 + RAND:3
		SOURCE:2 = 20 * (CFLAG:MASTER:0 + 1) + (B:2 - 30) * B:11 * (TALENT:59 + TALENT:MASTER:77 + TEQUIP:27) * 30 / 100
		SIF SOURCE:2 < 0
			SOURCE:2 = 0
		SOURCE:13 = 200 + TEQUIP:27 * 100
		SOURCE:14 = 50 + TEQUIP:27 * 100
		SOURCE:22 = SOURCE:2 / 2 + SOURCE:13 / 2 - CFLAG:MASTER:0 * 20 - B:11 * 10
		SIF SOURCE:22 < 0
			SOURCE:22 = 0
		SOURCE:23 = (B:11 - 30) * B:5 * 25 * SOURCE:2 / 10000
		SIF SOURCE:23 < 0
			SOURCE:23 = 0
		;導管はすでに入れてますから追加の注入は経験不足の処理を行いません
	ELSE
		SOURCE:2 = 500 * TEQUIP:27 * (100 + RAND:11 - RAND:11) / 100
		SOURCE:22 = SOURCE:2 - CFLAG:MASTER:0 * 80 - B:11 * 40 - TALENT:MASTER:80 * 500 - TALENT:MASTER:77 * 1000
		SOURCE:23 = (CFLAG:MASTER:0 - 5) * (B:5 + 5) * 2 * SOURCE:2 / 1000
		SIF SOURCE:23 < 0
			SOURCE:23 = 0
		TEQUIP:27 = 0
	ENDIF
	SOURCE:24 = SOURCE:13 / 3 + SOURCE:14 / 3 + SOURCE:22 / 3
	SIF SOURCE:23 > 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 68
		TIMES SOURCE:24 , 1.50
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:92 = 0
	TFLAG:64 += 3
;────────────────────────────────────
;070,フィストファック（Ｈ/Ａ）[罰]（快Ｖ、接触、痛み、逸脱、中毒充足、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 70
	TFLAG:102 = 5
	CALL LOTION_USE
	SOURCE:1 = 200 + S:2 * (ABL:MASTER:11 + 5) / 5
	SOURCE:10 = 300
	;経験を見る
	IF EXP:MASTER:1 < 20 || EXP:MASTER:52 < 1
		TIMES SOURCE:1 , 0.10
		SOURCE:13 = 3000 + TALENT:MASTER:110 * 500 - TALENT:MASTER:111 * 500 - TALENT:110 * 250 + TALENT:111 * 250
		SOURCE:22 = 2500
	ELSEIF EXP:MASTER:1 < 25 || EXP:MASTER:52 < 2
		TIMES SOURCE:1 , 0.25
		SOURCE:13 = 2500 + TALENT:MASTER:110 * 400 - TALENT:MASTER:111 * 400 - TALENT:110 * 200 + TALENT:111 * 200
		SOURCE:22 = 2000
	ELSEIF EXP:MASTER:1 < 30 || EXP:MASTER:52 < 4
		TIMES SOURCE:1 , 0.45
		SOURCE:13 = 1800 + TALENT:MASTER:110 * 300 - TALENT:MASTER:111 * 300 - TALENT:110 * 150 + TALENT:111 * 150
		SOURCE:22 = 1200
	ELSEIF EXP:MASTER:52 < 6
		TIMES SOURCE:1 , 0.70
		SOURCE:13 = 1000 + TALENT:MASTER:110 * 200 - TALENT:MASTER:111 * 200 - TALENT:110 * 100 + TALENT:111 * 100
		SOURCE:22 = 500
	ELSE
		SOURCE:13 = 500 + TALENT:MASTER:110 * 100 - TALENT:MASTER:111 * 100 - TALENT:110 * 50 + TALENT:111 * 50
		SOURCE:22 = 150
	ENDIF
	;潤滑不足
	IF PALAM:4 < 500 && TEQUIP:10 < 3
		TIMES SOURCE:1 , 0.40
		SOURCE:13 += 1500
	ELSEIF PALAM:4 < 1200 && TEQUIP:10 < 2
		TIMES SOURCE:1 , 0.60
		SOURCE:13 += 600
	ELSEIF PALAM:4 < 2000 && TEQUIP:10 < 1
		TIMES SOURCE:1 , 0.80
		SOURCE:13 += 300
	ENDIF
	SOURCE:23 = SOURCE:1 - SOURCE:22
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:24 = SOURCE:13 + SOURCE:22
	SIF SOURCE:23 > 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 70
		TIMES SOURCE:24 , 1.50
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:94 = 0
	TFLAG:130 += 50 + (B:11 - 20) * 4
	TFLAG:64 += 4
;────────────────────────────────────
;071,アナルフィスト（Ｈ/Ａ）[罰]（快Ａ、接触、痛み、逸脱、中毒充足、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 71
	TFLAG:102 = 5
	CALL LOTION_USE
	SOURCE:2 = 200 + S:3 * (ABL:MASTER:11 + 5) / 5
	SOURCE:10 = 300
	;経験を見る
	IF EXP:MASTER:2 < 20 || EXP:MASTER:53 < 1
		TIMES SOURCE:2 , 0.10
		SOURCE:13 = 4000 + TALENT:MASTER:110 * 700 - TALENT:MASTER:111 * 700 - TALENT:110 * 500 + TALENT:111 * 500
		SOURCE:22 = 5000
	ELSEIF EXP:MASTER:2 < 25 || EXP:MASTER:53 < 2
		TIMES SOURCE:2 , 0.20
		SOURCE:13 = 3000 + TALENT:MASTER:110 * 600 - TALENT:MASTER:111 * 600 - TALENT:110 * 420 + TALENT:111 * 420
		SOURCE:22 = 4000
	ELSEIF EXP:MASTER:2 < 30 || EXP:MASTER:53 < 4
		TIMES SOURCE:2 , 0.35
		SOURCE:13 = 2000 + TALENT:MASTER:110 * 500 - TALENT:MASTER:111 * 500 - TALENT:110 * 350 + TALENT:111 * 350
		SOURCE:22 = 3000
	ELSEIF EXP:MASTER:53 < 6
		TIMES SOURCE:2 , 0.60
		SOURCE:13 = 1000 + TALENT:MASTER:110 * 380 - TALENT:MASTER:111 * 380 - TALENT:110 * 280 + TALENT:111 * 280
		SOURCE:22 = 2000
	ELSE
		SOURCE:13 = 500 + TALENT:MASTER:110 * 250 - TALENT:MASTER:111 * 250 - TALENT:110 * 200 + TALENT:111 * 200
		SOURCE:22 = 1000
	ENDIF
	;潤滑不足
	IF PALAM:4 < 500 && TEQUIP:10 < 3
		TIMES SOURCE:2 , 0.40
		SOURCE:13 += 1500
	ELSEIF PALAM:4 < 1200 && TEQUIP:10 < 2
		TIMES SOURCE:2 , 0.60
		SOURCE:13 += 600
	ELSEIF PALAM:4 < 2000 && TEQUIP:10 < 1
		TIMES SOURCE:2 , 0.80
		SOURCE:13 += 300
	ENDIF
	SOURCE:23 = SOURCE:1 - SOURCE:22
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:24 = SOURCE:13 + SOURCE:22
	SIF SOURCE:23 > 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 70
		TIMES SOURCE:24 , 1.50
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:92 = 0
	TFLAG:130 += 50 + (B:11 - 20) * 4
	TFLAG:64 += 4
;────────────────────────────────────
;072,両穴フィスト（Ａ）[罰]（快Ｖ、快Ａ、接触、痛み、逸脱、中毒充足、トラウマ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 72
	TFLAG:102 = 5
	CALL LOTION_USE
	SOURCE:1 = 200 + S:2 * (ABL:MASTER:11 + 5) / 5
	SOURCE:2 = 200 + S:3 * (ABL:MASTER:11 + 5) / 5
	SOURCE:10 = 500
	;経験を見る
	IF EXP:MASTER:1 < 20 || EXP:MASTER:52 < 1
		TIMES SOURCE:1 , 0.10
		SOURCE:13 = 3000 + TALENT:MASTER:110 * 500 - TALENT:MASTER:111 * 500 - TALENT:110 * 250 + TALENT:111 * 250
		SOURCE:22 = 2500
	ELSEIF EXP:MASTER:1 < 25 || EXP:MASTER:52 < 2
		TIMES SOURCE:1 , 0.25
		SOURCE:13 = 2500 + TALENT:MASTER:110 * 400 - TALENT:MASTER:111 * 400 - TALENT:110 * 200 + TALENT:111 * 200
		SOURCE:22 = 2000
	ELSEIF EXP:MASTER:1 < 30 || EXP:MASTER:52 < 4
		TIMES SOURCE:1 , 0.45
		SOURCE:13 = 1800 + TALENT:MASTER:110 * 300 - TALENT:MASTER:111 * 300 - TALENT:110 * 150 + TALENT:111 * 150
		SOURCE:22 = 1200
	ELSEIF EXP:MASTER:52 < 6
		TIMES SOURCE:1 , 0.70
		SOURCE:13 = 1000 + TALENT:MASTER:110 * 200 - TALENT:MASTER:111 * 200 - TALENT:110 * 100 + TALENT:111 * 100
		SOURCE:22 = 500
	ELSE
		SOURCE:13 = 500 + TALENT:MASTER:110 * 100 - TALENT:MASTER:111 * 100 - TALENT:110 * 50 + TALENT:111 * 50
		SOURCE:22 = 150
	ENDIF
	IF EXP:MASTER:2 < 20 || EXP:MASTER:53 < 1
		TIMES SOURCE:2 , 0.10
		SOURCE:13 += 4000 + TALENT:MASTER:110 * 700 - TALENT:MASTER:111 * 700 - TALENT:110 * 500 + TALENT:111 * 500
		SOURCE:22 += 5000
	ELSEIF EXP:MASTER:2 < 25 || EXP:MASTER:53 < 2
		TIMES SOURCE:2 , 0.20
		SOURCE:13 += 3000 + TALENT:MASTER:110 * 600 - TALENT:MASTER:111 * 600 - TALENT:110 * 420 + TALENT:111 * 420
		SOURCE:22 += 4000
	ELSEIF EXP:MASTER:2 < 30 || EXP:MASTER:53 < 4
		TIMES SOURCE:2 , 0.35
		SOURCE:13 += 2000 + TALENT:MASTER:110 * 500 - TALENT:MASTER:111 * 500 - TALENT:110 * 350 + TALENT:111 * 350
		SOURCE:22 += 3000
	ELSEIF EXP:MASTER:53 < 6
		TIMES SOURCE:2 , 0.60
		SOURCE:13 += 1000 + TALENT:MASTER:110 * 380 - TALENT:MASTER:111 * 380 - TALENT:110 * 280 + TALENT:111 * 280
		SOURCE:22 += 2000
	ELSE
		SOURCE:13 += 500 + TALENT:MASTER:110 * 250 - TALENT:MASTER:111 * 250 - TALENT:110 * 200 + TALENT:111 * 200
		SOURCE:22 += 1000
	ENDIF
	;潤滑不足
	IF PALAM:4 < 500 && TEQUIP:10 < 3
		TIMES SOURCE:1 , 0.40
		TIMES SOURCE:2 , 0.40
		SOURCE:13 += 3000
	ELSEIF PALAM:4 < 1200 && TEQUIP:10 < 2
		TIMES SOURCE:1 , 0.60
		TIMES SOURCE:2 , 0.60
		SOURCE:13 += 1200
	ELSEIF PALAM:4 < 2000 && TEQUIP:10 < 1
		TIMES SOURCE:1 , 0.80
		TIMES SOURCE:2 , 0.80
		SOURCE:13 += 600
	ENDIF
	SOURCE:23 = SOURCE:1 + SOURCE:2 - SOURCE:22
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:24 = SOURCE:13 + SOURCE:22
	SIF SOURCE:23 > 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 70 || CFLAG:MASTER:8 == 71 || CFLAG:MASTER:8 == 72
		TIMES SOURCE:24 , 1.50
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:92 = 0
	TEQUIP:94 = 0
	TFLAG:130 += 50 + (B:11 - 30) * 6
	TFLAG:64 += 4
;────────────────────────────────────
;073,放置プレイ（Ｈ/Ａ）[罰]（露出、逸脱）
;────────────────────────────────────
ELSEIF TFLAG:90 == 73
	CALL MIYAKU_USE
	SOURCE:20 = 100 + (A:27 + 50) * (A:24 + 10) * A:30 / 100
	SOURCE:22 = 500 + SOURCE:20 - CFLAG:MASTER:0 * 80 - B:8 * 16
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TFLAG:64 += 2
;────────────────────────────────────
;074,公衆肉便器プレイ（Ａ）[罰]（情愛逆、拘束、露出、逸脱、トラウマ）
;────────────────────────────────────
;許しを乞うことでキャンセルできますので、ここは調教者が帰った後の処理を行いません
ELSEIF TFLAG:90 == 74
	TFLAG:102 = 7 + RAND:2
	CALL MIYAKU_USE
	SOURCE:11 -= 1500 - TALENT:MASTER:76 * 750 - TALENT:MASTER:79 * 250 + TALENT:MASTER:78 * 500 - CFLAG:MASTER:0 * 100
	SIF SOURCE:11 > 0
		SOURCE:11 = 0
	SOURCE:14 = 150 + (MARK:3 + 1) * (A:2 + 10) * (TALENT:58 + 1)
	SOURCE:20 = 500 + (A:27 + 50) * (A:24 + 10) * SOURCE:14 / 10000
	SOURCE:22 = 5000 + SOURCE:20 - CFLAG:MASTER:0 * 100 - B:8 * 30 - B:1 * 20 - TALENT:MASTER:76 * 2000
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	SOURCE:24 = SOURCE:22
	IF CFLAG:MASTER:8 == 74
		SOURCE:24 += 1000 + MARK:4 * 100
		TIMES SOURCE:24 , 1.50
	ENDIF
	SIF TEQUIP:20
		TEQUIP:20 = 0
	SIF TEQUIP:25
		TEQUIP:25 = 0
	SIF TEQUIP:26
		TEQUIP:26 = 0
	SIF TEQUIP:27
		TEQUIP:27 = 0
	SIF TEQUIP:30
		TEQUIP:30 = 0
	SIF TEQUIP:31
		TEQUIP:31 = 0
	SIF TEQUIP:35
		TEQUIP:35 = 0
	SIF TEQUIP:36
		TEQUIP:36 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0
	TEQUIP:91 = 0
	TEQUIP:92 = 0
	TEQUIP:93 = 0
	TEQUIP:94 = 0
	TFLAG:64 += 60
;────────────────────────────────────
;080,触手召喚（Ｈ/Ａ）[罰]（未実装）
;────────────────────────────────────
;ELSEIF TFLAG:90 == 80
;────────────────────────────────────
;090,休ませる（Ｒ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 90
	TEQUIP:40 = 0
	TEQUIP:41 = 0
	TEQUIP:42 = 0
	TEQUIP:43 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TEQUIP:46 = 0
	TEQUIP:47 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:91 = 0
	TEQUIP:92 = 0
	TEQUIP:93 = 0
	TEQUIP:94 = 0
	TFLAG:221 = 0
	TFLAG:115 = 0
	CALL STAIN_RESET
	TFLAG:64 += 7
;────────────────────────────────────
;091,栄養剤を飲ませる（Ｒ）（逸脱）
;────────────────────────────────────
ELSEIF TFLAG:90 == 91
	CALL MIYAKU_USE
	TEQUIP:42 = 0
	TEQUIP:43 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TEQUIP:46 = 0
	TEQUIP:47 = 0
	SOURCE:22 = 200 - CFLAG:MASTER:0 * 10 - B:30 * 2
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:91 = 0
	TEQUIP:92 = 0
	TEQUIP:93 = 0
	TEQUIP:94 = 0
	TFLAG:221 = 0
	TFLAG:115 = 0
	TFLAG:64 += 2
;────────────────────────────────────
;092,治療する（Ｒ）（接触）
;────────────────────────────────────
ELSEIF TFLAG:90 == 92
	CALL MIYAKU_USE
	TEQUIP:43 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	TEQUIP:46 = 0
	TEQUIP:47 = 0
	SOURCE:10 = 100 + A:30 * 10
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:91 = 0
	TEQUIP:92 = 0
	TEQUIP:93 = 0
	TEQUIP:94 = 0
	TFLAG:221 = 0
	TFLAG:115 = 0
	TFLAG:64 += 5
;────────────────────────────────────
;095,正常位させる（Ｎ/Ｈ/Ａ）[接触]（快Ｃ、接触、情愛、性行動、中毒充足、達成、悦楽、快ｖ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 95
	SOURCE:0 = 100 + S:7 * 3 / 2
	SIF TALENT:MASTER:121 == 0 && TALENT:MASTER:122 == 0 && TEQUIP:71
		SOURCE:0 = 0
	SOURCE:41 = 300 + S:47 * 3 / 2
	SOURCE:31 = SOURCE:41 / 2
	SOURCE:32 = SOURCE:0 / 2
	;経験不足
	IF EXP:MASTER:7 < 1 && CFLAG:MASTER:0 < 3
		TIMES SOURCE:0 , 0.80
		TIMES SOURCE:41 , 0.70
		SOURCE:13 += 150
		SOURCE:22 += 200
		SOURCE:24 += 200
	ELSEIF EXP:MASTER:7 < 6 && CFLAG:MASTER:0 < 5
		TIMES SOURCE:0 , 0.90
		TIMES SOURCE:41 , 0.80
		SOURCE:22 += 180
		SOURCE:24 += 100
	ELSEIF EXP:MASTER:7 < 13 && CFLAG:MASTER:0 < 8
		SOURCE:22 += 100
		SOURCE:24 += 40
	ELSEIF EXP:MASTER:7 < 26 && CFLAG:MASTER:0 < 11
		SOURCE:22 += 150
		SOURCE:24 += 50
	ENDIF
	SOURCE:10 = 200 + A:30 * 10
	SOURCE:12 = SOURCE:10 / 5
	;調教者が処女
	SIF TALENT:0 && CFLAG:MASTER:0 < 5 && CFLAG:2 < 1000
		SOURCE:22 += 3000 - CFLAG:2 * 2
	SOURCE:11 = SOURCE:0 / 5 - SOURCE:22 / 5 - SOURCE:24 / 5
	SIF SOURCE:11 > 0
		SOURCE:23 =(S:7 - 200) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:0 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 5
	STAIN:MASTER:2 |= STAIN:3
	STAIN:2 |= STAIN:MASTER:2
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:37
		TEQUIP:37 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0
	TEQUIP:91 = 0
	TFLAG:64 += 4
;────────────────────────────────────
;096,後背位させる（Ｎ/Ｈ/Ａ）[接触]（快Ｃ、接触、情愛、性行動、中毒充足、達成、悦楽、快ｖ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 96
	SOURCE:0 = 300 + S:7
	SIF TALENT:MASTER:121 == 0 && TALENT:MASTER:122 == 0 && TEQUIP:71
		SOURCE:0 = 0
	SOURCE:41 = 300 + S:47 * 3 / 2
	SOURCE:31 = SOURCE:41 / 2
	SOURCE:32 = SOURCE:0 / 2
	;経験不足
	IF EXP:MASTER:7 < 1 && CFLAG:MASTER:0 < 3
		TIMES SOURCE:0 , 0.80
		TIMES SOURCE:41 , 0.70
		SOURCE:13 += 150
		SOURCE:22 += 500
		SOURCE:24 += 200
	ELSEIF EXP:MASTER:7 < 6 && CFLAG:MASTER:0 < 5
		TIMES SOURCE:0 , 0.90
		TIMES SOURCE:41 , 0.80
		SOURCE:22 += 180
		SOURCE:24 += 100
	ELSEIF EXP:MASTER:7 < 13 && CFLAG:MASTER:0 < 8
		SOURCE:22 += 100
		SOURCE:24 += 40
	ELSEIF EXP:MASTER:7 < 26 && CFLAG:MASTER:0 < 11
		SOURCE:22 += 15
		SOURCE:24 += 5
	ENDIF
	SOURCE:10 = 200 + A:30 * 10
	SOURCE:12 = SOURCE:10 / 5
	;調教者が処女
	SIF TALENT:0 && CFLAG:MASTER:0 < 5 && CFLAG:2 < 1000
		SOURCE:22 += 3000 - CFLAG:2 * 2
	SOURCE:11 = SOURCE:0 / 5 - SOURCE:22 / 5 - SOURCE:24 / 5
	SIF SOURCE:11 > 0
		SOURCE:23 = (S:7 - 200) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:0 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 5
	STAIN:MASTER:2 |= STAIN:3
	STAIN:2 |= STAIN:MASTER:2
	SIF TEQUIP:37
		TEQUIP:37 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:91 = 0
	TFLAG:64 += 4
;────────────────────────────────────
;097,対面座位させる（Ｎ/Ｈ/Ａ）[接触]（快Ｃ、接触、情愛、性行動、中毒充足、達成、悦楽、快ｖ、快ｃ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 97
	SOURCE:0 = 300 + S:7
	SIF TALENT:MASTER:121 == 0 && TALENT:MASTER:122 == 0 && TEQUIP:71
		SOURCE:0 = 0
	SOURCE:40 = 50 + S:44 / 2
	SOURCE:41 = 300 + S:47
	SOURCE:31 = SOURCE:41 / 2
	SOURCE:32 = SOURCE:0 / 2
	;経験不足
	IF EXP:MASTER:7 < 1 && CFLAG:MASTER:0 < 3
		TIMES SOURCE:0 , 0.80
		TIMES SOURCE:41 , 0.70
		SOURCE:13 += 150
		SOURCE:22 += 500
		SOURCE:24 += 200
	ELSEIF EXP:MASTER:7 < 6 && CFLAG:MASTER:0 < 5
		TIMES SOURCE:0 , 0.90
		TIMES SOURCE:41 , 0.80
		SOURCE:22 += 180
		SOURCE:24 += 100
	ELSEIF EXP:MASTER:7 < 13 && CFLAG:MASTER:0 < 8
		SOURCE:22 += 100
		SOURCE:24 += 40
	ELSEIF EXP:MASTER:7 < 26 && CFLAG:MASTER:0 < 11
		SOURCE:22 += 15
		SOURCE:24 += 5
	ENDIF
	SOURCE:10 = 200 + A:30 * 10
	SOURCE:12 = SOURCE:10 / 5
	;調教者が処女
	SIF TALENT:0 && CFLAG:MASTER:0 < 5 && CFLAG:2 < 1000
		SOURCE:22 += 3000 - CFLAG:2 * 2
	SOURCE:11 = SOURCE:0 / 5 - SOURCE:22 / 5 - SOURCE:24 / 5
	SIF SOURCE:11 > 0
		SOURCE:23 = (S:7 - 200) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:0 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 5
	STAIN:MASTER:2 |= STAIN:3
	STAIN:2 |= STAIN:MASTER:2
	SIF TEQUIP:37
		TEQUIP:37 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0
	TEQUIP:91 = 0

	TFLAG:64 += 4
;────────────────────────────────────
;098,背面座位させる（Ｎ/Ｈ/Ａ）[接触]（快Ｃ、接触、情愛、性行動、中毒充足、達成、悦楽、快ｖ、快ｃ、快ｂ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 98
	SOURCE:0 = 100 + S:7
	SIF TALENT:MASTER:121 == 0 && TALENT:MASTER:122 == 0 && TEQUIP:71
		SOURCE:0 = 0
	SOURCE:40 = 50 + S:41
	SOURCE:41 = 300 + S:47
	SOURCE:43 = 50 + S:44
	SOURCE:31 = SOURCE:41 / 2
	SOURCE:32 = SOURCE:0 / 2
	;経験不足
	IF EXP:MASTER:7 < 1 && CFLAG:MASTER:0 < 3
		TIMES SOURCE:0 , 0.80
		TIMES SOURCE:41 , 0.70
		SOURCE:13 += 150
		SOURCE:22 += 500
		SOURCE:24 += 200
	ELSEIF EXP:MASTER:7 < 6 && CFLAG:MASTER:0 < 5
		TIMES SOURCE:0 , 0.90
		TIMES SOURCE:41 , 0.80
		SOURCE:22 += 180
		SOURCE:24 += 100
	ELSEIF EXP:MASTER:7 < 13 && CFLAG:MASTER:0 < 8
		SOURCE:22 += 100
		SOURCE:24 += 40
	ELSEIF EXP:MASTER:7 < 26 && CFLAG:MASTER:0 < 11
		SOURCE:22 += 15
		SOURCE:24 += 5
	ENDIF
	SOURCE:10 = 200 + A:30 * 10
	SOURCE:12 = SOURCE:10 / 5
	;調教者が処女
	SIF TALENT:0 && CFLAG:MASTER:0 < 5 && CFLAG:2 < 1000
		SOURCE:22 += 3000 - CFLAG:2 * 2
	SOURCE:11 = SOURCE:0 / 5 - SOURCE:22 / 5 - SOURCE:24 / 5
	SIF SOURCE:11 > 0
		SOURCE:23 = (S:7 - 200) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:0 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 5
	STAIN:MASTER:2 |= STAIN:3
	STAIN:2 |= STAIN:MASTER:2
	SIF TEQUIP:37
		TEQUIP:37 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:91 = 0
	TFLAG:64 += 4
;────────────────────────────────────
;099,アナルセックスさせる（Ｎ/Ｈ/Ａ）[接触]（快Ｃ、接触、情愛、性行動、中毒充足、達成、悦楽、快ｖ、快ｃ、快ｂ）
;────────────────────────────────────
ELSEIF TFLAG:90 == 99
	SOURCE:0 = 500 + S:8 * 2
	SIF TALENT:MASTER:121 == 0 && TALENT:MASTER:122 == 0 && TEQUIP:71
		SOURCE:0 = 0
	SOURCE:42 = 300 + S:48 * 2
	SOURCE:31 = SOURCE:42 / 2
	SOURCE:32 = SOURCE:0 / 2
	SOURCE:22 = RAND:500
	;経験不足
	IF EXP:MASTER:7 < 1 && CFLAG:MASTER:0 < 3
		TIMES SOURCE:0 , 0.80
		TIMES SOURCE:42 , 0.70
		SOURCE:13 += 150
		SOURCE:22 += 500
		SOURCE:24 += 200
	ELSEIF EXP:MASTER:7 < 6 && CFLAG:MASTER:0 < 5
		TIMES SOURCE:0 , 0.90
		TIMES SOURCE:42 , 0.80
		SOURCE:22 += 180
		SOURCE:24 += 100
	ELSEIF EXP:MASTER:7 < 13 && CFLAG:MASTER:0 < 8
		SOURCE:22 += 100
		SOURCE:24 += 40
	ELSEIF EXP:MASTER:7 < 26 && CFLAG:MASTER:0 < 11
		SOURCE:22 += 15
		SOURCE:24 += 5
	ENDIF
	SOURCE:10 = 200 + A:30 * 10
	SOURCE:12 = SOURCE:10 / 5
	SOURCE:11 = SOURCE:0 / 5 - SOURCE:22 / 5 - SOURCE:24 / 5
	SIF SOURCE:11 > 0
		SOURCE:23 = (S:8 - 200) * SOURCE:11 / 100
	SIF SOURCE:23 > 0
		SOURCE:23 += SOURCE:0 / 5
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:11 += SOURCE:23 / 5
	STAIN:MASTER:2 |= STAIN:3
	STAIN:2 |= STAIN:MASTER:2
	SIF TEQUIP:37
		TEQUIP:37 = 0
	TEQUIP:44 = 0
	TEQUIP:45 = 0
	SIF TEQUIP:69
		TEQUIP:69 = 0

	TEQUIP:91 = 0
	TFLAG:131 += 1500
	TFLAG:64 += 4
ENDIF
;────────────────────────────────────
;媚薬とローションの使用（潤滑、尿意）
;────────────────────────────────────
;実は媚薬の使用も拒否されたり抵抗されたりのが普通と思うが、処理が面倒なので使ったら絶対成功ようにしました
@MIYAKU_USE
LOCAL = 0
;今の薬力残量をチェック
SELECTCASE TEQUIP:11
	CASE IS < 1
		LOCAL += 20
	CASE IS < 3
		LOCAL += 10
	CASE IS < 5
		LOCAL += 5
	CASE IS < 7
		LOCAL += 1
	CASE IS < 9
		LOCAL -= 5
	CASE IS < 11
		LOCAL -= 10
	CASEELSE
		LOCAL -= 15
ENDSELECT
;欲情がすでに高まってる状態なら使わなくてよし
SELECTCASE PALAM:5
	CASE IS > 6999
		LOCAL -= 15
	CASE IS > 5199
		LOCAL -= 8
	CASE IS > 3799
		LOCAL -= 2
	CASE IS > 2599
		LOCAL += 1
	CASE IS > 1599
		LOCAL += 3
	CASE IS > 699
		LOCAL += 6
	CASE IS > 299
		LOCAL += 10
	CASEELSE
		LOCAL += 15
ENDSELECT
;ヒート値を差し引きます。ヒート値とは計算の概念で実際は薬の残量だと認知してください
LOCAL -= FLAG:21

;使用する場合
IF LOCAL > 0
	;薬効追加(助手がいればパワーアップ)
	TEQUIP:11 += 5 + TALENT:55 * 4 + (1 + RAND:4) * (TALENT:55 + 2)
	TEQUIP:11 += TFLAG:190 == 71 ? 3 + (TALENT:ASSI:55 * 2 + RAND:3) * (ABL:ASSI:2 + RAND:2) # 0
	;ヒート値に加算(助手がいれば増加量が低減)
	LOCAL:1 = 40 - (TALENT:55 * 5 + GET_TRAINLV(TARGET) + 1) * (4 + ABL:2 + TALENT:130 * 2) / 8
	LOCAL:1 -= TFLAG:190 == 71 ? LOCAL:1 / (4 - TALENT:ASSI:55) + (TALENT:ASSI:55 * 3 + RAND:2) * (ABL:ASSI:2 + 1 + RAND:3) # 0
	LOCAL:1 += FLAG:21 > 0 ? 2 + RAND:3 # 0
	FLAG:21 += LOCAL:1 < 1 ? 1 + RAND:3 # LOCAL:1
	;尿意追加
	BASE:MASTER:4 += 100 + RAND:5 * 50
	;フラグセット
	TFLAG:104 |= 2
	TFLAG:307 |= 2
	TFLAG:308 |= 2
ENDIF


@LOTION_USE
LOCAL = 0
;今の薬力残量をチェック
SELECTCASE TEQUIP:10
	CASE IS < 1
		LOCAL += 20
	CASE IS < 3
		LOCAL += 10
	CASE IS < 5
		LOCAL += 5
	CASE IS < 7
		LOCAL -= 1
	CASE IS < 9
		LOCAL -= 5
	CASE IS < 11
		LOCAL -= 10
	CASEELSE
		LOCAL -= 15
ENDSELECT
;潤滑がすでに高まってる状態なら使わなくてよし
SELECTCASE PALAM:4
	CASE IS > 5999
		LOCAL -= 15
	CASE IS > 4799
		LOCAL -= 8
	CASE IS > 3799
		LOCAL -= 2
	CASE IS > 2499
		LOCAL += 1
	CASE IS > 1199
		LOCAL += 3
	CASE IS > 499
		LOCAL += 6
	CASE IS > 299
		LOCAL += 10
	CASEELSE
		LOCAL += 15
ENDSELECT
;ヒート値を差し引きます
LOCAL -= FLAG:20

;上着がまだ残っているなら強制脱衣を呼び出し、失敗したらローションはなし
IF (TEQUIP:MASTER:4 || TEQUIP:MASTER:5 || TEQUIP:MASTER:6) && !TFLAG:102 && !TEQUIP:40
	TFLAG:102 = 3 + (RAND:2 * 4)
	CALL KYOUSEI_DATUI
ENDIF
SIF TEQUIP:MASTER:4 || TEQUIP:MASTER:5 || TEQUIP:MASTER:6
	LOCAL = 0

;使用する場合
IF LOCAL > 0
	;薬効追加(助手がいればパワーアップ)
	TEQUIP:10 += 5 + TALENT:55 * 4 + (1 + RAND:4) * (TALENT:55 + 2)
	TEQUIP:10 += TFLAG:190 == 71 ? 3 + (TALENT:ASSI:55 * 2 + RAND:3) * (ABL:ASSI:2 + RAND:2) # 0
	;ヒート値に加算(助手がいれば増加量が低減)
	LOCAL:1 = 40 - (TALENT:55 * 5 + GET_TRAINLV(TARGET) + 1) * (4 + ABL:2 + TALENT:130 * 2) / 8
	LOCAL:1 -= TFLAG:190 == 71 ? LOCAL:1 / (4 - TALENT:ASSI:55) + (TALENT:ASSI:55 * 3 + RAND:2) * (ABL:ASSI:2 + 1 + RAND:3) # 0
	LOCAL:1 += FLAG:20 > 0 ? 2 + RAND:3 # 0
	FLAG:20 += LOCAL:1 < 1 ? 1 + RAND:3 # LOCAL:1
	;潤滑追加
	PALAM:4 += 200
	;フラグセット
	TFLAG:104 |= 1
	TFLAG:307 |= 1
	TFLAG:308 |= 1
ENDIF


;────────────────────────────────────
;調教者による強制脱衣と自主脱衣（露出、トラウマ、反抗、お仕置きポイント）
;────────────────────────────────────
@KYOUSEI_DATUI
;現在の服装によるフラグ変動（たとえば上半身上着を剥ぐように指定したがすでに脱がれた場合、ブラジャーを剥ぐように）
IF TFLAG:102 == 1 && TEQUIP:MASTER:5 == 0 && TEQUIP:MASTER:6 == 0
	IF TEQUIP:MASTER:3
		TFLAG:102 = 4
	ELSE
		TFLAG:102 = 0
	ENDIF
ELSEIF TFLAG:102 == 2 && TEQUIP:MASTER:4 == 0 && TEQUIP:MASTER:6 == 0
	IF TEQUIP:MASTER:2
		TFLAG:102 = 5
	ELSE
		TFLAG:102 = 0
	ENDIF
ELSEIF TFLAG:102 == 3 && TEQUIP:MASTER:4 == 0 && TEQUIP:MASTER:5 == 0 && TEQUIP:MASTER:6 == 0
	IF TEQUIP:MASTER:2 || TEQUIP:MASTER:3
		TFLAG:102 = 7
	ELSE
		TFLAG:102 = 0
	ENDIF
ELSEIF TFLAG:102 == 4 && TEQUIP:MASTER:3 == 0 && TEQUIP:MASTER:5 == 0 && TEQUIP:MASTER:6 == 0
	TFLAG:102 = 0
ELSEIF TFLAG:102 == 5 && TEQUIP:MASTER:2 == 0 && TEQUIP:MASTER:4 == 0 && TEQUIP:MASTER:6 == 0
	TFLAG:102 = 0
ELSEIF TFLAG:102 == 6 && TEQUIP:MASTER:0 == 0 && TEQUIP:MASTER:1 == 0
	TFLAG:102 = 0
ELSEIF TFLAG:102 == 7
	SIF TEQUIP:MASTER:0 == 0 && TEQUIP:MASTER:1 == 0
		TFLAG:102 = 8
	SIF TEQUIP:MASTER:2 == 0 && TEQUIP:MASTER:3 == 0 && TEQUIP:MASTER:4 == 0 && TEQUIP:MASTER:5 == 0 && TEQUIP:MASTER:6 == 0
		TFLAG:102 = 0
ELSEIF TFLAG:102 == 8 && TEQUIP:MASTER:2 == 0 && TEQUIP:MASTER:3 == 0 && TEQUIP:MASTER:4 == 0 && TEQUIP:MASTER:5 == 0 && TEQUIP:MASTER:6 == 0
	IF TEQUIP:MASTER:0 || TEQUIP:MASTER:1
		TFLAG:102 = 6
	ELSE
		TFLAG:102 = 0
	ENDIF
ELSEIF TFLAG:102 == 9 && TEQUIP:MASTER:2 == 0 && TEQUIP:MASTER:3 == 0
	TFLAG:102 = 0
ENDIF


@KYOUSEI_DATUI_CHECK
CALL ABL_REVISION
;脱衣成功するかの判定
A = BASE:MASTER:0 / 500 + BASE:MASTER:1 / 200 + CFLAG:MASTER:7 + MARK:3 - MARK:2 + PALAM:9 / 1000 - PALAM:6 / 1200 - PALAM:8 / 1200
A += TALENT:MASTER:12 * 2 - TALENT:MASTER:10 * 2 + TALENT:MASTER:11 * 5 - TALENT:MASTER:13 * 5 + TALENT:MASTER:16 * 3 - TALENT:MASTER:14 * 3 + TALENT:MASTER:111 * 4 - TALENT:MASTER:110 * 4
A -= A:30 + ABL:2 + TALENT:111 * 3 - TALENT:110 * 3 + BASE:0 / 600 + BASE:1 / 300

B = TFLAG:80 + 20
A -= ABL:B * 3 / 2

;目標パーツの補正
SIF TFLAG:102 == 1
	A += RAND:2
IF TFLAG:102 == 2
	A += TEQUIP:MASTER:4
	SIF TEQUIP:MASTER:6
		A += 2
ENDIF
IF TFLAG:102 == 3
	A += 1 + TEQUIP:MASTER:4
	SIF TEQUIP:MASTER:6
		A += 3
ENDIF
IF TFLAG:102 == 4
	A -= 2
	SIF TEQUIP:MASTER:5 || TEQUIP:MASTER:6
		A += 4
ENDIF
IF TFLAG:102 == 5
	A -= 2
	SIF TEQUIP:MASTER:4
		A += 4 + TEQUIP:MASTER:4
	SIF TEQUIP:MASTER:6
		A += 5
ENDIF
SIF TFLAG:102 == 6
	A -= 3
IF TFLAG:102 == 7 || TFLAG:102 == 8
	A += 2 + TEQUIP:MASTER:4
	SIF TEQUIP:MASTER:5
		A += 2
	SIF TEQUIP:MASTER:6
		A += 4
ENDIF
SIF TFLAG:102 == 9
	A += TEQUIP:MASTER:4 - 2
SIF TFLAG:102 == 9 && TEQUIP:MASTER:6
	A += 1
SIF TEQUIP:41
	A -= 2
;ちょっとずるしてるけどこの処理がないといろいろ大変
SIF TEQUIP:40 || TFLAG:80 == 6 || TFLAG:80 == 7 || TFLAG:80 == 8
	A = 0

IF TFLAG:102 > 0
	;木馬による実行不可能判定
	IF TEQUIP:43
		TFLAG:102 = 0
	;助手の手伝うで必ず成功に、反抗の程度によって拘束のソース若干入ります
	ELSEIF TFLAG:190 == 70
		SIF A > 0
			SOURCE:14 += (A + RAND:3) * 20
	;縄による実行不可能判定
	ELSEIF TEQUIP:40 && TFLAG:102 != 6 && TFLAG:102 != 9
		TFLAG:102 = 0
	ELSEIF A > RAND:4
		SOURCE:33 += 80 * (2 + A) * (4 + CFLAG:MASTER:7 + TALENT:MASTER:11 * 3 - TALENT:MASTER:13 * 3) / (5 + CFLAG:MASTER:0 + ABL:MASTER:0)
		TFLAG:68 += 2 + RAND:5 + BASE:7 / 150
		TFLAG:102 = -1
	ENDIF
ENDIF

IF TFLAG:102 > 0
	SOURCE:20 += 100 * (3 + TEQUIP:52 * 3 + TEQUIP:56 * 2) * (3 + FLAG:102 + TALENT:MASTER:34 * 2 - TALENT:MASTER:35 * 2) / (5 + CFLAG:MASTER:0)
	SOURCE:24 += 50 * (FLAG:102 + 5) * (5 + MARK:MASTER:4) / (10 + CFLAG:MASTER:0 + ABL:MASTER:0 * 2)
	IF TFLAG:102 == 6
		TIMES SOURCE:20 , 0.10
		TIMES SOURCE:24 , 0.25
	ELSEIF TFLAG:102 == 9
		TIMES SOURCE:20 , 0.75
	ENDIF
	SIF TFLAG:190 == 70
		TIMES SOURCE:20 , 1.20
ENDIF
;ラブトラップ
SIF TFLAG:120 == 201
	TFLAG:102 = 5
@JISHU_DATUI
;現在の服装によるフラグ変動（たとえば上半身上着を剥ぐように指定したがすでに脱がれた場合、ブラジャーを剥ぐように）
IF TFLAG:103 == 1 && TEQUIP:5 == 0 && TEQUIP:6 == 0
	IF TEQUIP:3
		TFLAG:103 = 4
	ELSE
		TFLAG:103 = 0
	ENDIF
ELSEIF TFLAG:103 == 2 && TEQUIP:4 == 0 && TEQUIP:6 == 0
	IF TEQUIP:2
		TFLAG:103 = 5
	ELSE
		TFLAG:103 = 0
	ENDIF
ELSEIF TFLAG:103 == 3 && TEQUIP:4 == 0 && TEQUIP:5 == 0 && TEQUIP:6 == 0
	IF TEQUIP:2 || TEQUIP:3
		TFLAG:103 = 7
	ELSE
		TFLAG:103 = 0
	ENDIF
ELSEIF TFLAG:103 == 4 && TEQUIP:3 == 0 && TEQUIP:5 == 0 && TEQUIP:6 == 0
	TFLAG:103 = 0
ELSEIF TFLAG:103 == 5 && TEQUIP:2 == 0 && TEQUIP:4 == 0 && TEQUIP:6 == 0
	TFLAG:103 = 0
ELSEIF TFLAG:103 == 6 && TEQUIP:0 == 0 && TEQUIP:1 == 0
	TFLAG:103 = 0
ELSEIF TFLAG:103 == 7
	SIF TEQUIP:0 == 0 && TEQUIP:1 == 0
		TFLAG:103 = 8
	SIF TEQUIP:2 == 0 && TEQUIP:3 == 0 && TEQUIP:4 == 0 && TEQUIP:5 == 0 && TEQUIP:6 == 0
		TFLAG:103 = 0
ELSEIF TFLAG:103 == 8 && TEQUIP:2 == 0 && TEQUIP:3 == 0 && TEQUIP:4 == 0 && TEQUIP:5 == 0 && TEQUIP:6 == 0
	IF TEQUIP:0 || TEQUIP:1
		TFLAG:103 = 6
	ELSE
		TFLAG:103 = 0
	ENDIF
ELSEIF TFLAG:103 == 9 && TEQUIP:2 == 0 && TEQUIP:3 == 0
	TFLAG:103 = 0
ENDIF

