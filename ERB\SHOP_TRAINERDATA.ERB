﻿;────────────────────────────────────
;ステータス表示
;────────────────────────────────────
@PRINT_STATUS(ARG)
DRAWLINE
PRINTFORM ■ %CALLNAME:ARG%的状态 ■
IF FLAG:666 
	CALL CHARA_FACE(ARG)											
DRAWLINE
ENDIF
IF ARG != MASTER
	PRINTFORM  (好感度:{CFLAG:ARG:9}) 
ELSE
;	PRINTFORM 称号：[%SAVESTR:0%]
ENDIF
PRINTL 
PRINTFORML [LV {CFLAG:ARG:0,2}  EXP {CFLAG:ARG:1,6}/{CFLAG:ARG:4,6}  NEXT {CFLAG:ARG:4 - CFLAG:ARG:1,6}]
CALL PRINT_BASEL("体力", ARG, 0, 32, 1)
CALL PRINT_BASEL("气力", ARG, 1, 32, 1)
IF ARG == MASTER
	CALL PRINT_BASEL("理性", ARG, 5, 32, 1)
	SIF PENIS(ARG)
		CALL PRINT_BASEL("精力", ARG, 2, 32, 1)
ELSE
	CALL PRINT_TENSION(ARG)
ENDIF
SIF FLAG:2001 && !TALENT:ARG:122
	CALL NEW_PRINT_3SIZE(ARG)
CALL NEW_PRINT_TALENT(ARG)
CALL NEW_PRINT_EXP(ARG)
CALL NEW_PRINT_ABL(ARG)
PRINTL [100] - 返回　[102] - 下一页
$INPUT_LOOP
INPUT
IF RESULT != 100 && RESULT != 102
	CLEARLINE 1
	GOTO INPUT_LOOP
ENDIF
SIF RESULT == 102
	CALL PRINT_STATUS2(ARG)
SIF RESULT == 100
	RETURN 0
RESTART


@PRINT_STATUS2(ARG)
IF ARG == MASTER
	PRINTL □ 刻印 □---------------------------------------------------------------------
	SIF MARK:ARG:0 || MARK:ARG:1 || MARK:ARG:4
		PRINTFORML \@ MARK:ARG:0 ? %MARKNAME:0%Lv{MARK:ARG:0,2}%" "% # \@\@ MARK:ARG:1 ? %MARKNAME:1%Lv{MARK:ARG:1,2}%" "% # \@\@ MARK:ARG:4 ? %MARKNAME:4%Lv{MARK:ARG:4,2} # \@
	SIF CFLAG:ARG:8
		PRINTFORML 造成%CALLNAME:ARG%心理创伤的原因是：[%STR:(100 + CFLAG:ARG:8)%]
	PRINTFORML                      %MARKNAME:2%        %MARKNAME:3%
	FOR LOCAL, 0, CHARANUM
		;一時退避中のキャラは表示しない
		SIF LOCAL == ARG || CFLAG:LOCAL:92
			CONTINUE
		PRINTFORML [{LOCAL,3}] - %CALLNAME:LOCAL,16,LEFT% Lv{MARK:LOCAL:2,2}            Lv{MARK:LOCAL:3,2}
	NEXT
ELSE
	PRINTL □ 调教覆历 □-----------------------------------------------------------------
	LOCAL:1 = 1
	LOCAL:2 = 0
	PRINT Lv1
	FOR LOCAL, 0, 60
		IF GETBIT(CFLAG:ARG:300, LOCAL)
			LOCAL:2++
		ELSE
			SETCOLOR 150, 150, 150
		ENDIF
		PRINTFORMS @"\@ GETBIT(CFLAG:MASTER:300, LOCAL) ? 【%STR:(1300 + LOCAL)%】 # %" " * (4 + STRLENS(STR:(1300 + LOCAL)))% \@ "
		RESETCOLOR
		SELECTCASE LOCAL
			CASE 3, 7, 15, 20, 25, 30, 39, 43,47, 55
				PRINTL 
				PRINT    
			CASE 11, 33, 49, 56, 59
				PRINTL 
				SELECTCASE LOCAL:1
					CASE 1 TO 3
						PRINTFORML     {LOCAL:1,2}P×{LOCAL:2,2}＝{LOCAL:2 * LOCAL:1,2}P
					CASE 4
						PRINTFORML      5P×{LOCAL:2,2}＝{LOCAL:2 * 5,2}P
					CASE 5
						PRINTFORML     10P×{LOCAL:2,2}＝{LOCAL:2 * 10,2}P
				ENDSELECT
				LOCAL:2 = 0
				PRINTL -------------------------------------------------------------------------------
				PRINTFORM \@ LOCAL != 59 ? Lv{++LOCAL:1} # \@
		ENDSELECT
	NEXT
	PRINTFORML 合计:{CFLAG:ARG:301}P 屈服刻印:LV{MARK:ARG:2}
ENDIF
PRINTL [100] - 返回　[101] - 上一页
$INPUT_LOOP
INPUT
IF RESULT != 100 && RESULT != 101
	CLEARLINE 1
	GOTO INPUT_LOOP
ENDIF
RETURN RESULT


;3サイズ
@NEW_PRINT_3SIZE(ARG)
SIF !STRLENS(LOCALS)
	SPLIT "/AAA/AA/A/B/C/D/E/F/G/H/I/J/K/L/M", "/", LOCALS
PRINTL □ 体型 □---------------------------------------------------------------------
PRINTFORM \@ TALENT:ARG:152 ? 体重 # 身高 \@ {CFLAG:ARG:900}cm　胸围 {CFLAG:ARG:901}cm 
;1 2 3 |貧乳の壁| 4 5 6 |巨乳の壁| 7 8 9 |爆乳の壁| 10 11 12 13 14 15…
PRINTFORML (\@ 0 < CFLAG:ARG:904 && CFLAG:ARG:904 < 16 ? %LOCALS:(CFLAG:ARG:904)% # N \@罩杯)　腰围 {CFLAG:ARG:902}cm　臀围 {CFLAG:ARG:903}cm


@NEW_PRINT_EXP(ARG)
PRINTL □ 经验 □---------------------------------------------------------------------
;表示順の設定
; 0,　　　Ｃ経験　 1,　　　Ｖ経験　 2,　　　Ａ経験　 3,　　　Ｂ経験
; 4,　　絶頂経験　 5,　　射精経験　 6,　　噴乳経験　 7,　　性交経験
; 8,　　　性知識　 9,　　自慰経験　10,調教自慰経験　11,　　放尿経験
;13,　　被写経験　20,　　精液経験　21,奉仕快楽経験　22,　　口淫経験
;23,　　手淫経験　24,道具使用経験　25,　　キス経験　26,　　会話経験
;30,苦痛快楽経験　31,恥辱快楽経験　31,　　愛情経験　40,　　レズ経験
;50,　　異常経験　51,　　緊縛経験　52,　Ｖ拡張経験　53,　Ａ拡張経験
;55,　　触手経験　60,　　屈服経験　61,　　恐怖経験　62,　　露出経験
;70,　　開錠経験
LOCAL:2 = 0
FOR LOCAL:1, 0, 33
	LOCAL = -1
	SELECTCASE LOCAL:1
		CASE 0 TO 11
			LOCAL = LOCAL:1
		CASE 12
			LOCAL = 13
		CASE 13 TO 19
			LOCAL = LOCAL:1 + 7
		CASE 20 TO 22
			LOCAL = LOCAL:1 + 10
		;[オトコ]ならレズ経験のかわりにＢＬ経験を表示
		CASE 23
			LOCAL = TALENT:ARG:122 ? 41 # 40
		CASE 24 TO 27
			LOCAL = LOCAL:1 + 26
		CASE 28
			LOCAL = LOCAL:1 + 27
		CASE 29 TO 31
			LOCAL = LOCAL:1 + 31
		CASE 32
			LOCAL = 70
	ENDSELECT
	SIF LOCAL < 0
		CONTINUE
	PRINTFORM \@ EXP:ARG:LOCAL ? %EXPNAME:LOCAL, 12%:{EXP:ARG:LOCAL, 5} # ？？？？？？:　　0 \@
	IF ++LOCAL:2 == 4
		PRINTL 
		LOCAL:2 = 0
	ELSE
		PRINT 　
	ENDIF
NEXT
;最後のループで改行してなかったら改行
SIF LOCAL:2 != 0
	PRINTL 
;IF EXP:ARG:63
;	;名称募集中
;	PRINTFORML ラグジャラスヒット！　{EXP:ARG:63}
;ENDIF


@NEW_PRINT_ABL(ARG)
PRINTL □ 能力 □---------------------------------------------------------------------
;表示順の設定
; 0,　　従順　 1,　　欲望　 2,　　技巧　 7,奉仕精神
; 3,　Ｃ感覚　 4,　Ｖ感覚　 5,　Ａ感覚　 6,　Ｂ感覚
; 8,　露出癖　 9,レズっ気　11,マゾっ気　12,自慰中毒
;13,精液中毒　14,レズ中毒　15,苦痛中毒　16,拘束中毒
;20,　　会話　21,　　愛撫　22,　　道具　23,　　性交
;24,　　羞恥　25,　　奉仕　26,　　加虐　27,　　異常
;28,　　使役　30,　　開錠
LOCAL:2 = 0
FOR LOCAL:1, 0, 26
	LOCAL = -1
	SELECTCASE LOCAL:1
		CASE 0 TO 2
			LOCAL = LOCAL:1
		CASE 3
			LOCAL = 7
		CASE 4 TO 7
			LOCAL = LOCAL:1 - 1
		CASE 8
			LOCAL = LOCAL:1
		;[オトコ]ならレズっ気のかわりにＢＬっ気を表示
		CASE 9
			LOCAL = TALENT:ARG:122 ? 10 # 9
		CASE 10 TO 12
			LOCAL = LOCAL:1 + 1
		;[オトコ]ならレズ中毒を非表示
		CASE 13
			LOCAL = TALENT:ARG:122 ? -1 # 14
		CASE 14 TO 15
			LOCAL = LOCAL:1 + 1
		CASE 16 TO 24
			LOCAL = LOCAL:1 + 4
		CASE 25
			LOCAL = 30
	ENDSELECT
	SIF LOCAL < 0
		CONTINUE
	PRINTFORM %ABLNAME:LOCAL,8%:{ABL:ARG:LOCAL,2}
	IF ++LOCAL:2 == 4
		PRINTL 
		LOCAL:2 = 0
	ELSE
		PRINT 　
	ENDIF
NEXT
;最後のループで改行してなかったら改行
SIF LOCAL:2 != 0
	PRINTL 


@NEW_PRINT_TALENT(ARG)
PRINTL □ 素质 □---------------------------------------------------------------------
;SIF FLAG:2002
;	PRINTFORML %PERSONA(ARG)%
;通常表示
IF !FLAG:2003
	LOCAL:1 = 0
	FOR LOCAL, 0, 300
		;非表示素質
		SIF !TALENT_TYPE(LOCAL)
			CONTINUE
		IF TALENT:ARG:LOCAL
			LOCALS = %GET_TALENTNAME(ARG, LOCAL)%
			LOCAL:2 = STRLENS(LOCALS)
			LOCAL:1 += LOCAL:2 + 3
			;あまり詰めない
			IF LOCAL:1 > 70
				PRINTL 
				LOCAL:1 = LOCAL:2 + 3
			ENDIF
			PRINTFORM [%LOCALS%] 
		ENDIF
	NEXT
	PRINTL 
;素質整頓表示
ELSE
	;ループを200×ｎ回輪姦すのはさすがにちょっと重たいんじゃないか…と思ったのでこんなやり方に。
	;TALENTは使ってもせいぜい200種程度だと思うので所持している番号だけを、少し強引な手口を使用してLOCALだけで保持。
	;今のやり方だと各タイプについて99種しか扱えないけど、それ以前に素質を99種持つのも難しいと思うのできっと大丈夫。
	;ダメになったらダメになった時になんとかしてください。なお、現在の仕様では素質分類は9までしか扱えません。
	
	;LOCAL初期化
	VARSET LOCAL
	
	;素質分類処理のループ
	;LOCAL:0		ループカウンタ(素質番号)
	;LOCAL:1		素質分類番号
	;LOCAL:X00		その系統のTALENTをいくつ保有しているか(X=素質分類番号)
	;LOCAL:X01～X99	所持しているTALENTの番号を順に格納していく(X=素質分類番号)
	FOR LOCAL, 0, 300
		;素質が無いなら処理しない。無駄に関数呼ぶのも嫌なので
		SIF !TALENT:ARG:LOCAL
			CONTINUE
		LOCAL:1 = TALENT_TYPE(LOCAL)
		;非表示は表示しない。性別は別処理のためここでは扱わない
		SIF LOCAL:1 > 1
			LOCAL:(100 * LOCAL:1 + 1 + LOCAL:(100 * LOCAL:1)++) = LOCAL
	NEXT
	
	;性別は特殊なので別処理。ここで表示する
	PRINTFORML %TALENT_TYPENAME(1)%：[%GET_SEX(ARG)%]\@ TALENT:ARG:0 ? %" "%[%GET_TALENTNAME(ARG, 0)%] \@\@ TALENT:ARG:1 ? %" "%[%GET_TALENTNAME(ARG, 1)%] \@
	
	;表示のループ
	;LOCAL:0	ループカウンタ1(素質分類番号)
	;LOCAL:1	処理中の行オフセット(桁位置)
	;LOCAL:2	タイトル部分の文字数(『○○：』の部分)
	;LOCAL:3	ループカウンタ2(その分類の所持素質数だけループ)
	;LOCAL:4	素質名の長さ
	;LOCAL:5	その分類の素質所持数が格納されているLOCALの番号
	;LOCALS:0	汎用の文字列格納域(素質分類名/素質名)
	FOR LOCAL, 2, 8
		;素質分類番号を配列の添え字に変換
		LOCAL:5 = LOCAL * 100
		;その分類の素質を1つも所持していなければ何もしない
		SIF !LOCAL:(LOCAL:5)
			CONTINUE
		;素質分類名を取得→出力
		LOCALS = %TALENT_TYPENAME(LOCAL)%
		PRINTFORM %LOCALS%：
		LOCAL:1 = 0
		LOCAL:2 = STRLENS(LOCALS) + 2
		;素質を所持している数だけループ
		FOR LOCAL:3, 0, LOCAL:(LOCAL:5)
			;素質名を取得
			LOCALS = %GET_TALENTNAME(ARG, LOCAL:(LOCAL:5 + 1 + LOCAL:3))%
			LOCAL:4 = STRLENS(LOCALS)
			LOCAL:1 += LOCAL:4 + 3
			;改行処理
			IF LOCAL:1 > 78 - LOCAL:2
				PRINTL 
				PRINTS " " * LOCAL:2
				LOCAL:1 = LOCAL:2 + 3 + LOCAL:4
			ENDIF
			SIF STRLENS(LOCALS)
			;素質名の出力
			PRINTFORM [%LOCALS%] 
		NEXT
		PRINTL 
	NEXT
ENDIF


;────────────────────────────────────
;調教者のテンション表示
;────────────────────────────────────
@PRINT_TENSION(ARG)
SELECTCASE TALENT:MASTER:132
	CASE 1
		PRINTFORML 理性[%TENSION(ARG, 5)%]
		PRINTFORML 兴趣[%TENSION(ARG, 6)%]
		PRINTFORML 焦躁[%TENSION(ARG, 7)%]
		PRINTFORML 满意[%TENSION(ARG, 8)%]
	CASE 2
		CALL PRINT_BASEL("理性", ARG, 5, 32, 1)
		CALL PRINT_BASEL("兴趣", ARG, 6, 32, 1)
		CALL PRINT_BASEL("焦躁", ARG, 7, 32, 1)
		CALL PRINT_BASEL("满意", ARG, 8, 32, 1)
		PRINTFORM 罪恶感[{CFLAG:ARG:5}]　调教倾向
		LOCAL = GETCOLOR()
		SETCOLOR CFLAG:ARG:6 >= 0 ? 0x0000FF # 0xFF0000
		BAR CFLAG:ARG:6 + 100, 199, 32
		SETCOLOR LOCAL
		PRINTFORML [{CFLAG:ARG:6}]
	CASEELSE
		PRINTFORML 关于%CALLNAME:ARG%的心情，还不太明白…
ENDSELECT
