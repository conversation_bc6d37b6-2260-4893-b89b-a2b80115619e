﻿EVENT_EQUIP.ERB
	野外プレイより下をコメントアウト
ACT_APPLY2.ERB
	先頭付近のSPアクション部分をコメントアウト
SP_ACTION.ERB
	Reverseを見て適宜改変
SKILL.ERB
	全関数の先頭でリターン
SHOP.ERB
	空き部屋表示
	拠点増築	103
	脱走/迷宮	114/113
	ブックシェルフ	116
	称号	117
	アイテム管理の2-5、0をデフォ解放
	CALL GET_EXTALENT
SHOP_TRAINERDATA.ERB
	@PRINT_STATUS(ARG)の称号表示
	「\@ CFLAG:ARG:10 && NO:ARG != 21 && !TALENT:ARG:170 ? 種族:%NAME:ARG% # \@」
EVENT_HONOR.ERB
	@EVENT_HONORの先頭でリターン
CONFIGURE.ERB
	新しい調教者の追加方法
	キャラの性格、体型などに個性を持たせる
	@CONFIGUREの先頭でFLAG:2000 = 1、FLAG:2002 = 0
SYSTEM.ERB
	オープニング
	イージー	1
	体験版	10
	空き部屋の初期値を適当にでかい数
	FLAG:8 = 71
EVENT_S.ERB
	;ユニーク夢魔からの脱走イベントなど個別に処理すべきイベント
INFO.ERB
	@EQUIP_2USEのIF TEQUIP:52の次「IF ASSI:3 >= 0」を「IF 1」
COMMON_CLOTHES.ERB
	東方の設定に書き換え
CLOTHES.ERB
	@CLOTHES_SETUP(ARG)を東方の設定に書き換え
Talent.csv
	131,マスター,
Item.csv
	キャラカード
	カスタム化をコメントアウト
SHOP_ITEM.ERB
	@ITEMSALEの2
EVENT_S.ERB
	ターン毎に所持金追加(暫定処理)
	;ユニーク夢魔からの脱走イベントなど個別に処理すべきイベント
ENDING.ERB
	先頭でリターン
AFTERTRA.ERB
	恋慕/親愛条件から監禁中を削除
ACTABLE.ERB
	ホットパンツ判定削除