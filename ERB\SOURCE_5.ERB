﻿;────────────────────────────────────
;調教者快感（ｃｖａｂ）のソースと絶頂の処理（快ｃ、快ｖ、快ａ、快ｂ、体力、気力、射精、母乳、理性、興味）
;────────────────────────────────────
@CACL_SOURCE40
;────────────────────────────────────
;最初の変動
;────────────────────────────────────
;敏感と鈍感は最初で処理
SIF TALENT:100
	TIMES SOURCE:40 , 1.50
SIF TALENT:101
	TIMES SOURCE:40 , 0.70
SIF TALENT:102
	TIMES SOURCE:41 , 1.50
SIF TALENT:103
	TIMES SOURCE:41 , 0.70
SIF TALENT:104
	TIMES SOURCE:42 , 1.50
SIF TALENT:105
	TIMES SOURCE:42 , 0.70
SIF TALENT:106
	TIMES SOURCE:43 , 1.50
SIF TALENT:107
	TIMES SOURCE:43 , 0.70

;調教者の状態
IF TFLAG:60 == 1
	TIMES SOURCE:40 , 0.80
	TIMES SOURCE:41 , 0.80
	TIMES SOURCE:42 , 0.80
	TIMES SOURCE:43 , 0.80
ELSEIF TFLAG:60 == 2
	TIMES SOURCE:40 , 0.65
	TIMES SOURCE:41 , 0.65
	TIMES SOURCE:42 , 0.65
	TIMES SOURCE:43 , 0.65
ELSEIF TFLAG:60 == 3
	TIMES SOURCE:40 , 0.70
	TIMES SOURCE:41 , 0.70
	TIMES SOURCE:42 , 0.70
	TIMES SOURCE:43 , 0.70
ELSEIF TFLAG:60 == 5
	TIMES SOURCE:40 , 1.20
	TIMES SOURCE:41 , 1.20
	TIMES SOURCE:42 , 1.20
	TIMES SOURCE:43 , 1.20
ENDIF

;────────────────────────────────────
;快感（ＣＶＡＢ）の上昇
;────────────────────────────────────


IF PALAM:5 < 1000
	A = 4
ELSEIF PALAM:5 < 2000
	A = 7
ELSEIF PALAM:5 < 3000
	A = 9
ELSEIF PALAM:5 < 4000
	A = 10
ELSEIF PALAM:5 < 5000
	A = 11
ELSE
	A = 12
ENDIF

UP:40 += SOURCE:40 * A / 10
UP:41 += SOURCE:41 * A / 10
UP:42 += SOURCE:42 * A / 10
UP:43 += SOURCE:43 * A / 10

;調教中に集中的に責られると弱くなる気がする
UP:40 = UP:40 * CFLAG:MASTER:30 / 50
UP:41 = UP:41 * CFLAG:MASTER:31 / 50
UP:42 = UP:42 * CFLAG:MASTER:32 / 50
UP:43 = UP:43 * CFLAG:MASTER:33 / 50


;────────────────────────────────────
;絶頂、体力、気力、射精、母乳、尿意などの処理
;────────────────────────────────────

C = 0
V = 0
A = 0
B = 0
IF FLAG:3108 == 1
	LOCAL = 5
ELSEIF FLAG:3108 == 2
	LOCAL = 7
ELSEIF FLAG:3108 == 3
	LOCAL = 10
ENDIF
;絶頂Ｃ
IF UP:40 + PALAM:40 >= 20000 && (!PENIS(TARGET) || TCVAR:131 > 1)
	C = 2
	DOWN:40 = 19000
	STR:40 = 强绝顶Ｃ（调教者）
ELSEIF UP:40 + PALAM:40 >= 10000 && (!PENIS(TARGET) || TCVAR:131 > 1)
	C = 1
	DOWN:40 = 9000
	STR:40 = 绝顶Ｃ（调教者）
;ラグジャラスヒット
ELSEIF UP:40 && TC_PLAYER() == 1 && TFLAG:94 == 2
	IF GET_REVISION(ABL:MASTER:2,1000,5) - 500 > RAND:2000 || RAND:100 < LOCAL
		C = 1
		DOWN:40 = (UP:40 + PALAM:40) / 2
		STR:40 = ラグジャラスヒット！ 绝顶Ｃ（调教者）
	ENDIF
ENDIF
;DOWN:40で下げても絶頂以上なら
;その値-1になるように調整（10000で絶頂なら9999）
SIF UP:40 + PALAM:40 - DOWN:40 >= 10000
	DOWN:40 = UP:40 + PALAM:40 - 9999

;絶頂Ｖ
IF UP:41 + PALAM:41 >= 20000
	V = 2
	DOWN:41 = 19000
	STR:41 = 强绝顶Ｖ（调教者）
ELSEIF UP:41 + PALAM:41 >= 10000
	V = 1
	DOWN:41 = 9000
	STR:41 = 绝顶Ｖ（调教者）
;ラグジャラスヒット
ELSEIF UP:41 && TV_PLAYER() == 1 && TFLAG:94 == 2
	IF GET_REVISION(ABL:MASTER:2,1000,5) - 500 > RAND:2000 || RAND:100 < LOCAL
		V = 1
		DOWN:41 = (UP:41 + PALAM:41) / 2
		STR:41 = ラグジャラスヒット！ 绝顶Ｖ（调教者）
	ENDIF
ENDIF
SIF UP:41 + PALAM:41 - DOWN:41 >= 10000
	DOWN:41 = UP:41 + PALAM:41 - 9999

;絶頂Ａ
IF UP:42 + PALAM:42 >= 20000
	A = 2
	DOWN:42 = 19000
	STR:42 = 强绝顶Ａ（调教者）
ELSEIF UP:42 + PALAM:42 >= 10000
	A = 1
	DOWN:42 = 9000
	STR:42 = 绝顶Ａ（调教者）
;ラグジャラスヒット
ELSEIF UP:42 && TA_PLAYER() == 1 && TFLAG:94 == 2
	IF GET_REVISION(ABL:MASTER:2,1000,5) - 500 > RAND:2000 || RAND:100 < LOCAL
		A = 1
		DOWN:42 = (UP:42 + PALAM:42) / 2
		STR:42 = ラグジャラスヒット！ 绝顶Ａ（调教者）
	ENDIF
ENDIF
SIF UP:42 + PALAM:42 - DOWN:42 >= 10000
	DOWN:42 = UP:42 + PALAM:42 - 9999

;絶頂Ｂ
IF UP:43 + PALAM:43 >= 20000
	B = 2
	DOWN:43 = 19000
	STR:43 = 强绝顶Ｂ（调教者）
ELSEIF UP:43 + PALAM:43 >= 10000
	B = 1
	DOWN:43 = 9000
	STR:43 = 绝顶Ｂ（调教者）
;ラグジャラスヒット
ELSEIF UP:43 && TB_PLAYER() == 1 && TFLAG:94 == 2
	IF GET_REVISION(ABL:MASTER:2,1000,5) - 500 > RAND:2000 || RAND:100 < LOCAL
		B = 1
		DOWN:43 = (UP:43 + PALAM:43) / 2
		STR:43 = ラグジャラスヒット！ 绝顶Ｂ（调教者）
	ENDIF
ENDIF
SIF UP:43 + PALAM:43 - DOWN:43 >= 10000
	DOWN:43 = UP:43 + PALAM:43 - 9999

H = 1000 * (C + V + A + B)
I = 0
J = 0

;快感による体力気力理性消耗
IF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 10000
	K = 100
ELSEIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 8000
	K = 90
ELSEIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 6500
	K = 80
ELSEIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 5000
	K = 70
ELSEIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 4000
	K = 60
ELSEIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 3000
	K = 50
ELSEIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 2500
	K = 40
ELSEIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 2000
	K = 30
ELSEIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 1600
	K = 20
ELSEIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 1200
	K = 10
ELSE
	K = 5
ENDIF

;調教の度合いによる軽減
IF CFLAG:0 + ABL:1 > 12
	TIMES K , 0.50
ELSEIF CFLAG:0 + ABL:1 > 10
	TIMES K , 0.60
ELSEIF CFLAG:0 + ABL:1 > 8
	TIMES K , 0.65
ELSEIF CFLAG:0 + ABL:1 > 6
	TIMES K , 0.70
ELSEIF CFLAG:0 + ABL:1 > 5
	TIMES K , 0.75
ELSEIF CFLAG:0 + ABL:1 > 4
	TIMES K , 0.80
ELSEIF CFLAG:0 + ABL:1 > 3
	TIMES K , 0.85
ELSEIF CFLAG:0 + ABL:1 > 2
	TIMES K , 0.90
ELSEIF CFLAG:0 + ABL:1 > 1
	TIMES K , 0.95
ENDIF

IF TALENT:122 || TALENT:121
	IF C && BASE:2 < 50 + RAND:5 * 10
		STR:44 = 空射精（调教者）
		I = 50 + C * 10
		LOSEBASE:0 += 100
		LOSEBASE:1 += 80
		TFLAG:35 = 1
	ELSEIF C == 2
		STR:44 = 大量射精（调教者）
		I = 600
		TFLAG:35 = 3
	ELSEIF C == 1
		STR:44 = 射精（调教者）
		I = 300
		TFLAG:35 = 2
	ENDIF
	IF TFLAG:35 > 1
		STAIN:2 |= 4
		SIF TFLAG:90 == 50
			STAIN:MASTER:1 |= 4
		SIF TFLAG:90 == 51
			STAIN:MASTER:0 |= 4
		SIF TFLAG:90 == 52
			STAIN:MASTER:5 |= 4
		SIF TFLAG:90 == 53
			STAIN:MASTER:3 |= 4
		SIF TFLAG:90 == 54
			STAIN:MASTER:6 |= 4
	ENDIF
ENDIF


	;中/外に射精
	IF TEQUIP:70 && TFLAG:35
		;中に出さないで！
		IF SELECTCOM == 22
			IF TFLAG:94 != 3
				SOURCE:24 += 500 - CFLAG:MASTER:0 * 10
				IF TEQUIP:70 < 6
					STAIN:MASTER:3 |= 4
					TFLAG:105 = 1
				ELSE
					;アナル
					STAIN:MASTER:4 |= 4
					TFLAG:105 = 5
				ENDIF
			ENDIF
		;中に出して！
		ELSEIF SELECTCOM == 21
			IF TEQUIP:70 < 6
				STAIN:MASTER:3 |= 4
				TFLAG:105 = 1
			ELSE
				STAIN:MASTER:4 |= 4
				TFLAG:105 = 5
			ENDIF
		ELSEIF CFLAG:2 > RAND:8000
			IF TEQUIP:70 < 6
				STAIN:MASTER:3 |= 4
				TFLAG:105 = 1
			ELSE
				STAIN:MASTER:4 |= 4
				;アナル
				TFLAG:105 = 5
			ENDIF
		ENDIF
	ENDIF


IF B && TALENT:114
	IF BASE:3 < 50 + RAND:5 * 10
		STR:45 = 空喷乳（调教者）
		J = 50 + B * 10
		LOSEBASE:0 += 100
		LOSEBASE:1 += 80
		TFLAG:36 = 1
	ELSEIF C == 2
		STR:45 = 大量喷乳（调教者）
		TFLAG:36 = 2
		J = 500
	ELSE
		STR:45 = 喷乳（调教者）
		J = 300
		TFLAG:36 = 1
	ENDIF
	STAIN:5 |= 16
ENDIF

IF C && V && A && B
	STR:46 = 四 重 絶 頂（调教者）
	TIMES H , 2.00
	TIMES I , 1.40
	TIMES J , 1.40
	TIMES K , 1.50
ELSEIF C && V && A
	STR:46 = Ｃ＆Ｖ＆Ａ绝顶（调教者）
	TIMES H , 1.65
	TIMES I , 1.20
	TIMES J , 1.20
	TIMES K , 1.30
ELSEIF B && V && A
	STR:46 = Ｂ＆Ｖ＆Ａ绝顶（调教者）
	TIMES H , 1.65
	TIMES I , 1.20
	TIMES J , 1.20
	TIMES K , 1.30
ELSEIF C && B && A
	STR:46 = Ｃ＆Ｂ＆Ａ绝顶（调教者）
	TIMES H , 1.65
	TIMES I , 1.20
	TIMES J , 1.20
	TIMES K , 1.30
ELSEIF C && V && B
	STR:46 = Ｃ＆Ｖ＆Ｂ绝顶（调教者）
	TIMES H , 1.65
	TIMES I , 1.20
	TIMES J , 1.20
	TIMES K , 1.30
ELSEIF C && V
	STR:46 = Ｃ＆Ｖ绝顶（调教者）
	TIMES H , 1.30
	TIMES I , 1.10
	TIMES J , 1.10
	TIMES K , 1.30
ELSEIF C && A
	STR:46 = Ｃ＆Ａ绝顶（调教者）
	TIMES H , 1.30
	TIMES I , 1.10
	TIMES J , 1.10
	TIMES K , 1.30
ELSEIF V && A
	STR:46 = Ｖ＆Ａ绝顶（调教者）
	TIMES H , 1.30
	TIMES I , 1.10
	TIMES J , 1.10
	TIMES K , 1.30
ELSEIF C && B
	STR:46 = Ｃ＆Ｂ绝顶（调教者）
	TIMES H , 1.30
	TIMES I , 1.10
	TIMES J , 1.10
	TIMES K , 1.30
ELSEIF V && B
	STR:46 = Ｖ＆Ｂ绝顶（调教者）
	TIMES H , 1.30
	TIMES I , 1.10
	TIMES J , 1.10
	TIMES K , 1.30
ELSEIF A && B
	STR:46 = Ａ＆Ｂ绝顶（调教者）
	TIMES H , 1.30
	TIMES I , 1.10
	TIMES J , 1.10
	TIMES K , 1.30
ENDIF

;絶頂は達成の主なソースの一つ
SOURCE:31 += H + I + J

;体力と気力の消耗
LOSEBASE:0 += 20 * C + 40 * V + 60 * A + 20 * B + I / 10 + J / 10 + K
LOSEBASE:1 += 10 * C + 20 * V + 30 * A + 10 * B + I / 15 + J / 15 + (K * 4 / 5)
LOSEBASE:2 = I
SIF LOSEBASE:2 > BASE:2
	LOSEBASE:0 += 200 * (2 - TALENT:125 + TALENT:126) / 2
LOSEBASE:3 += J
SIF LOSEBASE:3 > BASE:3
	LOSEBASE:0 += 200 * (2 - TALENT:125 + TALENT:126) / 2

;理性
LOSEBASE:5 += K + (C + V + A + B + LOSEBASE:2 / 300 + TFLAG:36 + 5) * 20

;調教部位の累積値
SIF C
	CFLAG:30 = 50 + (CFLAG:30 - 50) / 2
SIF V
	CFLAG:31 = 50 + (CFLAG:31 - 50) / 2
SIF A
	CFLAG:32 = 50 + (CFLAG:32 - 50) / 2
SIF B
	CFLAG:33 = 50 + (CFLAG:33 - 50) / 2

;NOWEXにデータを入れる（絶頂時口上に使う）
NOWEX:40 = C
NOWEX:41 = V
NOWEX:42 = A
NOWEX:43 = B
NOWEX:51 = TFLAG:35
NOWEX:50 = TFLAG:36

;夢魔の勃起　快感を受けると+1射精すると0
SIF SOURCE:40 && PENIS(TARGET)
	TCVAR:131 ++
SIF NOWEX:51
	TCVAR:131 = 1
IF NOWEX:51
	CFLAG:(250 + NOWEX:51) += 1
	CFLAG:250 += 1
ENDIF
SIF C
	CFLAG:256 += 1
SIF V
	CFLAG:257 += 1
SIF A
	CFLAG:258 += 1
SIF B
	CFLAG:259 += 1

;絶頂回数を増やす
EX:40 += C
EX:41 += V
EX:42 += A
EX:43 += B
SIF TALENT:121 || TALENT:122
	EX:51 += 1
EX:50 += TFLAG:36


@PRINT_SOURCE40

A = 0

IF NOWEX:40
	PRINTSL STR:40
	A += 1
ENDIF
IF NOWEX:41
	PRINTSL STR:41
	A += 1
ENDIF
IF NOWEX:42
	PRINTSL STR:42
	A += 1
ENDIF
IF NOWEX:43
	PRINTSL STR:43
	A += 1
ENDIF

SIF NOWEX:51
	PRINTSL STR:44

SIF FLAG:12
	GOTO SKIP

;中/外に射精
IF TEQUIP:70 && NOWEX:51 > 0
	;中に出さないで！
	IF SELECTCOM == 22
		IF TFLAG:105 == 0
			PRINTFORML %CALLNAME:TARGET%接受了%CALLNAME:MASTER%的请求，射精前拔出了阴茎，在外面释放了白浊的液体……
		ELSE
			PRINTFORML %CALLNAME:TARGET%没有理会%CALLNAME:MASTER%的请求，在%CALLNAME:MASTER%体内释放了白浊的液体……
			;調教対象(マスター)の膣内射精量を増やす
			SIF TEQUIP:70 < 6
				CFLAG:45 += LOSEBASE:2
		ENDIF
	;中に出して！
	ELSEIF SELECTCOM == 21
		PRINTFORML %CALLNAME:TARGET%接受了%CALLNAME:MASTER%的请求，在%CALLNAME:MASTER%体内释放了白浊的液体……
		;調教対象(マスター)の膣内射精量を増やす
		SIF TEQUIP:70 < 6
			CFLAG:45 += LOSEBASE:2
	ELSEIF TFLAG:105 == 0
		PRINTFORML %CALLNAME:TARGET%在射精前拔出了阴茎，在外面释放了白浊的液体……
	ELSE
		PRINTFORML %CALLNAME:TARGET%在%CALLNAME:MASTER%体内释放了白浊的液体…
		;調教対象(マスター)の膣内射精量を増やす
		SIF TEQUIP:70 < 6
			CFLAG:45 += LOSEBASE:2
	ENDIF
ENDIF

;射精（その他）
IF NOWEX:51
	;対面
	IF TFLAG:90 == 97
		PRINTFORMW 紧贴在%CALLNAME:MASTER%身体上的%CALLNAME:TARGET%的阴茎喷出了精液…
	;背面
	ELSEIF TFLAG:90 == 98
		PRINTFORMW %CALLNAME:TARGET%含着%CALLNAME:MASTER%的阴茎射精了……
	;手淫
	ELSEIF TFLAG:90 == 50
		PRINTFORMW %CALLNAME:TARGET%将阴茎朝向%CALLNAME:MASTER%的脸射精……
	;口淫
	ELSEIF TFLAG:90 == 51
		PRINTFORMW %CALLNAME:TARGET%在%CALLNAME:MASTER%的口中吐出了欲望，满足的一边抚摸%CALLNAME:MASTER%的头一边沉浸在快感的余韵里…
	;素股	
	ELSEIF TFLAG:90 == 53
		PRINTFORMW %CALLNAME:TARGET%蹭着%CALLNAME:MASTER%的阴唇射精了…
	;パイズリ	
	ELSEIF TFLAG:90 == 52
		PRINTFORMW 从%CALLNAME:MASTER%胸口露出的龟头喷出了精液…
	;足コキ
	ELSEIF TFLAG:90 == 54
		PRINTFORMW %CALLNAME:TARGET%一边与%CALLNAME:MASTER%足交，一边射精了…
	ELSEIF TEQUIP:69 == 2 || TEQUIP:69 == 3
		TFLAG:23 = RAND:2
		IF TFLAG:23 == 1 && TFLAG:22 == 0
			PRINTFORML %CALLNAME:TARGET%对着%CALLNAME:MASTER%的脸，
			PRINTFORMW 浮现出恍惚的表情，射精了……
		ELSE
			PRINTFORMW %CALLNAME:TARGET%一边玩弄着自己的阴茎一边绝顶了……
		ENDIF
	ENDIF
ENDIF

IF (TFLAG:90 == 36 || TFLAG:90 == 95 || TFLAG:90 == 96 || TFLAG:90 == 97 || TFLAG:90 == 98 || TEQUIP:71) && NOWEX:41 > 0 
	IF TFLAG:18 == 1
		IF TFLAG:30 != 1
			IF TFLAG:106
				PRINTFORMW %CALLNAME:TARGET%被精液填满的的腔内还在微微颤抖，将%CALLNAME:MASTER%的阴茎勒的紧紧地…
			ELSE
				PRINTFORMW %CALLNAME:TARGET%在绝顶的余韵中绽放的腔口溢出了%CALLNAME:MASTER%释放出的精液……
			ENDIF
		ELSE
			IF TFLAG:106
				PRINTFORMW %CALLNAME:TARGET%的腔内微微颤抖，紧紧地缠着刚射完的%CALLNAME:MASTER%的阴茎……
			ELSE
				PRINTFORMW %CALLNAME:TARGET%将绽开的腔口散漫地暴露出来，沉浸在绝顶的余韵中……
			ENDIF
		ENDIF
	ELSE
		PRINTFORMW %CALLNAME:TARGET%沉浸在绝顶的余韵中，回味着腔内阴茎的触感……
	ENDIF
ENDIF

$SKIP

SIF TFLAG:105 < 0
	TFLAG:105 = 0

SIF NOWEX:50
	PRINTSL STR:45
SIF A > 1
	PRINTSL STR:46

