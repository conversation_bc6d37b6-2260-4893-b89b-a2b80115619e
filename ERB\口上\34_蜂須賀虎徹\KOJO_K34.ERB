﻿;-------------------------------------------------
;蜂須賀虎徹口上Ver: 1.00
;-------------------------------------------------
;表記にしたがって記入チェックを書き換えて各項目を編集してください。分岐の増減は自由にできます
;「LOCAL = 0」の0の部分を任意の数字に書き換えてください。また、0のままの分岐は消さなくてもよいです
;指示のない記入チェックは（0,非表示、1,表示）です
;口上は書いたけど表示させたくない場合はチェックのし忘れと区別するためそのことを明記したほうがいいです。

;※※※※※※他者による修正、加筆、改変についての但し書き※※※※※※
;前提としてオリジナル作者以外が手を入れる場合は、オリジナル作者の許可を求めること
;オリジナル作者とコンタクトが取れない場合には以下の但し書きに従ってUPできるかどうか判断してください。

;◎修正（誤字、脱字、条件分岐等ERB構文のバグ修正、本体のバージョンアップ、ダウン対応など）
;（　許可　）※どちらかを選択してください
;
;◎加筆（未記入箇所、ランダム分岐、条件分岐等の増補）
;（　許可　）※どちらかを選択してください
;
;◎改変（オリジナル作者によって書かれた部分に対しての上書き、消去を含む）
;（　許可　）※どちらかを選択してください
;
;-------------------------------------------------
;口上の初回判定は関数「FIRSTTIME」を作り、利用しています
;IFの条件にFIRSTTIME(番号)を入れると、その分岐は一度だけ表示されます
;番号は0～999の範囲で自由に利用してください
;各口上に応じて用意されている範囲内で未使用のものは、予備の番号です
;-------------------------------------------------

;*************************************************
;口上存在判定ついでに口上セレクタ
;*************************************************
@KOJO_k34
;NOが1のキャラとの掛け合い専用、たとえばKOJO_NO1_EVENT_K○_0などが呼ばれる
;SIF NO:MASTER == 1
;	RESULTS = _NO1
;予めCFLAGに適当な値を割り振ればセレクタとしてもつかえる
;IF !CFLAG:1000
;	PRINTL どの口上を用いますか？
;	PRINTL [1] - 口上1
;	PRINTL [2] - 口上2
;	$INPUT_LOOP
;	INPUT
;	IF RESULT = 1 || RESULT == 2
;		CFLAG:1000 = RESULT
;	ELSE
;		GOTO INPUT_LOOP
;	ENDIF
;ENDIF
;RESULTS = _{CFLAG:1000}
;この場合は選択に応じて@KOJO_1_EVENT_K○_0,@KOJO_2_EVENT_K○_0が呼ばれる
;RESULTSに何も代入しなければ@KOJO_EVENT_K○_0が呼ばれる
RETURN 1
;*************************************************
;地の文設定
;*************************************************
;・アクション口上の直前に呼ばれる
;・RETURN 1すると地の文が表示されなくなる
;・個別に設定したい場合はこの関数内でスキップする/しないの判定を行う
;-------------------------------------------------
@KOJO_TRAIN_MESSAGE_SKIP_k34
;構文例
;会話系アクションの地の文をスキップする例
;SIF 0 <= TFLAG:90 && TFLAG:90 <= 9
;	RETURN 1
;バイブ解除時の地の文をスキップする例
;SIF TFLAG:90 == 21 && TFLAG:93 == 1
;	RETURN 1
RETURN 0

;*************************************************
;口上色設定 
;蜂須賀仮文字色9883C9(藤紫)
;*************************************************
;・SETCOLOR命令はＲ(赤)Ｇ(緑)Ｂ(青)の順で文字の表示色を指定できる
;・各値は0～255までの範囲で設定できる。数字が大きいほど、その色の度合いが強くなる
;・色彩についてよくわからなければ「色見本」などで検索してみるのがよい
;・デフォルトは灰色
;・アライメントが負だと色が変わる等、自由に条件で分岐することもできる
;-------------------------------------------------
@KOJO_COLOR_k34
;右のような指定も可能→SETCOLOR 0xA0A0A0
;色見本に#ABCDEFというような書き方がされていた場合は、こちらの書き方(SETCOLOR 0xABCDEF)を使うと良い
SETCOLOR 0x9883C9
;*************************************************
;オープニング口上
;*************************************************
@KOJO_OPENING_k34
;-------------------------------------------------
;記入チェック
LOCAL = 0
;-------------------------------------------------
IF LOCAL
	;調教対象がオトコ
	IF TALENT:MASTER:男性
		PRINTFORMW 
		PRINTFORMW 
	;調教対象がふたなり
	ELSEIF TALENT:MASTER:扶她
		PRINTFORMW 
		PRINTFORMW 
	;調教対象が女
	ELSE
		PRINTFORMW 
		PRINTFORMW 
	ENDIF
	RETURN LOCAL
ENDIF

