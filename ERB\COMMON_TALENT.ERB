﻿;-------------------------------------------------
;汎用関数置き場
;素質関連(素質分類系など)
;-------------------------------------------------

;-------------------------------------------------
;関数名:GET_SEX
;概　要:性別取得関数
;引　数:ARG:0…キャラ登録番号
;戻り値:性別を表す文字列
;備　考:文中関数
;-------------------------------------------------
@GET_SEX(ARG)
#FUNCTIONS
SIF TALENT:ARG:121
	RETURNF TALENTNAME:121
RETURNF @"\@ TALENT:ARG:122 ? %TALENTNAME:122% # 女孩子 \@"


;-------------------------------------------------
;関数名:GET_TALENTNAME
;概　要:素質名取得関数
;引　数:ARG:0…キャラ登録番号
;　　　:ARG:1…TALENT番号
;戻り値:素質名
;備　考:文中関数
;素質の名前を返す。上位素質名の処理を行う
;
;爬虫類とジャバウォックの尻尾はどうしよう？
;-------------------------------------------------
@GET_TALENTNAME(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE ARG:1
	;処女/処女(再生)
	CASE 0
		RETURNF @"%TALENTNAME:0%\@ TALENT:ARG:0 && TALENT:ARG:0 != 1 ? (再生) # \@"
	;巨乳/爆乳
	CASE 109
		RETURNF @"\@ TALENT:ARG:109 == 2 ? 爆乳 # %TALENTNAME:109% \@"
	;陰毛
	CASE 119
		RETURNF @"\@ TALENT:ARG:119 == 2 ? 白虎 # \@"
ENDSELECT
RETURNF TALENTNAME:(ARG:1)


;-------------------------------------------------
;関数名:TALENT_TYPE
;概　要:素質分類関数
;引　数:ARG…TALENT番号
;戻り値:素質分類番号
;       (0.非表示/1.性別/2.性格/3.体質/4.技能/5.後天素質/6.種族特性/7.未分類)
;備　考:文中関数
;素質分類テーブル。素質の分類番号を返す。
;素質整頓表示に用いる
;
;治療って技能じゃなくて体質でいいのかな？
;あと勝手に着衣プレイ好きを後天素質に入れておきました。
;-------------------------------------------------
@TALENT_TYPE(ARG)
#FUNCTION
SELECTCASE ARG
	;0.非表示(表示させない素質)
	CASE 111, 170, 200 TO 230
		RETURNF 0
	;1.性別
	CASE 0, 1, 121, 122
		RETURNF 1
	;2.性格
	CASE 10 TO 17, 20 TO 37, 63, 65, 80 TO 88, 90, 92, 93
		RETURNF 2
	;3.体質
	CASE 40 TO 43, 56, 60 TO 62, 64, 70, 71, 100 TO 110, 112 TO 119, 123 TO 126
		RETURNF 3
	;4.技能
	CASE 50 TO 53, 55, 57 TO 59, 91, 130 TO 132
		RETURNF 4
	;5.後天素質
	CASE 2,3,72 TO 79, 89, 140
		RETURNF 5
	;6.種族特性
	CASE 54, 150 TO 163, 171 TO 177
		RETURNF 6
ENDSELECT
;7.未分類(これがあるのは望ましくない)
RETURNF 7


;-------------------------------------------------
;関数名:TALENT_TYPENAME
;概　要:素質分類名取得関数
;引　数:ARG…TALENT分類番号
;戻り値:素質分類を表す文字列
;       (0.非表示/1.性別/2.性格/3.体質/4.技能/5.後天素質/6.種族特性/7.未分類)
;備　考:文中関数
;素質の分類名を返す。素質整頓表示に用いる
;-------------------------------------------------
@TALENT_TYPENAME(ARG)
#FUNCTIONS
SIF !STRLENS(LOCALS)
	SPLIT "非表示/性別/性格/体质/技能/后天素质/种族特性/未分类", "/", LOCALS
RETURNF LOCALS:ARG
