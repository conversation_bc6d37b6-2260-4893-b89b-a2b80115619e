﻿@DUNGEON_MAP_PRODUCE_CAVE

;FLAG:1800～のそれぞれを10 * 10のパネルと対応させています。
REPEAT 100
A = 1800 + COUNT
FLAG:A = 0
REND

;ビット覚書
;1　侵入不可
;2　不可視
;4　マスター現在位置
;8　敵現在位置
;16　宝箱位置
;32　上り階段位置
;64　下り階段位置
;128　イベントパネル
;256　ダークゾーン
;512　水パネル
;1024　アイテム
;2048　ミミック
;4096　鍛冶屋
;8192　設置トラップ
;16384 ランダムイベント

REPEAT 100
A:COUNT = 0
REND

;侵入不可パネル生成
REPEAT 100
A = 0
B = 0
A = 1800 + RAND:100
;いろいろな形の障害物をばら撒きます。
;2*2の正方形
;A = 1850とすると
; 1850 1851
; 1860 1861
;の形に障害物を設置。FLAG:1850～に障害物フラグのビットを立てる。
;フロアの右と下の端からはみ出ないように。
IF RAND:6 == 0 && A < 1890 && A % 10 < 9

	;障害物が重なると下のように階段などが侵入不可になるかもしれないので
	;□■□
	;■＞■
	;□■□
	;障害物の周りには新たな障害物を置かないようにしています。
	
	;A:13,A:14,A:18,A:19に障害物を設置予定
	;その周り（7,8,9,10,12,15,17,20,22,23,24,25）の位置情報を取得
	;1  2  3  4  5
	;6  7  8  9  10
	;11 12 13 14 15
	;16 17 18 19 20
	;21 22 23 24 25
	
	B = A - 11
	A:7 = FLAG:B
	B = A - 10
	A:8 = FLAG:B
	B = A - 9
	A:9 = FLAG:B
	B = A - 8
	A:10 = FLAG:B
	B = A - 1
	A:12 = FLAG:B
	B = A + 2
	A:15 = FLAG:B
	B = A + 9
	A:17 = FLAG:B
	B = A + 12
	A:20 = FLAG:B
	B = A + 11
	A:19 = FLAG:B
	B = A + 19
	A:22 = FLAG:B
	B = A + 20
	A:23 = FLAG:B
	B = A + 21
	A:24 = FLAG:B
	B = A + 22
	A:25 = FLAG:B
	
	;障害物の周りにすでに侵入不可パネルがあるとCONTINUE
	SIF A:7 & 1 || A:8 & 1 || A:9 & 1 || A:10 & 1 || A:12 & 1 || A:15 & 1 || A:17 & 1 || A:20 & 1 || A:22 & 1 || A:23 & 1 || A:24 & 1 || A:25 & 1 
	CONTINUE

	;障害物を設置
	B = A
	FLAG:B |= 1
	B = A + 1
	FLAG:B |= 1
	B = A + 10
	FLAG:B |= 1
	B = A + 11
	FLAG:B |= 1

ELSEIF RAND:5 == 0 && A < 1890 && A % 10 < 9 && A != 1800 && A != 1888
	B = A - 10
	A:8 = FLAG:B
	B = A - 9
	A:9 = FLAG:B
	B = A - 8
	A:10 = FLAG:B
	B = A - 1
	A:12 = FLAG:B
	B = A
	A:13 = FLAG:B
	B = A + 1
	A:14 = FLAG:B
	B = A + 2
	A:15 = FLAG:B
	B = A + 9
	A:17 = FLAG:B
	B = A + 11
	A:19 = FLAG:B
	B = A + 12
	A:20 = FLAG:B
	B = A + 19
	A:22 = FLAG:B
	B = A + 20
	A:23 = FLAG:B
	B = A + 21
	A:24 = FLAG:B
	
	SIF A:8 & 1 || A:9 & 1 || A:10 & 1 || A:12 & 1 || A:13 & 1 || A:15 & 1 || A:17 & 1 || A:19 & 1 || A:20 & 1 || A:22 & 1 || A:23 & 1 || A:24 & 1 
	CONTINUE
	
	B = A + 1
	FLAG:B |= 1
	B = A + 10
	FLAG:B |= 1

ELSEIF RAND:4 == 0 && A < 1890 && A % 10 < 9 && A != 1880 && A != 1808
	B = A - 11
	A:7 = FLAG:B
	B = A - 10
	A:8 = FLAG:B
	B = A - 9
	A:9 = FLAG:B
	B = A - 1
	A:12 = FLAG:B
	B = A + 1
	A:14 = FLAG:B
	B = A + 2
	A:15 = FLAG:B
	B = A + 9
	A:17 = FLAG:B
	B = A + 10
	A:18 = FLAG:B
	B = A + 12
	A:20 = FLAG:B
	B = A + 20
	A:23 = FLAG:B
	B = A + 21
	A:24 = FLAG:B
	B = A + 22
	A:25 = FLAG:B
	
	SIF A:7 & 1 || A:8 & 1 || A:9 & 1 || A:12 & 1 || A:14 & 1 || A:15 & 1 || A:17 & 1 || A:18 & 1 || A:20 & 1 || A:23 & 1 || A:24 & 1 || A:25 & 1 
	CONTINUE
	
	B = A
	FLAG:B |= 1
	B = A + 11
	FLAG:B |= 1


ELSEIF RAND:3 == 0 && A < 1890
	B = A - 11
	A:7 = FLAG:B
	B = A - 10
	A:8 = FLAG:B
	B = A - 9
	A:9 = FLAG:B
	B = A - 1
	A:12 = FLAG:B
	B = A + 1
	A:14 = FLAG:B
	B = A + 9
	A:17 = FLAG:B
	B = A + 11
	A:19 = FLAG:B
	B = A + 19
	A:22 = FLAG:B
	B = A + 20
	A:23 = FLAG:B
	B = A + 21
	A:24 = FLAG:B
	
	SIF A:7 & 1 || A:8 & 1 || A:9 & 1 || A:12 & 1 || A:14 & 1 || A:17 & 1 || A:19 & 1 || A:22 & 1 || A:23 & 1 || A:24 & 1 
	CONTINUE
	
	B = A
	FLAG:B |= 1
	B = A + 10
	FLAG:B |= 1


ELSEIF RAND:2 == 0 && A % 10 < 9
	B = A - 11
	A:7 = FLAG:B
	B = A - 10
	A:8 = FLAG:B
	B = A - 9
	A:9 = FLAG:B
	B = A - 8
	A:10 = FLAG:B
	B = A - 1
	A:12 = FLAG:B
	B = A + 2
	A:15 = FLAG:B
	B = A + 9
	A:17 = FLAG:B
	B = A + 10
	A:18 = FLAG:B
	B = A + 11
	A:19 = FLAG:B
	B = A + 12
	A:20 = FLAG:B
	
	SIF A:7 & 1 || A:8 & 1 || A:9 & 1 || A:10 & 1 || A:12 & 1 || A:15 & 1 || A:17 & 1 || A:18 & 1 || A:19 & 1 || A:20 & 1 
	CONTINUE
	
	B = A
	FLAG:B |= 1
	B = A + 1
	FLAG:B |= 1


ELSE
	B = A - 11
	A:7 = FLAG:B
	B = A - 10
	A:8 = FLAG:B
	B = A - 9
	A:9 = FLAG:B
	B = A - 1
	A:12 = FLAG:B
	B = A
	A:13 = FLAG:B
	B = A + 1
	A:14 = FLAG:B
	B = A + 9
	A:17 = FLAG:B
	B = A + 10
	A:18 = FLAG:B
	B = A + 11
	A:19 = FLAG:B

	SIF A:7 & 1 || A:8 & 1 || A:9 & 1 || A:12 & 1 || A:13 & 1 || A:14 & 1 || A:17 & 1 || A:18 & 1 || A:19 & 1  
	CONTINUE

	FLAG:A |= 1
ENDIF

REND

;アイテム生成
REPEAT (5 + FLAG:3137)
	A = 0
	B = 0
	A = 1800 + RAND:100
	SIF FLAG:A & 1
		CONTINUE

	FLAG:A |= 16
	SIF RAND:5 == 0
		FLAG:A |= 2048
REND

REPEAT RAND:4 + 2 + FLAG:3137 
	A = 0
	B = 0
	A = 1800 + RAND:100
	SIF FLAG:A & 1
		CONTINUE
	SIF FLAG:A & 16
		CONTINUE
	FLAG:A |= 1024
REND

;マスター現在位置
LOCAL = 1
WHILE LOCAL
A = 1800 + RAND:100
SIF FLAG:A & 1
	CONTINUE
FLAG:A |= 4
LOCAL = 0
WEND
;一階マスターの四方にイベント設置
IF FLAG:1704 == 1 && !(FLAG:1741 & 1)
	FOR LOCAL, 0, 4
		SELECTCASE LOCAL
			CASE 0
				SIF A - 10 < 0 || FLAG:(A - 10) & 1
					CONTINUE
				FLAG:(A - 10) |= 128
				BREAK
			CASE 1
				SIF A + 1 > 99 || (A % 10 == 9) ||  FLAG:(A + 1) & 1
					CONTINUE
				FLAG:(A + 1) |= 128
				BREAK
			CASE 2
				SIF A + 10 > 99 || FLAG:(A + 10) & 1
					CONTINUE
				FLAG:(A + 10) |= 128
				BREAK
			CASE 3
				SIF A - 1 < 0 || (A % 10 == 0) || FLAG:(A - 1) & 1
					CONTINUE
				FLAG:(A - 1) |= 128
				BREAK
		ENDSELECT
	NEXT
ENDIF

;階段位置
LOCAL = 1
WHILE LOCAL
A = 1800 + RAND:100
SIF FLAG:A & 1 || FLAG:A & 4 || FLAG:A & 16 || FLAG:A & 1024
	CONTINUE
FLAG:A |= 64
LOCAL = 0
WEND

;ランダムイベント
IF FLAG:1704 % 5 != 0 && FLAG:1704 != 1
	LOCAL = 1
	WHILE LOCAL
		A = 1800 + RAND:100
		SIF FLAG:A & 1 || FLAG:A & 4 || FLAG:A & 16 || FLAG:A & 1024 || FLAG:A & 64
			CONTINUE
		FLAG:A |= 128
		LOCAL = 0
	WEND
ENDIF

;敵生成
X = 3 + RAND:3
LOCAL = X
WHILE LOCAL
	A = 0
	B = 0
	A = 1800 + RAND:100
	SIF FLAG:A & 1
		CONTINUE
	LOCAL -= 1
	FLAG:A |= 8
	;確率で鍛冶屋に
	SIF RAND:20 == 0
		FLAG:A |= 4096
WEND
;設置トラップ
FOR LOCAL,0,5
	A = 1800 + RAND:100
	SIF FLAG:A == 0
		FLAG:A |= 8192
NEXT
;ランダムイベント
LOCAL = 1800 + RAND:100
SIF RAND:10 == 0 && FLAG:LOCAL == 0
	FLAG:A |= 16384
