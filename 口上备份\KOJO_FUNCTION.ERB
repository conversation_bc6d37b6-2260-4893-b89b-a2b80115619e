﻿;≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
;口上向け関数ファイル
;またの名をぱんくしょんRev…口上パッチ作者が好き勝手やるファイルです
;※Emuera専用のためサブフォルダに配置すること※
;≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
;-------------------------------------------------
;助手と交代関数@ASSISWAP
;調教途中にTARGETとASSIを交代させるときに使います
;-------------------------------------------------
@ASSISWAP
;TARGETが管理しているMASTERのデータを移動
;そもそもMASTERのデータがTARGETで管理されている事自体がアレな気もするが、まぁ仕方ない
;調教対象が装備しているTEQUIPを移し変えます
FOR LOCAL, 10, 100
	SWAP TEQUIP:LOCAL, TEQUIP:ASSI:LOCAL
NEXT
;調教対象のPALAM, SOURCE, EX, NOWEXを移し変えます
FOR LOCAL, 0, 40
	SWAP PALAM:LOCAL,  PALAM:ASSI:LOCAL
	SWAP SOURCE:LOCAL, SOURCE:ASSI:LOCAL
	SWAP EX:LOCAL,     EX:ASSI:LOCAL
	SWAP NOWEX:LOCAL,  NOWEX:ASSI:LOCAL
NEXT
SWAP TARGET, ASSI
;ASSISWAP中フラグ(他関数からの参照用)
LOCAL:1 = !LOCAL:1
;LOCAL@～に対する私見：
;・非推奨とはいえ実際どこからでもアクセス出来るんだから仕方ないっちゃ仕方ない。将来的に廃止するならともかく
;・そもそもそれが可能だということは初心者にとって一般的でもないし衝突もしにくいと思うので、実際のところ一文字変数ほど変数破壊の危険度は少ないかと。
;　もちろん可能性が0ではないというのは承知の上
;・というかLOCAL@～への代入さえしなければそこそこ安全に使えると思うの。いっそ禁止にしたら…それは本来の用途に反するからダメか。Lv0警告とかもダメ？
;　(とはいえ注意しないと参照だけでもスパゲッティになるので関連の強い関数や同一ERBの中でしか利用しない方が賢明だと思います。他所で使うならきちんとフラグ化した方が)
;・作業用の一時変数や隠蔽すべき情報にまでTFLAGとか使う方が不思議に思うの。TFLAGのリストに大量のゴミが入ってみんなが面倒になるだけじゃない？
;　それならまだ一文字変数使ってでもシステムの中に隠した方がマシというか親切だと思う。そして一文字変数よりはLOCAL@～の方がマシ
;・用途外利用？普通でしょう。偉大な先人たちはバグですら有効に活用してきたと思っております
;・というわけで、纏めるとLOCAL@～の完全排除＞誰得？と思っていたりします。損することはあれ、誰も得をしないんじゃないかと。
;　得するなら多少損しても良いのですが…異論は大いに認めるのでその際はお任せします。
;　他所まで出て行って主張する気もないし、何より私はそんなに頭がよくないのでどえらい勘違いをしている可能性も否定できません
;・これでもいい具合に何とかした方なんです…配列渡しにLOCAL@～使っていた箇所は設計を見直してなんとか上手い形に収まりました。SPLIT最高です
;・ここのフラグはシステム内部で隠蔽したいのです。TARGETとASSIが入れ替わるけど、外部ではそれを意識することなく事が運ぶようにしたいので。
;　フラグにしてもいいけど、わざわざフラグにする理由が今のところ無い。そういうの、あるでしょう？
;・フラグ確保および管理がメンドイ、というのも少しあるｗ

;-------------------------------------------------
;未読判定関数@FIRSTTIME
;式中で使用する関数です。およそVARSIZE(LOCALS)^2個のイベントを登録出来ます
;ARGSはイベント番号、ARGは真だと初回判定を更新しない、ARG:1はキャラ登録番号（省略するとTARGET,-1ならMASTER）
;{イベント番号}/　という文字列をCSTR:0にぶっこんでいきます
;-------------------------------------------------
@FIRSTTIME(ARGS,ARG, ARG:1)
#FUNCTION
;ARG:1の変換
ARG:1 = ARG:1 ? ARG:1 # TARGET
ARG:1 = ARG:1 == -1 ? MASTER # ARG:1
SIF CSTR:(ARG:1):0 == ""
	CSTR:(ARG:1):0 = /

LOCALS = /%ARGS%/
IF STRCOUNT(CSTR:(ARG:1):0,LOCALS)
	RETURNF 0
ELSEIF ARG
	RETURNF 1
ENDIF
;初回判定更新
CSTR:(ARG:1):0 = %CSTR:(ARG:1):0%%ARGS%/
RETURNF 1

;-------------------------------------------------
;未読判定関数@ONCE
;式中で使用する関数です
;ARGSはイベント番号、ARGは真だと初回判定を更新しない、ARG:1は真だとフラグのオールリセットを行うARG:2はキャラ登録番号（省略するとTARGET,-1ならMASTER）
;{イベント番号}/　という文字列をCSTR:1にぶっこんでいきます
;-------------------------------------------------
@ONCE(ARGS,ARG, ARG:1,ARG:2)
#FUNCTION
;ARG:2の変換
ARG:2 = ARG:2 ? ARG:2 # TARGET
ARG:2 = ARG:2 == -1 ? MASTER # ARG:2
IF ARG:1
	CSTR:(ARG:2):1 = 
	RETURNF 0
ENDIF
SIF CSTR:(ARG:2):1 == ""
	CSTR:(ARG:2):1 = /

LOCALS = /%ARGS%/
IF STRCOUNT(CSTR:(ARG:2):1,LOCALS)
	RETURNF 0
ELSEIF ARG
	RETURNF 1
ENDIF
;初回判定更新
CSTR:(ARG:2):1 = %CSTR:(ARG:2):1%%ARGS%/
RETURNF 1
;フラグリセット処理
@EVENTTRAIN
FOR LOCAL,0,CHARANUM
	CSTR:LOCAL:1 = 
NEXT


;-------------------------------------------------
;マッチング関数@MATCH2
;式中で使用する関数です。引数0の値が与えられた値のいずれかにマッチした場合1を返します
;0とのマッチングを行う場合は、0を最後の引数にしてはいけません
;引数0はマッチング対象、引数1～20まで引き受け可能
;Emuera1734tよりMATCHが予約語になったため関数名変更
;-------------------------------------------------
@MATCH2(ARG:20, ARG:0, ARG:1, ARG:2, ARG:3, ARG:4, ARG:5, ARG:6, ARG:7, ARG:8, ARG:9, ARG:10, ARG:11, ARG:12, ARG:13, ARG:14, ARG:15, ARG:16, ARG:17, ARG:18, ARG:19)
#FUNCTION
FOR LOCAL, 19, -1, -1
	SIF ARG:LOCAL
		BREAK
NEXT
FOR LOCAL:1, 0, LOCAL+1
	SIF ARG:20 == ARG:(LOCAL:1)
		RETURNF 1
NEXT
RETURNF 0


;-------------------------------------------------
;真値集計関数@TRUECHECK
;式中で使用する関数です。引数の中で正であるものの数を返します（負はカウントしません）
;引数0～19まで引き受け可能
;-------------------------------------------------
@TRUECHECK(ARG:0, ARG:1, ARG:2, ARG:3, ARG:4, ARG:5, ARG:6, ARG:7, ARG:8, ARG:9, ARG:10, ARG:11, ARG:12, ARG:13, ARG:14, ARG:15, ARG:16, ARG:17, ARG:18, ARG:19)
#FUNCTION
LOCAL:1 = 0
FOR LOCAL, 0, 20
	SIF ARG:LOCAL > 0
		LOCAL:1++
NEXT
RETURNF LOCAL:1


;-------------------------------------------------
;ペニス判定関数@PENIS
;式中で使用する関数です。ペニスがある場合1を返します
;引数0は対象の登録番号。しょうもない中身
;-------------------------------------------------
@PENIS(ARG)
#FUNCTION
RETURNF TALENT:ARG:121 || TALENT:ARG:122


;-------------------------------------------------
;ペニスバンド判定関数@PENISBAND
;式中で使用する関数です。ペニスバンド使用中1を返します
;-------------------------------------------------
@PENISBAND(ARG)
#FUNCTION
RETURNF !(TALENT:ARG:121 || TALENT:ARG:122) && ITEM:3


;-------------------------------------------------
;確率判定関数@PERCENT
;式中で使用する関数です。引数％の確率判定を行います。通れば1を返します
;-------------------------------------------------
@PERCENT(ARG)
#FUNCTION
RETURNF (ARG - RAND:100) > 0


;-------------------------------------------------
;体位判定関数@POSITION
;式中で使用する関数です。引数をTFLAG:90とした場合の体位をTEQUIP:70/TEQUIP:71準拠で返します
;-------------------------------------------------
@POSITION(ARG)
#FUNCTION
SELECTCASE ARG
	;正常位/後背位/騎乗位/対面座位/背面座位/アナルセックス
	CASE 30 TO 35
		RETURNF ARG - 29
	;逆レイプ
	CASE 36
		RETURNF 3
	;正常位させる/後背位させる
	CASE 95, 96
		RETURNF ARG - 94
	;対面座位させる/背面座位させる/アナルセックスさせる
	CASE 97 TO 99
		RETURNF ARG - 93
ENDSELECT
RETURNF 0


;-------------------------------------------------
;アライメント判定関数@ALI
;式中で使用する関数です。アライメントが引数以上なら真になります
;-------------------------------------------------
@ALI(ARG)
#FUNCTION
RETURNF CFLAG:6 >= ARG


;-------------------------------------------------
;好感度判定関数@FAVOR
;式中で使用する関数です。好感度が指定以上なら真になります。ただし、0にした場合は条件に含みません
;引数0は調教対象から調教者、引数1は調教者から調教対象の好感度です
;-------------------------------------------------
@FAVOR(ARG,ARG:1)
#FUNCTION
RETURNF (!ARG || CFLAG:2 >= ARG) && (!ARG:1 || CFLAG:9 >= ARG:1)


;-------------------------------------------------
;ベース割合算出関数@BASERATIO
;式中で使用する関数です。ベース値がMAXBASEの何％あるか返します
;引数0は登録番号、引数1はベース番号、引数2を設定した場合はその％以上なら真を返します
;-------------------------------------------------
@BASERATIO(ARG:0, ARG:1, ARG:2)
#FUNCTION
RETURNF ARG:2 ? BASE:(ARG:0):(ARG:1) * 100 / MAXBASE:(ARG:0):(ARG:1) >= ARG:2 # BASE:(ARG:0):(ARG:1) * 100 / MAXBASE:(ARG:0):(ARG:1)


;-------------------------------------------------
;助手素質判定関数@ASSITALENT
;式中で使用する関数です。助手の存在を確認してから、引数番号のTALENTの数値を返します
;条件式簡略化のため、助手がいない場合も0を返します
;-------------------------------------------------
@ASSITALENT(ARG)
#FUNCTION
RETURNF ASSI >= 0 ? TALENT:ASSI:ARG # 0


;-------------------------------------------------
;助手能力判定関数@ASSIABL
;式中で使用する関数です。助手の存在を確認してから、引数番号のABLの数値を返します
;条件式簡略化のため、助手がいない場合も0を返します
;-------------------------------------------------
@ASSIABL(ARG)
#FUNCTION
RETURNF ASSI >= 0 ? ABL:ASSI:ARG # 0


;-------------------------------------------------
;助手CFLAG判定関数@ASSICFLAG
;式中で使用する関数です。助手の存在を確認してから、引数番号のCFLAGの数値を返します
;条件式簡略化のため、助手がいない場合も0を返します
;-------------------------------------------------
@ASSICFLAG(ARG)
#FUNCTION
RETURNF ASSI >= 0 ? CFLAG:ASSI:ARG # 0


;-------------------------------------------------
;助手番号判定関数@ASSINO
;式中で使用する関数です。助手の存在を確認してから、助手のNOを返します
;助手がいない場合は-1を返します
;-------------------------------------------------
@ASSINO(ARG)
#FUNCTION
RETURNF ASSI >= 0 ? NO:ASSI # -1


;-------------------------------------------------
;行動名表示関数@ACTSTR
;式中で使用する関数です。引数番号の調教者アクション名を文字列で返します
;引数1が0だとトラウマの種用に、「浣腸器＋プラグ」など無機なものは有機的な呼称に直します
;
;なんとなく追加したけどステキな表現が出来なかった…ごめんよ…
;この手の関数は必要ならキャラ別に持つことも検討した方がいいよ！
;汎用的にはしたつもりだけど、キャラや性格によって口調に合う単語・合わない単語ってあるよね
;-------------------------------------------------
@ACTSTR(ARG:0, ARG:1)
#FUNCTIONS
IF !ARG:1
	SELECTCASE ARG:0
		;今の気分を聞く/自分への気持ちを聞く/合意を求める
		CASE 0, 1, 6
			RETURNF "変な質問"
		;性についての話をする
		CASE 2
			RETURNF "いやらしい話"
		;優しく慰める
		CASE 3
			RETURNF "気休め"
		;厳しく脅す/怒鳴る/不気味に笑う
		CASE 4, 8, 9
			RETURNF "脅かすの"
		;衣装の変更を要求する
		CASE 5
			RETURNF "脱がされるの"
		;お仕置きと宣言する
		CASE 7
			RETURNF "惩罚"
		;手で愛撫/胸愛撫/性器を擦り合う/パイズリする/足コキする
		CASE 10, 11, 16 TO 18
			RETURNF "変な触り方"
		;口で愛撫
		CASE 12
			RETURNF "舐められるの"
		;アナル愛撫/アナル舐め
		CASE 13, 14
			RETURNF "後ろ弄るの"
		;Ｃ愛撫道具
		CASE 24
			RETURNF @"\@ PENIS(MASTER) ? オナホ # 阴蒂夹 \@"
		;押し倒す
		CASE 27
			RETURNF "押し倒されるの"
		;正常位/対面座位/逆レイプ
		CASE 30, 33, 36
			RETURNF "犯されるの"
		;後背位/背面座位
		CASE 31, 34
			RETURNF "後ろから犯されるの"
		;アナルセックス
		CASE 35
			RETURNF "後ろを犯されるの"
		;自慰
		CASE 40
			RETURNF "オナニー"
		;秘貝開帳
		CASE 41
			RETURNF "広げるの"
		;自慰みせつけ
		CASE 42
			RETURNF "オナニー見せられるの"
		;羞恥プレイ
		CASE 43
			RETURNF "鏡"
		;野外プレイ
		CASE 44
			RETURNF "外"
		;放尿
		CASE 45
			RETURNF "漏らすの"
		;手で愛撫を強制
		CASE 50
			RETURNF "触るの"
		;口で愛撫を強制
		CASE 51
			RETURNF "舐めるの"
		;足舐め強制
		CASE 55
			RETURNF "足舐め"
		;胸愛撫強制
		CASE 57
			RETURNF "おっぱい"
		;スパンキング
		CASE 60
			RETURNF "叩かれるの"
		;縄
		CASE 63
			RETURNF "縛られるの"
		;アイマスク
		CASE 64
			RETURNF "目隠し"
		;罵倒
		CASE 66
			RETURNF "言葉で責めるの"
		;浣腸器＋プラグ
		CASE 68
			RETURNF "灌肠"
		;フィストファック/アナルフィスト/両穴フィスト
		CASE 70 TO 72
			RETURNF "フィスト"
		;公衆肉便器プレイ
		CASE 74
			RETURNF "公共厕所"
		;触手召喚
		CASE 80
			RETURNF "触手"
		;休ませる
		CASE 90
			RETURNF "休憩"
		;栄養剤を飲ませる
		CASE 91
			RETURNF "変な薬"
		;治療する
		CASE 92
			RETURNF "治疗"
		;正常位させる/後背位させる/対面座位させる/背面座位させる
		CASE 95 TO 98
			RETURNF "セックスさせられるの"
		;アナルセックスさせる
		CASE 99
			RETURNF "後ろに入れさせられるの"
	ENDSELECT
ENDIF
RETURNF STR:(ARG + 100)


;-------------------------------------------------
;妨害服装関数@CLOTHESTYPE
;式中で使用する関数です。強度と言っても破くわけではなく、どれくらい行為の邪魔になるかを指します。
;引数1が0なら下半身下着
;戻り値	1=履いたまま性交できる				(パンティ/コスプレ)
;		2=履いてると挿入しにくい			(ホットパンツ/ドロワーズ/トランクス)
;引数が1なら下半身上着
;戻り値	1=股部分がなく、ほぼ邪魔にならない	(スカート/ワンピース/レオタード/コスプレ)
;		2=邪魔になる						(ズボン/ズボン)
;引数が2なら上半身下着
;戻り値	1=柔らかく弄りやすい				(コスプレ)
;		2=弄りにくい						(ブラジャー)
;引数が3なら上半身上着
;戻り値	1=裾から手が入る					(上着/コスプレ)
;		2=手が入らない						(ワンピース/レオタード)
;いずれも、戻り値0だと着ていないことを表す	(ボンデージ)
;引数0は0だとTARGET、1ならMASTER
;
;コスプレは意見の分かれるところだと思いますが…
;わざわざ弄りにくい服着せてから弄るというのは、個人的にはいささか合理性に欠く気がしたので…
;(合理性で割り切れない面があるのは重々承知の上ですが)
;きっと弄りやすいカスタム衣装なんだということにします。特にボンデージは。
;異論は受け付けます。
;-------------------------------------------------
@CLOTHESTYPE(ARG,ARG:1)
#FUNCTION
;引数が正常でない場合は-1を返す
SIF ARG < 0 || 1 < ARG || ARG:1 < 0 || 3 < ARG:1
	RETURNF -1
SELECTCASE ARG:1
	;下半身下着
	CASE 0
		RETURNF LOWERUNDER(ARG)
	;下半身上着
	CASE 1
		RETURNF LOWERJACKET(ARG)
	;上半身下着
	CASE 2
		RETURNF UPPERUNDER(ARG)
	;上半身上着
	CASE 3
		RETURNF UPPERJACKET(ARG)
ENDSELECT


;-------------------------------------------------
;下半身下着関数@LOWERUNDER
;戻り値	0=はいてない
;		1=履いたまま性交できる				(パンティ/コスプレ)
;		2=履いてると挿入しにくい			(ホットパンツ/ドロワーズ/トランクス)
;引数は0だとTARGET、1ならMASTER
;-------------------------------------------------
@LOWERUNDER(ARG)
#FUNCTION
LOCAL = ARG ? MASTER # TARGET
;引数が正常でない場合は-1を返す
SIF ARG < 0 || 1 < ARG
	RETURNF -1
;コスプレ
SELECTCASE TEQUIP:LOCAL:9
	;ボンデージ
	CASE 1
		RETURNF 0
	;その他
	CASE IS != 0
		RETURNF 1
ENDSELECT
;下半身下着
SIF TEQUIP:LOCAL:2 == 1
	RETURNF 1
RETURNF TEQUIP:LOCAL:2 ? 2 # 0


;-------------------------------------------------
;上半身下着関数@UPPERUNDER
;戻り値	0=はいてない
;		1=柔らかく弄りやすい				(コスプレ)
;		2=弄りにくい						(ブラジャー)
;引数は0だとTARGET、1ならMASTER
;-------------------------------------------------
@UPPERUNDER(ARG)
#FUNCTION
LOCAL = ARG ? MASTER # TARGET
;引数が正常でない場合は-1を返す
SIF ARG < 0 || 1 < ARG
	RETURNF -1
;コスプレ
SELECTCASE TEQUIP:LOCAL:9
	;ボンデージ
	CASE 1
		RETURNF 0
	;その他
	CASE IS != 0
		RETURNF 1
ENDSELECT
;上半身下着
RETURNF TEQUIP:LOCAL:3 ? 2 # 0


;-------------------------------------------------
;下半身上着関数@LOWERJACKET
;戻り値	0=はいてない
;		1=股部分がなく、ほぼ邪魔にならない	(スカート/ワンピース/レオタード/コスプレ)
;		2=邪魔になる						(ズボン/ズボン)
;引数は0だとTARGET、1ならMASTER
;-------------------------------------------------
@LOWERJACKET(ARG)
#FUNCTION
LOCAL = ARG ? MASTER # TARGET
;引数が正常でない場合は-1を返す
SIF ARG < 0 || 1 < ARG
	RETURNF -1
;コスプレ
SELECTCASE TEQUIP:LOCAL:9
	;ボンデージ
	CASE 1
		RETURNF 0
	;その他
	CASE IS != 0
		RETURNF 1
ENDSELECT
;全身上着
SIF TEQUIP:LOCAL:6
	RETURNF 1
;下半身上着
SIF TEQUIP:LOCAL:4 == 1
	RETURNF 1
RETURNF TEQUIP:LOCAL:4 ? 2 # 0


;-------------------------------------------------
;上半身上着関数@UPPERJACKET
;戻り値	0=はいてない
;		1=裾などから手が入る				(上着/コスプレ)
;		2=手が入らない						(ワンピース/レオタード)
;引数は0だとTARGET、1ならMASTER
;-------------------------------------------------
@UPPERJACKET(ARG)
#FUNCTION
LOCAL = ARG ? MASTER # TARGET
;引数が正常でない場合は-1を返す
SIF ARG < 0 || 1 < ARG
	RETURNF -1
;コスプレ
SELECTCASE TEQUIP:LOCAL:9
	;ボンデージ
	CASE 1
		RETURNF 0
	;その他
	CASE IS != 0
		RETURNF 1
ENDSELECT
;全身上着
SIF TEQUIP:LOCAL:6
	RETURNF 2
;上半身上着
RETURNF TEQUIP:LOCAL:5 ? 1 # 0


;-------------------------------------------------
;下半身服装判定関数@PANTSCHECK
;式中で使用する関数です。下半身の服装が現在どうなっているか判定します
;「着ているか」ではなく「愛撫の妨げになるか」を返すので、たとえばスカートは履いててもスルーされます
;戻り値は0なら触り放題、1なら下着、2なら上着、3なら両方が邪魔をします。
;引数は0だとTARGET、1ならMASTER。
;-------------------------------------------------
@PANTSCHECK(ARG)
#FUNCTION
;引数が正常でない場合は-1を返す
SIF ARG < 0 || 1 < ARG
	RETURNF -1
LOCAL = 0
LOCAL |= LOWERUNDER(ARG)  ? 1 # 0
LOCAL |= LOWERJACKET(ARG) ? 2 # 0
RETURNF LOCAL


;-------------------------------------------------
;上半身服装判定関数@BRACHECK
;式中で使用する関数です。上半身の服装が現在どうなっているか判定します
;下半身と違い、やわらかい服でも着ていれば判定に引っかかります
;戻り値は0なら触り放題、1なら下着、2なら上着、3なら両方が邪魔をします。
;引数は0だとTARGET、1ならMASTER。
;-------------------------------------------------
@BRACHECK(ARG)
#FUNCTION
;引数が正常でない場合は-1を返す
SIF ARG < 0 || 1 < ARG
	RETURNF -1
LOCAL = 0
LOCAL |= UPPERUNDER(ARG)  ? 1 # 0
LOCAL |= UPPERJACKET(ARG) ? 2 # 0
RETURNF LOCAL


;-------------------------------------------------
;全裸判定関数@ZENRACHECK
;式中で使用する関数です。脱衣アクション口上でのみ使用します
;この脱衣で全裸になるかどうかを判定し、なる場合のみ1を返します。
;引数は0だとTARGET、1ならMASTER
;-------------------------------------------------
@ZENRACHECK(ARG)
#FUNCTION
LOCAL = ARG ? MASTER # TARGET
;引数が正常でない場合は0を返す
SIF ARG < 0 || 1 < ARG || (!ARG && LOCAL@ASSISWAP:1)
	RETURNF 0
;脱衣失敗
SIF ARG && TFLAG:102 < 0
	RETURNF 0
SELECTCASE ARG ? TFLAG:102 # TFLAG:103
	;上半身上着
	CASE 1
		RETURNF !(TEQUIP:LOCAL:0 || TEQUIP:LOCAL:1 || TEQUIP:LOCAL:2 || TEQUIP:LOCAL:3 || TEQUIP:LOCAL:4)
	;下半身上着
	CASE 2
		RETURNF !(TEQUIP:LOCAL:0 || TEQUIP:LOCAL:1 || TEQUIP:LOCAL:2 || TEQUIP:LOCAL:3 || TEQUIP:LOCAL:5)
	;上＋下上着
	CASE 3
		RETURNF !(TEQUIP:LOCAL:0 || TEQUIP:LOCAL:1 || TEQUIP:LOCAL:2 || TEQUIP:LOCAL:3)
	;上半身全部
	CASE 4
		RETURNF !(TEQUIP:LOCAL:0 || TEQUIP:LOCAL:1 || TEQUIP:LOCAL:2 || TEQUIP:LOCAL:4)
	;下半身全部
	CASE 5
		RETURNF !(TEQUIP:LOCAL:0 || TEQUIP:LOCAL:1 || TEQUIP:LOCAL:3 || TEQUIP:LOCAL:5)
	;アクセサリー
	CASE 6
		RETURNF !(TEQUIP:LOCAL:2 || TEQUIP:LOCAL:3 || TEQUIP:LOCAL:4 || TEQUIP:LOCAL:5 || TEQUIP:LOCAL:6)
	;アクセサリ－まで
	CASE 7
		RETURNF TEQUIP:LOCAL:0 !| TEQUIP:LOCAL:1
	;全裸まで
	CASE 8
		RETURNF 1
	;下着だけ
	CASE 9
		RETURNF !(TEQUIP:LOCAL:0 || TEQUIP:LOCAL:1 || TEQUIP:LOCAL:4 || TEQUIP:LOCAL:5 || TEQUIP:LOCAL:6)
ENDSELECT
RETURNF 0


;-------------------------------------------------
;胸見せ判定関数@B_DATUICHECK
;式中で使用する関数です。脱衣アクション口上でのみ使用します
;この脱衣で胸を見せるかどうかを判定し、ブラまで見せる場合は2、おっぱいうｐの場合は3を返します
;最初から胸が見えている場合は1になります
;引数は0だとTARGET、1ならMASTER
;-------------------------------------------------
@B_DATUICHECK(ARG)
#FUNCTION
LOCAL = ARG ? MASTER # TARGET

;引数が正常でない場合は0を返す
SIF ARG < 0 || 1 < ARG || (!ARG && LOCAL@ASSISWAP:1)
	RETURNF 0
;脱衣失敗
SIF ARG && TFLAG:102 < 0
	RETURNF 0

;最初から胸が見えている場合
SIF !(TEQUIP:LOCAL:3 || TEQUIP:LOCAL:5 || TEQUIP:LOCAL:6) || (TEQUIP:88 & (ARG ? 8 # 2))
	RETURNF 1
SELECTCASE ARG ? TFLAG:102 # TFLAG:103
	;上半身上着
	CASE 1
		RETURNF TEQUIP:LOCAL:3 ? 2 # 3
	;上＋下上着(上半身上着/全身上着を既に脱いでいる場合は露出済みなのでスルー)
	CASE 3
		SIF TEQUIP:LOCAL:5 || TEQUIP:LOCAL:6
			RETURNF TEQUIP:LOCAL:3 ? 2 # 3
	;上半身全部/アクセサリ－まで/全裸まで
	CASE 4, 7, 8
		RETURNF 3
	;下着だけ(上半身上着/全身上着を既に脱いでいる場合のみおっぱいうｐ)
	CASE 9
		SIF TEQUIP:LOCAL:5 !| TEQUIP:LOCAL:6
			RETURNF 3
ENDSELECT
RETURNF 0


;-------------------------------------------------
;性器見せ判定関数@P_DATUICHECK
;式中で使用する関数です。脱衣アクション口上でのみ使用します
;この脱衣でパンツ・性器を見せるかどうかを判定し、ぱんつまで見せる場合は2、性器まで見せる場合は3を返します
;最初から性器が見えている場合は1になります
;引数は0だとTARGET、1ならMASTER
;-------------------------------------------------
@P_DATUICHECK(ARG)
#FUNCTION
LOCAL = ARG ? MASTER # TARGET
;引数が正常でない場合は0を返す
SIF ARG < 0 || 1 < ARG || (!ARG && LOCAL@ASSISWAP:1)
	RETURNF 0
;脱衣失敗
SIF ARG && TFLAG:102 < 0
	RETURNF 0
;最初から性器が見えている場合
SIF !(TEQUIP:LOCAL:2 || TEQUIP:LOCAL:4 || TEQUIP:LOCAL:6) || (TEQUIP:88 & (ARG ? 4 # 1))
	RETURNF 1
SELECTCASE ARG ? TFLAG:102 # TFLAG:103
	;下半身上着
	CASE 2
		RETURNF TEQUIP:LOCAL:2 ? 2 # 3
	;上＋下上着(下半身上着/全身上着を既に脱いでいる場合は露出済みなのでスルー)
	CASE 3
		SIF TEQUIP:LOCAL:4 || TEQUIP:LOCAL:6
			RETURNF TEQUIP:LOCAL:2 ? 2 # 3
	;下半身全部/アクセサリ－まで/全裸まで
	CASE 5, 7, 8
		RETURNF 3
	;下着だけ(下半身上着/全身上着を既に脱いでいる場合のみ性器うｐ)
	CASE 9
		SIF TEQUIP:LOCAL:4 !| TEQUIP:LOCAL:6
			RETURNF 3
ENDSELECT
RETURNF 0
