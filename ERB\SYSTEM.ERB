﻿;=============================================================================
;標題畫面
;=============================================================================
@SYSTEM_TITLE
CALL SPACE
HTML_PRINT "<p align='center'><img src='TITLE' width='6000' height='3000' ypos='-3000'>"
ALIGNMENT CENTER
SETCOLOR 240,50,0
PRINTFORML %GAMEBASE_TITLE%
RESETCOLOR
PRINTFORML Author:%GAMEBASE_AUTHOR%
PRINTFORML Ver0.149
PRINTFORML %GAMEBASE_INFO%
PRINTBUTTON " 0[最初开始]", 0
PRINTL 
PRINTBUTTON " 1[继续游戏]", 1
PRINTL 
ALIGNMENT LEFT
$INPUT_LOOP
INPUT
IF RESULT == 0
	RESETDATA
	CALL PATCH_ADD_CHARA
	ADDCHARA 0
	BEGIN FIRST
ELSEIF RESULT == 1
	LOADGAME
	CALL PATCH_ADD_CHARA
	RESTART
ENDIF
GOTO INPUT_LOOP
;────────────────────────────────────
;新しいゲームの処理
;────────────────────────────────────

@EVENTFIRST
TARGET = -1
ASSI = -1
ASSI:1 = -1
ASSI:2 = -1
ASSI:3 = -1
FLAG:2999 = 18
PBAND = 4

;空室
FLAG:50 = 1000

FLAG:5 = 0
FLAG:6 = 1
FLAG:11 = 0

;主人公以外のキャラクターの総数。新しいキャラクターを追加した場合はこの値を変更してください
;（以下の処理で自動計算に切り替え）

A = 100
B = 0
REPEAT 200
	IF ITEMNAME:A != ""
		B = A
	ENDIF
	A += 1
REND
FLAG:8 = B - 100 + 1

;登場フラグをセット
A = 100
REPEAT FLAG:8
	FLAG:A = 1
	A += 1
REND



;モード選択
PRINTL ★★请选择模式★★
PRINTL [0]NORMAL
;PRINTL [1]EASY(一部リアクションコマンドを最初から実行できます)
;PRINTL [8]シナリオ（期限????）
PRINTL [9]EXTRA   （无期限）
;PRINTL [10]体験版
;シナリオモードは暫定。口上みたいな外部機能拡張システムで、特定の主人と調教対象の組み合わせではじめる＆外部編集できるシナリオ。

$INPUT_LOOP
INPUT

IF RESULT == 0
	FLAG:2001 = 2
	FLAG:2002 = 1
	FLAG:2003 = 1
	FLAG:2005 = 1
	CALL MASTER_CUSTOM
;ELSEIF RESULT == 1
;	FLAG:2001 = 2
;	FLAG:2002 = 1
;	FLAG:2003 = 1
;	FLAG:2005 = 1
;	CALL MASTER_CUSTOM
;ELSEIF RESULT == 8
;	PRINTW まだ実装していません
;	FLAG:5 = 8
;	CALL SCENARIO_SELECT
;	GOTO INPUT_LOOP
ELSEIF RESULT == 9
	FLAG:5 = 9
	DELCHARA 0
	CALL START_CHARA_SELECT
;ELSEIF RESULT == 10
;	FLAG:2000 = 2
;	CALL TESTPLAY
ELSE
	GOTO INPUT_LOOP
ENDIF

SIF FLAG:2000 == 0
CALL CONFIGURE

IF FLAG:2000 == 1
CALL START_CHARA_SELECT_T
CALL BASE_TRAINER_SETUP
TALENT:131 = 1
ENDIF

MONEY = 1000
SIF FLAG:5 == 9
	MONEY += 9000
ITEM:505 = 1
;主人公のゲージをセットアップ、MASTERCUSTOM.ERBに格納
CALL BASE_MASTER_SETUP


PRINTL 角色的口上要如何表示？
PRINTL [0] - 表示OFF
PRINTL [1] - 表示ON（颜色）
PRINTL [2] - 表示ON（默认）
$INPUT_LOOP_KOJO
INPUT
IF RESULT == 0
	PRINTW 不显示角色的口上
	FLAG:7 = 0
ELSEIF RESULT == 1
	PRINTW 显示角色口上（彩色）
	FLAG:7 = 1
ELSEIF RESULT == 2
	PRINTW 显示角色的口上（默认颜色）
	FLAG:7 = 2
ELSE
	GOTO INPUT_LOOP_KOJO
ENDIF
PRINTL 
DRAWLINE
IF FLAG:2005

ENDIF
;オープニングを追加
PRINTL 要看开场故事吗？
PRINTL [0]是
PRINTL [1]否
INPUT
$INPUT_LOOP_OPENING
IF RESULT == 0
	CALL OPENING_STORY
	;CALL KOJO_EVENT(1)
ELSEIF RESULT == 1
	
ELSE
	GOTO INPUT_LOOP_OPENING
ENDIF
[SKIPSTART]
PRINTL 
PRINTL 
PRINTW SHOP画面输入777可进入调试模式
[SKIPEND]
BEGIN SHOP

@TESTPLAY
ADDCHARA 38
ADDCHARA 39
ADDCHARA 40
TARGET = 1
CALL BASE_TRAINER_SETUP
TARGET = 2
CALL BASE_TRAINER_SETUP
TARGET = 3
CALL BASE_TRAINER_SETUP
TARGET = 1
ASSI = 2
ASSI:1 = 2
ASSI:2 = 3

TALENT:MASTER:132 = 2
@SPACE
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 
PRINTFORML 

