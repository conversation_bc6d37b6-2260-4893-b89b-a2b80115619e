﻿;初期設定
@DUNGEON_TOWN
;ダンジョンタイプ
FLAG:1709 = 6
;ダンジョン難易度補正
FLAG:1705 = FLAG:1704
CALL DUNGEON_MAP_PRODUCE_TOWN

;階層構造
@DUNGEON_TOWN_STAIRS
FLAG:1704 = 0
FLAG:1705 = 0
FLAG:1700 = 6
CALL DUNGEON_MAP_PRODUCE_TOWN

@DUNGEON_EVENT_TOWN
LOCAL = 0
FLAG:72 = 0
SELECTCASE M
	CASE 1812
		LOCAL = 1
	CASE 1814
		LOCAL = 2
	CASE 1818
		LOCAL = 3
	CASE 1841
		LOCAL = 4
	CASE 1845
		LOCAL = 5
	CASE 1881
		LOCAL = 6
	CASE 1887
		LOCAL = 7
ENDSELECT
IF LOCAL
	TRYCALLFORM TOWN_EVENT_{LOCAL}
	PRINTL 
	FLAG:1711 = 1
ENDIF
;1=敵出現2=調教開始(立位)3=調教開始(通常)4=日付送り5～9=ダンジョン移動10=ダンジョンランダム移動
SELECTCASE FLAG:72
	;ダンジョン移動
	CASE 5 TO 10
		LOCAL = FLAG:72 - 4
		SIF FLAG:72 == 10
			LOCAL = RAND:5 + 1
		FLAG:1700 = LOCAL
		FLAG:1704 = RAND:((FLAG:(1720 + LOCAL) + 1) * 5)
		FLAG:72 = 0
		SELECTCASE LOCAL
			CASE 1
				CALL DUNGEON_CAVE
			CASE 2
				CALL DUNGEON_FOREST
			CASE 3
				CALL DUNGEON_SEA
			CASE 4
				CALL DUNGEON_LIBRARY
			CASE 5
				CALL DUNGEON_DESERT
		ENDSELECT
ENDSELECT

