﻿;こういう画面なら設定しやすいんじゃね？という電波を受信したので、やってみた結果がこれだよ！！！
;画面の情報量が思っていた以上に多過ぎてパッと見、訳が判らん。こりゃ失敗ですな。
;改良の余地は…あるのだろうか？
@MASTER_CUSTOM2
;-----------------------------------------------------------
;表示部分
;-----------------------------------------------------------
PRINTFORML 可以设定%CALLNAME:MASTER%的状态。
PRINTL 请选择想变更的项目。
PRINTW (请注意：如果极端的设置、难易度会发生很大的变化)

WHILE 1
	DRAWLINE
	LOCAL:1 = 0
	LOCAL:2 = GETCOLOR()
	PRINT 姓名： 
	PRINTBUTTON @"[{LOCAL:1,2}] %CALLNAME:MASTER%", LOCAL:1++
	PRIN<PERSON> 
	PRINTL □ 素质 □-----------------------------------------------------------------------
	PRINT 性別： 
	SETCOLOR !TALENT:MASTER:121 && !TALENT:MASTER:122 ? 0x00FF00 # 0xA0A0A0
	PRINTBUTTON @"[{LOCAL:1,2}] 女孩子      　", LOCAL:1++
	SETCOLOR TALENT:MASTER:121 ? 0x00FF00 # 0xA0A0A0
	PRINTFORM [{LOCAL:1++,2}] %TALENTNAME:121,12,LEFT%　
	SETCOLOR TALENT:MASTER:122 ? 0x00FF00 # 0xA0A0A0
	PRINTFORML [{LOCAL:1++,2}] %TALENTNAME:122,12,LEFT%
	SETCOLOR LOCAL:2

	LOCAL:3 = 4
	PRINT 性格： 
	FOR LOCAL, 10, 94
		IF LOCAL == 10
			;最初はPRINTBUTTONを使わないと、『性格：』とくっつく
			LOCAL:3--
			SETCOLOR TALENT:MASTER:LOCAL ? 0x00FF00 # 0xA0A0A0
			PRINTBUTTON @"[{LOCAL:1,2}] %TALENTNAME:LOCAL,12,LEFT%", LOCAL:1++
			CONTINUE
		ELSEIF LOCAL:3
			;空白
			PRINT 　
		ELSE
			;改行
			PRINTL 
			PRINT 　　　 
			LOCAL:3 = 4
		ENDIF
		LOCAL:3--
		SETCOLOR TALENT:MASTER:LOCAL ? 0x00FF00 # 0xA0A0A0
		PRINTFORM [{LOCAL:1++,2}] %TALENTNAME:LOCAL,12,LEFT%
		;性格に分類されない素質を飛ばす
		SELECTCASE LOCAL
			CASE 17
				LOCAL += 2
			CASE 37
				LOCAL += 25
			CASE 63, 88, 90
				LOCAL += 1
			CASE 65
				LOCAL += 14
		ENDSELECT
	NEXT
	PRINTL 
	SETCOLOR LOCAL:2

	LOCAL:3 = 4
	LOCAL:4 = GETSTYLE()
	PRINT 体质： 
	FOR LOCAL, 40, 127
		IF LOCAL == 40
			LOCAL:3--
			SETCOLOR TALENT:MASTER:LOCAL ? 0x00FF00 # 0xA0A0A0
			PRINTBUTTON @"[{LOCAL:1,2}] %TALENTNAME:LOCAL,12,LEFT%", LOCAL:1++
			CONTINUE
		ELSEIF LOCAL:3
			PRINT 　
		ELSE
			PRINTL 
			PRINT 　　　 
			LOCAL:3 = 4
		ENDIF
		LOCAL:3--
		;女の子/オトコで選択できない素質に取り消し線を入れてみる
		SELECTCASE LOCAL
			CASE 102, 103, 108, 109
				SIF TALENT:MASTER:122
					FONTSTYLE 4
			CASE 123, 124
				SIF !TALENT:MASTER:121 && !TALENT:MASTER:122
					FONTSTYLE 4
		ENDSELECT
		SETCOLOR TALENT:MASTER:LOCAL ? 0x00FF00 # 0xA0A0A0
		PRINTFORM [{LOCAL:1++,2}] %TALENTNAME:LOCAL,12,LEFT%
		FONTSTYLE LOCAL:4
		SELECTCASE LOCAL
			CASE 43
				LOCAL += 16
			CASE 62
				LOCAL += 7
			CASE 71
				LOCAL += 28
			CASE 113
				LOCAL += 2
			CASE 116
				LOCAL += 6
		ENDSELECT
	NEXT
	PRINTL 
	SETCOLOR LOCAL:2

	PRINT 技能： 
	SETCOLOR TALENT:MASTER:50 ? 0x00FF00 # 0xA0A0A0
	PRINTBUTTON @"[{LOCAL:1,2}] %TALENTNAME:50,12,LEFT%　", LOCAL:1++
	SETCOLOR TALENT:MASTER:51 ? 0x00FF00 # 0xA0A0A0
	PRINTFORML [{LOCAL:1++,2}] %TALENTNAME:51,12,LEFT%
	SETCOLOR LOCAL:2

	PRINTL □ 经验 □-----------------------------------------------------------------------
	LOCAL:1 = 100
	PRINT 性经验： 
	SETCOLOR !EXP:MASTER:7 ? 0x00FF00 # 0xA0A0A0
	PRINTBUTTON @"[{LOCAL:1,3}] 无　　", LOCAL:1++
	SETCOLOR EXP:MASTER:7 == 1 ? 0x00FF00 # 0xA0A0A0
	PRINTFORM [{LOCAL:1++,3}] 稍微　　
	SETCOLOR EXP:MASTER:7 > 1 ? 0x00FF00 # 0xA0A0A0
	PRINTFORML [{LOCAL:1++,3}] 丰富
	SETCOLOR LOCAL:2

	PRINT 性知识： 
	SETCOLOR !CFLAG:MASTER:5 ? 0x00FF00 # 0xA0A0A0
	PRINTBUTTON @"[{LOCAL:1,3}] 全无　　", LOCAL:1++
	SETCOLOR CFLAG:MASTER:5 == 1 ? 0x00FF00 # 0xA0A0A0
	PRINTFORM [{LOCAL:1++,3}] 稍微　　
	SETCOLOR CFLAG:MASTER:5 > 1 ? 0x00FF00 # 0xA0A0A0
	PRINTFORML [{LOCAL:1++,3}] 丰富
	SETCOLOR LOCAL:2

	DRAWLINE
	ALIGNMENT RIGHT
	PRINT [997] - 设定模式变更　　
	PRINT [998] - 复位　　
	PRINTL  [999] - 设定完成　
	ALIGNMENT LEFT

;-----------------------------------------------------------
;入力処理部分
;-----------------------------------------------------------
	$INPUT_LOOP
	INPUT
	SELECTCASE RESULT
		;名前の変更
		CASE 0
			PRINTL 请输入姓名。
			INPUTS
			CALLNAME:MASTER = %RESULTS%
;-----------------------------------------------------------
		;女の子→ふたなり/オトコ/早漏/遅漏をOFF
		CASE 1
			TALENT:MASTER:121 = 0
			TALENT:MASTER:122 = 0
			TALENT:MASTER:123 = 0
			TALENT:MASTER:124 = 0
		;ふたなり→ふたなりをON。オトコをOFF
		CASE 2
			TALENT:MASTER:121 = 1
			TALENT:MASTER:122 = 0
		;オトコ→オトコをON。Ｖ敏感/Ｖ鈍感/貧乳/巨乳/ふたなりをOFF
		CASE 3
			TALENT:MASTER:102 = 0
			TALENT:MASTER:103 = 0
			TALENT:MASTER:108 = 0
			TALENT:MASTER:109 = 0
			TALENT:MASTER:121 = 0
			TALENT:MASTER:122 = 1
;-----------------------------------------------------------
		;臆病→臆病を反転。気丈をOFF
		CASE 4
			TALENT:MASTER:10 = !TALENT:MASTER:10
			TALENT:MASTER:12 = 0
		;反抗的→反抗的を反転。素直をOFF
		CASE 5
			TALENT:MASTER:11 = !TALENT:MASTER:11
			TALENT:MASTER:13 = 0
		;気丈→気丈を反転。臆病をOFF
		CASE 6
			TALENT:MASTER:10 = 0
			TALENT:MASTER:12 = !TALENT:MASTER:12
		;素直→素直を反転。反抗的をOFF
		CASE 7
			TALENT:MASTER:11 = 0
			TALENT:MASTER:13 = !TALENT:MASTER:13
		;大人しい→大人しいを反転。生意気をOFF
		CASE 8
			TALENT:MASTER:14 = !TALENT:MASTER:14
			TALENT:MASTER:16 = 0
		;プライド高い→プライド高いを反転。プライド低いをOFF
		CASE 9
			TALENT:MASTER:15 = !TALENT:MASTER:15
			TALENT:MASTER:17 = 0
		;生意気→生意気を反転。素直をOFF
		CASE 10
			TALENT:MASTER:14 = 0
			TALENT:MASTER:16 = !TALENT:MASTER:16
		;プライド低い→プライド低いを反転。プライド高いをOFF
		CASE 11
			TALENT:MASTER:15 = 0
			TALENT:MASTER:17 = !TALENT:MASTER:17
		;自制的→自制的を反転。衝動的をOFF
		CASE 12
			TALENT:MASTER:20 = !TALENT:MASTER:20
			TALENT:MASTER:21 = 0
		;衝動的→衝動的を反転。自制的をOFF
		CASE 13
			TALENT:MASTER:20 = 0
			TALENT:MASTER:21 = !TALENT:MASTER:21
		;無関心→無関心を反転。好奇心をOFF
		CASE 14
			TALENT:MASTER:22 = !TALENT:MASTER:22
			TALENT:MASTER:23 = 0
		;好奇心→好奇心を反転。無関心をOFF
		CASE 15
			TALENT:MASTER:22 = 0
			TALENT:MASTER:23 = !TALENT:MASTER:23
		;感情乏しい→感情乏しいを反転。情緒豊かをOFF
		CASE 16
			TALENT:MASTER:24 = !TALENT:MASTER:24
			TALENT:MASTER:25 = 0
		;情緒豊か→情緒豊かを反転。感情乏しいをOFF
		CASE 17
			TALENT:MASTER:24 = 0
			TALENT:MASTER:25 = !TALENT:MASTER:25
		;楽観的→楽観的を反転。悲観的をOFF
		CASE 18
			TALENT:MASTER:26 = !TALENT:MASTER:26
			TALENT:MASTER:27 = 0
		;悲観的→悲観的を反転。楽観的をOFF
		CASE 19
			TALENT:MASTER:26 = 0
			TALENT:MASTER:27 = !TALENT:MASTER:27
		;一線越えない→一線越えないを反転
		CASE 20
			TALENT:MASTER:28 = !TALENT:MASTER:28
		;目立ちたがり→目立ちたがりを反転
		CASE 21
			TALENT:MASTER:29 = !TALENT:MASTER:29
		;貞操観念→貞操観念を反転。貞操無頓着をOFF
		CASE 22
			TALENT:MASTER:30 = !TALENT:MASTER:30
			TALENT:MASTER:31 = 0
		;貞操無頓着→貞操無頓着を反転。貞操観念をOFF
		CASE 23
			TALENT:MASTER:30 = 0
			TALENT:MASTER:31 = !TALENT:MASTER:31
		;抑圧→抑圧を反転。解放をOFF
		CASE 24
			TALENT:MASTER:32 = !TALENT:MASTER:32
			TALENT:MASTER:33 = 0
		;解放→解放を反転。抑圧をOFF
		CASE 25
			TALENT:MASTER:32 = 0
			TALENT:MASTER:33 = !TALENT:MASTER:33
		;恥じらい→恥じらいを反転。恥薄いをOFF
		CASE 26
			TALENT:MASTER:34 = !TALENT:MASTER:34
			TALENT:MASTER:35 = 0
		;恥薄い→恥薄いを反転。恥じらいをOFF
		CASE 27
			TALENT:MASTER:34 = 0
			TALENT:MASTER:35 = !TALENT:MASTER:35
		;好色→好色を反転。清楚をOFF
		CASE 28
			TALENT:MASTER:36 = !TALENT:MASTER:36
			TALENT:MASTER:37 = 0
		;清楚→清楚を反転。好色をOFF
		CASE 29
			TALENT:MASTER:36 = 0
			TALENT:MASTER:37 = !TALENT:MASTER:37
		;献身的→献身的を反転。受け身をOFF
		CASE 30
			TALENT:MASTER:63 = !TALENT:MASTER:63
			TALENT:MASTER:65 = 0
		;受け身→受け身を反転。献身的をOFF
		CASE 31
			TALENT:MASTER:63 = 0
			TALENT:MASTER:65 = !TALENT:MASTER:65
		;倒錯的→倒錯的を反転
		CASE 32
			TALENT:MASTER:80 = !TALENT:MASTER:80
		;両刀→両刀を反転。男嫌いをOFF
		CASE 33
			TALENT:MASTER:81 = !TALENT:MASTER:81
			TALENT:MASTER:82 = 0
		;男嫌い→男嫌いを反転。両刀をOFF
		CASE 34
			TALENT:MASTER:81 = 0
			TALENT:MASTER:82 = !TALENT:MASTER:82
		;サド→サドを反転
		CASE 35
			TALENT:MASTER:83 = !TALENT:MASTER:83
		;慎重→慎重を反転。短気をOFF
		CASE 36
			TALENT:MASTER:84 = !TALENT:MASTER:84
			TALENT:MASTER:85 = 0
		;短気→短気を反転。慎重をOFF
		CASE 37
			TALENT:MASTER:84 = 0
			TALENT:MASTER:85 = !TALENT:MASTER:85
		;意地悪→意地悪を反転。心根優しいをOFF
		CASE 38
			TALENT:MASTER:86 = !TALENT:MASTER:86
			TALENT:MASTER:87 = 0
		;心根優しい→心根優しいを反転。意地悪をOFF
		CASE 39
			TALENT:MASTER:86 = 0
			TALENT:MASTER:87 = !TALENT:MASTER:87
		;幼稚→幼稚を反転
		CASE 40
			TALENT:MASTER:88 = !TALENT:MASTER:88
		;威圧感→威圧感を反転
		CASE 41
			TALENT:MASTER:90 = !TALENT:MASTER:90
		;親しみやすい→親しみやすいを反転。近寄り難いをOFF
		CASE 42
			TALENT:MASTER:92 = !TALENT:MASTER:92
			TALENT:MASTER:93 = 0
		;近寄り難い→近寄り難いを反転。親しみやすいをOFF
		CASE 43
			TALENT:MASTER:92 = 0
			TALENT:MASTER:93 = !TALENT:MASTER:93
;-----------------------------------------------------------
		;痛みに弱い→痛みに弱いを反転。痛みに強いをOFF
		CASE 44
			TALENT:MASTER:40 = !TALENT:MASTER:40
			TALENT:MASTER:41 = 0
		;痛みに強い→痛みに強いを反転。痛みに弱いをOFF
		CASE 45
			TALENT:MASTER:40 = 0
			TALENT:MASTER:41 = !TALENT:MASTER:41
		;濡れやすい→濡れやすいを反転。濡れにくいをOFF
		CASE 46
			TALENT:MASTER:42 = !TALENT:MASTER:42
			TALENT:MASTER:43 = 0
		;濡れにくい→濡れにくいを反転。濡れやすいをOFF
		CASE 47
			TALENT:MASTER:42 = 0
			TALENT:MASTER:43 = !TALENT:MASTER:43
		;自慰しやすい→自慰しやすいを反転。
		CASE 48
			TALENT:MASTER:60 = !TALENT:MASTER:60
		;汚臭鈍感→汚臭鈍感を反転。汚臭敏感をOFF
		CASE 49
			TALENT:MASTER:61 = !TALENT:MASTER:61
			TALENT:MASTER:62 = 0
		;汚臭敏感→汚臭敏感を反転。汚臭鈍感をOFF
		CASE 50
			TALENT:MASTER:61 = 0
			TALENT:MASTER:62 = !TALENT:MASTER:62
		;快感に素直→快感に素直を反転。快感の否定をOFF
		CASE 51
			TALENT:MASTER:70 = !TALENT:MASTER:70
			TALENT:MASTER:71 = 0
		;快感の否定→快感の否定を反転。快感に素直をOFF
		CASE 52
			TALENT:MASTER:70 = 0
			TALENT:MASTER:71 = !TALENT:MASTER:71
		;Ｃ敏感→Ｃ敏感を反転。Ｃ鈍感をOFF
		CASE 53
			TALENT:MASTER:100 = !TALENT:MASTER:100
			TALENT:MASTER:101 = 0
		;Ｃ鈍感→Ｃ鈍感を反転。Ｃ敏感をOFF
		CASE 54
			TALENT:MASTER:100 = 0
			TALENT:MASTER:101 = !TALENT:MASTER:101
		;Ｖ敏感→オトコの場合は入力エラー。Ｖ敏感を反転。Ｖ鈍感をOFF
		CASE 55
			IF TALENT:MASTER:122
				CLEARLINE 1
				REUSELASTLINE 男性ですよね？
				GOTO INPUT_LOOP
			ENDIF
			TALENT:MASTER:102 = !TALENT:MASTER:102
			TALENT:MASTER:103 = 0
		;Ｖ鈍感→オトコの場合は入力エラー。Ｖ鈍感を反転。Ｖ敏感をOFF
		CASE 56
			IF TALENT:MASTER:122
				CLEARLINE 1
				REUSELASTLINE 是男性吧？
				GOTO INPUT_LOOP
			ENDIF
			TALENT:MASTER:102 = 0
			TALENT:MASTER:103 = !TALENT:MASTER:103
		;Ａ敏感→Ａ敏感を反転。Ａ鈍感をOFF
		CASE 57
			TALENT:MASTER:104 = !TALENT:MASTER:104
			TALENT:MASTER:105 = 0
		;Ａ鈍感→Ａ鈍感を反転。Ａ敏感をOFF
		CASE 58
			TALENT:MASTER:104 = 0
			TALENT:MASTER:105 = !TALENT:MASTER:105
		;Ｂ敏感→Ｂ敏感を反転。Ｂ鈍感をOFF
		CASE 59
			TALENT:MASTER:106 = !TALENT:MASTER:106
			TALENT:MASTER:107 = 0
		;Ｂ鈍感→Ｂ鈍感を反転。Ｂ敏感をOFF
		CASE 60
			TALENT:MASTER:106 = 0
			TALENT:MASTER:107 = !TALENT:MASTER:107
		;貧乳→オトコの場合は入力エラー。貧乳を反転。巨乳をOFF
		CASE 61
			IF TALENT:MASTER:122
				CLEARLINE 1
				REUSELASTLINE 是男性吧？
				GOTO INPUT_LOOP
			ENDIF
			TALENT:MASTER:108 = !TALENT:MASTER:108
			TALENT:MASTER:109 = 0
		;巨乳→オトコの場合は入力エラー。巨乳を反転。貧乳をOFF
		CASE 62
			IF TALENT:MASTER:122
				CLEARLINE 1
				REUSELASTLINE 是男性吧？
				GOTO INPUT_LOOP
			ENDIF
			TALENT:MASTER:108 = 0
			TALENT:MASTER:109 = !TALENT:MASTER:109
		;小柄体型→小柄体型を反転。長身をOFF
		CASE 63
			TALENT:MASTER:110 = !TALENT:MASTER:110
			TALENT:MASTER:111 = 0
		;長身→長身を反転。小柄体型をOFF
		CASE 64
			TALENT:MASTER:110 = 0
			TALENT:MASTER:111 = !TALENT:MASTER:111
		;回復早い→回復早いを反転。回復遅いをOFF
		CASE 65
			TALENT:MASTER:112 = !TALENT:MASTER:112
			TALENT:MASTER:113 = 0
		;回復遅い→回復遅いを反転。回復早いをOFF
		CASE 66
			TALENT:MASTER:112 = 0
			TALENT:MASTER:113 = !TALENT:MASTER:113
		;おもらし癖→おもらし癖を反転
		CASE 67
			TALENT:MASTER:116 = !TALENT:MASTER:116
		;早漏→女の子の場合は入力エラー。早漏を反転。遅漏をOFF
		CASE 68
			IF !TALENT:MASTER:121 && !TALENT:MASTER:122
				CLEARLINE 1
				REUSELASTLINE 是女孩子吧？
				GOTO INPUT_LOOP
			ENDIF
			TALENT:MASTER:123 = !TALENT:MASTER:123
			TALENT:MASTER:124 = 0
		;遅漏→女の子の場合は入力エラー。遅漏を反転。早漏をOFF
		CASE 69
			IF !TALENT:MASTER:121 && !TALENT:MASTER:122
				CLEARLINE 1
				REUSELASTLINE 是女孩子吧？
				GOTO INPUT_LOOP
			ENDIF
			TALENT:MASTER:123 = 0
			TALENT:MASTER:124 = !TALENT:MASTER:124
		;精力絶倫→精力絶倫を反転。精力薄弱をOFF
		CASE 70
			TALENT:MASTER:125 = !TALENT:MASTER:125
			TALENT:MASTER:126 = 0
		;精力薄弱→精力薄弱を反転。精力絶倫をOFF
		CASE 71
			TALENT:MASTER:125 = 0
			TALENT:MASTER:126 = !TALENT:MASTER:126
		;習得早い→習得早いを反転。習得遅いをOFF
		CASE 72
			TALENT:MASTER:50 = !TALENT:MASTER:50
			TALENT:MASTER:51 = 0
		;習得遅い→習得遅いを反転。習得早いをOFF
		CASE 73
			TALENT:MASTER:50 = 0
			TALENT:MASTER:51 = !TALENT:MASTER:51
;-----------------------------------------------------------
		;性経験なし
		CASE 100
			EXP:MASTER:7 = 0
		;性経験少し
		CASE 101
			EXP:MASTER:7 = 1
		;性経験多め
		CASE 102
			EXP:MASTER:7 = 3
		;性知識皆無
		CASE 103
			CFLAG:MASTER:5 = 0
		;性知識少し
		CASE 104
			CFLAG:MASTER:5 = 1
		;性知識豊富
		CASE 105
			CFLAG:MASTER:5 = 2
;-----------------------------------------------------------
		CASE 997
			JUMP MASTER_CUSTOM
		CASE 998
			REPEAT 300
				TALENT:MASTER:COUNT = 0
			REND
			REPEAT 100
				EXP:MASTER:COUNT = 0
			REND
			CFLAG:MASTER:5 = 0
			TALENT:MASTER:122 = 1
		;設定完了
		CASE 999
			BREAK
		CASEELSE
			CLEARLINE 1
			GOTO INPUT_LOOP
	ENDSELECT
WEND

;-----------------------------------------------------------
;後処理部分
;-----------------------------------------------------------
;性経験による後処理
SELECTCASE EXP:MASTER:7
	CASE 0
		;性経験なし
		TALENT:MASTER:0 = !TALENT:MASTER:122
		TALENT:MASTER:1 = TALENT:MASTER:121 || TALENT:MASTER:122
		EXP:MASTER:5 = (TALENT:MASTER:121 || TALENT:MASTER:122) ? TALENT:MASTER:60 # 0
		EXP:MASTER:8 = CFLAG:MASTER:5 * 5
	CASE 1
		;性経験少し
		TALENT:MASTER:1 = 0
		EXP:MASTER:0 = 4
		EXP:MASTER:1 = TALENT:MASTER:122 ? 0 # 2
		EXP:MASTER:3 = TALENT:MASTER:122 ? 0 # 3
		EXP:MASTER:5 = (TALENT:MASTER:121 || TALENT:MASTER:122) ? 3 + TALENT:MASTER:60 # 0
		EXP:MASTER:8 = 5 + CFLAG:MASTER:5 * 5
	CASE IS > 1
		;性経験多め
		TALENT:MASTER:1 = 0
		ABL:MASTER:1 = 1
		EXP:MASTER:0 = 10
		EXP:MASTER:1 = TALENT:MASTER:122 ? 0 # 10
		EXP:MASTER:3 = TALENT:MASTER:122 ? 0 # 10
		EXP:MASTER:5 = (TALENT:MASTER:121 || TALENT:MASTER:122) ? 12 + TALENT:MASTER:60 # 0
		EXP:MASTER:8 = 10 + CFLAG:MASTER:5 * 5
ENDSELECT

;自慰経験。自慰しやすい場合、+10
EXP:MASTER:10 = TALENT:MASTER:60 * 10
