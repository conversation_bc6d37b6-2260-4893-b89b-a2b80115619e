﻿;────────────────────────────────────
;ショップメニューの表示
;────────────────────────────────────
@SHOW_SHOP
CALL VERSION_UP
CALL ACCESSORY_SET
FOR LOCAL, 0, CHARANUM
	CFLAG:MASTER:300 |= CFLAG:LOCAL:300
NEXT

;IF FLAG:4
;	FOR LOCAL:0, 0, CHARANUM
;		PRINTFORM %CALLNAME:(LOCAL:0)%：
;		FOR LOCAL:1, 80, 88
;			PRINTFORM {EQUIP:(LOCAL:0):(LOCAL:1)}, 
;		NEXT
;		PRINTL 
;	NEXT
;ENDIF

IF !FLAG:1700
	FOR LOCAL, 0, 10
		CFLAG:MASTER:(220 + LOCAL) = 0
	NEXT
ENDIF

FOR LOCAL, 0, CHARANUM
	SIF CFLAG:LOCAL:999
		DELCHARA LOCAL--
NEXT

IF FLAG:1700
	PRINTL [100] - 继续探索
ELSE
	;アイテム解禁状況のリセット
	IF !FLAG:1701
		FOR LOCAL, 0, 50
			ITEM:LOCAL = FLAG:(1650 + LOCAL)
		NEXT
	ENDIF
	SIF !FLAG:1701
		CALL EVENT_HONOR
	LOCAL:1 = TARGET
	FOR LOCAL, 0, CHARANUM
		TARGET = LOCAL
		;特殊な素質
		;CALL GET_EXTALENT
		;3サイズと身長
		CALL SIZE_SET
		SIF LOCAL == 0 || FLAG:1701 || FLAG:1700
			CONTINUE
		IF !CFLAG:LOCAL:91
			CFLAG:MASTER:91++
			CFLAG:LOCAL:91 = CFLAG:MASTER:91
		ENDIF
	NEXT
	TARGET = LOCAL:1
	N = 0
	REPEAT CHARANUM
		SIF CFLAG:COUNT:92
			N += 1
	REND
	
	DRAWLINE
	PRINTFORML {FLAG:32+1}年{FLAG:31+1}日(%GET_TIME()%) %GET_SEASON()% 星期%GET_DAY()% \@ FLAG:1701 ? 梦魔の巣 # 据点　所持金{MONEY}$ \@
;	SIF !FLAG:1701
;		PRINTFORML 空室数　{MAX(FLAG:50 - CAPACITY(),0)}
	SIF TARGET >= 0
		PRINTFORML %CALLNAME:MASTER%被%CALLNAME:TARGET%调教中
	SIF ASSI:1 >= 0
		PRINTFORML （助手１：%CALLNAME:(ASSI:1)%）\@ ASSI:2 >= 0 ? （助手２：%CALLNAME:(ASSI:2)%） # \@
	CALL PRINT_BASE("体力", MASTER, 0, 16)
	CALL PRINT_BASEL("气力", MASTER, 1, 16)
	SIF TARGET >= 0
		PRINTFORML (剩余MP　{CFLAG:201})
	DRAWLINE
	PRINTFORML \@ TARGET >= 0 ? [100] - 调教的时间到了 # [---] - 请决定这次的调教者 \@
	PRINTL [101] - 能力的表示
	SIF TARGET >= 0
		PRINTL [102] - 获得奖励
;	SIF !FLAG:1701
;		PRINTL [103] - 拠点の増築
	;PRINTL [104] - 珠の管理
	;PRINTL [105] - 衣装や調教外時間用道具の取得
	SIF CHARANUM == N + 1
		PRINTL [106] - 休息
	SIF CHARANUM > 2 + N
		PRINTL [109] - 助手变更
	SIF CHARANUM > 1 + N
		PRINTL [110] - 调教者切换
;	PRINTFORML \@ FLAG:1701 ? [114] - 脱走する # [113] - 迷宮にもぐる（未完成） \@
	PRINTL [115] - 道具管理
;	SIF !FLAG:1701
;		PRINTL [116] - ブックシェルフ
;	SIF !FLAG:1701 && FLAG:41
;		PRINTL [117] - 称号
	SIF TARGET > 0
	PRINTL [155] - 服装设定
	PRINTL [177] - 查看数据库
	PRINTL [178] - 设置
	PRINTL [200] - 保存
	PRINTL [300] - 读取
	IF FLAG:4
		PRINTL 
		PRINTL [777] - 调试模式
		PRINTL 
		PRINTL --因为调试指令是为了简单地改变状况而制作的，
		PRINTL 以下命令造成的异常不属于动作保障---
		PRINTL [778] - Show me the Money（奖励点）
		PRINTL [779] - 转生仪式
		PRINTL [780] - 标志检查
	ENDIF
	PRINTL 
	PRINTL [888] - 口上表示設定
ENDIF
DRAWLINE


;────────────────────────────────────
;ショップメニュー
;────────────────────────────────────
@USERSHOP
IF FLAG:1700
	CALL DUNGEON
ELSEIF RESULT == 100 && TARGET >= 0
	IF FLAG:14 == 1
		CALL COMPETITION(TARGET)
	ELSE
		BEGIN TRAIN
	ENDIF
	RETURN 1
ELSEIF RESULT == 101
	CALL SHOW_CHARADATA
ELSEIF RESULT == 102 && TARGET >= 0
	CALL BONUS_GAIN
	RETURN 1
;ELSEIF RESULT == 103 && !FLAG:1701
;	CALL BASE_ENLARGEMENT
;	RETURN 1
;ELSEIF RESULT == 104
;	CALL JUEL_MANAGE
;	RETURN 1
;ELSEIF RESULT == 105
;	CALL ITEM_GAIN2
;	RETURN 1
ELSEIF RESULT == 106 && CHARANUM == N + 1
	BASE:MASTER:0 = MAXBASE:MASTER:0
	BASE:MASTER:1 = MAXBASE:MASTER:1
	CFLAG:MASTER:11 = 0
	PRINTW 休息了
	;日時更新
	BEGIN TURNEND
ELSEIF RESULT == 109 && CHARANUM > 2
	CALL SELECT_ASSI
ELSEIF RESULT == 110 && CHARANUM > 1
	CALL CHANGE_TARGET
;ELSEIF RESULT == 113 && !FLAG:1701
;	CALL DUNGEON_SELECT
;ELSEIF RESULT == 114 && FLAG:1701
;	CALL ESCAPE
ELSEIF RESULT == 115
	CALL SHOP_ITEM
;ELSEIF RESULT == 116 && !FLAG:1701
;	CALL BOOKSHELF
;ELSEIF RESULT == 117 && !FLAG:1701 && FLAG:41
;	CALL HONOR_SETTING
ELSEIF RESULT == 155 && TARGET >= 0
	CALL CLOTHES_SETTING
ELSEIF RESULT == 177
	CALL DATABASE_OUTPUT
ELSEIF RESULT == 178
	CALL CONFIGURE
ELSEIF RESULT == 200
	SAVEGAME
ELSEIF RESULT == 300
	LOADGAME
ELSEIF RESULT == 777
	PRINTFORMW 显示调试信息\@ FLAG:4 ? 否 # 是 \@这样设定了
	FLAG:4 = !FLAG:4
ELSEIF RESULT == 778 && FLAG:4
	SIF TARGET >= 0
		CFLAG:201 += 1000
	MONEY += 1
	PRINTW 取得了１点奖励点
ELSEIF RESULT == 779 && FLAG:4
	PRINTW 可以重新设定调教对象（主人公）
	CALL MASTER_CUSTOM
	CALL BASE_MASTER_SETUP
ELSEIF RESULT == 780 && FLAG:4
	CALL FLAGCHECK
ELSEIF RESULT == 888
	CALL KOJO_SET
;ELSEIF RESULT == 999
;	PRINTFORMW テキスト表示を\@ FLAG:6 ? OFF # ON \@にします
;	FLAG:6 = !FLAG:6
ENDIF


@KOJO_SET
PRINTL 角色口上怎么显示？
PRINTL [0] - 显示OFF
PRINTL [1] - 显示ON（颜色）
PRINTL [2] - 显示ON（默认）
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
		PRINTW 不显示角色的口上
	CASE 1
		PRINTW 显示角色口上（颜色）
	CASE 2
		PRINTW 显示角色的口上（默认颜色）
	CASEELSE
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
FLAG:7 = RESULT
PRINTL 


;────────────────────────────────────
;調教対象選択
;────────────────────────────────────
@CHANGE_TARGET(ARG)
PRINTFORML 目前 \@ TARGET >= 0 ? [{TARGET,2}] %CALLNAME:TARGET%正在调教中 # 没有调教者 \@
PRINTL 这次的调教者是誰？
CALL LIFE_LIST
SIF !ARG
	PRINTFORML  [100] 返回
$INPUT_LOOP1
INPUT
LOCAL = RESULT
;戻る
IF LOCAL == 100 && !ARG
	RETURN 0
;不正値, 調教対象, 不在キャラ, 監禁中の拠点キャラをはじく
ELSEIF LOCAL <= 0 || CHARANUM <= LOCAL || CFLAG:LOCAL:92 || (FLAG:1701 && LOCAL < FLAG:30)
	CLEARLINE 1
	GOTO INPUT_LOOP1
;変更が行われていない場合
ELSEIF LOCAL == TARGET
	;マスターの場合
	IF TALENT:TARGET:131
		PRINTFORMW %CALLNAME:TARGET%决定就这样自己调教%CALLNAME:MASTER%…
	;それ以外
	ELSE
		PRINTFORMW 就这样让%CALLNAME:TARGET%去调教%CALLNAME:MASTER%…
	ENDIF
;変更が行われている場合
ELSE
	;口上の為にAに交代先の登録番号を入れる
	A = LOCAL
	CALL KOJO_EVENT(102)
	;マスター→マスター以外への変更
	IF TARGET >= 0 && TALENT:TARGET:131
		PRINTFORMW %CALLNAME:TARGET%委托%CALLNAME:LOCAL%调教%CALLNAME:MASTER%……
	;マスター以外→マスターへの変更
	ELSEIF TALENT:LOCAL:131
		PRINTFORMW %CALLNAME:LOCAL%决定自己调教%CALLNAME:MASTER%…
	;マスター以外→マスター以外への変更
	ELSE
		PRINTFORMW \@ TARGET >= 0 ? 代替%CALLNAME:TARGET%、 # \@%CALLNAME:LOCAL%调教着%CALLNAME:MASTER%…
	ENDIF
	;助手１が調教者に→調教者と助手１を入れ替え。その後、助手１が空きで助手２がいる場合、詰める
	IF LOCAL == ASSI:1
		SWAP TARGET, ASSI:1
		ASSI:0 = ASSI:1
		IF ASSI:1 < 0 && ASSI:2 >= 0
			ASSI:0 = ASSI:2
			ASSI:1 = ASSI:2
			ASSI:2 = -1
		ENDIF
	;助手２が調教者に→調教者と助手２を入れ替え
	ELSEIF LOCAL == ASSI:2
		SWAP TARGET, ASSI:2
	;その他が調教者に→調教者に上書きのみ
	ELSE
		IF ASSI:1 < 0
			ASSI:0 = TARGET
			ASSI:1 = TARGET
		ELSEIF ASSI:2 < 0
			ASSI:2 = TARGET
		ENDIF
		TARGET = LOCAL
	ENDIF
	CALL KOJO_EVENT(102,1)
ENDIF
;助手の設定
IF !ARG
	IF ASSI:2 >= 0
		PRINTL 请选择助手的模式。
		PRINTFORML [0] - 助手１：无　　　　　　　助手２：无
		PRINTFORML [1] - 助手１：%CALLNAME:(ASSI:1),16,LEFT%　助手２：%CALLNAME:(ASSI:2)%
		PRINTFORML [2] - 助手１：%CALLNAME:(ASSI:2),16,LEFT%　助手２：%CALLNAME:(ASSI:1)%
		PRINTFORML [3] - 助手１：%CALLNAME:(ASSI:1),16,LEFT%　助手２：无
		PRINTFORML [4] - 助手１：%CALLNAME:(ASSI:2),16,LEFT%　助手２：无
		$INPUT_LOOP2
		INPUT
		SELECTCASE RESULT
			CASE 0
				ASSI:0 = -1
				ASSI:1 = -1
				ASSI:2 = -1
			CASE 1
				;変更なし
			CASE 2
				SWAP ASSI:1, ASSI:2
				ASSI:0 = ASSI:1
			CASE 3
				ASSI:2 = -1
			CASE 4
				ASSI:0 = ASSI:2
				ASSI:1 = ASSI:2
				ASSI:2 = -1
			CASEELSE
				CLEARLINE 1
				GOTO INPUT_LOOP2
		ENDSELECT
	ELSEIF ASSI:1 >= 0
		PRINTL 请选择助手的模式。
		PRINTFORML [0] - 助手１：无
		PRINTFORML [1] - 助手１：%CALLNAME:(ASSI:1)%
		$INPUT_LOOP3
		INPUT
		SELECTCASE RESULT
			CASE 0
				ASSI:0 = -1
				ASSI:1 = -1
			CASE 1
				;変更なし
			CASEELSE
				CLEARLINE 1
				GOTO INPUT_LOOP3
		ENDSELECT
	ENDIF
ENDIF


;────────────────────────────────────
;助手選択
;────────────────────────────────────
@SELECT_ASSI
WHILE 1
	IF ASSI:1 >= 0
		PRINTFORML 目前 [{ASSI:1,3}] %CALLNAME:(ASSI:1)%\@ ASSI:2 >= 0 ? 、[{ASSI:2,3}] %CALLNAME:(ASSI:2)% # \@是助手
	ELSE
		PRINTL 目前 没有助手
	ENDIF
	PRINTL 要让谁做助手？
	PRINTL  [  0] 无助手
	FOR LOCAL, 0, CHARANUM
		;MASTER, 不在キャラ, 監禁時拠点に残っているキャラは表示しない
		SIF LOCAL == MASTER || CFLAG:LOCAL:92 || (FLAG:1701 && LOCAL < FLAG:30)
			CONTINUE
		PRINTFORM  [{LOCAL,3}] %CALLNAME:LOCAL,16,LEFT% 体力({MAX(BASE:LOCAL:0,0),4}/{MAXBASE:LOCAL:0,4}) 
		PRINTFORM  [LV {CFLAG:LOCAL:0,2} NEXT {CFLAG:LOCAL:4 - CFLAG:LOCAL:1,6}]
		SELECTCASE LOCAL
			CASE TARGET
				PRINT  调教者
			CASE ASSI:1
				PRINT  助手１
			CASE ASSI:2
				PRINT  助手２
		ENDSELECT
		SIF TALENT:LOCAL:131
			PRINT  <主人>
		PRINTL 
	NEXT
	PRINTL  [100] 返回
	$INPUT_LOOP1
	INPUT
	LOCAL = RESULT
	IF LOCAL == 100
		BREAK
	ELSEIF LOCAL == 0
		ASSI:0 = -1
		ASSI:1 = -1
		ASSI:2 = -1
	;0未満, キャラ登録数以上は不正値
	;不在キャラ, 監禁時拠点に残っているキャラは助手になれない。MASTERは0＝助手なしなので判定しない
	ELSEIF LOCAL < 0 || CHARANUM <= LOCAL || CFLAG:LOCAL:92 || (FLAG:1701 && LOCAL < FLAG:30)
		CLEARLINE 1
		GOTO INPUT_LOOP1
	;調教者が助手に選ばれた場合
	ELSEIF LOCAL == TARGET
		;助手なし
		IF ASSI:1 < 0
			ASSI:0 = TARGET
			ASSI:1 = TARGET
			TARGET = -1
		;助手１
		ELSEIF ASSI:2 < 0
			PRINTL 请选择模式。
			PRINTFORML [0] - 助手１：%CALLNAME:(ASSI:1),16,LEFT%　助手２：%CALLNAME:TARGET%
			PRINTFORML [1] - 助手１：%CALLNAME:TARGET,16,LEFT%　助手２：%CALLNAME:(ASSI:1)%
			PRINTFORML [2] - 助手１：%CALLNAME:TARGET,16,LEFT%　助手２：无
			PRINTL [3] - 还是算了
			$INPUT_LOOP2
			INPUT
			SELECTCASE RESULT
				CASE 0
					ASSI:2 = TARGET
					TARGET = -1
				CASE 1
					ASSI:2 = ASSI:1
					ASSI:0 = TARGET
					ASSI:1 = TARGET
					TARGET = -1
				CASE 2
					ASSI:0 = TARGET
					ASSI:1 = TARGET
					TARGET = -1
				CASE 3
				CASEELSE
					CLEARLINE 1
					GOTO INPUT_LOOP2
			ENDSELECT
		;助手１・助手２
		ELSE
			PRINTL 请选择模式。
			PRINTFORML [0] - 助手１：%CALLNAME:TARGET,16,LEFT%　助手２：无
			PRINTFORML [1] - 助手１：%CALLNAME:TARGET,16,LEFT%　助手２：%CALLNAME:(ASSI:1)%
			PRINTFORML [2] - 助手１：%CALLNAME:TARGET,16,LEFT%　助手２：%CALLNAME:(ASSI:2)%
			PRINTFORML [3] - 助手１：%CALLNAME:(ASSI:1),16,LEFT%　助手２：%CALLNAME:TARGET%
			PRINTFORML [4] - 助手１：%CALLNAME:(ASSI:2),16,LEFT%　助手２：%CALLNAME:TARGET%
			PRINTL [5] - 还是算了
			$INPUT_LOOP3
			INPUT
			SELECTCASE RESULT
				CASE 0
					ASSI:0 = TARGET
					ASSI:1 = TARGET
					ASSI:2 = -1
					TARGET = -1
				CASE 1
					ASSI:2 = ASSI:1
					ASSI:0 = TARGET
					ASSI:1 = TARGET
					TARGET = -1
				CASE 2
					ASSI:0 = TARGET
					ASSI:1 = TARGET
					TARGET = -1
				CASE 3
					ASSI:2 = TARGET
					TARGET = -1
				CASE 4
					ASSI:0 = ASSI:2
					ASSI:1 = ASSI:2
					ASSI:2 = TARGET
					TARGET = -1
				CASE 5
				CASEELSE
					CLEARLINE 1
					GOTO INPUT_LOOP3
			ENDSELECT
		ENDIF
	;助手１が助手に選ばれた場合
	ELSEIF LOCAL == ASSI:1
		;助手１
		IF ASSI:2 < 0
			PRINTFORML %CALLNAME:(ASSI:1)%已经是助手了。
		;助手１・助手２
		ELSE
			PRINTL 请选择模式。
			PRINTFORML [0] - 助手１：%CALLNAME:(ASSI:1),16,LEFT%　助手２：无
			PRINTFORML [1] - 助手１：%CALLNAME:(ASSI:2),16,LEFT%　助手２：%CALLNAME:(ASSI:1)%
			PRINTL [2] - 还是算了
			$INPUT_LOOP4
			INPUT
			SELECTCASE RESULT
				CASE 0
					ASSI:2 = -1
				CASE 1
					SWAP ASSI:1, ASSI:2
					ASSI:0 = ASSI:1
				CASE 2
				CASEELSE
					CLEARLINE 1
					GOTO INPUT_LOOP4
			ENDSELECT
		ENDIF
	;助手２が助手に選ばれた場合
	ELSEIF LOCAL == ASSI:2
		PRINTL 请选择模式。
		PRINTFORML [0] - 助手１：%CALLNAME:(ASSI:2),16,LEFT%　助手２：无
		PRINTFORML [1] - 助手１：%CALLNAME:(ASSI:2),16,LEFT%　助手２：%CALLNAME:(ASSI:1)%
		PRINTL [2] - 还是算了
		$INPUT_LOOP5
		INPUT
		SELECTCASE RESULT
			CASE 0
				ASSI:0 = ASSI:2
				ASSI:1 = ASSI:2
				ASSI:2 = -1
			CASE 1
				SWAP ASSI:1, ASSI:2
				ASSI:0 = ASSI:1
			CASE 2
			CASEELSE
				CLEARLINE 1
				GOTO INPUT_LOOP5
		ENDSELECT
	;その他が助手に選ばれた場合
	ELSE
		;助手なし
		IF ASSI:1 < 0
			ASSI:0 = LOCAL
			ASSI:1 = LOCAL
		;助手１
		ELSEIF ASSI:2 < 0
			PRINTL 请选择模式。
			PRINTFORML [0] - 助手１：%CALLNAME:(ASSI:1),16,LEFT%　助手２：%CALLNAME:LOCAL%
			PRINTFORML [1] - 助手１：%CALLNAME:LOCAL,16,LEFT%　助手２：%CALLNAME:(ASSI:1)%
			PRINTFORML [2] - 助手１：%CALLNAME:LOCAL,16,LEFT%　助手２：无
			PRINTL [3] - 还是算了
			$INPUT_LOOP6
			INPUT
			SELECTCASE RESULT
				CASE 0
					ASSI:2 = LOCAL
				CASE 1
					ASSI:2 = ASSI:1
					ASSI:0 = LOCAL
					ASSI:1 = LOCAL
				CASE 2
					ASSI:0 = LOCAL
					ASSI:1 = LOCAL
				CASE 3
				CASEELSE
					CLEARLINE 1
					GOTO INPUT_LOOP6
			ENDSELECT
		;助手２
		ELSE
			PRINTL 请选择模式。
			PRINTFORML [0] - 助手１：%CALLNAME:LOCAL,16,LEFT%　助手２：无
			PRINTFORML [1] - 助手１：%CALLNAME:LOCAL,16,LEFT%　助手２：%CALLNAME:(ASSI:1)%
			PRINTFORML [2] - 助手１：%CALLNAME:LOCAL,16,LEFT%　助手２：%CALLNAME:(ASSI:2)%
			PRINTFORML [3] - 助手１：%CALLNAME:(ASSI:1),16,LEFT%　助手２：%CALLNAME:LOCAL%
			PRINTFORML [4] - 助手１：%CALLNAME:(ASSI:2),16,LEFT%　助手２：%CALLNAME:LOCAL%
			PRINTL [5] - 还是算了
			$INPUT_LOOP7
			INPUT
			SELECTCASE RESULT
				CASE 0
					ASSI:0 = LOCAL
					ASSI:1 = LOCAL
					ASSI:2 = -1
				CASE 1
					ASSI:2 = ASSI:1
					ASSI:0 = LOCAL
					ASSI:1 = LOCAL
				CASE 2
					ASSI:0 = LOCAL
					ASSI:1 = LOCAL
				CASE 3
					ASSI:2 = LOCAL
				CASE 4
					ASSI:0 = ASSI:2
					ASSI:1 = ASSI:2
					ASSI:2 = LOCAL
				CASE 5
				CASEELSE
					CLEARLINE 1
					GOTO INPUT_LOOP7
			ENDSELECT
		ENDIF
	ENDIF
WEND
SIF TARGET < 0
	CALL CHANGE_TARGET(1)


;────────────────────────────────────
;脱走する
;────────────────────────────────────
@ESCAPE_X
PRINTFORMW %CALLNAME:MASTER%は監視の目を盗み、脱走を試みた！
PRINTFORMW ・
PRINTFORMW ・
PRINTFORMW ・
IF FLAG:1701 == 1
	IF CFLAG:210 == 0 && CFLAG:MASTER:15 != 3
		PRINTFORMW しかし、つかまってしまった…
		RETURN 0
	ENDIF
	FLAG:51 ++
	IF CFLAG:210 == 2 && FLAG:50 - (CAPACITY() - FLAG:1703) > 0
		PRINTFORML %CALLNAME:MASTER%は何とか迷宮から脱出し、拠点までたどり着いた。
		PRINTFORML 久々の我が家でくつろいでいると、ドアをノックする音が聞こえる。
		PRINTFORML %CALLNAME:MASTER%がドアを開けると%CALLNAME:TARGET%が立っていた。
		PRINTFORMW どうやら尾行されていたようだ…
		PRINTFORMW %CALLNAME:TARGET%が拠点に住み着きました。
		;押しかけ口上
		CALL KOJO_EVENT(14)
		FLAG:1701 = 0
		LOCAL:1 = 1
		FOR LOCAL, 0, FLAG:1703
			IF (CHARANUM - LOCAL:1) == TARGET
				LOCAL:1++
				CONTINUE
			ENDIF
			DELCHARA CHARANUM - LOCAL:1
		NEXT
		FOR LOCAL, 0, 50
			ITEM:COUNT = FLAG:(1650 + LOCAL)
		NEXT
		TARGET = CHARANUM - 1
		ASSI = -1
		ASSI:1 = -1
		ASSI:2 = -1
	ELSE
		PRINTFORMW 無事に拠点までたどり着いた。
		FLAG:1701 = 0
		FOR LOCAL, 0, FLAG:1703
			DELCHARA CHARANUM - 1
		NEXT
		TARGET = TARGET:10
		ASSI = ASSI:10
		ASSI:1 = ASSI:11
		ASSI:2 = ASSI:12
	ENDIF
ELSEIF FLAG:1701 == 2
	RESULT = 0
	CALL ESCAPE_EVENT
	IF RESULT
		FOR LOCAL, 0, 50
			ITEM:COUNT = FLAG:(1650 + LOCAL)
		NEXT
	ENDIF
ENDIF


;────────────────────────────────────
;アイテム管理
;────────────────────────────────────
@SHOP_ITEM
;SIF FLAG:4
	PRINTFORML [\@ FLAG:1701 ? --- # {0, 3} \@] - 道具购入
PRINTFORML [\@ FLAG:1701 ? --- # {1, 3} \@] - 道具管理
;PRINTFORML [  2] - 消費アイテム使用
;PRINTFORML [\@ FLAG:1701 ? --- # {3, 3} \@] - 倉庫
;PRINTFORML [\@ FLAG:1701 ? --- # {4, 3} \@] - アイテム売却
;PRINTFORML [\@ FLAG:1701 ? --- # {5, 3} \@] - アクセサリセット
SIF !FLAG:1701 && EQUIP:MASTER:10
	PRINTFORML [  6] - 礼物
PRINTL [100] - 返回
DRAWLINE
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
		SIF FLAG:1701
			GOTO CHECK_NG
		CALL ITEM_BUY
	CASE 1
		SIF FLAG:1701
			GOTO CHECK_NG
		CALL ITEM_MANAGEMENT
;	CASE 2
;		CALL ITEM_SHOW_SHOP
;	CASE 3
;		SIF FLAG:1701
;			GOTO CHECK_NG
;		CALL ITEM_STORAGE
;	CASE 4
;		SIF FLAG:1701
;			GOTO CHECK_NG
;		CALL ITEMSALE
;	CASE 5
;		SIF FLAG:1701
;			GOTO CHECK_NG
;		CALL ACCESSORY
	CASE 6
		SIF FLAG:1701 || !EQUIP:MASTER:10
			GOTO CHECK_NG
		CALL PRESENT
	CASE 100
		RETURN 0
	CASEELSE
		$CHECK_NG
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
RESTART


@ITEM_BUY
;分類の手法は素質と一緒です
VARSET LOCAL
FOR LOCAL, 0, 50
	ITEMSALES:LOCAL = !FLAG:(1600 + LOCAL)
	;価格0のITEM→未定義としておく。仕様的にマズくなったら要再検討
	SIF ITEMSALES:LOCAL !& ITEMPRICE:LOCAL
		CONTINUE
	LOCAL:1 = ITEM_TYPE(LOCAL)
	SIF LOCAL:1
		LOCAL:(100 * LOCAL:1 + 1 + LOCAL:(100 * LOCAL:1)++) = LOCAL
NEXT
IF !(LOCAL:100 || LOCAL:200 || LOCAL:300)
	PRINTW …没有发现特别值得一提的东西。
	RETURN 0
ENDIF
PRINTFORML 请选择要购买的调教道具。 所持金{MONEY}$
FOR LOCAL, 1, 4
	LOCALS = %ITEM_TYPENAME(LOCAL)%
	LOCAL:1 = STRLENS(LOCALS)
	PRINTFORML □ %LOCALS% □%"-" * (75 - LOCAL:1)%
	LOCAL:2 = LOCAL * 100
	SIF !LOCAL:(LOCAL:2)
		PRINTL ・卖完了
	FOR LOCAL:3, 0, LOCAL:(LOCAL:2)
		PRINTFORMLC [{LOCAL:(LOCAL:2 + 1 + LOCAL:3),2}] %ITEMNAME:(LOCAL:(LOCAL:2 + 1 + LOCAL:3))%(${ITEMPRICE:(LOCAL:(LOCAL:2 + 1 + LOCAL:3))})
	NEXT
NEXT
DRAWLINE
PRINTL 
PRINTL [100] - 返回
$INPUT_LOOP
INPUT
IF RESULT != 100
	IF ITEMSALES:RESULT && ITEMPRICE:RESULT && ITEM_TYPE(RESULT)
		IF MONEY < ITEMPRICE:RESULT
			CLEARLINE 1
			REUSELASTLINE 钱不够。
			GOTO INPUT_LOOP
		ENDIF
		MONEY -= ITEMPRICE:RESULT
		FLAG:(1600 + RESULT) = ++ITEM:RESULT
		PRINTFORML 购入了%ITEMNAME:RESULT%。
		IF LOCAL:100 + LOCAL:200 + LOCAL:300 == 1
			PRINTFORMW 好像没有其他值得一提的东西。%CALLNAME:MASTER%结束了购物…
			RETURN 0
		ENDIF
		RESTART
	ENDIF
	CLEARLINE 1
	REUSELASTLINE 没有卖。
	GOTO INPUT_LOOP
ENDIF


@ITEM_MANAGEMENT
;分類の手法は素質と一緒です
VARSET LOCAL
FOR LOCAL, 0, 50
	SIF !FLAG:(1600 + LOCAL)
		CONTINUE
	LOCAL:1 = ITEM_TYPE(LOCAL)
	SIF LOCAL:1
		LOCAL:(100 * LOCAL:1 + 1 + LOCAL:(100 * LOCAL:1)++) = LOCAL
NEXT
IF !(LOCAL:100 || LOCAL:200 || LOCAL:300)
	PRINTFORMW 但是，%CALLNAME:MASTER%一个调教道具也没有…
	RETURN 0
ENDIF
WHILE 1
	PRINTL 请选择要封印/解禁的调教道具。
	FOR LOCAL, 1, 4
		LOCALS = %ITEM_TYPENAME(LOCAL)%
		LOCAL:1 = STRLENS(LOCALS)
		PRINTFORML □ %LOCALS% □%"-" * (75 - LOCAL:1)%
		LOCAL:2 = LOCAL * 100
		SIF !LOCAL:(LOCAL:2)
			PRINTFORML 没有・%LOCALS%的调教道具
		FOR LOCAL:3, 0, LOCAL:(LOCAL:2)
			IF ITEM:(LOCAL:(LOCAL:2 + 1 + LOCAL:3))
				LOCAL:4 = GETSTYLE()
				FONTBOLD
				PRINTFORMLC 　 [{LOCAL:(LOCAL:2 + 1 + LOCAL:3),2}] %ITEMNAME:(LOCAL:(LOCAL:2 + 1 + LOCAL:3))%
				FONTSTYLE LOCAL:4
			ELSE
				LOCAL:4 = GETCOLOR()
				SETCOLOR 0xA0A0A0
				PRINTFORMLC 封 [{LOCAL:(LOCAL:2 + 1 + LOCAL:3),2}] %ITEMNAME:(LOCAL:(LOCAL:2 + 1 + LOCAL:3))%
				SETCOLOR LOCAL:4
			ENDIF
		NEXT
	NEXT
	DRAWLINE
	FOR LOCAL, 2, 8
		IF LOCAL:(LOCAL / 2 * 100)
			PRINTFORMLC [{LOCAL + 90, 3}] %ITEM_TYPENAME(LOCAL / 2)%\@ LOCAL & 1 ? 解禁 # 封印 \@
			SIF LOCAL & 1
				PRINTL 
		ENDIF
	NEXT
	PRINTLC [ 98] 封印一切
	PRINTLC [ 99] 全部解禁
	PRINTL 
	PRINTL 
	PRINTL [100] - 返回
	$INPUT_LOOP
	INPUT
	SELECTCASE RESULT
		CASE 92, 93
			SIF !LOCAL:100
				GOTO CHECK_NG
			FOR LOCAL, 0, LOCAL:100
				ITEM:(LOCAL:(101 + LOCAL)) = RESULT == 92 ? 0 # FLAG:(1600 + LOCAL:(101 + LOCAL))
			NEXT
		CASE 94, 95
			SIF !LOCAL:200
				GOTO CHECK_NG
			FOR LOCAL, 0, LOCAL:200
				ITEM:(LOCAL:(201 + LOCAL)) = RESULT == 94 ? 0 # FLAG:(1600 + LOCAL:(201 + LOCAL))
			NEXT
		CASE 96, 97
			SIF !LOCAL:300
				GOTO CHECK_NG
			FOR LOCAL, 0, LOCAL:300
				ITEM:(LOCAL:(301 + LOCAL)) = RESULT == 96 ? 0 # FLAG:(1600 + LOCAL:(301 + LOCAL))
			NEXT
		CASE 98, 99
			FOR LOCAL, 0, 50
				ITEM:LOCAL = RESULT == 98 ? 0 # FLAG:(1600 + LOCAL)
			NEXT
		CASE 100
			BREAK
		CASEELSE
			IF !FLAG:(1600 + RESULT)
				$CHECK_NG
				CLEARLINE 1
				GOTO INPUT_LOOP
			ENDIF
			ITEM:RESULT = ITEM:RESULT ? 0 # FLAG:(1600 + RESULT)
	ENDSELECT
WEND
REPEAT 50
	FLAG:(1650 + COUNT) = ITEM:COUNT
REND

@BASE_ENLARGEMENT
DRAWLINE
PRINTFORML 总房间数{FLAG:50}
PRINTFORML 现在据点生活着{CAPACITY()}人
PRINTL [0] - 返回
PRINTFORML [1] - 增建房间　{FLAG:50 * 10000}$
PRINTFORML \@ !GETBIT(FLAG:60,0) ? [2] # [-] \@ - 大浴场　100000$

INPUT
IF RESULT == 0
	RETURN 0
ELSEIF RESULT == 1
	PRINTFORML {FLAG:50 * 10000}$必要です
	IF MONEY < FLAG:50 * 10000
		PRINTW 持有金不足
		RESTART
	ELSE
		PRINTL 要增建吗？
		PRINTL [0] - 是
		PRINTL [1] - 否
		INPUT
		IF RESULT == 0
			MONEY -= FLAG:50 * 10000
			FLAG:50 += 1
		ENDIF
	ENDIF
	RESTART
ELSEIF RESULT == 2 && !GETBIT(FLAG:60,0)
	PRINTFORML 需要100000$
	IF MONEY < 100000
		PRINTW 持有金不足
		RESTART
	ELSE
		PRINTL 要建筑吗？
		PRINTL [0] - 是
		PRINTL [1] - 否
		INPUT
		IF RESULT == 0
			MONEY -= 100000
			SETBIT FLAG:60,0
		ENDIF
	ENDIF
	RESTART
ELSE
	RESTART
ENDIF

;────────────────────────────────────
;キャラの体力一覧
;────────────────────────────────────
@LIFE_LIST
DRAWLINE
FOR LOCAL, 0, CHARANUM
	;調教中は、調教対象/調教者/助手１/助手２以外を表示しない
	;監禁中は、調教対象と、監禁している夢魔(FLAG:30番以降の登録番号)以外を表示しない
	;一時退避中のキャラは表示しない
	SIF (FLAG:10 && LOCAL != MASTER && LOCAL != TARGET && LOCAL != ASSI:1 && LOCAL != ASSI:2 && LOCAL != ASSI:3) || (FLAG:1701 && LOCAL != MASTER && LOCAL < FLAG:30) || CFLAG:LOCAL:92
		CONTINUE
	PRINTFORM  [{LOCAL,3}] %CALLNAME:LOCAL,16,LEFT% 体力({MAX(BASE:LOCAL:0,0),4}/{MAXBASE:LOCAL:0,4}) 
	PRINTFORM  [LV {CFLAG:LOCAL:0,2} NEXT {CFLAG:LOCAL:4 - CFLAG:LOCAL:1,6}]
	SELECTCASE LOCAL
		CASE TARGET
			PRINT  调教者
		CASE ASSI:1
			PRINT  助手１
		CASE ASSI:2
			PRINT  助手２
		CASE ASSI:3
			IF FLAG:1710
				PRINT  助手３
			ELSE
				PRINT  乱入者
			ENDIF
	ENDSELECT
	SIF TALENT:LOCAL:131
		PRINT  <主人>
	PRINTL 
	SIF LOCAL == MASTER
		DRAWLINE
NEXT


;────────────────────────────────────
;キャラのステータス表示
;────────────────────────────────────
@SHOW_CHARADATA(ARG)
CALL LIFE_LIST
DRAWLINE
PRINTL  [100] - 返回
$INPUT_LOOP
INPUT
SIF RESULT == 100
	RETURN 0
IF RESULT < 0 || CHARANUM <= RESULT || CFLAG:RESULT:92
	CLEARLINE 1
	GOTO INPUT_LOOP
ENDIF
;調教中に他のキャラのステータス見れたり、監禁中に拠点に残ってるキャラのステータス見れたりするのはどうしようか…
;見れてもいいような気もしないでもないし、見れなくていいような気もしないでもない
;とりあえずデバッグモード中だけ見れるようにしてみる
IF !FLAG:4
	IF (FLAG:10 && RESULT != MASTER && RESULT != TARGET && RESULT != ASSI:1 && RESULT != ASSI:2 && RESULT != ASSI:3) || (FLAG:1701 && RESULT != MASTER && RESULT < FLAG:30)
		CLEARLINE 1
		GOTO INPUT_LOOP
	ENDIF
ENDIF
LOCAL = RESULT
CALL PRINT_STATUS(RESULT)
IF ARG
	IF !CFLAG:LOCAL:204
		CFLAG:LOCAL:204 = 1
		FLAG:(4000 + NO:LOCAL) += CFLAG:MASTER:15 == 2 ? 120 # 100
	ENDIF
	RETURN 0
ENDIF
RESTART


@FLAGCHECK
PRINTL [0] - 返回
PRINTL [1] - FLAG
PRINTL [2] - CFLAG
PRINTL [3] - FLAG转储器
PRINTL [4] - CFLAG转储器
$INPUT_LOOP1
INPUT
SELECTCASE RESULT
	CASE 0
	CASE 1
		VARSIZE FLAG
		LOCAL = RESULT
		PRINTFORML 请输入FLAG号码(0～{LOCAL-1})。
		$INPUT_LOOP2
		INPUT
		IF RESULT < 0 || LOCAL <= RESULT
			CLEARLINE 1
			REUSELASTLINE LAG的范围是(0～{LOCAL-1})。
			GOTO INPUT_LOOP2
		ENDIF
		PRINTFORMW FLAG:{RESULT} = {FLAG:RESULT}
		RESTART
	CASE 2
		PRINTFORML 请输入对象角色的登录号码(0～{CHARANUM-1})。
		$INPUT_LOOP3
		INPUT
		IF RESULT < 0 || CHARANUM <= RESULT
			CLEARLINE 1
			REUSELASTLINE 不存在。
			GOTO INPUT_LOOP3
		ENDIF
		LOCAL = RESULT
		VARSIZE CFLAG
		LOCAL:1 = RESULT
		PRINTFORML 选择了%CALLNAME:LOCAL%。
		PRINTFORML 请输入CFLAG号码(0～{LOCAL:1-1})。
		$INPUT_LOOP4
		INPUT
		IF RESULT < 0 || LOCAL:1 <= RESULT
			CLEARLINE 1
			REUSELASTLINE CFLAG的范围是(0～{LOCAL:1-1})
			GOTO INPUT_LOOP4
		ENDIF
		PRINTFORMW %CALLNAME:LOCAL%的CFLAG:{RESULT} = {CFLAG:LOCAL:RESULT}
		RESTART
	CASE 3
		PRINTL FLAG一览(省略值为0)
		FOR LOCAL, 0, VARSIZE("FLAG")
			SIF FLAG:LOCAL
				PRINTFORMLC [FLAG:{LOCAL,4}={FLAG:LOCAL,10}]
		NEXT
		WAIT
		RESTART
	CASE 4
		PRINTFORML 请输入对象角色的登录号码(0～{CHARANUM-1})。
		$INPUT_LOOP5
		INPUT
		IF RESULT < 0 || CHARANUM <= RESULT
			CLEARLINE 1
			REUSELASTLINE 不存在。
			GOTO INPUT_LOOP5
		ENDIF
		PRINTFORML %CALLNAME:RESULT%的CFLAG一览(价值为0的东西省略)
		FOR LOCAL, 0, VARSIZE("CFLAG")
			SIF CFLAG:RESULT:LOCAL
				PRINTFORMLC [CFLAG:{LOCAL,4}={CFLAG:RESULT:LOCAL,10}]
		NEXT
		WAIT
		RESTART
	CASEELSE
		CLEARLINE 1
		GOTO INPUT_LOOP1
ENDSELECT


;────────────────────────────────────
;セーブインフォ
;────────────────────────────────────
@SAVEINFO
PUTFORM {DAY+1}日%GET_TIME()% \@ TARGET >= 0 ? %CALLNAME:TARGET%调教%CALLNAME:MASTER%中 # \@


;────────────────────────────────────
;バージョンアップ
;────────────────────────────────────
@VERSION_UP
IF FLAG:2999 == 0
	LOCAL = TARGET
	REPEAT CHARANUM
		CALL CLOTHES_SETUP(COUNT)
	REND
	TARGET = LOCAL
FLAG:2999 = 1
ENDIF
IF FLAG:2999 < 2
	FOR LOCAL , 0 , CHARANUM
		SIF TALENT:LOCAL:170
			TALENT:LOCAL:118 = 0
	NEXT
	TALENT:MASTER:118 = 0
	FLAG:2999 = 2
ENDIF
IF FLAG:2999 < 3
	LOCAL:1 = TARGET
	FOR LOCAL , 0 , CHARANUM
		CALL CLOTHES_SETUP(LOCAL)
	NEXT
	TARGET = LOCAL:1
	FLAG:2999 = 3
ENDIF
IF FLAG:2999 < 4
	FOR LOCAL , 0 , CHARANUM
		CFLAG:MASTER:300 |= CFLAG:LOCAL:300
	NEXT
	FLAG:31 = DAY % 120
	FLAG:32 = DAY / 120
	FLAG:33 = FLAG:31 / 30
	FLAG:2999 = 4
ENDIF
IF FLAG:2999 < 5
	FOR LOCAL , 3 , 10
		ASSI:LOCAL = -1
	NEXT
	FLAG:2999 = 5
ENDIF
IF FLAG:2999 < 7
	FOR LOCAL,0,CHARANUM
		FOR LOCAL:1,2000,2300
			CFLAG:LOCAL:(LOCAL:1) = 0
		NEXT
	NEXT
	FLAG:2999 = 7
ENDIF
IF FLAG:2999 < 8
	SIF !FLAG:50
		FLAG:50 = 4
	FLAG:2999 = 8
ENDIF
IF FLAG:2999 < 10
	CFLAG:MASTER:203 = MAXBASE:MASTER:2 / 500 + 5
	FLAG:2999 = 10
ENDIF
IF FLAG:2999 < 11
	FOR LOCAL,1,CHARANUM
		LOCAL:1 = RAND:100 + 1
		LOCAL:2 = RAND:100 + 1
		LOCAL:3 = RAND:100 + 1
		
		TALENT:LOCAL:220 = 100 * LOCAL:1 / (LOCAL:1 + LOCAL:2 + LOCAL:3)
		TALENT:LOCAL:221 = 100 * LOCAL:2 / (LOCAL:1 + LOCAL:2 + LOCAL:3)
		TALENT:LOCAL:222 = 100 - TALENT:LOCAL:220 - TALENT:LOCAL:221
	NEXT
	FLAG:2999 = 11
ENDIF
IF FLAG:2999 < 12
	SAVESTR:0 = %STR:(500 + CFLAG:MASTER:15)%
	FOR LOCAL,0,CHARANUM
		CFLAG:MASTER:303 += POWER(MARK:LOCAL:2,2)
	NEXT
	FLAG:2999 = 12
ENDIF
IF FLAG:2999 < 13
	FOR LOCAL,0,CHARANUM
		SIF CFLAG:LOCAL:302
			SAVESTR:1 = %CALLNAME:LOCAL%
	NEXT
	FLAG:2999 = 13
ENDIF
IF FLAG:2999 < 14
	FOR LOCAL,0,CHARANUM
		CFLAG:LOCAL:100 = TALENT:LOCAL:220
		CFLAG:LOCAL:101 = TALENT:LOCAL:221
		CFLAG:LOCAL:102 = TALENT:LOCAL:222
	NEXT
	FLAG:2999 = 14
ENDIF
IF FLAG:2999 < 15
	MAXBASE:MASTER:9 = 2
	BASE:MASTER:9 = 2
	FLAG:2999 = 15
ENDIF
IF FLAG:2999 < 16
	FOR LOCAL,0,CHARANUM
		CFLAG:LOCAL:40 = 0
		CFLAG:LOCAL:41 = 0
		TALENT:LOCAL:2 = 0
		TALENT:LOCAL:3 = 0
		TALENT:LOCAL:114 = 0
		
	NEXT
	FLAG:2999 = 16
ENDIF
IF FLAG:2999 < 17
	FOR LOCAL,0,CHARANUM
		IF TALENT:LOCAL:122
			CFLAG:LOCAL:40 = 0
			CFLAG:LOCAL:41 = 0
			TALENT:LOCAL:2 = 0
			TALENT:LOCAL:3 = 0
			TALENT:LOCAL:114 = 0
		ENDIF
	NEXT
	FLAG:2999 = 17
ENDIF
IF FLAG:2999 < 18
	FLAG:1745 = FLAG:1746
	FLAG:1746 = 0
	FLAG:2999 = 18
ENDIF