﻿;初期設定
@DUNGEON_ROOM
CALL DUNGEON_MAP_PRODUCE_ROOM

;階層構造
@DUNGEON_ROOM_STAIRS
;PRINTFORMW 
FLAG:1704 = 0
FLAG:1705 = 0
FLAG:1700 = 6
CALL DUNGEON_MAP_PRODUCE_ROOM

@DUNGEON_EVENT_ROOM
PRINTL ROOM
IF !FLAG:1746
	FOR LOCAL,0,4
		SELECTCASE LOCAL
			CASE 0
				Z = 1
			CASE 1
				Z = 13
			CASE 2
				Z = 33
			CASE 3
				Z = 21
		ENDSELECT
		ADDCHARA Z
		TARGET = CHARANUM - 1
		;レベル、BASE、３サイズ、経験、能力等々の設定
		CFLAG:0 = 80
		SIF LOCAL == 3
			CFLAG:0 = 100
		CFLAG:10 = 2
		CFLAG:94 = 1
		CALL CALL_NAME
		CALL BASE_TRAINER_SETUP
;		CALL GET_EXTALENT
;		CALL SIZE_SET
		CALL EXP_REVISION
		CALL ABL_SET
		CALL BASE_TRAINER_SETUP
	NEXT
	;夢魔の数
	FLAG:1703 = 4
	ASSI   = CHARANUM - 2
	ASSI:1 = CHARANUM - 2
	ASSI:2 = CHARANUM - 3
	ASSI:3 = CHARANUM - 4
	;イベントバトルフラグ
	FLAG:1710 = 1
	FLAG:1746 |= 1
	PRINTFORML アスモデが現れた！
	PRINTFORML レヴィアタンが現れた！
	PRINTFORML マモンが現れた！
	PRINTFORMW ルシフェルが現れた！
	BEGIN TRAIN
ELSEIF FLAG:1746 & 1
	IF FLAG:1710 == 1
		PRINTFORML %CALLNAME:MASTER%は夢魔を振りほどき一目散に逃げ出した
		PRINTFORMW しかし出入口はいつの間にか消え去っていた…
		PRINTFORMW 夢魔達は再び%CALLNAME:MASTER%に群がり始めた…
		BEGIN TRAIN
	ELSE
		FLAG:1710 = 1
		BASE:MASTER:2 = MAXBASE:MASTER:2
		FOR LOCAL,0,4
			DELCHARA CHARANUM - 1
		NEXT
		TARGET = -1
		ASSI = -1
		ASSI:1 = -1
		ASSI:2 = -1
		ASSI:3 = -1
		PRINTFORMW エスト様が現れた！
		Z = 48
		ADDCHARA 48
		TARGET = CHARANUM - 1
		CFLAG:0 = 500
		CFLAG:10 = 5
		TALENT:220 = 100
		TALENT:221 = 100
		TALENT:221 = 100
		TALENT:追加愛撫 = 2
		TALENT:エナジードレイン = 2
		CALL CALL_NAME
		CALL BASE_TRAINER_SETUP
;		CALL GET_EXTALENT
;		CALL SIZE_SET
		CALL EXP_REVISION
		CALL ABL_SET
		CALL BASE_TRAINER_SETUP
		BEGIN TRAIN
	ENDIF
ENDIF
