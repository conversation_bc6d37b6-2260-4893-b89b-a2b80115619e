﻿;────────────────────────────────────
;性交(奉仕) (正常95/後背96/騎乗36/対面97/背面98/ＡＳ99)
;────────────────────────────────────
@ACT_M10
CALL ABL_REVISION
;基準値
LOCAL:95 = 10
LOCAL:96 = 8 + RAND:2
LOCAL:36 = 8 + RAND:2
LOCAL:97 = 8 + RAND:2
LOCAL:98 = 7 + RAND:4
LOCAL:99 = 5

;調教方針(1=休憩/2=ソフト/3=ノーマル/4=ハード/5=異常)
SELECTCASE TFLAG:70
	CASE 2
		LOCAL:99 += 2
	CASE 4
		LOCAL:96 += 4
		LOCAL:98 += 3
		LOCAL:99 += 3
	CASE 5
		LOCAL:95 += 1 + RAND:3
		LOCAL:36 += 1 + RAND:5
		LOCAL:97 += 2
ENDSELECT

;────────────────────────────────────
;素質による変動
;────────────────────────────────────
;調教者が臆病/気丈
IF TALENT:10
	LOCAL:95 += RAND:3
	LOCAL:36 -= 5
	LOCAL:97 += RAND:4
ELSEIF TALENT:12
	LOCAL:96 += 2
	LOCAL:98 += 2
	LOCAL:99 += 1
ENDIF

;調教者が反抗的/素直
IF TALENT:11
	LOCAL:36 += RAND:10
ELSEIF TALENT:13
	LOCAL:36 -= 3
ENDIF

;調教者が一線越えない
IF TALENT:28
	LOCAL:96 -= 3
	LOCAL:36 -= 4
	LOCAL:97 -= 1
	LOCAL:98 -= 3
	LOCAL:99 -= 5
ENDIF
;調教者が清楚
IF TALENT:37
	LOCAL:95 += 3
	LOCAL:97 -= 1
	LOCAL:98 -= 3
	LOCAL:99 -= 3
ENDIF
;調教者が目立ちたがり
SIF TALENT:29
	LOCAL:36 += RAND:6

;調教者の処女、貞操観念などの処理
IF TALENT:0 && CFLAG:2 < 1000
	LOCAL:95 -= 5 + TALENT:30 * 5 - TALENT:31 * 3 + CFLAG:2 / 500
	LOCAL:96 -= 5 + TALENT:30 * 5 - TALENT:31 * 3 + CFLAG:2 / 500
	LOCAL:36 -= 5 + TALENT:30 * 5 - TALENT:31 * 3 + CFLAG:2 / 500
	LOCAL:97 -= 5 + TALENT:30 * 5 - TALENT:31 * 3 + CFLAG:2 / 500
	LOCAL:98 -= 5 + TALENT:30 * 5 - TALENT:31 * 3 + CFLAG:2 / 500
ENDIF

;調教者が受け身
IF TALENT:65
	LOCAL:96 += 3
	LOCAL:98 += 3
ENDIF
;調教者が献身的
IF TALENT:63
	LOCAL:96 -= 3
	LOCAL:98 -= 3
ENDIF
;調教者が淫壷
IF TALENT:73
	LOCAL:95 += 4 + RAND:3
	LOCAL:96 += 4 + RAND:3
	LOCAL:36 += 4 + RAND:3
	LOCAL:97 += 4 + RAND:3
	LOCAL:98 += 4 + RAND:3
ENDIF

;調教者がサド
IF TALENT:83
	LOCAL:95 += 2
	LOCAL:36 += 5
	LOCAL:97 += 3
ENDIF
;調教者が心優しい
IF TALENT:87
	LOCAL:95 += 2
	LOCAL:97 += 2
ENDIF
;調教者がＣ敏感/鈍感
IF TALENT:100
	LOCAL:97 += RAND:3
	LOCAL:98 += RAND:3
ELSEIF TALENT:101
	LOCAL:97 -= RAND:3
	LOCAL:98 -= RAND:3
ENDIF

;調教者がＶ敏感/鈍感
IF TALENT:102
	LOCAL:95 += RAND:5
	LOCAL:96 += RAND:5
	LOCAL:36 += RAND:5
	LOCAL:97 += RAND:5
	LOCAL:98 += RAND:5
ELSEIF TALENT:103
	LOCAL:99 += RAND:3
ENDIF

;調教対象が童貞
IF TALENT:MASTER:1
	LOCAL:95 -= 7 - CFLAG:3 * 5 - CFLAG:2 / 500 - A:30 / 3
	LOCAL:96 -= 7 - CFLAG:3 * 5 - CFLAG:2 / 500 - A:30 / 3
	LOCAL:36 -= 7 - CFLAG:3 * 5 - CFLAG:2 / 500 - A:30 / 3
	LOCAL:97 -= 7 - CFLAG:3 * 5 - CFLAG:2 / 500 - A:30 / 3
	LOCAL:98 -= 7 - CFLAG:3 * 5 - CFLAG:2 / 500 - A:30 / 3
ENDIF

;調教対象が倒錯的
SIF TALENT:MASTER:80
	LOCAL:99 += 2 + RAND:2

;────────────────────────────────────
;能力、パラメーターによる変動
;────────────────────────────────────
;調教者のＣ感覚
IF ABL:3
	LOCAL:97 += ABL:3 + RAND:2
	LOCAL:98 += ABL:3 + RAND:2
ENDIF

;調教者のＶ感覚
IF ABL:4
	LOCAL:95 += ABL:4 + RAND:3
	LOCAL:96 += ABL:4 + RAND:3
	LOCAL:36 += ABL:4 + RAND:3
	LOCAL:97 += ABL:4 + RAND:2
	LOCAL:98 += ABL:4 + RAND:2
ENDIF

;調教者のＡ感覚
SIF ABL:5
	LOCAL:99 += ABL:5 + RAND:2

;調教者のＡ経験
SELECTCASE EXP:2
	CASE IS < 2
		LOCAL:99 -= 15
	CASE IS < 5
		LOCAL:99 -= 10
	CASE IS < 10
		LOCAL:99 -= 7
	CASE IS < 15
		LOCAL:99 -= 3
	CASE IS < 20
		LOCAL:99 -= 1
ENDSELECT

;────────────────────────────────────
;前回の行動や状況による変動
;────────────────────────────────────
;縄
IF TEQUIP:40 || TEQUIP:46 || TEQUIP:47
	LOCAL:96 -= 5
	LOCAL:99 -= 5
ENDIF

;アイマスク
IF TEQUIP:41
	LOCAL:96 -= 5
	LOCAL:99 -= 5
ENDIF

;大鏡
SIF TEQUIP:56
	LOCAL:98 += 3

;同一内容をやりすぎないように
SELECTCASE TEQUIP:71
	CASE 1
		LOCAL:95 -= 2 * (TFLAG:450 - 1)
	CASE 2
		LOCAL:96 -= 2 * (TFLAG:450 - 1)
	CASE 3
		LOCAL:36 -= 2 * (TFLAG:450 - 1)
	CASE 4
		LOCAL:97 -= 2 * (TFLAG:450 - 1)
	CASE 5
		LOCAL:98 -= 2 * (TFLAG:450 - 1)
	CASE 6
		LOCAL:99 -= 2 * (TFLAG:450 - 1)
ENDSELECT
;同一体位抑制
IF TFLAG:351 > 1
	IF TFLAG:351 > 100
		TFLAG:351 = 0
	ELSEIF TFLAG:351 > 10
		TFLAG:351 += 100
	ENDIF
	TFLAG:351 += 10
	LOCAL:95 = MIN(LOCAL:95, LOCAL:96, LOCAL:36, LOCAL:97, LOCAL:98) - 1
ENDIF
IF TFLAG:352 > 1
	IF TFLAG:352 > 100
		TFLAG:352 = 0
	ELSEIF TFLAG:352 > 10
		TFLAG:352 += 100
	ENDIF
	TFLAG:352 += 10
	LOCAL:96 = MIN(LOCAL:95, LOCAL:96, LOCAL:36, LOCAL:97, LOCAL:98) - 1
ENDIF
IF TFLAG:353 > 1
	IF TFLAG:353 > 100
		TFLAG:353 = 0
	ELSEIF TFLAG:353 > 10
		TFLAG:353 += 100
	ENDIF
	TFLAG:353 += 10
	LOCAL:36 = MIN(LOCAL:95, LOCAL:96, LOCAL:36, LOCAL:97, LOCAL:98) - 1
ENDIF
IF TFLAG:354 > 1
	IF TFLAG:354 > 100
		TFLAG:354 = 0
	ELSEIF TFLAG:354 > 10
		TFLAG:354 += 100
	ENDIF
	TFLAG:354 += 10
	LOCAL:97 = MIN(LOCAL:95, LOCAL:96, LOCAL:36, LOCAL:97, LOCAL:98) - 1
ENDIF
IF TFLAG:355 > 1
	IF TFLAG:355 > 100
		TFLAG:355 = 0
	ELSEIF TFLAG:355 > 10
		TFLAG:355 += 100
	ENDIF
	TFLAG:355 += 10
	LOCAL:98 = MIN(LOCAL:95, LOCAL:96, LOCAL:36, LOCAL:97, LOCAL:98) - 1
ENDIF

;────────────────────────────────────
;実行不可能の判定
;────────────────────────────────────
;不可能判定とカウンタ値の下限チェック
CALL ACT_ABLE36
SIF !RESULT || LOCAL:36 < -99
	LOCAL:36 = -99
FOR LOCAL:900, 95, 100
	CALLFORM ACT_ABLE{LOCAL:900}
	SIF !RESULT || LOCAL:(LOCAL:900) < -99
		LOCAL:(LOCAL:900) = -99
NEXT

;────────────────────────────────────
;たまにはアナルも使うように
;────────────────────────────────────
IF LOCAL:99 > 0
	LOCAL:99 += CFLAG:199 / 10
	CFLAG:199 += LOCAL:99
ENDIF
;────────────────────────────────────
;最終判定
;────────────────────────────────────
SELECTCASE MAX(LOCAL:95, LOCAL:96, LOCAL:36, LOCAL:97, LOCAL:98, LOCAL:99)
;ここには来ないはず
;	CASE -99
;		PRINTL (性交奉仕カウンタ異常)
;		TFLAG:80 = 0
;		TFLAG:90 = 0
	CASE LOCAL:95
		TFLAG:90 = 95
		TFLAG:351 += 1
	CASE LOCAL:97
		TFLAG:90 = 97
		TFLAG:354 += 1
	CASE LOCAL:96
		TFLAG:90 = 96
		TFLAG:352 += 1
	CASE LOCAL:98
		TFLAG:90 = 98
		TFLAG:355 += 1
	CASE LOCAL:99
		TFLAG:90 = 99
		CFLAG:199 = 0
	CASE LOCAL:36
		TFLAG:90 = 36
		TFLAG:353 += 1
ENDSELECT

;デバッグ＆調整用カウンタ
IF FLAG:4
	PRINTFORML 性交侍奉：正常[{LOCAL:95,3}]/后背[{LOCAL:96,3}]/骑乘[{LOCAL:36,3}]/对面[{LOCAL:97,3}]/背面[{LOCAL:98,3}]
	PRINTFORML 　　　　　ＡＳ[{LOCAL:99,3}]
ENDIF


;-----------------------------------------------------------
;性交(奉仕) の実行判定
;-----------------------------------------------------------
@ACTM_ABLE10
CALL ACT_ABLE36
SIF RESULT
	RETURN 1
FOR LOCAL, 95, 100
	CALLFORM ACT_ABLE{LOCAL}
	SIF RESULT
		RETURN 1
NEXT
RETURN 0
