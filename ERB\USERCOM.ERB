﻿@SHOW_USERCOM
TFLAG:3 = 1
FOR LOCAL,0,100
	RESULT = 0
	TRYCALLFORM COM_ABLE{LOCAL}
	IF RESULT
		IF STRLENS(TRAINNAME:LOCAL)
			TSTR:3 = %TRAINNAME:LOCAL%
			SIF LOCAL == 71 && PENIS(TARGET)
				TSTR:3 = 口交
			PRINTFORMC %TSTR:3%[{LOCAL,3}]
		ENDIF
	ENDIF
NEXT
PRINTL 
PRINTC 显示覆历[800]
PRINTC 显示脏污[801]
SIF !FLAG:1700 || FLAG:4 || CFLAG:MASTER:15 == 2
	PRINTC 能力表示[802]
PRINTL 
IF FLAG:4
	PRINTC 行动预约[997]
	PRINTC 激情值表示[998]
	PRINTC 强制结束调教[999]
	;デバッグのため999は調教強制終了のままで
ENDIF


@USERCOM
SELECTCASE RESULT
	CASE 0 TO 100
		SIF TRAINNAME:RESULT == ""
			RETURN 0
		SELECTCOM = RESULT
		RESULT = 0
		TRYCALLFORM COM_ABLE{SELECTCOM}
		SIF RESULT == 0
			RETURN 0
		DOTRAIN SELECTCOM
	CASE 800
		CALL SHOW_EQUIP
		TFLAG:92 = 0
		RETURN 1
	CASE 801
		CALL STAIN_INFO
		TFLAG:92 = 0
		RETURN 1
	CASE 802
		IF !FLAG:1700 || FLAG:4 || CFLAG:MASTER:15 == 2
			CALL SHOW_CHARADATA
			TFLAG:92 = 0
			RETURN 1
		ENDIF
	CASE 899
		IF FLAG:4
			PRINTFORML TFLAG:92 = {TFLAG:92}
			TFLAG:92 = 0
			RETURN 1
		ENDIF
	CASE 996
		IF FLAG:1700
			PRINTW Qy@
			BASE:MASTER:2 = 0
			BASE:MASTER:5 = 0
			BASE:MASTER:9 = 0
			TFLAG:92 = 0
			RETURN 1
		ENDIF
	CASE 997
		IF FLAG:4
			CALL ACTION_RESERVATION
			TFLAG:92 = 0
			RETURN 1
		ENDIF
	CASE 998
		IF FLAG:4
			CALL SHOW_HEAT
			TFLAG:92 = 0
			RETURN 1
		ENDIF
	CASE 999
		SIF FLAG:4
			BEGIN AFTERTRAIN
ENDSELECT

@SHOW_EQUIP
SIF TEQUIP:52
	PRINT  [野外]
SIF TEQUIP:56
	PRINT  [大镜子]
SIF TEQUIP:68
	PRINT  [挑逗]
SIF TEQUIP:69 == 1
	PRINTFORM  [%CALLNAME:MASTER%自慰中]
SIF TEQUIP:69 == 2
	PRINTFORM  [%CALLNAME:TARGET%自慰中]
SIF TEQUIP:69 == 3
	PRINT  [二人自慰中]
SIF TEQUIP:70
	PRINT  [插入中]
SIF TEQUIP:90
	PRINT  [触手] 
PRINTL 

SIF TEQUIP:10
	PRINTFORM [润滑乳液(%EFFECT(TEQUIP:10, 1)%)]
SIF TEQUIP:11
	PRINTFORM [媚药(%EFFECT(TEQUIP:11)%)]
SIF TEQUIP:12
	PRINTFORM [利尿剂(%EFFECT(TEQUIP:12)%)]
SIF TEQUIP:18
	PRINTFORM [铃兰的毒(%EFFECT(TEQUIP:18)%)]
SIF TEQUIP:20
	PRINTFORM [\@ TEQUIP:20 > 1 ? 特注 # \@振动棒]
SIF TEQUIP:25
	PRINTFORM [\@ TEQUIP:25 > 1 ? 特注 # \@肛用振动棒]
SIF TEQUIP:26
	PRINT [后庭拉珠]
SIF TEQUIP:27
	PRINT [灌肠＋肛门塞]
SIF TEQUIP:30
	PRINTFORM [\@ TEQUIP:30 > 1 ? 特注 # \@阴蒂夹]
SIF TEQUIP:31
	PRINTFORM [\@ TEQUIP:31 > 1 ? 特注 # \@飞机杯]
SIF TEQUIP:35
	PRINTFORM [\@ TEQUIP:35 > 1 ? 特注 # \@乳头夹]
SIF TEQUIP:36
	PRINTFORM [\@ TEQUIP:36 > 1 ? 特注 # \@挤奶器]
SIF TEQUIP:40
	PRINT [绳子紧缚]
SIF TEQUIP:41
	PRINT [眼罩]
SIF TEQUIP:42
	PRINT [口塞球]
SIF TEQUIP:43
	PRINT [三角木马骑马中]
SIF TEQUIP:46
	PRINT [藤蔓拘束中]
SIF TEQUIP:47
	PRINT [蛇体拘束中]
PRINTL 

PRINTFORM \@ EX:0 ? Ｃ绝顶:{EX:0}回%" "% # \@\@ EX:1 ? Ｖ绝顶:{EX:1}回%" "% # \@\@ EX:2 ? Ａ绝顶:{EX:2}回%" "% # \@\@ EX:3 ? Ｂ绝顶:{EX:3}回%" "% # \@
PRINTFORM \@ EX:10 ? 喷乳:{EX:10}回%" "% # \@\@ EX:11 ? 射精:{EX:11}回%" "% # \@\@ EX:12 ? 放尿:{EX:12}回%" "% # \@
SIF EX:0 || EX:1 || EX:2 || EX:3 || EX:10 || EX:11 || EX:12
	PRINTL 
SIF EX:40 || EX:41 || EX:42 || EX:43 || EX:50 || EX:51
	PRINTL （调教者）
PRINTFORM \@ EX:40 ? ｃ绝顶:{EX:40}回%" "% # \@\@ EX:41 ? ｖ绝顶:{EX:41}回%" "% # \@\@ EX:42 ? ａ绝顶:{EX:42}回%" "% # \@\@ EX:43 ? ｂ绝顶:{EX:43}回%" "% # \@
PRINTFORM \@ EX:50 ? 喷乳:{EX:50}回%" "% # \@\@ EX:51 ? 射精:{EX:51}回%" "% # \@
SIF EX:40 || EX:41 || EX:42 || EX:43 || EX:50 || EX:51
	PRINTL 
WAIT


@SHOW_HEAT
PRINTFORML 休息[{TFLAG:71,3}]/ 温柔[{TFLAG:72,3}]/正常[{TFLAG:73,3}]/严厉[{TFLAG:74,3}]/异常[{TFLAG:75,3}]
PRINTFORML 聊天[{TFLAG:81,3}]/爱抚[{TFLAG:82,3}]/道具[{TFLAG:83,3}]/性交[{TFLAG:84,3}]/羞恥[{TFLAG:85,3}]
PRINTFORML 侍奉[{TFLAG:86,3}]/加虐[{TFLAG:87,3}]/异常[{TFLAG:88,3}]/使役[{TFLAG:89,3}]/性奉[{TFLAG:350,3}]
PRINTFORML ﾛｰｼｮ[{FLAG:20,3}]/媚药[{FLAG:21,3}]/营养[{FLAG:23,3}]/治疗[{FLAG:24,3}]
WAIT


@ACTION_RESERVATION
DRAWLINE
PRINTL [0] - 返回
PRINTFORML [1] - 調教对象\@ TFLAG:211 >= 0 ? 下一次的行动:%STR:(TFLAG:211 + 100)% # 行动预约 \@
SIF ASSI:1 >= 0
	PRINTL [2] - 助手1的行动预约
SIF ASSI:2 >= 0
	PRINTL [3] - 助手2的行动预约
SIF ASSI:1 >= 0 && !TFLAG:210 && TFLAG:211 >= 0 && TFLAG:212 < 0
	PRINTL [4] - 组合预约
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
		RETURN 0
	CASE 1
		PRINTL 请输入行动编号。
		INPUT
		TFLAG:211 = RESULT
	CASE 2
		SIF ASSI:1 < 0
			GOTO INPUT_LOOP
		PRINTL 请输入行动编号。
		INPUT
		TFLAG:212 = RESULT
	CASE 3
		SIF ASSI:2 < 0
			GOTO INPUT_LOOP
		PRINTL 请输入行动编号。
		INPUT
		TFLAG:213 = RESULT
	CASE 4
		SIF ASSI:1 < 0 || TFLAG:210 || TFLAG:212 >= 0 || TFLAG:211 < 0
			GOTO INPUT_LOOP
		PRINTL 预约了组合。
		TFLAG:210 = 1
	CASEELSE
		GOTO INPUT_LOOP
ENDSELECT
RESTART
