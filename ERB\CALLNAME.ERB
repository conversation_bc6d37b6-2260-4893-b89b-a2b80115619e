﻿@CALL_NAME
IF Z == 0
	CALLNAME:TARGET = 你
ELSEIF Z == 1
	IF CFLAG:10 >= 3
		CALLNAME:TARGET = 莉莉丝
	ELSEIF CFLAG:10 >= 2
		CALLNAME:TARGET = 阿斯莫德
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET = 梦魇
	ELSE
		CALLNAME:TARGET = 梦魔
	ENDIF
ELSEIF Z == 2
	IF CFLAG:10 >= 2
		CALLNAME:TARGET = 莉莉姆
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET = 阿尔卑
	ELSE
		CALLNAME:TARGET = 恶魔
	ENDIF
ELSEIF Z == 3
	IF CFLAG:10 >= 2
		CALLNAME:TARGET = 苏克斯罗
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET = 妖树
	ELSE
		CALLNAME:TARGET = 树精
	ENDIF
ELSEIF Z == 4
	IF CFLAG:10 >= 2
		CALLNAME:TARGET = 妖精王
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET = 菈南希
	ELSE
		CALLNAME:TARGET = 皮克西
	ENDIF
ELSEIF Z == 5
	IF CFLAG:10 >= 1
		CALLNAME:TARGET =  水之精灵
	ELSE
		CALLNAME:TARGET = 希尔芙
	ENDIF
ELSEIF Z == 6
	IF CFLAG:10 >= 2
		CALLNAME:TARGET =斯芬克斯
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET =  妖精猫
	ELSE
		CALLNAME:TARGET = 猫又
	ENDIF
ELSEIF Z == 7
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = 可鲁贝洛斯
	ELSE
		CALLNAME:TARGET = 狼人
	ENDIF
ELSEIF Z == 8
	IF CFLAG:10 >= 2
		CALLNAME:TARGET = 阿尔维斯
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET = バンシー
	ELSE
		CALLNAME:TARGET = ヴィレッジャ
	ENDIF
ELSEIF Z == 9
	IF CFLAG:10 >= 2
		CALLNAME:TARGET = アルバスタ
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET = バンシー
	ELSE
		CALLNAME:TARGET = ヴィレッジャ
	ENDIF
ELSEIF Z == 10
	IF CFLAG:10 >= 2
		CALLNAME:TARGET = エキドナ
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET = メリュジーヌ
	ELSE
		CALLNAME:TARGET = 蛇妖
	ENDIF
ELSEIF Z == 11
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = モーリアン
	ELSE
		CALLNAME:TARGET = 鸟人
	ENDIF
ELSEIF Z == 12
	IF CFLAG:10 >= 2
		CALLNAME:TARGET = ヴィーヴル
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET = サラマンダー
	ELSE
		CALLNAME:TARGET = リザードマン
	ENDIF
ELSEIF Z == 13
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = レヴィアタン
	ELSE
		CALLNAME:TARGET = 鱼尾裙
	ENDIF
ELSEIF Z == 14
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = ダーキニー
	ELSE
		CALLNAME:TARGET = アプサラス
	ENDIF
ELSEIF Z == 15
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = カリュブディス
	ELSE
		CALLNAME:TARGET = スキュラ
	ENDIF
ELSEIF Z == 16
	IF CFLAG:10 >= 2
		CALLNAME:TARGET = タマモ
	ELSEIF CFLAG:10 >= 1
		CALLNAME:TARGET = ダッキ
	ELSE
		CALLNAME:TARGET = クズノハ
	ENDIF
ELSEIF Z == 17
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = マイェストレ
	ELSE
		CALLNAME:TARGET = クィーンメイブ
	ENDIF
ELSEIF Z == 18
	CALLNAME:TARGET = アルケニー
ELSEIF Z == 19
	CALLNAME:TARGET = ウィッチ
ELSEIF Z == 20
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = ガブリエル
	ELSE
		CALLNAME:TARGET = エンジェル
	ENDIF
ELSEIF Z == 21
	CALLNAME:TARGET = ルシフェル
ELSEIF Z == 22
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = ハツメ
	ELSE
		CALLNAME:TARGET = クノイチ
	ENDIF
ELSEIF Z == 23
	CALLNAME:TARGET = ホワイトラビット
ELSEIF Z == 24
	CALLNAME:TARGET = サロメ
ELSEIF Z == 25
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = カーミラ
	ELSE
		CALLNAME:TARGET = ヴァンパイア
	ENDIF
ELSEIF Z == 26
	CALLNAME:TARGET = 龙
ELSEIF Z == 27
	CALLNAME:TARGET = プロフェテス
ELSEIF Z == 28
	CALLNAME:TARGET = ソルジャー
ELSEIF Z == 29
	CALLNAME:TARGET = 史莱姆
ELSEIF Z == 30
	CALLNAME:TARGET = ユニコーン
ELSEIF Z == 31
	CALLNAME:TARGET = ゴブリン
ELSEIF Z == 32
	CALLNAME:TARGET = マーシャ
ELSEIF Z == 33
	IF CFLAG:10 >= 1
		CALLNAME:TARGET = マモン
	ELSE
		CALLNAME:TARGET = ミミック
	ENDIF
ELSEIF Z == 34
	CALLNAME:TARGET = ストゥーナ
ELSEIF Z == 35
	CALLNAME:TARGET = メローナ
ELSEIF Z == 36
	CALLNAME:TARGET = セルマ
ELSEIF Z == 37
	CALLNAME:TARGET = フレデリカ
ELSEIF Z == 38
	CALLNAME:TARGET = エスプラーナ
ELSEIF Z == 39
	CALLNAME:TARGET = リズ・ラズ
ELSEIF Z == 40
	CALLNAME:TARGET = ロッコ・バロッコ
ELSEIF Z == 41
	CALLNAME:TARGET = ジャバウォック
ELSEIF Z == 42
	CALLNAME:TARGET = バンダースナッチ
ELSEIF Z == 43
	CALLNAME:TARGET = ジャブジャブ
ELSEIF Z == 44
	CALLNAME:TARGET = リン
ELSEIF Z == 45
	CALLNAME:TARGET = クリスティン
ELSEIF Z == 46
	CALLNAME:TARGET = クララ
ELSEIF Z == 47
	CALLNAME:TARGET = ベルナドット
ELSEIF Z == 48
	CALLNAME:TARGET = エスト
ELSEIF Z == 49
	CALLNAME:TARGET = フィムブルヴェト
ELSEIF Z == 50
	CALLNAME:TARGET = ステラ
ELSEIF Z == 51
	CALLNAME:TARGET = オリビア
ELSEIF Z == 52
	CALLNAME:TARGET = ルヴィッサ
ELSEIF Z == 53
	CALLNAME:TARGET = ファミリア
ELSEIF Z == 54
	CALLNAME:TARGET = メルヤ
ELSEIF Z == 55
	CALLNAME:TARGET = 少女
ELSEIF Z == 56
	CALLNAME:TARGET = サフィ
ELSEIF Z == 57
	CALLNAME:TARGET = 黒サフィ
ELSEIF Z == 58
	CALLNAME:TARGET = マーチヘア
ELSEIF Z == 59
	CALLNAME:TARGET = ドロレス
ELSEIF Z == 94
	CALLNAME:TARGET = ラルス
ELSEIF Z == 96
	CALLNAME:TARGET = アイゼクト
ENDIF
