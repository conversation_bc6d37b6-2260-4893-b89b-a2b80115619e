﻿;────────────────────────────────────
;露出のソース（欲情、恥情、不快、抑鬱、気力、興味）
;────────────────────────────────────
@CACL_SOURCE20

;恥じらいと恥薄いはここで処理
SIF TALENT:MASTER:34
	TIMES SOURCE:20 , 1.50
SIF TALENT:MASTER:35
	TIMES SOURCE:20 , 0.75

;性知識不足、もしくは幼稚なら裸になる意味とかよくわからないから
SIF EXP:8 < 10 || TALENT:MASTER:88
	TIMES SOURCE:20 , 0.80

;欲情、露出癖と調教度合いを見る
UP:5 += SOURCE:20 * (10 + B:8 * 2 + CFLAG:MASTER:0 * 10) / 250

;恥情、PALAM:恥情をみる
B = (PALAM:11 + 1) / 1000
UP:11 += SOURCE:20 * (11 - B) / 10

;性格を判定、強気なら不快、弱気なら抑鬱が上がります
C = TALENT:MASTER:12 * 2 - TALENT:MASTER:10 * 2 + TALENT:MASTER:16 - TALENT:MASTER:14 + TALENT:MASTER:15 * 2 - TALENT:MASTER:17 * 2 + TALENT:MASTER:89 * 3 - TALENT:MASTER:77 * 3

SIF SELECTCOM == 8
	TIMES C , 0.20
SIF SELECTCOM == 14
	TIMES C , 0.30
SIF SELECTCOM == 42
	TIMES C , 0.40
SIF SELECTCOM == 43
	TIMES C , 0.10
SIF SELECTCOM == 50
	TIMES C , 0.25

;従順と露出癖を見る
IF C > 0
	UP:12 += SOURCE:20 * (400 - B:30 * 4) * (200 - B:8 * 2) / 20000
ELSE
	UP:13 += SOURCE:20 * (200 - B:30 * 2) * (400 - B:8 * 4) / 20000
ENDIF

IF SOURCE:20 < 100
	D = 5
ELSEIF SOURCE:20 < 250
	D = 10
ELSEIF SOURCE:20 < 450
	D = 15
ELSEIF SOURCE:20 < 700
	D = 20
ELSEIF SOURCE:20 < 1000
	D = 25
ELSEIF SOURCE:20 < 1400
	D = 30
ELSEIF SOURCE:20 < 1900
	D = 35
ELSEIF SOURCE:20 < 2500
	D = 42
ELSE
	D = 50
ENDIF

;気力
LOSEBASE:91 += 20 + D * 2

;興味
BASE:6 += D * (3 + TALENT:23 - TALENT:22) / 2


;────────────────────────────────────
;不潔のソース（不快、抑鬱、気力）
;────────────────────────────────────
@CACL_SOURCE21

;関連素質の処理
SIF TALENT:MASTER:64
	TIMES SOURCE:21 , 0.30
SIF TALENT:MASTER:61
	TIMES SOURCE:21 , 0.60
SIF TALENT:MASTER:62
	TIMES SOURCE:21 , 1.35

;性格を判定、強気なら不快が多く、弱気なら抑鬱も上がります
C = TALENT:MASTER:12 * 2 - TALENT:MASTER:10 * 2 + TALENT:MASTER:16 - TALENT:MASTER:14 + TALENT:MASTER:15 * 2 - TALENT:MASTER:17 * 2 + TALENT:MASTER:89 * 3 - TALENT:MASTER:77 * 3

;従順と調教度合いを見る
IF C > 0 && CFLAG:MASTER:0 < 30
	UP:12 += SOURCE:20 * (400 - B:30 * 4) * (30 - CFLAG:MASTER:0) / 6000
ELSE
	UP:12 += SOURCE:20 * (400 - B:30 * 4) * (30 - CFLAG:MASTER:0) / 12000
	UP:13 += SOURCE:20 * (200 - B:30 * 2) * (60 - CFLAG:MASTER:0 * 2) / 6000
ENDIF

;気力
LOSEBASE:91 += SOURCE:21 / 20


;────────────────────────────────────
;逸脱のソース（反抗ソース、恐怖ソース、気力、興味）
;────────────────────────────────────
@CACL_SOURCE22

;好奇心と感情豊富なはここで処理
SIF TALENT:MASTER:23
	TIMES SOURCE:22 , 0.50
SIF TALENT:MASTER:25
	TIMES SOURCE:22 , 1.75

;従順、欲情と調教度合いを見る
SIF CFLAG:MASTER:0 < 30
	A = SOURCE:22 * (400 - B:30 * 4) * (30 - CFLAG:MASTER:0) / 10000 + EXP:MASTER:50 * 10
SIF TFLAG:94 < 4
	TIMES A , 0.60
SIF A > 0
	SOURCE:33 += A
SIF CFLAG:MASTER:0 < 30
	SOURCE:34 += SOURCE:22 * (400 - B:1 * 4) * (30 - CFLAG:MASTER:0) / 10000 + EXP:MASTER:50 * 10

;気力
LOSEBASE:91 += SOURCE:22 / 30

;興味
BASE:6 += SOURCE:22 / 50 + 1


;────────────────────────────────────
;中毒充足のソース（欲情、恭順、理性、好感度）
;────────────────────────────────────
@CACL_SOURCE23

;欲情をみる
UP:5 += SOURCE:23 * (10 + B:1) / 60
UP:8 += SOURCE:23 * (10 + B:1) / 60

;理性
LOSEBASE:95 += SOURCE:23 / 50 + 1

;好感度変動
IF SOURCE:23 < 200
	TFLAG:95 += 1
ELSEIF SOURCE:23 < 500
	TFLAG:95 += 3
ELSEIF SOURCE:23 < 1000
	TFLAG:95 += 6
ELSEIF SOURCE:23 < 2000
	TFLAG:95 += 10
ELSEIF SOURCE:23 < 5000
	TFLAG:95 += 15
ELSE
	TFLAG:95 += 25
ENDIF


;────────────────────────────────────
;トラウマのソース（反抗ソース、恐怖ソース、屈服、不快、抑鬱、気力、好感度）
;────────────────────────────────────
@CACL_SOURCE24
;トラウマ刻印、従順と調教度合いを見る
SIF MARK:MASTER:4 < 10
	SOURCE:33 += SOURCE:24 * (400 - B:30 * 4) * (10 - MARK:MASTER:0) / 10000
SIF CFLAG:MASTER:0 < 30
	SOURCE:34 += SOURCE:24 * (400 - B:30 * 4) * (30 - CFLAG:MASTER:0) / 10000

;屈服、臆病と気丈を見る
UP:6 += SOURCE:24 * (2 + TALENT:MASTER:10 - TALENT:MASTER:11) / 3

;性格を判定、強気なら不快、弱気なら抑鬱が上がります
C = TALENT:MASTER:12 * 2 - TALENT:MASTER:10 * 2 + TALENT:MASTER:16 - TALENT:MASTER:14 + TALENT:MASTER:15 * 2 - TALENT:MASTER:17 * 2 + TALENT:MASTER:89 * 3 - TALENT:MASTER:77 * 3
;従順とマゾっ気を見る
IF C > 0
	UP:12 += SOURCE:24 * (400 - B:30 * 4) * (200 - B:11 * 2) / 20000
ELSE
	UP:13 += SOURCE:24 * (200 - B:30 * 2) * (400 - B:11 * 4) / 20000
ENDIF

;気力、好感度変動
IF SOURCE:24 < 200
	LOSEBASE:91 += 50
	TFLAG:95 -= 1
ELSEIF SOURCE:24 < 500
	LOSEBASE:91 += 100
	TFLAG:95 -= 3
ELSEIF SOURCE:24 < 1000
	LOSEBASE:91 += 150
	TFLAG:95 -= 6
ELSEIF SOURCE:24 < 2000
	LOSEBASE:91 += 200
	TFLAG:95 -= 10
ELSEIF SOURCE:24 < 5000
	LOSEBASE:91 += 250
	TFLAG:95 -= 15
ELSE
	LOSEBASE:91 += 300
	TFLAG:95 -= 25
ENDIF
