﻿;-------------------------------------------------
;汎用関数置き場
;日時関連(曜日季節など)
;-------------------------------------------------

;-------------------------------------------------
;関数名:GET_DAY
;概　要:曜日を表す文字列を取得する関数
;引　数:なし
;戻り値:曜日を表す文字列(日/月/火/水/木/金/土)
;備　考:文中関数
;-------------------------------------------------
@GET_DAY
#FUNCTIONS
SIF !STRLENS(LOCALS)
	SPLIT "日/一/二/三/四/五/六", "/", LOCALS
RETURNF LOCALS:(DAY % 7)


;-------------------------------------------------
;関数名:GET_SEASON
;概　要:季節を表す文字列を取得する関数
;引　数:なし
;戻り値:季節を表す文字列(春/夏/秋/冬)
;備　考:文中関数
;-------------------------------------------------
@GET_SEASON
#FUNCTIONS
SIF !STRLENS(LOCALS)
	SPLIT "春/夏/秋/冬", "/", LOCALS
RETURNF LOCALS:(FLAG:33)


;-------------------------------------------------
;関数名:GET_TIME
;概　要:時間を表す文字列を取得する関数
;引　数:なし
;戻り値:時間を表す文字列(昼/夜)
;備　考:文中関数
;-------------------------------------------------
@GET_TIME
#FUNCTIONS
RETURNF @"\@ TIME ? 夜 # 昼 \@"
