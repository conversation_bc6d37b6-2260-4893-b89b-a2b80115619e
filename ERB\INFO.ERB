﻿;────────────────────────────────────
;メイン調教画面の表示、調教者行動を呼び出し、そしてCOMABLE前の必要な処理
;────────────────────────────────────
@SHOW_STATUS
TFLAG:3 = 0
IF TFLAG:77 == 6
	TEQUIP:52 = 1
	TFLAG:77 = 0
ENDIF
;助手調教アクション登録フラグの初期化
CVARSET CFLAG, 172
CVARSET CFLAG, 190
CVARSET CFLAG, 193

DRAWLINE
PRINTFORM {DAY + 1}日期(%GET_TIME()%)　经过时间[{TFLAG:64}]　
SIF TALENT:MASTER:132 > 1
	PRINTFORM /　惩罚\@ TFLAG:69 ? 模式 # 点[{TFLAG:68}]\@
PRINTL 
;添加調教頭像
IF FLAG:666 
	CALL CHARA_FACE_T												
	PRINTL                調教者:                        助手1:                        助手2:                

DRAWLINE
ENDIF
CALL SHOW_EQUIP_3
CALL INFO_GAUGE_TRAINER
;CALL INFO_GAUGE_TENTACLE
DRAWLINE

IF !TFLAG:65
	;処理の関係上先に拘束する
	CALL PLAYER_ACT_EXTRA_PRI
	;調教者の行動を選択します。
	CALL PLAYER_ACT
	CALL BEFORE_ACT
	;行動予約がされた場合は↑に上書き
	IF TFLAG:211 >= 0
		TFLAG:90 = TFLAG:211
		TFLAG:211 = -1
	ENDIF
	;各調教アクションの累積値

	;選択された行動によってMASTERの体勢と使用中の体の部位を記録します。
	CALL MASTER_POSE
	CALL PLAYER_ACT_EXTRA_LATER
ENDIF

PRINT_PALAM TARGET
CALL SHOW_EQUIP_1
CALL SHOW_EQUIP_2
DRAWLINE
TSTR:1 = %STR:(TFLAG:90 + 100)%
;調教者行動の表示
IF !TFLAG:65
	TFLAG:65 = 1
	;前回の行動を表示して今回の行動を決定
	SIF STRLENS(TSTR:2)
	PRINTFORML 上次的调教指令：＜%TSTR:2%＞
	CALL EQUIP_2USE
	CALL SP_CHECK
	DRAWLINE
	
	;選択された行動が派生する場合、そのフラグを立てます
	CALL TRAIN_VARIATION
	IF TFLAG:120 < 200
		;PALAM,勃起度などの計算
		CALL ACTION_APPLY
	ELSE
		CALL SP_ACTION
	ENDIF
	
	SIF TFLAG:102 > 0
		CALL KYOUSEI_DATUI
	SIF TFLAG:103 > 0
		CALL JISHU_DATUI
	IF ASSI:1 > 0
		TFLAG:114 = 1
		;助手行動の決定し、MASTERの体勢の情報を上書き
		CALL ASSI_ACT
		;行動予約
		IF TFLAG:212 >= 0
			TFLAG:190 = TFLAG:212
			TFLAG:212 = -1
		ENDIF
		CALL MASTER_POSE_ASSI
		CALL ASSI_TRAIN_VARIATION
	ENDIF
	SIF TFLAG:102 > 0
		CALL KYOUSEI_DATUI_CHECK
	;強制・自主脱衣の表示とその口上
	SIF TFLAG:102 || TFLAG:103
		CALL DATUI_MESSAGE
	
	;調教者アクションの表示とその口上
	CALL KOJO_ACT
	CALL PLAYER_ACT_EXTRA_MESSAGE
	CALL TOUCH_SUCCESSION(TARGET)
	
	;助手の行動とその口上
	IF ASSI:1 > 0
		ASSI = ASSI:1
		PRINTL 
		RESULT = 0
		CFLAG:ASSI:172 = TFLAG:172
		IF TFLAG:172 != 1
			;各助手調教アクションの累積値（コンビネーションが発生したときは加算しない）
			CFLAG:(ASSI:1):(2200 + TFLAG:190) += 1
			CFLAG:MASTER:(2200 + TFLAG:190) += 1
			CFLAG:(ASSI:1):190 = TFLAG:190
			CFLAG:(ASSI:1):193 = TFLAG:193
			ALIGNMENT RIGHT
			IF TFLAG:113 < 100
				CALL ASSI_ACT_APPLY
			ELSE
				CALL SP_ACTION_A
			ENDIF
			SIF TFLAG:172
				CALL ASSI_TRAIN_MESSAGE
			ALIGNMENT LEFT
		ENDIF
	ENDIF
	;ほぼASSI:1と同じだが、コンビネーションは発生しない。誰と誰がコンビネーションしているのかの判定が複雑になりそうなので
	IF ASSI:2 > 0
		TFLAG:114 = 2
		ASSI = ASSI:2
		CALL ASSI_ACT
		;行動予約
		IF TFLAG:213 >= 0
			TFLAG:190 = TFLAG:213
			TFLAG:213 = -1
		ENDIF
		CALL MASTER_POSE_ASSI
		CALL ASSI_TRAIN_VARIATION
		;各助手調教アクションの累積値
		CFLAG:(ASSI:2):(2200 + TFLAG:190) += 1
		CFLAG:MASTER:(2200 + TFLAG:190) += 1
		CFLAG:(ASSI:2):172 = TFLAG:172
		CFLAG:(ASSI:2):190 = TFLAG:190
		CFLAG:(ASSI:2):193 = TFLAG:193
		PRINTL 
		RESULT = 0
		ALIGNMENT RIGHT
		IF TFLAG:113 < 100
			CALL ASSI_ACT_APPLY
		ELSE
			CALL SP_ACTION_A
		ENDIF
		SIF TFLAG:172
			CALL ASSI_TRAIN_MESSAGE
		ALIGNMENT LEFT
		ASSI = ASSI:1
	ENDIF
	;↑を少し書き換えてコピペすることで、助手の増員もできる。ただプレイした限り、4pで情報が妄想力を上回る。
	;野外プレイ時の乱入
	IF ASSI:3 > 0
		TFLAG:114 = 3
		ASSI = ASSI:3
		CALL ASSI_ACT
		CALL MASTER_POSE_ASSI
		CALL ASSI_TRAIN_VARIATION
		;各助手調教アクションの累積値
		CFLAG:(ASSI:3):(2200 + TFLAG:190) += 1
		CFLAG:MASTER:(2200 + TFLAG:190) += 1
		CFLAG:(ASSI:3):172 = TFLAG:172
		CFLAG:(ASSI:3):190 = TFLAG:190
		CFLAG:(ASSI:3):193 = TFLAG:193
		PRINTL 
		RESULT = 0
		ALIGNMENT RIGHT
		IF TFLAG:113 < 100
			CALL ASSI_ACT_APPLY
		ELSE
			CALL SP_ACTION_A
		ENDIF
		SIF TFLAG:172
			CALL ASSI_TRAIN_MESSAGE
		ALIGNMENT LEFT
		ASSI = ASSI:1 < 0 ? ASSI:3 # ASSI:1
	ENDIF
	;媚薬・ローション使用とその口上
	SIF TFLAG:104
		CALL USE_MESSAGE
	TFLAG:102 = 0
	TFLAG:103 = 0
	TFLAG:104 = 0
	TFLAG:105 = 0
	CALL EQUIP_CHECK
	CALL CLOTHES_CHECK
	DRAWLINE
	;ここでリアクション派生をリセットしておく
	TFLAG:300 = 0
ELSE
	SIF STRLENS(TSTR:2)
	PRINTFORML 上次的调教指令＜%TSTR:2%＞
	PRINTFORML 这次的调教指令＜%TSTR:1%＞
	DRAWLINE
ENDIF
SIF FLAG:4
CALL SHOW_TOUCH
;────────────────────────────────────
;行動前判定（EVETRAINより移動）
;────────────────────────────────────
@BEFORE_ACT
IF TFLAG:77 == 8
	;調教者行動前口上・ご褒美による中止
	CALL KOJO_EVENT(12, 3)
	IF RESULT < 2
		SIF RESULT
			PRINTL 
		PRINTFORML %CALLNAME:TARGET%中止了今天的调教，
		PRINTFORML 和%CALLNAME:MASTER%度过了一段悠闲的时间……
	ENDIF
	CALL _BEFORE_ACT_SUB1
ELSEIF CFLAG:11 > 5 && !FLAG:1700
	;調教者行動前口上・調教者の疲労による中止
	CALL KOJO_EVENT(12, 1)
	IF RESULT < 2
		SIF RESULT
			PRINTL 
		PRINTFORML 调教的时间到了，进来的%CALLNAME:TARGET%看起来好像很疲惫的样子。
		PRINTFORML 说这次就先休息，然后出去了……
	ENDIF
	CALL _BEFORE_ACT_SUB1
ELSEIF CFLAG:MASTER:11 > 5 && !FLAG:1700
	;調教者行動前口上・調教対象の疲労による中止
	CALL KOJO_EVENT(12, 2)
	IF RESULT < 2
		SIF RESULT
			PRINTL 
		PRINTFORML 调教的时间到了，看到%CALLNAME:MASTER%一脸很凶的样子，%CALLNAME:TARGET%想了想
		PRINTFORML 说这次可以先休息就出去了……
	ENDIF
	CALL _BEFORE_ACT_SUB1
ELSE
	;調教者行動前口上
	CALL BEFORE_ACT_MESSAGE
	CALL KOJO_EVENT(12)
	IF RESULT
		DRAWLINE
	ELSE
		WAIT
	ENDIF
ENDIF

@_BEFORE_ACT_SUB1
DRAWLINE
CFLAG:TARGET:11 = 0
CFLAG:MASTER:11 = 0
TFLAG:65 = 1
TFLAG:70 = -1
TFLAG:80 = -1
TFLAG:90 = -1
TFLAG:101 = 1
CVARSET CFLAG, 172
CVARSET CFLAG, 190


;────────────────────────────────────
;汚れ表示
;────────────────────────────────────
@STAIN_INFO
CALL _STAIN_INFO_SUB1(MASTER)
CALL _STAIN_INFO_SUB1(TARGET)
CALL _STAIN_INFO_SUB1(ASSI:1)
CALL _STAIN_INFO_SUB1(ASSI:2)
CALL _STAIN_INFO_SUB1(ASSI:3)
WAIT

@_STAIN_INFO_SUB1(ARG)
SIF ARG < 0
	RETURN 0
SIF !STRLENS(LOCALS)
	SPLIT "口/手/Ｐ/Ｖ/Ａ/Ｂ/足/Ｖ/Ｐ/精/Ａ/乳/血/粘液", "/", LOCALS
FOR LOCAL, 0, 7
	;ペニスが無い場合、Ｐは処理しない。オトコの場合、ＶとＢは処理しない
	SIF (LOCAL == 2 && !PENIS(ARG)) || ((LOCAL == 3 || LOCAL == 5) && TALENT:ARG:122)
		CONTINUE
	PRINTFORM %CALLNAME:ARG%の%LOCALS:LOCAL%：
	FOR LOCAL:1, 0, 7
		PRINTFORM \@ GETBIT(STAIN:ARG:LOCAL, LOCAL:1) ? <%LOCALS:(7 + LOCAL:1)%> # \@
	NEXT
	PRINTL 
NEXT


;────────────────────────────────────
;状況の表示
;────────────────────────────────────
@SHOW_EQUIP_2
SIF TFLAG:371
	PRINTL [安心]
SIF TFLAG:372
	PRINTL [恐慌]
SIF CFLAG:MASTER:220 || TFLAG:373
	PRINTL [敏感]
SIF TFLAG:112
	PRINT  [仰卧]
SIF TEQUIP:37
	PRINT  [推倒]
SIF TEQUIP:38
	PRINT  [缠绕]
SIF TEQUIP:44 || TEQUIP:45
	PRINT  [颜面骑乘]
SIF TEQUIP:46
	PRINT  [藤蔓拘束中]
SIF TEQUIP:47
	PRINT  [蛇体拘束中]
SIF TEQUIP:52
	PRINT  [野外]
SIF TEQUIP:56
	PRINT  [大镜子]
SIF TEQUIP:68
	PRINT  [挑逗]
SIF TEQUIP:69 == 1
	PRINTFORM  [%CALLNAME:MASTER%自慰中]
SIF TEQUIP:69 == 2
	PRINTFORM  [%CALLNAME:TARGET%自慰中]
SIF TEQUIP:69 == 3
	PRINT  [二人自慰中]
SIF TEQUIP:70
	PRINTFORM  [%CALLNAME:TARGET%插入中]
SIF TEQUIP:71
	PRINTFORM  [%CALLNAME:MASTER%插入中]
SIF TEQUIP:90 || TEQUIP:91 || TEQUIP:92 || TEQUIP:93 || TEQUIP:94
	PRINT  [触手] 
PRINTL 


@EQUIP_2USE
SIF TEQUIP:37 || TEQUIP:38 || TEQUIP:44 || TEQUIP:45 || TEQUIP:46 || TEQUIP:47 || TEQUIP:52 || TEQUIP:56 || TEQUIP:68 || TEQUIP:69 || TEQUIP:70 || TEQUIP:71 || TEQUIP:90 || TFLAG:170
	DRAWLINE
SIF TEQUIP:44
	PRINTFORML %CALLNAME:TARGET%\@ TALENT:152 ? 的蛇体缠绕在%CALLNAME:MASTER%的上半身， # 跨坐着 \@将阴唇贴在了%CALLNAME:MASTER%的脸上…
SIF TEQUIP:45
	PRINTFORML %CALLNAME:TARGET%\@ TALENT:152 ? 的蛇体缠绕在%CALLNAME:MASTER%的上半身， # 跨坐在%CALLNAME:MASTER%的脸上， \@强行要求%CALLNAME:MASTER%侍奉后穴…
SIF TEQUIP:46
	PRINTFORML %CALLNAME:MASTER%被%CALLNAME:TARGET%的藤蔓拘束住了四肢……
SIF TEQUIP:47
	PRINTFORML %CALLNAME:MASTER%被%CALLNAME:TARGET%的蛇体拘束住了四肢……
IF TEQUIP:52
	IF 1
		PRINTDATAL
			DATA 周围好像没有人的迹象……
			DATA 附近听到了谁的脚步声……
			DATA 从背后感觉到人的视线……
		ENDDATA
	ELSE
		SELECTCASE TFLAG:140
			CASE 0
				PRINTL 周围好像没有人的迹象……
				TFLAG:140 += RAND:2
			CASE 1, 2
				PRINTL 附近听到了谁的脚步声……
				TFLAG:140 += RAND:2
			CASE 3
				PRINTL 从背后感觉到人的视线……
				TFLAG:140 += RAND:2
			CASE 4
				LOCAL = RAND:34 + 1
				SIF LOCAL > 31
					LOCAL += 9
				ADDCHARA LOCAL
				LOCAL:1 = TARGET
				TARGET = CHARANUM - 1
				
				CFLAG:0 = RAND:(CFLAG:(LOCAL:1):0 + 1)
				CFLAG:10 = RAND:(20 + CFLAG:0) / 20
				Z = NO:TARGET
				SIF Z == 21 && CFLAG:10 == 0
					CFLAG:10 = 1
				
				CALL CALL_NAME
				CALL BASE_TRAINER_SETUP
				CALL GET_EXTALENT
				CALL SIZE_SET
				SIF FLAG:2002
					CALL PERSONALITY
				CALL EXP_REVISION
				CALL ABL_SET
				CALL BASE_TRAINER_SETUP
				
				ASSI:3 = TARGET
				TARGET = LOCAL:1
				PRINTFORML %CALLNAME:(ASSI:3)%出现了！
				TFLAG:140 = 0
		ENDSELECT
	ENDIF
ENDIF
SIF TEQUIP:56
	PRINTFORML 眼前的镜子里映着%CALLNAME:TARGET%和%CALLNAME:MASTER%的痴态……
SIF TEQUIP:68
	PRINTFORML %CALLNAME:MASTER%被%CALLNAME:TARGET%弄得焦灼地喘着气…
SIF TEQUIP:69 == 1
	PRINTFORML %CALLNAME:TARGET%眯着眼睛看着%CALLNAME:MASTER%自慰的样子……
SIF TEQUIP:69 == 2
	PRINTFORML %CALLNAME:TARGET%向%CALLNAME:MASTER%展示了自己的痴态……
SIF TEQUIP:69 == 3
	PRINTFORML %CALLNAME:MASTER%和%CALLNAME:TARGET%凝视着彼此的痴态，自慰了起来……
SIF TEQUIP:37
	PRINTFORML %CALLNAME:MASTER%被%CALLNAME:TARGET%推倒，身体无法动弹……
SIF TEQUIP:38
	PRINTFORML %CALLNAME:MASTER%和%CALLNAME:TARGET%结合时被蛇体缠住……
SIF TEQUIP:70
	PRINTFORML %CALLNAME:TARGET%的阴茎连同其根部都深深地插入了%CALLNAME:MASTER%…
IF TEQUIP:71
	IF TFLAG:166
		PRINTFORML %CALLNAME:MASTER%的阴茎在%CALLNAME:TARGET%的\@ TEQUIP:71 == 6 ? 肠内 # 腔内 \@迎来了高潮……
	ELSEIF TFLAG:34 == 2
		PRINTFORML %CALLNAME:TARGET%的\@ TEQUIP:71 == 6 ? 肠内 # 腔内 \@紧缩着，试图从刚高潮过的%CALLNAME:MASTER%的阴茎中
		PRINTFORML 榨出更多的精液……
	ELSE
		PRINTFORML %CALLNAME:TARGET%的\@ TEQUIP:71 == 6 ? 肛门 # 性器 \@将%CALLNAME:MASTER%的阴茎连同根部都含进去了……
	ENDIF
ENDIF
SIF TEQUIP:90
	PRINTFORML %CALLNAME:MASTER%被蠢蠢欲动的触手缠住…
SELECTCASE TEQUIP:100
	CASE 1
		PRINTFORML %CALLNAME:TARGET%握着%CALLNAME:MASTER%的阴茎，缓缓地上下抚动着……
	CASE 2
		PRINTFORML %CALLNAME:TARGET%对着%CALLNAME:MASTER%的龟头伸出了舌头…
ENDSELECT
SIF TFLAG:170 && !TFLAG:166
	PRINTFORML %CALLNAME:MASTER%看起来好像要高潮了...


;────────────────────────────────────
;使用中の道具を表示
;────────────────────────────────────
@SHOW_EQUIP_1
IF TEQUIP:10 || TEQUIP:11 || TEQUIP:12 || TEQUIP:18 || TEQUIP:20 || TEQUIP:25 || TEQUIP:26 || TEQUIP:27 || TEQUIP:30 || TEQUIP:31 || TEQUIP:35 || TEQUIP:36 || TEQUIP:40 || TEQUIP:41 || TEQUIP:42 || TEQUIP:43
	PRINT 使用中　
	SIF TEQUIP:10
		PRINTFORM [润滑乳液(%EFFECT(TEQUIP:10, 1)%)]
	SIF TEQUIP:11
		PRINTFORM [媚药(%EFFECT(TEQUIP:11)%)]
	SIF TEQUIP:12
		PRINTFORM [利尿剂(%EFFECT(TEQUIP:12)%)]
	SIF TEQUIP:18
		PRINTFORM [铃兰的毒(%EFFECT(TEQUIP:18)%)]
	SIF TEQUIP:20
		PRINTFORM [\@ TEQUIP:20 > 1 ? 特注 # \@振动棒]
	SIF TEQUIP:25
		PRINTFORM [\@ TEQUIP:25 > 1 ? 特注 # \@肛用振动棒]
	SIF TEQUIP:26
		PRINT [后庭拉珠]
	SIF TEQUIP:27
		PRINT [灌肠＋肛门塞]
	SIF TEQUIP:30
		PRINTFORM [\@ TEQUIP:30 > 1 ? 特注 # \@阴蒂夹]
	SIF TEQUIP:31
		PRINTFORM [\@ TEQUIP:31 > 1 ? 特注 # \@飞机杯]
	SIF TEQUIP:35
		PRINTFORM [\@ TEQUIP:35 > 1 ? 特注 # \@乳头夹]
	SIF TEQUIP:36
		PRINTFORM [\@ TEQUIP:36 > 1 ? 特注 # \@挤奶器]
	SIF TEQUIP:40
		PRINT [用绳子紧缚]
	SIF TEQUIP:41
		PRINT [眼罩]
	SIF TEQUIP:42
		PRINT [口塞球]
	SIF TEQUIP:43
		PRINT [三角木马骑马中]
	;ココより↑にTEQUIPに登録したものを書き込む
	PRINTL 
ENDIF


;────────────────────────────────────
;服装の表示
;────────────────────────────────────
@SHOW_EQUIP_3
PRINTFORM %CALLNAME:TARGET%的服装：
PRINTFORM \@ TEQUIP:6 ? [%DRESS(TARGET)%] # \@\@ TEQUIP:5 ? [%OUTER_T(TARGET)%] # \@\@ TEQUIP:4 ? [%OUTER_B(TARGET)%] # \@\@ TEQUIP:3 ? [%INNER_T(TARGET)%] # \@
PRINTFORML \@ TEQUIP:2 ? [%INNER_B(TARGET)%] # \@\@ TEQUIP:1 ? [%SOCKS(TARGET)%] # \@\@ TEQUIP:0 ? [%GLOVES(TARGET)%] # \@\@ TEQUIP:9 ? [%COSPLAY(TARGET)%] # \@
PRINTFORM %CALLNAME:MASTER%的服装：
PRINTFORM \@ TEQUIP:MASTER:6 ? [%DRESS(MASTER)%] # \@\@ TEQUIP:MASTER:5 ? [%OUTER_T(MASTER)%] # \@\@ TEQUIP:MASTER:4 ? [%OUTER_B(MASTER)%] # \@\@ TEQUIP:MASTER:3 ? [%INNER_T(MASTER)%] # \@
PRINTFORML \@ TEQUIP:MASTER:2 ? [%INNER_B(MASTER)%] # \@\@ TEQUIP:MASTER:1 ? [%SOCKS(MASTER)%] # \@\@ TEQUIP:MASTER:0 ? [%GLOVES(MASTER)%] # \@\@ TEQUIP:MASTER:9 ? [%COSPLAY(MASTER)%] # \@

;────────────────────────────────────
;行動前
;────────────────────────────────────
@BEFORE_ACT_MESSAGE
