﻿;────────────────────────────────────
;加虐 (平手60/むち61/はり62/なわ63/目隠64/口枷65/罵倒66/木馬67/浣腸68/イラ56)
;────────────────────────────────────
@ACT_M6
;基準値
LOCAL:60 = 15
LOCAL:61 = 12
LOCAL:62 = 6
LOCAL:63 = 8
LOCAL:64 = 8
LOCAL:65 = 8
LOCAL:66 = 5
LOCAL:67 = 5
LOCAL:68 = 5
LOCAL:56 = 5

;調教方針がSM
IF TFLAG:70 == 6 || TFLAG:70 == 4
	LOCAL:68 += 5
	SIF TEQUIP:40 == 0
		LOCAL:63 += 7
	SIF TEQUIP:41 == 0
		LOCAL:64 += 5
	SIF TEQUIP:42 == 0
		LOCAL:65 += 5
ENDIF

;調教方針が異常
SIF TFLAG:70 == 5
	LOCAL:67 += 5

;苦痛刻印(未使用)
SIF !TFLAG:40
	TFLAG:40 += MARK:3 / 2

;────────────────────────────────────
;素質による変動
;────────────────────────────────────
;調教者が臆病/気丈
IF TALENT:10
	LOCAL:64 += TEQUIP:41 ? 0 # RAND:5
ELSEIF TALENT:12
	LOCAL:66 += RAND:5
	LOCAL:67 += TEQUIP:43 ? 0 # RAND:6
	LOCAL:68 += RAND:6
ENDIF

;調教者がプライド高い/低い
IF TALENT:15
	LOCAL:66 += 2 + RAND:3
ELSEIF TALENT:17
	LOCAL:66 -= 2 + RAND:3
ENDIF

;調教者が一線越えないで三角木馬未装着
SIF TALENT:28 && !TEQUIP:43
	LOCAL:67 -= 5

;調教者が目立ちたがり
SIF TALENT:29
	LOCAL:56 += RAND:7

;調教者が抑圧/解放
IF TALENT:32
	LOCAL:64 += TEQUIP:41 ? 0 # 3
	LOCAL:68 -= 5
ELSEIF TALENT:33
	LOCAL:64 += TEQUIP:41 ? 0 # -3
	LOCAL:68 += RAND:8
ENDIF

;調教者が恥じらいでアイマスク未装着
SIF TALENT:34 && !TEQUIP:41
	LOCAL:64 += 5

;調教者が痛みに弱い
SIF TALENT:40
	LOCAL:60 -= 5

;調教者が針さばき
SIF TALENT:53
	LOCAL:62 += 5

;調教者が縛り上手で縄未装着
SIF TALENT:58 && !TEQUIP:40
	LOCAL:63 += 5

;調教者が道具使い
SIF TALENT:59
	LOCAL:68 += 5

;調教者が汚臭敏感
SIF TALENT:62
	LOCAL:68 -= 8

;調教者がサド
IF TALENT:83
	LOCAL:61 += RAND:3
	LOCAL:62 += RAND:5
	LOCAL:63 += TEQUIP:40 ? 0 # RAND:3
	LOCAL:64 += TEQUIP:41 ? 0 # RAND:3
	LOCAL:65 += TEQUIP:42 ? 0 # RAND:5
	LOCAL:66 += RAND:7
	LOCAL:67 += TEQUIP:43 ? 0 # RAND:7
	LOCAL:68 += RAND:5
ENDIF

;調教者が慎重/短気
IF TALENT:84
	LOCAL:63 += TEQUIP:40 ? 0 # RAND:5
	LOCAL:67 += TEQUIP:43 ? 0 # -1 - RAND:5
ELSEIF TALENT:85
	LOCAL:63 += TEQUIP:40 ? 0 # -RAND:5
	LOCAL:67 += TEQUIP:43 ? 0 # 1 + RAND:5
ENDIF

;調教者が意地悪/心根優しい
IF TALENT:86
	LOCAL:64 += TEQUIP:41 ? 0 # RAND:5
	LOCAL:65 += TEQUIP:42 ? 0 # RAND:5
	LOCAL:66 += RAND:5
ELSEIF TALENT:87
	LOCAL:64 += TEQUIP:41 ? 0 # -RAND:5
	LOCAL:65 += TEQUIP:42 ? 0 # -RAND:5
	LOCAL:66 -= RAND:5
	LOCAL:67 += TEQUIP:43 ? 10 # 0
ENDIF

;調教者が幼稚
SIF TALENT:88
	LOCAL:60 += 1 + RAND:5

;調教者が狂気
IF TALENT:89
	LOCAL:67 += TEQUIP:43 ? 0 # RAND:12
	LOCAL:68 += RAND:10
	LOCAL:56 += RAND:10
ENDIF

;調教者がＣ敏感
SIF TALENT:100
	LOCAL:56 += 2 + RAND:5

;調教対象が反抗的/素直
IF TALENT:MASTER:11
	LOCAL:63 += TEQUIP:40 ? 0 # 2 + RAND:5
	LOCAL:64 += TEQUIP:41 ? 0 # RAND:8
	LOCAL:65 += TEQUIP:42 ? 0 # RAND:8
ELSEIF TALENT:MASTER:13
	LOCAL:63 += TEQUIP:40 ? 0 # -2
	LOCAL:64 += TEQUIP:41 ? 0 # -RAND:4
	LOCAL:65 += TEQUIP:42 ? 0 # -RAND:4
ENDIF

;調教対象が大人しい/生意気
IF TALENT:MASTER:16
	LOCAL:63 += TEQUIP:40 ? 0 # RAND:5
	LOCAL:64 += TEQUIP:41 ? 0 # RAND:5
	LOCAL:65 += TEQUIP:42 ? 0 # 2 + RAND:5
ELSEIF TALENT:MASTER:14
	LOCAL:63 += TEQUIP:40 ? 0 # -RAND:5
	LOCAL:64 += TEQUIP:41 ? 0 # -RAND:5
	LOCAL:65 += TEQUIP:42 ? 0 # -2
ENDIF

;調教対象がプライド高い/低い
IF TALENT:MASTER:15
	LOCAL:65 += TEQUIP:42 ? 0 # RAND:5
ELSEIF TALENT:MASTER:17
	LOCAL:65 += TEQUIP:42 ? 0 # -RAND:5
ENDIF

;調教対象が恥じらい/恥薄い
IF TALENT:MASTER:34
	LOCAL:66 += 2 + RAND:5
ELSEIF TALENT:MASTER:35
	LOCAL:66 -= 2 + RAND:5
ENDIF

;調教対象が痛みに弱い/強い
IF TALENT:MASTER:40
	LOCAL:60 += 2 + RAND:3
	LOCAL:61 += 3 + RAND:3
	LOCAL:62 += 2 + RAND:7
ELSEIF TALENT:MASTER:41
	LOCAL:60 -= 3
	LOCAL:61 -= 4
	LOCAL:62 -= 4
ENDIF

;────────────────────────────────────
;能力、パラメーターによる変動
;────────────────────────────────────
;調教者の技巧
SELECTCASE ABL:2
	CASE IS > 4
		LOCAL:61 += 2
		LOCAL:62 += 3 + RAND:3
	CASE 4
		LOCAL:61 += 3
		LOCAL:62 += 2 + RAND:3
	CASE 3
		LOCAL:61 += 3 + RAND:3
		LOCAL:62 += 2
	CASE 2
		LOCAL:61 += 2 + RAND:3
		LOCAL:62 += 1 + RAND:2
	CASE 1
		LOCAL:61 += 2
		LOCAL:62 += 1
ENDSELECT

;調教者のＣ感覚
SELECTCASE ABL:3
	CASE IS > 4
		LOCAL:56 += 3 + RAND:3
	CASE 4
		LOCAL:56 += 2 + RAND:3
	CASE 3
		LOCAL:56 += 2
	CASE 2
		LOCAL:56 += 1 + RAND:2
	CASE 1
		LOCAL:56 += 1
ENDSELECT

;調教者の会話
SELECTCASE ABL:20
	CASE IS > 4
		LOCAL:66 += 3 + RAND:3
	CASE 4
		LOCAL:66 += 2 + RAND:3
	CASE 3
		LOCAL:66 += 2
	CASE 2
		LOCAL:66 += 1 + RAND:2
	CASE 1
		LOCAL:66 += 1
ENDSELECT

;調教者の道具
SELECTCASE ABL:22
	CASE IS > 4
		LOCAL:68 += 3 + RAND:3
	CASE 4
		LOCAL:68 += 2 + RAND:3
	CASE 3
		LOCAL:68 += 2
	CASE 2
		LOCAL:68 += 1 + RAND:2
	CASE 1
		LOCAL:68 += 1
ENDSELECT

;調教者の奉仕
SELECTCASE ABL:25
	CASE IS > 4
		LOCAL:56 += 3 + RAND:3
	CASE 4
		LOCAL:56 += 2 + RAND:3
	CASE 3
		LOCAL:56 += 2
	CASE 2
		LOCAL:56 += 1 + RAND:2
	CASE 1
		LOCAL:56 += 1
ENDSELECT

;調教対象の奉仕精神
SELECTCASE ABL:MASTER:7
	CASE IS > 4
		LOCAL:56 += 3 + RAND:5
	CASE 4
		LOCAL:56 += 2 + RAND:4
	CASE 3
		LOCAL:56 += 1 + RAND:4
	CASE 2
		LOCAL:56 += RAND:4
	CASE 1
		LOCAL:56 += RAND:2
ENDSELECT

;調教対象の苦痛中毒
SELECTCASE ABL:MASTER:15
	CASE IS > 4
		LOCAL:60 += RAND:3
		LOCAL:61 += 1 + RAND:5
		LOCAL:62 += 3 + RAND:6
		LOCAL:67 += TEQUIP:43 ? 0 # 2 + RAND:9
	CASE 4
		LOCAL:60 += 1 + RAND:5
		LOCAL:61 += 2 + RAND:6
		LOCAL:62 += 2 + RAND:6
		LOCAL:67 += TEQUIP:43 ? 0 # RAND:7
	CASE 3
		LOCAL:60 += 2 + RAND:5
		LOCAL:61 += 2 + RAND:5
		LOCAL:62 += 1 + RAND:5
		LOCAL:67 += TEQUIP:43 ? 0 # RAND:4
	CASE 2
		LOCAL:60 += 2 + RAND:3
		LOCAL:61 += 2 + RAND:3
		LOCAL:62 += RAND:4
		LOCAL:67 += TEQUIP:43 ? 0 # RAND:2
	CASE 1
		LOCAL:60 += 1 + RAND:3
		LOCAL:61 += RAND:3
		LOCAL:62 += RAND:2
ENDSELECT

;調教対象の拘束中毒
SELECTCASE ABL:MASTER:16
	CASE IS > 4
		LOCAL:63 += TEQUIP:40 ? 0 # 5 + RAND:5
		LOCAL:64 += TEQUIP:41 ? 0 # 3 + RAND:6
		LOCAL:65 += TEQUIP:42 ? 0 # 2 + RAND:9
	CASE 4
		LOCAL:63 += TEQUIP:40 ? 0 # 4 + RAND:4
		LOCAL:64 += TEQUIP:41 ? 0 # 3 + RAND:4
		LOCAL:65 += TEQUIP:42 ? 0 # 2 + RAND:7
	CASE 3
		LOCAL:63 += TEQUIP:40 ? 0 # 3 + RAND:3
		LOCAL:64 += TEQUIP:41 ? 0 # 2 + RAND:3
		LOCAL:65 += TEQUIP:42 ? 0 # 1 + RAND:6
	CASE 2
		LOCAL:63 += TEQUIP:40 ? 0 # 2 + RAND:4
		LOCAL:64 += TEQUIP:41 ? 0 # 2 + RAND:2
		LOCAL:65 += TEQUIP:42 ? 0 # 1 + RAND:4
	CASE 1
		LOCAL:63 += TEQUIP:40 ? 0 # RAND:6
		LOCAL:64 += TEQUIP:41 ? 0 # RAND:4
		LOCAL:65 += TEQUIP:42 ? 0 # RAND:2
ENDSELECT

;反抗
SELECTCASE PALAM:9
	CASE IS > 7500
		LOCAL:63 += TEQUIP:40 ? 0 # 5 + RAND:3
		LOCAL:64 += TEQUIP:41 ? 0 # 4 + RAND:4
		LOCAL:65 += TEQUIP:42 ? 0 # 3 + RAND:2
	CASE IS > 6000
		LOCAL:63 += TEQUIP:40 ? 0 # 4 + RAND:2
		LOCAL:64 += TEQUIP:41 ? 0 # 3 + RAND:3
		LOCAL:65 += TEQUIP:42 ? 0 # 3
	CASE IS > 4500
		LOCAL:63 += TEQUIP:40 ? 0 # 3 + RAND:2
		LOCAL:64 += TEQUIP:41 ? 0 # 2 + RAND:3
		LOCAL:65 += TEQUIP:42 ? 0 # 2
	CASE IS > 3000
		LOCAL:63 += TEQUIP:40 ? 0 # 2 + RAND:2
		LOCAL:64 += TEQUIP:41 ? 0 # 2 + RAND:2
		LOCAL:65 += TEQUIP:42 ? 0 # RAND:2
	CASE IS > 1500
		LOCAL:63 += TEQUIP:40 ? 0 # 2
		LOCAL:64 += TEQUIP:41 ? 0 # 1
	CASE IS > 500
		LOCAL:63 += TEQUIP:40 ? 0 # 1
ENDSELECT

;罪悪感
SELECTCASE CFLAG:5
	CASE IS > 100
		LOCAL:62 -= 1
		LOCAL:65 -= 2
		LOCAL:67 -= 5
		LOCAL:68 -= 3
	CASE IS > 80
		LOCAL:65 -= 1
		LOCAL:67 -= 4
		LOCAL:68 -= 2
	CASE IS > 60
		LOCAL:67 -= 3
		LOCAL:68 -= 1
	CASE IS > 40
		LOCAL:67 -= 1
	CASE IS > 20
		LOCAL:67 -= RAND:2
ENDSELECT

;アライメント
SELECTCASE CFLAG:6
	CASE IS > 49
		LOCAL:60 += 3
		LOCAL:66 += 2
		LOCAL:67 -= 3
	CASE IS > 39
		LOCAL:60 += 2 + RAND:2
		LOCAL:66 += 1 + RAND:2
		LOCAL:67 -= 2 + RAND:2
	CASE IS > 29
		LOCAL:60 += 2
		LOCAL:66 += 1
		LOCAL:67 -= 2
	CASE IS > 19
		LOCAL:60 += 1 + RAND:2
		LOCAL:67 -= 1 + RAND:2
	CASE IS > 9
		LOCAL:60 += 1
		LOCAL:67 -= 1
	CASE IS > -1
		LOCAL:60 += RAND:2
		LOCAL:67 -= RAND:2
	CASE IS > -11
		LOCAL:60 -= RAND:2
		LOCAL:67 += RAND:2
		LOCAL:68 += RAND:2
	CASE IS > -21
		LOCAL:60 -= 1
		LOCAL:67 += 1
		LOCAL:68 += 1
	CASE IS > -31
		LOCAL:60 -= 1 + RAND:2
		LOCAL:67 += 1 + RAND:2
		LOCAL:68 += 1 + RAND:2
	CASE IS > -41
		LOCAL:60 -= 2
		LOCAL:67 += 2
		LOCAL:68 += 2
	CASE IS > -51
		LOCAL:60 -= 2 + RAND:2
		LOCAL:67 += 2 + RAND:2
		LOCAL:68 += 2 + RAND:2
	CASEELSE
		LOCAL:60 -= 3
		LOCAL:67 += 3
		LOCAL:68 += 3
ENDSELECT

;────────────────────────────────────
;ゲージや状態による変動
;────────────────────────────────────
;調教対象の体力
SELECTCASE BASE:MASTER:0
	CASE IS < 300
		LOCAL:60 -= 1
		LOCAL:61 -= 3
		LOCAL:62 -= 4
		LOCAL:67 += TEQUIP:43 ? 4 # -4
		LOCAL:68 += TEQUIP:27 ? RAND:10 # -4
		LOCAL:56 -= 3
	CASE IS < 600
		LOCAL:61 -= 1
		LOCAL:62 -= 3
		LOCAL:67 += TEQUIP:43 ? 2 # -2
		LOCAL:68 += TEQUIP:27 ? RAND:8 # -2
		LOCAL:56 -= 2
	CASE IS < 900
		LOCAL:62 -= 1
		LOCAL:67 += TEQUIP:43 ? 1 # -1
		LOCAL:68 += TEQUIP:27 ? RAND:4 # -1
		LOCAL:56 -= 1
ENDSELECT

;調教対象の気力
SELECTCASE BASE:MASTER:1
	CASE IS > 800
		LOCAL:63 += TEQUIP:40 ? -10 # RAND:5
		LOCAL:64 += TEQUIP:41 ? -8 # RAND:5
		LOCAL:65 += TEQUIP:42 ? -8 # RAND:5
	CASE IS > 600
		LOCAL:63 += TEQUIP:40 ? -7 # RAND:4
		LOCAL:64 += TEQUIP:41 ? -7 # RAND:4
		LOCAL:65 += TEQUIP:42 ? -6 # RAND:4
	CASE IS > 400
		LOCAL:63 += TEQUIP:40 ? -4 # RAND:3
		LOCAL:64 += TEQUIP:41 ? -5 # RAND:3
		LOCAL:65 += TEQUIP:42 ? -4 # RAND:3
ENDSELECT

;疲弊で拘束などを解く(調教対象の消耗)
IF TFLAG:63
	LOCAL:63 += TEQUIP:40 ? 4 + TFLAG:63 / 2 # -3 - TFLAG:63 / 4
	LOCAL:64 += TEQUIP:41 ? 2 + TFLAG:63 / 3 # -1 - TFLAG:63 / 6
	LOCAL:65 += TEQUIP:42 ? 3 + TFLAG:63 / 3 # -2 - TFLAG:63 / 5
	LOCAL:67 += TEQUIP:43 ? 9 + TFLAG:63 # -9 - TFLAG:63
ENDIF

;調教者の体力
SELECTCASE BASE:0
	CASE IS < 500
		LOCAL:60 -= 3
		LOCAL:61 -= 3
	CASE IS < 1000
		LOCAL:60 -= 2
		LOCAL:61 -= 2
	CASE IS < 1500
		LOCAL:60 -= 1
		LOCAL:61 -= 1
ENDSELECT

;調教者の気力
SELECTCASE BASE:1
	CASE IS < 250
		LOCAL:60 -= 1
		LOCAL:61 -= 1
		LOCAL:62 -= 1
		LOCAL:66 -= 3
		LOCAL:68 -= 1
		LOCAL:56 -= 2
	CASE IS < 500
		LOCAL:66 -= 2
		LOCAL:56 -= 1
	CASE IS < 750
		LOCAL:66 -= 1
ENDSELECT

;調教者の苛立ち
SELECTCASE BASE:7
	CASE IS > 800
		LOCAL:60 += 6
		LOCAL:61 += 10
		LOCAL:62 += 8
		LOCAL:66 -= 6
	CASE IS > 600
		LOCAL:60 += 5
		LOCAL:61 += 8
		LOCAL:62 += 7
		LOCAL:66 -= 4
	CASE IS > 400
		LOCAL:60 += 4
		LOCAL:61 += 6
		LOCAL:62 += 5
		LOCAL:66 -= 3
	CASE IS > 200
		LOCAL:60 += 2
		LOCAL:61 += 3
		LOCAL:62 += 2
		LOCAL:66 -= 2
ENDSELECT

;調教者の状態(0=通常/1=疲弊/2=衰弱/3=無気力/4=朦朧/5=情欲/6=怒り/7=退屈/8=狂乱)
SELECTCASE TFLAG:60
	CASE 5
		LOCAL:56 += RAND:5
	CASE 6
		LOCAL:66 += RAND:5
	CASE 8
		LOCAL:67 += 2 + RAND:8
ENDSELECT

;────────────────────────────────────
;前回の行動や状況による変動
;────────────────────────────────────
;罵倒の話はした
SIF TFLAG:100 & 64
	LOCAL:66 -= 5

;暴れた
IF SELECTCOM == 32
	LOCAL:62 -= 2
	LOCAL:63 += TEQUIP:40 ? 0 # 10
	LOCAL:64 += TEQUIP:41 ? 0 # 6
	LOCAL:67 += TEQUIP:42 ? 0 # 3
ENDIF

;勝手に道具をはずされた＆縄未装着
SIF SELECTCOM == 51 && !TEQUIP:40
	LOCAL:63 += 5

;調教対象自慰中
SIF TEQUIP:69 & 1
	LOCAL:66 += RAND:10
;調教者自慰中
SIF TEQUIP:69 & 2
	LOCAL:66 += RAND:5
;※二人自慰中なら両方の補正が有効となる

;挿入中
IF TEQUIP:70
	SIF TEQUIP:70 == 3 || TEQUIP:70 == 5 || TEQUIP:70 == 7
		LOCAL:60 -= 5
	LOCAL:62 -= 5
	LOCAL:68 -= 5
	LOCAL:56 -= 5
ENDIF

;お仕置きモード
IF TFLAG:69
	LOCAL:67 += RAND:10
	LOCAL:68 += 1 + RAND:7
	LOCAL:56 += 3 + RAND:4
ENDIF

;調教対象が下半身下着を着ている
SIF TEQUIP:MASTER:2
	LOCAL:67 -= 2

;調教対象がスカート以外の下半身上着を着ている
IF TEQUIP:MASTER:84 == 1
	LOCAL:67 -= 5
	LOCAL:68 -= 2
ENDIF

;調教対象が上半身上着または全身上着を着ている
IF TEQUIP:MASTER:85 || TEQUIP:MASTER:86
	LOCAL:61 -= 5
	LOCAL:62 -= 5
ENDIF

;────────────────────────────────────
;同じ行動連続実行の確率をダウンします
;────────────────────────────────────
SELECTCASE TFLAG:91
	CASE 56, 60 TO 68
		LOCAL:(TFLAG:91) -= 3 + RAND:5
ENDSELECT

;────────────────────────────────────
;その他変動や実行不可能の判定
;────────────────────────────────────
;不可能判定とカウンタ値の下限チェック
CALL ACT_ABLE56
SIF !RESULT || LOCAL:56 < -99
	LOCAL:56 = -99
FOR LOCAL:900, 60, 69
	CALLFORM ACT_ABLE{LOCAL:900}
	SIF !RESULT || LOCAL:(LOCAL:900) < -99
		LOCAL:(LOCAL:900) = -99
NEXT

;────────────────────────────────────
;最終判定
;────────────────────────────────────
SELECTCASE MAX(LOCAL:60, LOCAL:61, LOCAL:62, LOCAL:63, LOCAL:64, LOCAL:65, LOCAL:66, LOCAL:67, LOCAL:68, LOCAL:56)
;ここには来ないはず
;	CASE -99
;		PRINTL (加虐カウンタ異常)
;		TFLAG:90 = 60
	CASE LOCAL:60
		TFLAG:90 = 60
	CASE LOCAL:61
		TFLAG:90 = 61
	CASE LOCAL:63
		TFLAG:90 = 63
	CASE LOCAL:62
		TFLAG:90 = 62
	CASE LOCAL:64
		TFLAG:90 = 64
	CASE LOCAL:65
		TFLAG:90 = 65
	CASE LOCAL:66
		TFLAG:90 = 66
	CASE LOCAL:67
		TFLAG:90 = 67
	CASE LOCAL:68
		TFLAG:90 = 68
	CASE LOCAL:56
		TFLAG:90 = 56
ENDSELECT

;デバッグ＆調整用カウンタ
IF FLAG:4
	PRINTFORML 　　加虐：平手[{LOCAL:60,3}]/むち[{LOCAL:61,3}]/はり[{LOCAL:62,3}]/なわ[{LOCAL:63,3}]/目隠[{LOCAL:64,3}]
	PRINTFORML 　　　　　口枷[{LOCAL:65,3}]/辱骂[{LOCAL:66,3}]/木馬[{LOCAL:67,3}]/灌肠[{LOCAL:68,3}]/イラ[{LOCAL:56,3}]
ENDIF


;-----------------------------------------------------------
;加虐 の実行判定
;-----------------------------------------------------------
@ACTM_ABLE6
CALL ACT_ABLE56
SIF RESULT
	RETURN 1
FOR LOCAL, 60, 69
	CALLFORM ACT_ABLE{LOCAL}
	SIF RESULT
		RETURN 1
NEXT
RETURN 0
