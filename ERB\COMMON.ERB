﻿;-------------------------------------------------
;汎用関数置き場
;未分類
;-------------------------------------------------


;-------------------------------------------------
;関数名:CAPACITY
;概　要:拠点にいる夢魔の数を返す
;引　数:なし
;戻り値:拠点にいる夢魔の数(あなたを含む)
;備　考:式中関数
;あなたを含む。一時退避中のキャラを含まない
;-------------------------------------------------
@CAPACITY
#FUNCTION
RETURNF CMATCH(CFLAG:92, 0)


;-------------------------------------------------
;関数名:CHOICE
;概　要:２～４択関数
;引　数:ARGS:0…質問内容
;      :ARGS:1～4…選択肢の文字列(3,4は省略可)
;戻り値:ユーザ入力結果(0～3)
;選択条件の無い、シンプルな選択肢作成関数
;口上とかでも使えるかもしれない
;とりあえず4択まで。増やしてもいいけど
;-------------------------------------------------
@CHOICE(ARGS:0, ARGS:1, ARGS:2, ARGS:3, ARGS:4)
PRINTSL ARGS:0
FOR LOCAL, 0, 4
	PRINTFORML [{LOCAL}] - %ARGS:(1 + LOCAL)%
	SIF LOCAL && !STRLENS(ARGS:(2 + LOCAL))
		BREAK
NEXT
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0 TO LOCAL - 1
	CASEELSE
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
RETURN RESULT


;-------------------------------------------------
;関数名:CONDITION
;概　要:状態値から文字列を取得する関数
;引　数:ARG…状態フラグ(TFLAG:60or61)
;戻り値:状態を表す文字列
;備　考:式中関数
;-------------------------------------------------
@CONDITION(ARG)
#FUNCTIONS
SIF !STRLENS(LOCALS)
	SPLIT "通常/疲弊/衰弱/無气力/朦胧/情欲/生气/退屈/狂乱", "/", LOCALS
RETURNF LOCALS:ARG


;-------------------------------------------------
;関数名:EFFECT
;概　要:薬剤などの効果を表す関数
;引　数:ARG:0…薬剤の効果量(TEQUIP)
;       ARG:1…0なら弱中強、1なら少中多を返す
;戻り値:効果の大きさを表す文字
;       10以上は強、5以上は中、5未満は弱
;備　考:式中関数
;-------------------------------------------------
@EFFECT(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE ARG:0
	CASE IS > 9
		RETURNF @"\@ ARG:1 ? 多 # 强 \@"
	CASE IS > 4
		RETURNF "中"
	CASEELSE
		RETURNF @"\@ ARG:1 ? 少 # 弱 \@"
ENDSELECT


;-------------------------------------------------
;関数名:PERSONA
;概　要:性格を表す文字列を取得する関数
;引　数:ARG…キャラ登録番号
;戻り値:性格を表す文字列
;備　考:式中関数
;-------------------------------------------------
@PERSONA(ARG)
#FUNCTIONS
SIF !STRLENS(LOCALS)
	SPLIT "普通/施虐狂/受虐狂/姐姐/妹/兴奋/傲娇/淫乱/贞淑", "/", LOCALS
RETURNF LOCALS:(CFLAG:ARG:20)


;-------------------------------------------------
;関数名:PRINT_BASE
;概　要:BASE表示用関数
;引　数:ARGS …BASEの名前
;　　　 ARG:0…キャラ登録番号
;       ARG:1…表示するBASE
;       ARG:2…BARの長さ(省略した場合32)
;       ARG:3…真ならBASEの値を0以上に補正する
;              (省略した場合、補正しない)
;戻り値:なし
;-------------------------------------------------
@PRINT_BASE(ARGS, ARG:0, ARG:1, ARG:2, ARG:3)
PRINTS ARGS
LOCAL:0 = ARG:3 ? MAX(BASE:(ARG:0):(ARG:1), 0) # BASE:(ARG:0):(ARG:1)
LOCAL:1 = ARG:0 == MASTER ? ACCESSORY_MAXBASE(ARG:1) # MAXBASE:(ARG:0):(ARG:1)
BAR LOCAL:0, LOCAL:1, ARG:2 ? ARG:2 # 32
PRINTFORM ({LOCAL:0, 5}/{LOCAL:1, 5})

@PRINT_BASEL(ARGS, ARG:0, ARG:1, ARG:2, ARG:3)
CALL PRINT_BASE(ARGS, ARG:0, ARG:1, ARG:2, ARG:3)
PRINTL 

@PRINT_BASEW(ARGS, ARG:0, ARG:1, ARG:2, ARG:3)
CALL PRINT_BASE(ARGS, ARG:0, ARG:1, ARG:2, ARG:3)
WAIT


;-------------------------------------------------
;関数名:TENSION
;概　要:BASE値を大雑把に表す関数
;引　数:ARG:0…キャラ登録番号
;       ARG:1…BASE番号
;戻り値:BASE値を大雑把に表す文字列
;       50%以上は高い、25%以上は一般、25%未満は低い
;備　考:式中関数
;調教者の理性や満足などを文字で表現する場合に使用
;-------------------------------------------------
@TENSION(ARG:0, ARG:1)
#FUNCTIONS
LOCAL = MAX(BASE:(ARG:0):(ARG:1), 0)
SIF LOCAL < MAXBASE:(ARG:0):(ARG:1) / 4
	RETURNF "低"
RETURNF @"\@ LOCAL < MAXBASE:(ARG:0):(ARG:1) / 2 ? 一般 # 高 \@"


