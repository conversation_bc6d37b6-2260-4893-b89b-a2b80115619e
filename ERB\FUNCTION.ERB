﻿;-------------------------------------------------
;ハートマークの出力…口上テキストには組み込みにくいが、はっきりとしたハートマーク
;-------------------------------------------------
;Lucida Sans Unicode は大きめだが、下の文章をかなり消してしまうんですよねぇ
@HEARTMARK
GETFONT
CHKFONT "Times New Roman"
SIF RESULT
	SETFONT "Times New Roman"
PRINTFORM %UNICODE(0x2665)%
SETFONT RESULTS

;口上の最後の時などに適している
@HEARTMARK_E
GETFONT
CHKFONT "Times New Roman"
SIF RESULT
	SETFONT "Times New Roman"
PRINTFORM %UNICODE(0x2665)%
SETFONT RESULTS
PRINTFORMW 」
;-------------------------------------------------
;@SAVE_TEMPと@LOAD_TEMPは、後で戻すことが確実な変数をSAVE&LOADするのに使用。SAVEとLOADを必ずペアで使うこと。
;REPEATを使用するとCOUNTの管理が面倒になるので、あえて使わない。
;20より階層が深くなった場合にはエラーメッセージを出力させる。
;使用例…記録させた順番は絶対に守ること！
;T = TARGET
;CALL SAVE_TEMP
;T = COUNT
;CALL SAVE_TEMP
;T = A:5
;CALL SAVE_TEMP
;@何らかの関数とか
;CALL LOAD_TEMP
;A:5 = T
;CALL LOAD_TEMP
;COUNT = T
;CALL LOAD_TEMP
;TARGET = T
;-------------------------------------------------
@SAVE_TEMP
IF TFLAG:600 == -2
	TFLAG:600 = T
ELSEIF TFLAG:601 == -2
	TFLAG:601 = T
ELSEIF TFLAG:602 == -2
	TFLAG:602 = T
ELSEIF TFLAG:603 == -2
	TFLAG:603 = T
ELSEIF TFLAG:604 == -2
	TFLAG:604 = T
ELSEIF TFLAG:605 == -2
	TFLAG:605 = T
ELSEIF TFLAG:606 == -2
	TFLAG:606 = T
ELSEIF TFLAG:607 == -2
	TFLAG:607 = T
ELSEIF TFLAG:608 == -2
	TFLAG:608 = T
ELSEIF TFLAG:609 == -2
	TFLAG:609 = T
ELSEIF TFLAG:610 == -2
	TFLAG:610 = T
ELSEIF TFLAG:611 == -2
	TFLAG:611 = T
ELSEIF TFLAG:612 == -2
	TFLAG:612 = T
ELSEIF TFLAG:613 == -2
	TFLAG:613 = T
ELSEIF TFLAG:614 == -2
	TFLAG:614 = T
ELSEIF TFLAG:615 == -2
	TFLAG:615 = T
ELSEIF TFLAG:616 == -2
	TFLAG:616 = T
ELSEIF TFLAG:617 == -2
	TFLAG:617 = T
ELSEIF TFLAG:618 == -2
	TFLAG:618 = T
ELSEIF TFLAG:619 == -2
	TFLAG:619 = T
ELSE
	PRINTFORMW 错误：@SAVE_TEMP 无法记住更多了！
ENDIF

RETURN 0

@LOAD_TEMP
IF TFLAG:619 > -2
	T = TFLAG:619
	TFLAG:619 = -2
ELSEIF TFLAG:618 > -2
	T = TFLAG:618
	TFLAG:618 = -2
ELSEIF TFLAG:617 > -2
	T = TFLAG:617
	TFLAG:617 = -2
ELSEIF TFLAG:616 > -2
	T = TFLAG:616
	TFLAG:616 = -2
ELSEIF TFLAG:615 > -2
	T = TFLAG:615
	TFLAG:615 = -2
ELSEIF TFLAG:614 > -2
	T = TFLAG:614
	TFLAG:614 = -2
ELSEIF TFLAG:613 > -2
	T = TFLAG:613
	TFLAG:613 = -2
ELSEIF TFLAG:612 > -2
	T = TFLAG:612
	TFLAG:612 = -2
ELSEIF TFLAG:611 > -2
	T = TFLAG:611
	TFLAG:611 = -2
ELSEIF TFLAG:610 > -2
	T = TFLAG:610
	TFLAG:610 = -2
ELSEIF TFLAG:609 > -2
	T = TFLAG:609
	TFLAG:609 = -2
ELSEIF TFLAG:608 > -2
	T = TFLAG:608
	TFLAG:608 = -2
ELSEIF TFLAG:607 > -2
	T = TFLAG:607
	TFLAG:607 = -2
ELSEIF TFLAG:606 > -2
	T = TFLAG:606
	TFLAG:606 = -2
ELSEIF TFLAG:605 > -2
	T = TFLAG:605
	TFLAG:605 = -2
ELSEIF TFLAG:604 > -2
	T = TFLAG:604
	TFLAG:604 = -2
ELSEIF TFLAG:603 > -2
	T = TFLAG:603
	TFLAG:603 = -2
ELSEIF TFLAG:602 > -2
	T = TFLAG:602
	TFLAG:602 = -2
ELSEIF TFLAG:601 > -2
	T = TFLAG:601
	TFLAG:601 = -2
ELSEIF TFLAG:600 > -2
	T = TFLAG:600
	TFLAG:600 = -2
ENDIF

RETURN 0

;-------------------------------------------------
;@SAVE_A_Zと@LOAD_A_ZはA～Zを一度にSAVE&LOADする関数
;-------------------------------------------------
@SAVE_A_Z
TFLAG:620 = A
TFLAG:621 = B
TFLAG:622 = C
TFLAG:623 = D
TFLAG:624 = E
TFLAG:625 = F
TFLAG:626 = G
TFLAG:627 = H
TFLAG:628 = I
TFLAG:629 = J
TFLAG:630 = K
TFLAG:631 = L
TFLAG:632 = M
TFLAG:633 = N
TFLAG:634 = O
TFLAG:635 = P
TFLAG:636 = Q
TFLAG:637 = R
TFLAG:638 = S
TFLAG:639 = T
TFLAG:640 = U
TFLAG:641 = V
TFLAG:642 = W
TFLAG:643 = X
TFLAG:644 = Y
TFLAG:645 = Z

@LOAD_A_Z
A = TFLAG:620
B = TFLAG:621
C = TFLAG:622
D = TFLAG:623
E = TFLAG:624
F = TFLAG:625
G = TFLAG:626
H = TFLAG:627
I = TFLAG:628
J = TFLAG:629
K = TFLAG:630
L = TFLAG:631
M = TFLAG:632
N = TFLAG:633
O = TFLAG:634
P = TFLAG:635
Q = TFLAG:636
R = TFLAG:637
S = TFLAG:638
T = TFLAG:639
U = TFLAG:640
V = TFLAG:641
W = TFLAG:642
X = TFLAG:643
Y = TFLAG:644
Z = TFLAG:645

;-------------------------------------------------
;IF文中で"GOTO INPUT_LOOP"を使うとエラーが出るのを回避する。以下は使い方のテンプレ：
;	PRINTL [0] 
;	PRINTL [1] 
;	CALL INPUT_SELECT_2
;	IF R == 0
;	ELSEIF R == 1
;	ENDIF
;便利な関数だが、R == 0 && ABL:4 >= 3 みたいな、条件も同時についているものには向かないので注意。
;-------------------------------------------------
@INPUT_SELECT_2
INPUT
SIF RESULT < 0 || RESULT > 1
	RESTART
R = RESULT

;0～2の3択用
@INPUT_SELECT_3
INPUT
SIF RESULT < 0 || RESULT > 2
	RESTART
R = RESULT

;0～3の4択用
@INPUT_SELECT_4
INPUT
SIF RESULT < 0 || RESULT > 3
	RESTART
R = RESULT

;0～4の5択用
@INPUT_SELECT_5
INPUT
SIF RESULT < 0 || RESULT > 4
	RESTART
R = RESULT
