﻿;────────────────────────────────────
;調教開始の処理
;────────────────────────────────────
@EVENTTRAIN
FLAG:10 = 1
;衣装をInitialize
TEQUIP:MASTER:0 = CFLAG:MASTER:12 < 2 ? CFLAG:MASTER:80 # 0
TEQUIP:MASTER:1 = CFLAG:MASTER:12 < 2 ? CFLAG:MASTER:81 # 0
TEQUIP:MASTER:2 = CFLAG:MASTER:12 < 2 ? CFLAG:MASTER:82 # 0
TEQUIP:MASTER:3 = CFLAG:MASTER:12 < 2 ? CFLAG:MASTER:83 # 0
TEQUIP:MASTER:4 = CFLAG:MASTER:12 < 1 ? CFLAG:MASTER:84 # 0
TEQUIP:MASTER:5 = CFLAG:MASTER:12 < 1 ? CFLAG:MASTER:85 # 0
TEQUIP:MASTER:6 = CFLAG:MASTER:12 < 1 ? CFLAG:MASTER:86 # 0
TEQUIP:MASTER:9 = CFLAG:MASTER:12 == 3 ? CFLAG:MASTER:79 # 0

TEQUIP:0 = CFLAG:TARGET:12 < 2 ? CFLAG:TARGET:80 # 0
TEQUIP:1 = CFLAG:TARGET:12 < 2 ? CFLAG:TARGET:81 # 0
TEQUIP:2 = CFLAG:TARGET:12 < 2 ? CFLAG:TARGET:82 # 0
TEQUIP:3 = CFLAG:TARGET:12 < 2 ? CFLAG:TARGET:83 # 0
TEQUIP:4 = CFLAG:TARGET:12 < 1 ? CFLAG:TARGET:84 # 0
TEQUIP:5 = CFLAG:TARGET:12 < 1 ? CFLAG:TARGET:85 # 0
TEQUIP:6 = CFLAG:TARGET:12 < 1 ? CFLAG:TARGET:86 # 0
TEQUIP:9 = CFLAG:TARGET:12 == 3 ? CFLAG:TARGET:79 # 0

;コスプレ
IF CFLAG:12 == 3
	SELECTCASE CFLAG:79
		;ボンデージ
		CASE 1
			TALENT:施虐狂 += 1
		;和服
		CASE 2
			TALENT:清楚 += 1
		;メイド服
		CASE 3
			TALENT:自我奉献 += 1
		;チャイナ服
		CASE 4
			TALENT:美脚 += 1
		;バニースーツ
		CASE 7
			TALENT:兽耳 += 1
	ENDSELECT
ENDIF

;-1に初期化するTFLAGをリセット
TFLAG:91 = -1
TFLAG:211 = -1
TFLAG:212 = -1
TFLAG:213 = -1

;調教中に用いるCFLAGをリセット
FOR LOCAL, 30, 34
	CVARSET CFLAG, LOCAL, 50
NEXT
FOR LOCAL, 250, 260
	CVARSET CFLAG, LOCAL
NEXT

;現在のアライメント=平時アライメント
CFLAG:90 = LIMIT(CFLAG:90, -999, 999)
CFLAG:6 = CFLAG:90 / 10
;奴隷の首輪装備中
SIF EQUIP:MASTER:11 == 1 && !FLAG:1700 && !FLAG:1701
	CFLAG:6 = -99

;開始時の調教者の射精ゲージが0になっていたので、ここで修正
BASE:2 = MAXBASE:2
;尿意ゲージの設定
BASE:MASTER:4 = 0
MAXBASE:MASTER:4 = 10000 - TALENT:MASTER:123 * 5000 + TALENT:MASTER:124 * 5000
;まだ解消してない疲弊を調教フェースに反応
TFLAG:62 = CFLAG:11
TFLAG:63 = CFLAG:MASTER:11

;状態異常
;敏感
IF CFLAG:MASTER:220
	TFLAG:373 = 1
	TEQUIP:11 = 10
	TFLAG:131 = 1500
ENDIF
TFLAG:131 = 1000 * (BASE:MASTER:2 + 5000) * (MAXBASE:MASTER:5 - BASE:MASTER:5) / ((MAXBASE:MASTER:2 + 5000) * MAXBASE:MASTER:5)
IF FLAG:1700
	A = (2 * ABL:MASTER:2 + CFLAG:MASTER:0) * BASE:MASTER:0 / MAXBASE:MASTER:0 + 1
	B = 2 * ABL:TARGET:2 + CFLAG:TARGET:0 + 1
	IF FLAG:1781 == 0 && FLAG:1710 == 0
		IF RAND:B > RAND:A && RAND:(1 + FLAG:3133) == 0
			PRINTW 突然袭击！
			TFLAG:112 = 2
			TFLAG:50 ++
		ENDIF
	ENDIF
	FLAG:1781 = 0
ENDIF

;────────────────────────────────────
;今回の方針
;────────────────────────────────────
IF CFLAG:0 > 4 && !FLAG:1700 && CFLAG:11 <= 5 && CFLAG:MASTER:11 <= 5
	SELECTCASE ABL:MASTER:5
		CASE 0
			TFLAG:77 = !RAND:3
		CASE 1
			TFLAG:77 = !RAND:4
		CASE 2
			TFLAG:77 = !RAND:6
		CASE 3
			TFLAG:77 = !RAND:8
		CASE 4
			TFLAG:77 = !RAND:12
	ENDSELECT
ENDIF

;確率でいつもどおりでない方針をとるように
IF !RAND:5 && CFLAG:0 > 4 && !FLAG:1700 && CFLAG:11 <= 5 && CFLAG:MASTER:11 <= 5
	LOCAL = RAND:4
	IF BASE:MASTER:0 < 2 * MAXBASE:MASTER:0 / 3 && LOCAL == 0
		TFLAG:77 = 4
	ELSEIF	BASE:MASTER:2 < 9 * MAXBASE:MASTER:0 / 10 && LOCAL == 1
		TFLAG:77 = 3
	ELSEIF ITEM:42  && LOCAL == 2
		TFLAG:77 = 6
	ELSEIF GETBIT(FLAG:60,0)  && LOCAL == 3
		TFLAG:77 = 9
	;お仕置きは反発依存にしたいが仕様がさだまっていないので後回し
	;ELSEIF RAND:((2 + MARK:3) * (TALENT:83 + 1)) > RAND:30
	;	TFLAG:77 = 2
	ENDIF	
ENDIF

;調教開始口上
DRAWLINE
RESULT = 0
IF CFLAG:11 > 5
	CALL KOJO_EVENT(11, 1)
ELSEIF CFLAG:MASTER:11 > 5
	CALL KOJO_EVENT(11, 2)
ELSE
	CALL KOJO_EVENT(11)
ENDIF

LOCAL = 0
IF CFLAG:202 && CFLAG:9 > 1000 && !TFLAG:77 && CFLAG:11 <= 5 && CFLAG:MASTER:11 <= 5
	PRINTFORMW %CALLNAME:TARGET%的大满足奖励
	CALL KOJO_EVENT(16)
	PRINTFORMW %CALLNAME:TARGET%看上去兴致勃勃问%CALLNAME:MASTER%今天想做什么
	PRINTL [0] - 和往常一样
	PRINTL [1] - 欺负肛门
	PRINTL [2] - 惩罚
	PRINTL [3] - 激烈
	PRINTL [4] - 撒娇
	PRINTL [5] - 直到坏掉
	SIF ITEM:42
		PRINTL [6] - 想出去
	SIF CFLAG:2 + CFLAG:9 > 10000 && !TALENT:2 && FLAG:2007
		PRINTL [7] - 生孩子
	PRINTL [8] - 休息
	SIF GETBIT(FLAG:60,0)
		PRINTL [9] - 温柔PLAY
	$INPUT_LOOP
	INPUT
	SELECTCASE RESULT
		CASE 2
			TFLAG:69 = 1
		CASE 6
			SIF !ITEM:42
				GOTO INPUT_LOOP
			TEQUIP:52 = 1
		CASE 7
			SIF CFLAG:2 + CFLAG:9 <= 6000 || TALENT:2 || (!FLAG:2007 && !FLAG:2008)
				GOTO INPUT_LOOP
		CASE 9
			SIF !GETBIT(FLAG:60,0)
				GOTO INPUT_LOOP
		CASE 0 TO 9
		CASEELSE
			GOTO INPUT_LOOP
	ENDSELECT
	TFLAG:77 = RESULT
	CFLAG:202 = TFLAG:77 ? 0 # CFLAG:202
	CALL KOJO_EVENT(16, 1)
	SIF !TFLAG:77
		PRINTFORMW %CALLNAME:TARGET%听到%CALLNAME:MASTER%的回话后就开始准备调教了
	LOCAL = 1
ENDIF
SIF !LOCAL && TFLAG:77
	CALL KOJO_EVENT(17)
SELECTCASE TFLAG:77
	CASE 1
		PRINTFORMW 这次%CALLNAME:TARGET%决定开发%CALLNAME:MASTER%的肛门
	CASE 2
		PRINTFORMW %CALLNAME:TARGET%惩罚了%CALLNAME:MASTER%
	CASE 3
		PRINTFORMW %CALLNAME:TARGET%比平时更激烈地调教着
	CASE 4
		PRINTFORMW %CALLNAME:TARGET%向%CALLNAME:MASTER%招手、给了一个温柔的拥抱
	CASE 5
		PRINTFORMW %CALLNAME:TARGET%用充斥着嗜虐和情欲的眼睛看着%CALLNAME:MASTER%
	CASE 6
		PRINTFORMW %CALLNAME:TARGET%给%CALLNAME:MASTER%戴上项圈，开始做外出的准备
	CASE 7
		PRINTFORMW %CALLNAME:TARGET%因为对孩子的期待而接近了%CALLNAME:MASTER%
	CASE 8
		;INFO.ERBに処理を移動
	CASE 9
		PRINTFORMW %CALLNAME:TARGET%把%CALLNAME:MASTER%带到了大浴场
		TEQUIP:MASTER:0 = 0
		TEQUIP:MASTER:1 = 0
		TEQUIP:MASTER:2 = 0
		TEQUIP:MASTER:3 = 0
		TEQUIP:MASTER:4 = 0
		TEQUIP:MASTER:5 = 0
		TEQUIP:MASTER:6 = 0
		TEQUIP:MASTER:9 = 0

		TEQUIP:0 = 0
		TEQUIP:1 = 0
		TEQUIP:2 = 0
		TEQUIP:3 = 0
		TEQUIP:4 = 0
		TEQUIP:5 = 0
		TEQUIP:6 = 0
		TEQUIP:9 = 0
ENDSELECT


;────────────────────────────────────
;汚れのリセット
;────────────────────────────────────
@STAIN_RESET
RESET_STAIN MASTER
RESET_STAIN TARGET
SIF ASSI:1 >= 0
	RESET_STAIN ASSI:1
SIF ASSI:2 >= 0
	RESET_STAIN ASSI:2
SIF ASSI:3 >= 0
	RESET_STAIN ASSI:3
