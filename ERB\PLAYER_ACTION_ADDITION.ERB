﻿;────────────────────────────────────
;一般追加行動 (Ｃ愛撫1/Ｖ愛撫2/Ａ愛撫3/Ｂ愛撫4)
;────────────────────────────────────
@ACT_EXTRA_M0
LOCAL:1 = (10 + ABL:MASTER:3) * RAND:100
LOCAL:2 = (10 + ABL:MASTER:4) * RAND:100
LOCAL:3 = (10 + ABL:MASTER:5) * RAND:100
LOCAL:4 = (10 + ABL:MASTER:6) * RAND:100

FOR LOCAL, 1, 5
	CALLFORM ACT_EXTRA_ABLE{LOCAL}
	LOCAL:LOCAL = RESULT && LOCAL:LOCAL > -99 ? LOCAL:LOCAL # -99
NEXT
RESULT = 0
CALL SKILL_ACTION
;TARGETの気力が低いと追加行動が実行出来なくなる処理
;このタイミングで処理する理由
;1.RAND要素があるため、実行判定には組み込めない
;2.TFLAG:229決定後に行うと、MASTERの部位使用フラグを破壊してしまう
IF (GET_ABL(TARGET, 2) * BASE:TARGET:1 < RAND:200 * MAXBASE:TARGET:1 && !RESULT) || !TALENT:200
	FOR LOCAL, 1, 5
		LOCAL:LOCAL = -99
	NEXT
ENDIF

SELECTCASE MAX(LOCAL:1, LOCAL:2, LOCAL:3, LOCAL:4)
	CASE -99
		TFLAG:229 = 0
	CASE LOCAL:1
		TFLAG:229 = 1
		TFLAG:175 += 1
	CASE LOCAL:2
		TFLAG:229 = 2
		TFLAG:179 += 1
	CASE LOCAL:3
		TFLAG:229 = 3
		TFLAG:176 += 1
	CASE LOCAL:4
		TFLAG:229 = 4
		TFLAG:178 += 1
ENDSELECT
IF TFLAG:229 > 0 && TFLAG:229 <= 4
	CFLAG:2902 ++
	CFLAG:MASTER:2902 ++
	CALL SKILL_SECRET(1)
ENDIF
RETURN TFLAG:229


@ACT_EXTRA_MESSAGE_M0
SELECTCASE TFLAG:229
	CASE 1
		PRINTFORML 并且%CALLNAME:TARGET%开始爱抚起%CALLNAME:MASTER%的\@ PENIS(MASTER) ? 男性器 # 身体 \@……
		SOURCE:0 += 75 + ACT_FROM(TARGET, 1) * (2 + TALENT:57) / 8
		TFLAG:130 += (100 + GET_ABL(TARGET, 2) + GET_ABL(TARGET, 21) + TALENT:57 * 50) / 2
	CASE 2
		PRINTFORML 并且%CALLNAME:TARGET%将手指插入了%CALLNAME:MASTER%的秘唇，在里面搅动着…
		SOURCE:1 +=  100 + ACT_FROM(TARGET, 2) * (2 + TALENT:57) / 4
		TFLAG:130 += (GET_ABL(TARGET, 2) + GET_ABL(TARGET, 21) + TALENT:57 * 50) / 4
	CASE 3
		PRINTFORML 并且%CALLNAME:TARGET%开始爱抚起%CALLNAME:MASTER%的后穴…
		SOURCE:2 += 60 + ACT_FROM(TARGET, 3) * (2 + TALENT:57) / 4
		TFLAG:130 += (100 + GET_ABL(MASTER, 5) + GET_ABL(MASTER, 11) * GET_ABL(TARGET, 2) / 50) / 2
	CASE 4
		PRINTFORML 并且%CALLNAME:TARGET%开始爱抚起%CALLNAME:MASTER%的乳头…
		SOURCE:3 += 75 + ACT_FROM(TARGET, 4) * (2 + TALENT:57) / 6
		TFLAG:130 += 40 + (GET_ABL(TARGET, 2) + GET_ABL(TARGET, 21) + TALENT:57 * 50) / 6
ENDSELECT


;-----------------------------------------------------------
;一般追加行動 の実行判定
;-----------------------------------------------------------
@ACTM_EXTRA_ABLE0
FOR LOCAL, 1, 5
	CALLFORM ACT_EXTRA_ABLE{LOCAL}
	SIF RESULT
		RETURN 1
NEXT
RETURN 0

;-----------------------------------------------------------
;Ｃ愛撫 の実行判定
;-----------------------------------------------------------
@ACT_EXTRA_ABLE1
;調教メニューが愛撫/道具以外
SIF TFLAG:80 != 1 && TFLAG:80 != 2
	RETURN 0
;調教行動が押し倒し
SIF TFLAG:90 == 27
	RETURN 0
;Ｐが塞がっている(Ｐ挿入中/Ｐ使用中/性交奉仕中/オナホール/MASTERが助手に挿入中)
SIF TFLAG:174 || TFLAG:175 || TEQUIP:71 || TEQUIP:31 || TFLAG:221
	RETURN 0
RETURN 1

;-----------------------------------------------------------
;Ｖ愛撫 の実行判定
;-----------------------------------------------------------
@ACT_EXTRA_ABLE2
;調教メニューが愛撫/道具以外
SIF TFLAG:80 != 1 && TFLAG:80 != 2
	RETURN 0
;調教行動が押し倒し
SIF TFLAG:90 == 27
	RETURN 0
;Ｖが塞がっている(Ｖ使用中/バイブ/Ｖ性交)
SIF TFLAG:179 || TEQUIP:20 || (TEQUIP:70 && TEQUIP:70 < 6)
	RETURN 0
;下半身上着がズボン(股が開いてない)かつ、性器露出していない
SIF TEQUIP:MASTER:4 > 1 && !(TEQUIP:MASTER:8 & 4)
	RETURN 0
;MASTERが処女
SIF TALENT:MASTER:0
	RETURN 0
;MASTERがオトコ
SIF TALENT:MASTER:122
	RETURN 0
RETURN 1

;-----------------------------------------------------------
;Ａ愛撫 の実行判定
;-----------------------------------------------------------
@ACT_EXTRA_ABLE3
;調教メニューが愛撫/道具以外
SIF TFLAG:80 != 1 && TFLAG:80 != 2
	RETURN 0
;調教行動が押し倒し
SIF TFLAG:90 == 27
	RETURN 0
;Ａが塞がっている(Ａ使用中/アナルバイブ/アナルビーズ/浣腸器＋プラグ/Ａ性交)
SIF TFLAG:176 || TEQUIP:25 || TEQUIP:26 || TEQUIP:27 || TEQUIP:70 == 6
	RETURN 0
;性交奉仕中で、体勢的に無理な体位(騎乗位/対面座位/背面座位)
SIF 3 <= TEQUIP:71 && TEQUIP:71 <= 5
	RETURN 0
RETURN 1

;-----------------------------------------------------------
;Ｂ愛撫 の実行判定
;-----------------------------------------------------------
@ACT_EXTRA_ABLE4
;調教メニューが愛撫/道具以外
SIF TFLAG:80 != 1 && TFLAG:80 != 2
	RETURN 0
;調教行動が押し倒し
SIF TFLAG:90 == 27
	RETURN 0
;Ｂが塞がっている(Ｂ使用中/ニプルキャップ/搾乳器)
SIF TFLAG:178 || TEQUIP:35 || TEQUIP:36
	RETURN 0
;オトコで、上半身に何か着ている(揉めない弄れない)
SIF TALENT:MASTER:122 && (TEQUIP:MASTER:3 || TEQUIP:MASTER:5 || TEQUIP:MASTER:6)
	RETURN 0
RETURN 1
