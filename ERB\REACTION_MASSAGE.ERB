﻿;ACTAPPLY2を抜けてきた場合に表示する
@REACTION_MESSAGE

SELECTCASE SELECTCOM
	;000,沈黙する
	CASE 0
		PRINTFORML %CALLNAME:MASTER%什么也没说…
	;001,気弱く応答する
	CASE 1
		IF TFLAG:94 == 3
			PRINTFORML %CALLNAME:MASTER%软弱地说，不要再做这种事了…
		ELSEIF TFLAG:94 == 1
			PRINTFORML %CALLNAME:MASTER%软弱地回答了…
		ENDIF
	;002,無愛想に応答する
	CASE 2
		PRINTFORML %CALLNAME:MASTER%冷淡地\@ TFLAG:90 == 27 ? 应答 # 回应 \@了…
	;003,愛想よく応答する
	CASE 3
		PRINTFORML %CALLNAME:MASTER%亲切地\@ TFLAG:90 == 27 ? 应答 # 回应 \@了…
	;004,強気に応答する
	CASE 4
		IF TFLAG:90 == 27
			;「無愛想に」「愛想よく」に派生がなく、「強気に」だけ派生フラグがセットしてある理由は、
			;「強気で返事した」と「もうやめろと強く言った」のニュアンスが異なるためです。
			;基準としては、それぞれの地の文に対して同じ口上が表示されるのがよいのか、別の口上を用意したほうがよいのかです。
			TFLAG:300 = 1
			PRINTFORML %CALLNAME:MASTER%强烈地要求停止…
		ELSE
			PRINTFORML %CALLNAME:MASTER%强硬地回答了…
		ENDIF
	;005,皮肉を言う
	CASE 5
		SIF TFLAG:90 != 0 && TFLAG:90 != 1
			PRINTFORML %CALLNAME:MASTER%发出了讽刺，但是没有做反抗的事情。
	;006,もっとハードにして
	CASE 6
		PRINTFORML %CALLNAME:MASTER%提出了再用力一点…
	;007,許しを乞う
	CASE 7
		PRINTFORML %CALLNAME:MASTER%说已经够了、饶了我吧…
	;008,気持ち良くして
	CASE 8
		PRINTFORML %CALLNAME:MASTER%说自己想要得到舒服…
	;010,恥ずかしがる
	CASE 10
		IF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 10
			PRINTFORML %CALLNAME:MASTER%因为快感而感到羞耻
		ELSE
			PRINTFORML %CALLNAME:MASTER%对现在的状况感到羞耻…
		ENDIF
	;011,嫌がる
	CASE 11
		IF TFLAG:94 != 4
			PRINTFORML %CALLNAME:MASTER%讨厌这样…
		ELSE
			PRINTFORML %CALLNAME:MASTER%因为讨厌妨碍了%CALLNAME:TARGET%的行动…
		ENDIF
	;012,下手だと罵る
	CASE 12
		IF TFLAG:125 == 4
			PRINTFORML 对于%CALLNAME:TARGET%不成熟的技巧、%CALLNAME:MASTER%说感觉很糟糕…
		ELSEIF TFLAG:125 == 3
			PRINTFORML 虽然着实感受到了快感但还是说%CALLNAME:MASTER%的技术很糟糕…
		ENDIF
	;013,不敵に笑う
	CASE 13
		PRINTFORML %CALLNAME:MASTER%故意挑衅%CALLNAME:TARGET%似的笑了…
	;014,快感を我慢する
	CASE 14
		IF TFLAG:94 == 3
			PRINTFORML %CALLNAME:MASTER%一边忍耐着强烈的快感，一边恳求着不要再继续了…
		ELSE
			PRINTFORML %CALLNAME:MASTER%拼命忍住了快感…
		ENDIF
	;015,快感を受け入れる
	CASE 15
		PRINTFORML %CALLNAME:MASTER%接受了快感、发出甜美的娇声…
	;020,腰を振る
	CASE 20
		PRINTFORML %CALLNAME:MASTER%回应了%CALLNAME:TARGET%的要求积极地挺动着腰…
	;021,中に出して！
	CASE 21
		PRINTFORML %CALLNAME:MASTER%一边沉溺于被贯穿的快感一边叫喊中出吧！…
	;022,中に出さないで！
	CASE 22
		PRINTFORML %CALLNAME:MASTER%边忍受被贯穿的快感一边叫喊不要中出！…
	;030,痛みを我慢する
	CASE 30
		PRINTFORML %CALLNAME:MASTER%忍耐着疼痛…
	;031,悲鳴を上げる
	CASE 31
		IF SOURCE:13 > 500 || SOURCE:13 > PALAM:7 * 2
			PRINTFORML %CALLNAME:MASTER%疼得受不了，发出了悲鸣声…
		ELSE
			PRINTFORML %CALLNAME:MASTER%感到恐怖而发出悲鸣声…

		ENDIF
	;032,暴れる
	CASE 32
		IF TFLAG:94 == 4
			PRINTFORML %CALLNAME:MASTER%挣扎着想要妨碍%CALLNAME:TARGET%的行动。%CALLNAME:TARGET%好象相当生气…
		ELSE
			IF TEQUIP:40 && TFLAG:90 != 63
				PRINTFORML 被绑着的%CALLNAME:MASTER%挣扎着想要抵抗、但是绳越陷越深只得到了疼痛…
			ELSEIF TFLAG:232
				PRINTFORML 被绑着的%CALLNAME:MASTER%挣扎着想要抵抗，但是绳越陷越深只得到了疼痛…
			ELSEIF TFLAG:233
				PRINTFORML %CALLNAME:MASTER%试图用全身的力气挣扎，但是被%CALLNAME:TARGET%压制了…
			ELSE
				PRINTFORML %CALLNAME:MASTER%想要抵抗，但是被%CALLNAME:TARGET%压制了…
			ENDIF
		ENDIF
	;033,怯える
	CASE 33
		PRINTFORML %CALLNAME:MASTER%感到恐惧、害怕…
	;040,拒否する
	CASE 40
		PRINTFORML %CALLNAME:MASTER%拒绝了%CALLNAME:TARGET%的要求…
	;041,消極的やる
	CASE 41
	;042,積極的やる
	CASE 42
	;043,愛情を込めてやる
	CASE 43
		PRINTFORML %CALLNAME:MASTER%带着一脸陶醉认真地执行着命令…
	;050,自慰し始める
	CASE 50
		PRINTFORML %CALLNAME:MASTER%没有听从%CALLNAME:TARGET%的命令，随意地开始自慰…
	;051,道具を外す
	CASE 51
		PRINTFORML %CALLNAME:MASTER%随意取下了安着的道具、%CALLNAME:TARGET%好像很生气的样子…
	;052,ぼーっとする
	CASE 52
		PRINTFORML %CALLNAME:MASTER%没有任何反应只是发呆…
	;061,なすがまま
	CASE 61
	
	CASE 70
		IF TFLAG:126 == 1
			PRINTFORML %CALLNAME:TARGET%微笑着，继续温柔地爱抚%CALLNAME:MASTER%。
		ELSEIF TFLAG:126 > 1
			PRINTFORML %CALLNAME:TARGET%浮起了坏心眼的笑，%CALLNAME:MASTER%被挑逗了
		ENDIF
	;クン二、フェラ
	CASE 71
		IF PENIS(TARGET)
			PRINTFORML %CALLNAME:MASTER%用嘴巴吮吸着%CALLNAME:TARGET%的阴茎…
		ELSE
			PRINTFORML %CALLNAME:MASTER%吮吸着%CALLNAME:TARGET%充血的阴蒂…
		ENDIF
ENDSELECT

