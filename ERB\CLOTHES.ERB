﻿;衣装関連の处理
;────────────────────────────────────
;衣装のソース補正
;────────────────────────────────────
@CLOTHES_CHECK
CALL ABL_REVISION
;下半身下着
IF TEQUIP:MASTER:2 == 2 || TEQUIP:MASTER:2 == 4
	TIMES SOURCE:0 , 0.95
	TIMES SOURCE:1 , 0.90
	TIMES SOURCE:2 , 0.90
	TIMES SOURCE:20 , 0.95
ELSEIF TEQUIP:MASTER:2 == 1 || TEQUIP:MASTER:2 == 3
	TIMES SOURCE:0 , 0.90
	TIMES SOURCE:1 , 0.80
	TIMES SOURCE:2 , 0.80
	TIMES SOURCE:20 , 0.90
ENDIF

;上半身下着
IF TEQUIP:MASTER:3 == 1 || TEQUIP:MASTER:3 == 3
	TIMES SOURCE:3 , 0.95
	TIMES SOURCE:20 , 0.90
ELSEIF TEQUIP:MASTER:3 == 2
	TIMES SOURCE:3 , 0.85
	TIMES SOURCE:20 , 0.85
ENDIF

;下半身上着
IF TEQUIP:MASTER:4 > 1
	IF TALENT:140 == 0
		TIMES SOURCE:0 , 0.60
		TIMES SOURCE:1 , 0.70
		TIMES SOURCE:2 , 0.70
		TIMES SOURCE:20 , 0.60
	ELSE
		TIMES SOURCE:0 , 0.80
		TIMES SOURCE:1 , 0.90
		TIMES SOURCE:2 , 0.90
		TIMES SOURCE:20 , 0.70
	ENDIF
ELSEIF TEQUIP:MASTER:4 == 1
	IF TALENT:140 == 0
		TIMES SOURCE:0 , 0.70
		TIMES SOURCE:1 , 0.80
		TIMES SOURCE:2 , 0.80
		TIMES SOURCE:20 , 0.70
	ELSE
		TIMES SOURCE:20 , 0.90
	ENDIF
ENDIF

;上半身上着
IF TEQUIP:MASTER:5 && TALENT:140 == 0
	TIMES SOURCE:3 , 0.60
	TIMES SOURCE:20 , 0.60
ENDIF

;全身上着
IF TALENT:140 == 0
	IF TEQUIP:MASTER:6 && TEQUIP:MASTER:6 != 3
		TIMES SOURCE:0 , 0.60
		TIMES SOURCE:1 , 0.70
		TIMES SOURCE:2 , 0.70
		TIMES SOURCE:2 , 0.60
		TIMES SOURCE:20 , 0.40
	ELSEIF TEQUIP:MASTER:6 == 3
		TIMES SOURCE:0 , 0.70
		TIMES SOURCE:1 , 0.80
		TIMES SOURCE:2 , 0.80
		TIMES SOURCE:2 , 0.70
		TIMES SOURCE:20 , 0.60
	ENDIF
ENDIF

;カスタム服装/コスプレ
;ボンデージ
IF TEQUIP:MASTER:9 == 1
	TIMES SOURCE:10 , 1.05
	TIMES SOURCE:11 , 0.80
	TIMES SOURCE:20 , 1.40
	SOURCE:22 += 100
	TIMES SOURCE:22 , 1.10
	SOURCE:23 += B:11 * B:16 * (B:16 + 10 + CFLAG:MASTER:0 * 5 ) / 1000
	SIF ABL:MASTER:11 + ABL:MASTER:16 > 4
		TIMES SOURCE:23 , 1.25
	TIMES SOURCE:24 , 1.20
	SOURCE:30 += 50
	TIMES SOURCE:30 , 1.25
	TIMES SOURCE:33 , 0.75
ENDIF
;────────────────────────────────────
;調教者の衣装初期化。勝手に拝借したものなので作者さんに最高の敬意を
;────────────────────────────────────
@CLOTHES_SETUP(ARG)
CFLAG:ARG:80 = 0
CFLAG:ARG:81 = 0
CFLAG:ARG:82 = 0
CFLAG:ARG:83 = 0
CFLAG:ARG:84 = 0
CFLAG:ARG:85 = 0
CFLAG:ARG:86 = 0
CFLAG:ARG:87 = 0
C = NO:ARG
;加州と安定にはストールをつける
SIF C == 26 || C == 27
	CFLAG:ARG:80 = 3
;江雪、宗三、小夜には袈裟をつける
SIF C == 23 || C == 24 || C == 25
	CFLAG:ARG:80 = 2
;三日月、小狐丸、岩融、にっかり、鳴狐、苺、骨喰、薬研、蜻蛉切、燭台切、大倶利伽羅、へし、
;獅子王、鶴丸、太郎には手袋をつける
SIF C == 1 || C == 2 || C == 4 || C == 6 || C == 7 || C == 8 || C == 10 || C == 17 || C == 21 || C == 22 || C == 35 || C == 36 || C == 37 || C == 39 || C == 40
	CFLAG:ARG:80 = 1
;乱君専用ニーソ
SIF C == 15
	CFLAG:ARG:81 = 3
;三日月、小狐丸、石切丸、岩融、蜻蛉切、江雪、安定、歌仙、兼さん、陸奥、たぬき、鶴丸、太郎、次郎は足袋着用
SIF C == 0 || C == 1 || C == 2 || C == 3 || C == 4 || C == 21 || C == 23 || C == 27 || C == 28 || C == 29 || C == 30 || C == 38 || C == 39 || C == 40 || C == 41
	CFLAG:ARG:81 = 2
;にっかり、鳴狐、苺、鯰尾、骨喰、平野、厚、前田、秋田、五虎退、薬研、鶯丸、蛍丸、愛染、
;燭台切、加州、山姥切、堀川、蜂須賀、大倶利伽羅、へし、御手杵は靴下着用
SIF C == 6 || C == 7 || C == 8 || C == 9 || C == 10 || C == 11 || C == 12 || C == 13 || C == 14 || C == 16 || C == 17 || C == 18 || C == 19 || C == 20 || C == 22 || C == 26 || C == 31 || C == 33 || C == 34 || C == 35 || C == 36 || C == 42 || C == 116
	CFLAG:ARG:81 = 1
;三日月、小狐丸、石切丸、岩融、今剣、蜻蛉切、江雪、宗三、小夜、安定、歌仙、陸奥、
;山伏、たぬき、鶴丸、太郎、次郎は下穿き着用。
SIF C == 1 || C == 2 || C == 3 || C == 4 || C == 5 || C == 21 || C == 23 || C == 24 || C == 25 || C == 27 || C == 28 || C == 30 || C == 32 || C == 38 || C == 39 || C == 40 || C == 41
	CFLAG:ARG:82 = 3
;トランクス着用。
SIF C == 6 || C == 7 || C == 8 || C == 9 || C == 10 || C == 11 || C == 12 || C == 13 || C == 14 || C == 16 || C == 17 || C == 18 || C == 19 || C == 20 || C == 22 || C == 26 || C == 29 || C == 31 || C == 33 || C == 34 || C == 35 || C == 36 || C == 37 || C == 42 || C == 116
	CFLAG:ARG:82 = 2
;パンティ着用。流石は乱くん…
SIF C == 0 || C == 15
	CFLAG:ARG:82 = 1
;シャツ着用。にっかり、鳴狐、苺、鯰尾、骨喰、平野、厚、前田、秋田、乱、五虎退、薬研、
;鶯丸、蛍丸、愛染
SIF C == 6 || C == 7 || C == 8 || C == 9 || C == 10 || C == 11 || C == 12 || C == 13 || C == 14 || C == 15 || C == 16 || C == 17 || C == 18 || C == 19 || C == 20 || C == 22 || C == 26 || C == 31 || C == 33 || C == 34 || C == 35 || C == 36 || C == 37 || C == 42 || C == 116
	CFLAG:ARG:83 = 3
;さらし着用。蜻蛉、小夜、安定、陸奥、たぬき
SIF C == 21 || C == 25 || C == 27 || C == 30 || C == 38
	CFLAG:ARG:83 = 2
;ブラ着用。
SIF C == 0
	CFLAG:ARG:83 = 1
;全身上着は先に処理。着物
SIF C == 23 || C == 24 || C == 41
	CFLAG:ARG:86 = 1
;全身上着は先に処理。ワンピ
SIF C == 15
	CFLAG:ARG:86 = 2
;ズボン着用。
SIF C == 6 || C == 7 || C == 8 || C == 9 || C == 10 || C == 11 || C == 12 || C == 13 || C == 14 || C == 16 || C == 17 || C == 18 || C == 19 || C == 20 || C == 22 || C == 25 || C == 26 || C == 31 || C == 32 || C == 33 || C == 34 || C == 35 || C == 36 || C == 37 || C == 38 || C == 42
	CFLAG:ARG:84 = 3
;袴着用。
SIF C == 1 || C == 2 || C == 3 || C == 4 || C == 5 || C == 21 || C == 27 || C == 28 || C == 29 || C == 30 || C == 39 || C == 40 || C == 116
	CFLAG:ARG:84 = 2
;女袴着用。
SIF C == 0
	CFLAG:ARG:84 = 1
;コート着用。
SIF C == 26 || C == 34 || C == 36 || C == 37 || C == 116
	CFLAG:ARG:85 = 3
;和服着用。
SIF C == 0 || C == 1 || C == 2 || C == 3 || C == 4 || C == 5 || C == 21 || C == 25 || C == 27 || C == 28 || C == 29 || C == 30 || C == 38 || C == 39 || C == 40
	CFLAG:ARG:85 = 1
;ジャケット着用。
SIF C == 6 || C == 7 || C == 8 || C == 9 || C == 10 || C == 11 || C == 12 || C == 13 || C == 14 || C == 16 || C == 17 || C == 18 || C == 19 || C == 20 || C == 22 || C == 31 || C == 32 || C == 33 || C == 35 || C == 42
	CFLAG:ARG:85 = 2

[SKIPSTART]
;あなたの服装
IF C == 0
	IF TALENT:ARG:122
		CFLAG:ARG:80 = 0
		CFLAG:ARG:81 = 2
		CFLAG:ARG:82 = 2
		CFLAG:ARG:83 = 0
		CFLAG:ARG:84 = 2
		CFLAG:ARG:85 = 1
	ELSE
		CFLAG:ARG:80 = 0
		CFLAG:ARG:81 = 2
		CFLAG:ARG:82 = 1
		CFLAG:ARG:83 = 1
		CFLAG:ARG:84 = 1
		CFLAG:ARG:85 = 1
	ENDIF
ENDIF
[SKIPEND]

FOR LOCAL, 0, 8
	EQUIP:ARG:LOCAL = CFLAG:ARG:(80 + LOCAL)
NEXT

;────────────────────────────────────
;ショップメニューでの服装セッティング
;────────────────────────────────────
@CLOTHES_SETTING
PRINTFORM %CALLNAME:MASTER%的服装：
SIF CFLAG:MASTER:12 == 0
	PRINTFORM \@ CFLAG:MASTER:86 ? [%DRESS(MASTER)%] # \@\@ CFLAG:MASTER:85 ? [%OUTER_T(MASTER)%] # \@\@ CFLAG:MASTER:84 ? [%OUTER_B(MASTER)%] # \@
SELECTCASE CFLAG:MASTER:12
	CASE IS < 2
		PRINTFORM \@ CFLAG:MASTER:83 ? [%INNER_T(MASTER)%] # \@\@ CFLAG:MASTER:82 ? [%INNER_B(MASTER)%] # \@\@ CFLAG:MASTER:81 ? [%SOCKS(MASTER)%] # \@\@ CFLAG:MASTER:80 ? [%GLOVES(MASTER)%] # \@
	CASE 2
		PRINT [全裸]
	CASEELSE
		PRINTFORM \@ CFLAG:MASTER:79 ? [%COSPLAY(MASTER)%] # \@
ENDSELECT
PRINTL 

IF TARGET >= 0
	PRINTFORM %CALLNAME:TARGET%的服装：
	SIF CFLAG:TARGET:12 == 0
		PRINTFORM \@ CFLAG:TARGET:86 ? [%DRESS(TARGET)%] # \@\@ CFLAG:TARGET:85 ? [%OUTER_T(TARGET)%] # \@\@ CFLAG:TARGET:84 ? [%OUTER_B(TARGET)%] # \@
	SELECTCASE CFLAG:TARGET:12
		CASE IS < 2
			PRINTFORM \@ CFLAG:TARGET:83 ? [%INNER_T(TARGET)%] # \@\@ CFLAG:TARGET:82 ? [%INNER_B(TARGET)%] # \@\@ CFLAG:TARGET:81 ? [%SOCKS(TARGET)%] # \@\@ CFLAG:TARGET:80 ? [%GLOVES(TARGET)%] # \@
		CASE 2
			PRINT [全裸]
		CASEELSE
			PRINTFORM \@ CFLAG:TARGET:79 ? [%COSPLAY(TARGET)%] # \@
	ENDSELECT
	PRINTL 
ENDIF

DRAWLINE
PRINTL  [ 0] 返回
PRINTFORML  [ 1] 将%CALLNAME:MASTER%的服装设定为[通常]
PRINTFORML  [ 2] 将%CALLNAME:MASTER%的服装设定为[半裸]
PRINTFORML  [ 3] 将%CALLNAME:MASTER%的服装设定为[全裸]
SIF ITEM:70
	PRINTFORML  [ 4] 更改%CALLNAME:TARGET%的服装
IF TARGET >= 0
	PRINTFORML  [51] 将%CALLNAME:TARGET%的服装设定为[通常]
	PRINTFORML  [52] 将%CALLNAME:TARGET%的服装设定为[半裸]
	PRINTFORML  [53] 将%CALLNAME:TARGET%的服装设定为[全裸]
	SIF ITEM:70
		PRINTFORML  [54] 更改%CALLNAME:TARGET%的服装
ENDIF

$INPUT_LOOPC
INPUT
IF RESULT == 0
	RETURN 0
ELSEIF RESULT == 1
	IF CFLAG:MASTER:12 == 0
		PRINTFORMW %CALLNAME:MASTER%的服装已经设定为[通常]了
		PRINTL 
		CALL KOJO_EVENT(101, 1000)
		GOTO INPUT_LOOPC
	ELSE
		CFLAG:MASTER:12 = 0
		PRINTFORMW %CALLNAME:MASTER%的服装设定为[通常]了
		PRINTL 
		CALL KOJO_EVENT(101, 1001)
	ENDIF
	RESTART
ELSEIF RESULT == 2
	IF CFLAG:MASTER:12 == 1
		PRINTFORMW %CALLNAME:MASTER%的服装已经设定为[半裸]了
		PRINTL 
		CALL KOJO_EVENT(101, 1010)
	ELSEIF TALENT:MASTER:32 * 3 - TALENT:MASTER:33 * 2 + TALENT:MASTER:34 * 2 - TALENT:MASTER:35 + 2 > CFLAG:MASTER:0 / 2 + ABL:MASTER:1
		PRINTFORMW %CALLNAME:MASTER%因为害羞，所以不能接受[半裸]
		PRINTL 
		CALL KOJO_EVENT(101, 1012)
	ELSEIF TALENT:MASTER:11 * 3 - TALENT:MASTER:13 * 2 + MARK:3 - MARK:2 + 2 > CFLAG:MASTER:0 / 2 + ABL:MASTER:0 + ABL:MASTER:1
		PRINTFORMW %CALLNAME:MASTER%因为讨厌，所以不愿意[半裸]
		PRINTL 
		CALL KOJO_EVENT(101, 1013)
	ELSE
		CFLAG:MASTER:12 = 1
		PRINTFORMW %CALLNAME:MASTER%的服装设定为[半裸]
		PRINTL 
		CALL KOJO_EVENT(101, 1011)
	ENDIF
	RESTART
ELSEIF RESULT == 3
	IF CFLAG:MASTER:12 == 2
		PRINTFORMW %CALLNAME:MASTER%的服装已经设定为[全裸]了
		PRINTL 
		CALL KOJO_EVENT(101, 1020)
	ELSEIF TALENT:MASTER:32 * 3 - TALENT:MASTER:33 * 2 + TALENT:MASTER:34 * 2 - TALENT:MASTER:35 + 5 > CFLAG:MASTER:0 / 2 + ABL:MASTER:1
		PRINTFORMW %CALLNAME:MASTER%因为害羞，所以不能接受[全裸]
		PRINTL 
		CALL KOJO_EVENT(101, 1022)
	ELSEIF TALENT:MASTER:11 * 3 - TALENT:MASTER:13 * 2 + MARK:3 - MARK:2 + 4 > CFLAG:MASTER:0 / 2 + ABL:MASTER:0 + ABL:MASTER:1
		PRINTFORMW %CALLNAME:MASTER%因为讨厌，所以不愿意[全裸]
		PRINTL 
		CALL KOJO_EVENT(101, 1023)
	ELSE
		CFLAG:MASTER:12 = 2
		PRINTFORMW %CALLNAME:MASTER%的服装设定为[全裸]
		PRINTL 
		CALL KOJO_EVENT(101, 1021)
	ENDIF
	RESTART
ELSEIF RESULT == 4
	IF ITEM:70 == 0
		PRINTW 没有持有那个衣服
		PRINTL 
		CALL KOJO_EVENT(101, 1039)
	ELSEIF TALENT:MASTER:32 * 3 - TALENT:MASTER:33 * 2 + TALENT:MASTER:34 * 2 - TALENT:MASTER:35 + 7 > CFLAG:MASTER:0 / 2 + ABL:MASTER:11 + ABL:MASTER:15 / 2 + ABL:MASTER:16 /2
		PRINTFORMW %CALLNAME:MASTER%太害羞了，没法换衣服
		PRINTL 
		CALL KOJO_EVENT(101, 1032)
	ELSEIF TALENT:MASTER:11 * 3 - TALENT:MASTER:13 * 2 + MARK:3 - MARK:2 + 6 > CFLAG:MASTER:0 / 2 + ABL:MASTER:0 + ABL:MASTER:11
		PRINTFORMW %CALLNAME:MASTER%因为讨厌，所以不愿意换衣服
		PRINTL 
		CALL KOJO_EVENT(101, 1033)
	ELSE
		CALL COSPLAY_SET(MASTER)
		;CFLAG:MASTER:12 = 3
		;CFLAG:MASTER:79 = 1
		;PRINTFORMW %CALLNAME:MASTER%の衣装を[ボンデージ]に設定しました
		;PRINTL 
		CALL KOJO_EVENT(101, 1031)
	ENDIF
	RESTART
ELSEIF RESULT == 51
	IF CFLAG:TARGET:12 == 0
		PRINTFORMW %CALLNAME:TARGET%的服装已经设定为[通常]了
		PRINTL 
		CALL KOJO_EVENT(101, 0)
	ELSE
		CFLAG:TARGET:12 = 0
		PRINTFORMW %CALLNAME:TARGET%的服装设定为[通常]
		PRINTL 
		CALL KOJO_EVENT(101, 1)
	ENDIF
	RESTART
ELSEIF RESULT == 52
	IF CFLAG:TARGET:12 == 1
		PRINTFORMW %CALLNAME:TARGET%的服装已经设定为[半裸]了
		PRINTL 
		CALL KOJO_EVENT(101, 10)
	ELSEIF TALENT:32 * 3 - TALENT:33 * 2 + TALENT:34 * 2 - TALENT:35 + 2 > CFLAG:0 / 2 + ABL:1
		PRINTFORMW %CALLNAME:TARGET%因为害羞，所以不能接受[半裸]
		PRINTL 
		CALL KOJO_EVENT(101, 12)
	ELSE
		CFLAG:TARGET:12 = 1
		PRINTFORMW %CALLNAME:TARGET%的服装设定为[半裸]
		PRINTL 
		CALL KOJO_EVENT(101, 11)
	ENDIF
	RESTART
ELSEIF RESULT == 53
	IF CFLAG:TARGET:12 == 2
		PRINTFORMW %CALLNAME:TARGET%的服装已经设定为[全裸]了
		PRINTL 
		CALL KOJO_EVENT(101, 20)
	ELSEIF TALENT:32 * 3 - TALENT:33 * 2 + TALENT:34 * 2 - TALENT:35 + 5 > CFLAG:0 / 2 + ABL:1
		PRINTFORMW %CALLNAME:TARGET%因为害羞，所以不能接受[全裸]
		PRINTL 
		CALL KOJO_EVENT(101, 22)
	ELSE
		CFLAG:TARGET:12 = 2
		PRINTFORMW %CALLNAME:TARGET%的服装设定为[全裸]
		PRINTL 
		CALL KOJO_EVENT(101, 21)
	ENDIF
	RESTART
ELSEIF RESULT == 54
	IF ITEM:70 == 0
		PRINTW 没有持有那个衣服
		PRINTL 
		CALL KOJO_EVENT(101, 39)
	ELSEIF TALENT:32 * 3 - TALENT:33 * 2 + TALENT:34 * 2 - TALENT:35 + 7 > CFLAG:0 / 2 + ABL:1 / 2 + ABL:26 + ABL:27 /2
		PRINTFORMW %CALLNAME:TARGET%太害羞了没法换衣服
		PRINTL 
		CALL KOJO_EVENT(101, 32)
	ELSE
		CALL COSPLAY_SET(TARGET)
		;CFLAG:TARGET:12 = 3
		;CFLAG:TARGET:79 = 1
		;PRINTFORMW %CALLNAME:TARGET%の衣装を[ボンデージ]に設定しました
		;PRINTL 
	ENDIF
	RESTART
ENDIF
RESTART
@COSPLAY_SET(ARG)
PRINTL 持有的服装
PRINTL [0] - 返回
FOR LOCAL,71,80
	SIF STRLENS(ITEMNAME:LOCAL)
		PRINTFORML [{LOCAL - 70}] - %ITEMNAME:LOCAL%
NEXT
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
		RETURN 0
	CASE 1 TO 10
		IF STRLENS(ITEMNAME:(RESULT + 70))
			CFLAG:ARG:12 = 3
			CFLAG:ARG:79 = RESULT
			PRINTFORMW %CALLNAME:ARG%的服装设定为%ITEMNAME:(RESULT + 70)%了
			CALL KOJO_EVENT(101, 31)
		ELSE
			GOTO INPUT_LOOP
		ENDIF
	CASEELSE
		GOTO INPUT_LOOP
ENDSELECT
