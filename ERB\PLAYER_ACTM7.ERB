﻿;────────────────────────────────────
;異常 (Fist70/ＡFi71/両Fi72/放置73/公衆74/嗤う9/足で18/足舐55/木馬67)
;────────────────────────────────────
@ACT_M7
;基準値
LOCAL:70 = 10
LOCAL:71 = 10
LOCAL:72 = 10
LOCAL:73 = 10
LOCAL:74 = 10
LOCAL:9 = 8
LOCAL:18 = 5
LOCAL:55 = 5
LOCAL:67 = 5

;────────────────────────────────────
;素質による変動
;────────────────────────────────────
;調教者が臆病/気丈
IF TALENT:10
	LOCAL:74 -= 5
ELSEIF TALENT:12
	LOCAL:74 += RAND:6
ENDIF

;調教者が大人しい/生意気
IF TALENT:14
	LOCAL:70 -= 5
	LOCAL:71 -= 6
	LOCAL:72 -= 7
ELSEIF TALENT:16
	LOCAL:73 += RAND:5
	LOCAL:9 += RAND:7
	LOCAL:18 += 2 + RAND:3
ENDIF

;調教者がプライド高い/低い
IF TALENT:15
	LOCAL:55 += RAND:6
ELSEIF TALENT:17
	LOCAL:55 -= RAND:6
ENDIF

;調教者が自制的/衝動的
IF TALENT:20
	LOCAL:73 += RAND:5
	LOCAL:74 -= 5
ELSEIF TALENT:21
	LOCAL:74 += RAND:6
	LOCAL:67 += RAND:5
ENDIF

;調教者が無関心/好奇心
IF TALENT:22
	LOCAL:74 += 5
ELSEIF TALENT:23
	LOCAL:18 += RAND:7
ENDIF

;調教者が感情乏しい/感情豊富な
IF TALENT:24
	LOCAL:74 += 5
ELSEIF TALENT:25
	LOCAL:9 += RAND:7
ENDIF

;調教者が目立ちたがり
IF TALENT:29
	LOCAL:18 += 5
	LOCAL:55 += 3
ENDIF

;調教者が抑圧/解放
IF TALENT:32
	LOCAL:74 -= 5
	LOCAL:9 -= 5
	LOCAL:18 -= 5
	LOCAL:55 -= 5
ELSEIF TALENT:33
	LOCAL:74 += RAND:7
	LOCAL:18 += RAND:5
ENDIF

;調教者が恥じらい/恥薄い
IF TALENT:34
	LOCAL:74 -= 5
ELSEIF TALENT:35
	LOCAL:74 += 5
ENDIF

;調教者が献身的/受け身
IF TALENT:63
	LOCAL:18 += 5
ELSEIF TALENT:65
	LOCAL:55 += 5
ENDIF

;調教者が男嫌いで調教対象がオトコ
SIF TALENT:82 && TALENT:MASTER:122
	LOCAL:18 -= 7

;調教者がサド
IF TALENT:83
	LOCAL:55 += RAND:7
	LOCAL:67 += 3 + RAND:5
ENDIF

;調教者が慎重/短気
IF TALENT:84
	LOCAL:74 -= 5
ELSEIF TALENT:85
	LOCAL:74 += 5
ENDIF

;調教者が意地悪/心根優しい
IF TALENT:86
	LOCAL:9 += 3
	LOCAL:18 += 3
ELSEIF TALENT:87
	LOCAL:74 -= 5
	LOCAL:67 -= 5
ENDIF

;調教者が狂気
IF TALENT:89
	LOCAL:70 += RAND:5
	LOCAL:71 += RAND:5
	LOCAL:72 += RAND:5
	LOCAL:73 += RAND:5
	LOCAL:74 += RAND:5
	LOCAL:9 += RAND:8
	LOCAL:18 += RAND:5
	LOCAL:55 += RAND:5
	LOCAL:67 += RAND:5
ENDIF

;調教者がＣ敏感
SIF TALENT:100
	LOCAL:18 += 2 + RAND:5

;小柄体型/巨躯/ＶＡ経験/拡張経験/淫系素質などでフィストの処理をしてみる
IF TALENT:110 || TALENT:MASTER:111
	IF EXP:1 < 10
		LOCAL:70 -= 5
		LOCAL:72 -= 5
	ELSEIF EXP:1 < 20 && EXP:52 < 1
		LOCAL:70 -= 2
		LOCAL:72 -= 2
	ELSEIF EXP:1 < 40 && EXP:52 < 3
		LOCAL:70 -= 1
		LOCAL:72 -= 1
	ENDIF
	IF EXP:2 < 10
		LOCAL:71 -= 5
		LOCAL:72 -= 5
	ELSEIF EXP:2 < 20 && EXP:53 < 1
		LOCAL:71 -= 2
		LOCAL:72 -= 2
	ELSEIF EXP:2 < 40 && EXP:53 < 3
		LOCAL:71 -= 1
		LOCAL:72 -= 1
	ENDIF
	IF TALENT:73 && TALENT:74
		LOCAL:70 += 4
		LOCAL:71 += 4
		LOCAL:72 += 4
	ELSEIF TALENT:73
		LOCAL:70 += 4
		LOCAL:72 += 4
	ELSEIF TALENT:74
		LOCAL:71 += 4
		LOCAL:72 += 4
	ENDIF
ELSEIF TALENT:111 || TALENT:MASTER:110
	IF EXP:1 < 10
		LOCAL:70 -= 8
		LOCAL:72 -= 8
	ELSEIF EXP:1 < 20 && EXP:52 < 1
		LOCAL:70 -= 5
		LOCAL:72 -= 5
	ELSEIF EXP:1 < 40 && EXP:52 < 3
		LOCAL:70 -= 2
		LOCAL:72 -= 2
	ENDIF
	IF EXP:2 < 10
		LOCAL:71 -= 8
		LOCAL:72 -= 8
	ELSEIF EXP:2 < 20 && EXP:53 < 1
		LOCAL:71 -= 5
		LOCAL:72 -= 5
	ELSEIF EXP:2 < 40 && EXP:53 < 3
		LOCAL:71 -= 2
		LOCAL:72 -= 2
	ENDIF
	IF TALENT:73 && TALENT:74
		LOCAL:70 += 5
		LOCAL:71 += 5
		LOCAL:72 += 5
	ELSEIF TALENT:73
		LOCAL:70 += 5
		LOCAL:72 += 5
	ELSEIF TALENT:74
		LOCAL:71 += 5
		LOCAL:72 += 5
	ENDIF
ELSE
	IF EXP:1 < 10
		LOCAL:70 -= 6
		LOCAL:72 -= 6
	ELSEIF EXP:1 < 20 && EXP:52 < 1
		LOCAL:70 -= 4
		LOCAL:72 -= 4
	ELSEIF EXP:1 < 40 && EXP:52 < 3
		LOCAL:70 -= 2
		LOCAL:72 -= 2
	ENDIF
	IF EXP:2 < 10
		LOCAL:71 -= 6
		LOCAL:72 -= 6
	ELSEIF EXP:2 < 20 && EXP:53 < 1
		LOCAL:71 -= 4
		LOCAL:72 -= 4
	ELSEIF EXP:2 < 40 && EXP:53 < 3
		LOCAL:71 -= 2
		LOCAL:72 -= 2
	ENDIF
	IF TALENT:73 && TALENT:74
		LOCAL:70 += 4
		LOCAL:71 += 4
		LOCAL:72 += 4
	ELSEIF TALENT:73
		LOCAL:70 += 4
		LOCAL:72 += 4
	ELSEIF TALENT:74
		LOCAL:71 += 4
		LOCAL:72 += 4
	ENDIF
ENDIF

;調教対象が臆病/気丈
IF TALENT:MASTER:10
	LOCAL:9 += 3
ELSEIF TALENT:MASTER:12
	LOCAL:74 += 3
ENDIF

;調教対象が反抗的/素直
IF TALENT:MASTER:11
	LOCAL:67 += 3
ELSEIF TALENT:MASTER:13
	LOCAL:55 += 3
ENDIF

;調教対象が恥じらい/恥薄い
IF TALENT:MASTER:34
	LOCAL:73 += 3
ELSEIF TALENT:MASTER:35
	LOCAL:73 -= 3
ENDIF

;調教対象が痛みに弱い/痛みに強い
IF TALENT:MASTER:40
	LOCAL:67 += 5
ELSEIF TALENT:MASTER:41
	LOCAL:67 -= 5
ENDIF

;調教対象が淫茎
SIF TALENT:MASTER:72
	LOCAL:18 += RAND:10

;調教対象がＣ敏感/鈍感
IF TALENT:MASTER:100
	LOCAL:18 += 1 + RAND:7
ELSEIF TALENT:MASTER:101
	LOCAL:18 -= 1 + RAND:7
ENDIF

;調教対象が蓬莱人
SIF TALENT:115
	LOCAL:74 += RAND:10

;調教対象がふたなり
SIF TALENT:MASTER:121
	LOCAL:18 += RAND:5 + TALENT:23 * 3 - TALENT:22

;調教対象が処女
IF TALENT:MASTER:0
	LOCAL:70 -= 10
	LOCAL:72 -= 10
	LOCAL:74 -= 10
ENDIF

;調教対象が童貞
SIF TALENT:MASTER:1
	LOCAL:74 -= 5

;────────────────────────────────────
;能力、パラメーターによる変動
;────────────────────────────────────
;調教者の会話
SELECTCASE ABL:20
	CASE 2
		LOCAL:9 += RAND:2
	CASE 3
		LOCAL:9 += RAND:3
	CASE 4
		LOCAL:9 += RAND:4
	CASE IS > 4
		LOCAL:9 += RAND:6
ENDSELECT

;調教者の愛撫
SELECTCASE ABL:21
	CASE 1
		LOCAL:18 += RAND:2
	CASE 2
		LOCAL:18 += RAND:3
	CASE 3
		LOCAL:18 += RAND:4
	CASE 4
		LOCAL:18 += 1 + RAND:3
	CASE IS > 4
		LOCAL:18 += 2 + RAND:3
ENDSELECT

;調教者の奉仕
SELECTCASE ABL:25
	CASE 1
		LOCAL:55 += 1
	CASE 2
		LOCAL:55 += 1 + RAND:2
	CASE 3
		LOCAL:55 += 2
	CASE 4
		LOCAL:55 += 2 + RAND:3
	CASE IS > 4
		LOCAL:55 += 3 + RAND:3
ENDSELECT

;調教対象のＶ感覚
SELECTCASE ABL:MASTER:4
	CASE 1
		LOCAL:70 += 1
	CASE 2
		LOCAL:70 += 1
		LOCAL:72 += 1
	CASE 3
		LOCAL:70 += 2
		LOCAL:72 += 1
	CASE 4
		LOCAL:70 += 2
		LOCAL:72 += 2
	CASE IS > 4
		LOCAL:70 += 3
		LOCAL:72 += 2
ENDSELECT

;調教対象のＡ感覚
SELECTCASE ABL:MASTER:5
	CASE 1
		LOCAL:71 += 1
	CASE 2
		LOCAL:71 += 1
		LOCAL:72 += 1
	CASE 3
		LOCAL:71 += 2
		LOCAL:72 += 1
	CASE 4
		LOCAL:71 += 2
		LOCAL:72 += 2
	CASE IS > 4
		LOCAL:71 += 3
		LOCAL:72 += 2
ENDSELECT

;アライメント
SELECTCASE CFLAG:6
	CASE IS > 49
		LOCAL:74 -= 3 + RAND:5
		LOCAL:18 += 3 + RAND:5
	CASE IS > 39
		LOCAL:74 -= 2 + RAND:4
		LOCAL:18 += 2 + RAND:4
	CASE IS > 29
		LOCAL:74 -= 2 + RAND:3
		LOCAL:18 += 2 + RAND:3
	CASE IS > 19
		LOCAL:74 -= 1 + RAND:2
		LOCAL:18 += 1 + RAND:2
		LOCAL:55 += 1
	CASE IS > 9
		LOCAL:70 += 1
		LOCAL:74 -= RAND:2
		LOCAL:18 += RAND:2
		LOCAL:55 += 2
	CASE IS > -1
		LOCAL:70 += 2
		LOCAL:55 += 3
	CASE IS > -11
		LOCAL:70 += 1
		LOCAL:74 += RAND:2
		LOCAL:18 -= RAND:2
		LOCAL:55 += 2
	CASE IS > -21
		LOCAL:74 += 1 + RAND:2
		LOCAL:18 -= 1 + RAND:2
		LOCAL:55 += 1
	CASE IS > -31
		LOCAL:74 += 2 + RAND:3
		LOCAL:18 -= 2 + RAND:3
	CASE IS > -41
		LOCAL:74 += 2 + RAND:4
		LOCAL:18 -= 2 + RAND:4
	CASE IS > -51
		LOCAL:74 += 3 + RAND:5
		LOCAL:18 -= 3 + RAND:5
	CASEELSE
		LOCAL:74 += 3 + RAND:6
		LOCAL:18 -= 3 + RAND:6
ENDSELECT

;────────────────────────────────────
;ゲージや状態による変動
;────────────────────────────────────
;調教者の気力
SELECTCASE BASE:1
	CASE IS < 250
		LOCAL:73 += 3 + TFLAG:62 * 3
		LOCAL:74 += 1 + RAND:3 + TFLAG:62 * 3
	CASE IS < 500
		LOCAL:73 += 2 + TFLAG:62 * 3
		LOCAL:74 += RAND:2 + TFLAG:62 * 3
	CASE IS < 750
		LOCAL:73 += TFLAG:62 * 2
	CASE IS < 1000
		LOCAL:73 -= 2
		LOCAL:74 -= 1
	CASEELSE
		LOCAL:73 -= 3
		LOCAL:74 -= 2
ENDSELECT

;調教者の理性
SELECTCASE BASE:5
	CASE IS < 250
		LOCAL:9 += 2 + RAND:6
	CASE IS < 500
		LOCAL:9 += 1 + RAND:3
	CASE IS < 750
		LOCAL:9 -= RAND:2
	CASE IS < 1000
		LOCAL:18 += 2
		LOCAL:55 += 1 + RAND:2
	CASEELSE
		LOCAL:18 += 3
		LOCAL:55 += 2 + RAND:4
ENDSELECT

;調教者の興味
SELECTCASE BASE:6
	CASE IS < 250
		LOCAL:70 -= 3
		LOCAL:71 -= 3
		LOCAL:72 -= 3
		LOCAL:73 += 3
		LOCAL:74 += 5
		LOCAL:18 -= 5
		LOCAL:55 -= 5
	CASE IS < 400
		LOCAL:70 -= 1
		LOCAL:71 -= 1
		LOCAL:72 -= 1
		LOCAL:73 += 2
		LOCAL:74 += 3
		LOCAL:18 -= 3
		LOCAL:55 -= 3
	CASE IS < 550
		LOCAL:73 += 1
		LOCAL:74 += 2
		LOCAL:18 -= 2
		LOCAL:55 -= 2
	CASE IS < 700
		LOCAL:73 -= 2
		LOCAL:74 -= 3
	CASEELSE
		LOCAL:73 -= 3
		LOCAL:74 -= 5
ENDSELECT

;調教対象の体力
SELECTCASE BASE:MASTER:0
	CASE IS < 500
		LOCAL:70 -= 1
		LOCAL:71 -= 3
		LOCAL:72 -= 4
		LOCAL:67 += TEQUIP:43 ? 9 # -9
	CASE IS < 1000
		LOCAL:71 -= 1
		LOCAL:72 -= 2
		LOCAL:67 += TEQUIP:43 ? 7 # -7
	CASE IS < 1500
		LOCAL:67 += TEQUIP:43 ? 5 # -5
ENDSELECT

;調教者の状態(0=通常/1=疲弊/2=衰弱/3=無気力/4=朦朧/5=情欲/6=怒り/7=退屈/8=狂乱)
SELECTCASE TFLAG:60
	CASE 1, 2
		;調教者の消耗
		LOCAL:70 -= TFLAG:62
		LOCAL:71 -= TFLAG:62
		LOCAL:72 -= TFLAG:62 + 3
		LOCAL:18 -= TFLAG:62 * 2
		LOCAL:55 -= TFLAG:62 / 2
		LOCAL:67 -= TFLAG:62 + RAND:6
	CASE 7
		LOCAL:73 += 5
		LOCAL:74 += 5
ENDSELECT

;────────────────────────────────────
;前回の行動や状況による変動
;────────────────────────────────────
;媚薬
SIF TEQUIP:11
	LOCAL:73 += RAND:5 + TALENT:86 * 3 - TALENT:87 * 3

;縄
SIF TEQUIP:40 || TEQUIP:46 || TEQUIP:47
	LOCAL:73 += 5

;アイマスク
SIF TEQUIP:41
	LOCAL:73 += 3

;ボールギャグ
SIF TEQUIP:42
	LOCAL:73 += RAND:4

;三角木馬
SIF TEQUIP:43
	LOCAL:73 += RAND:8

;野外プレイ
IF TEQUIP:52
	TIMES LOCAL:73, 1.50
	TIMES LOCAL:74, 1.25
ENDIF

;調教者のみ自慰中
SIF TEQUIP:69 == 2
	LOCAL:73 += RAND:10

;調教対象の下半身下着
SIF TEQUIP:MASTER:2
	LOCAL:67 -= 2

;調教対象の下半身上着がスカート
IF TEQUIP:MASTER:4 == 1
	LOCAL:70 -= 5
	LOCAL:71 -= 5
	LOCAL:72 -= 5
	LOCAL:67 -= 5
ENDIF

;オトコ
SIF TALENT:MASTER:122
	LOCAL:74 -= 3 + RAND:5

;もっとハードにお願いした
IF SELECTCOM == 6
	LOCAL:73 -= 10
	LOCAL:74 += 2
	LOCAL:9 -= 3
	LOCAL:18 -= 3
ENDIF

;許しを乞ったかつ、調教者の状態が狂乱以外
SIF SELECTCOM == 7 && TFLAG:60 < 8
	LOCAL:73 += 5

;────────────────────────────────────
;同じ行動連続実行の確率をダウンします
;────────────────────────────────────
;前のターンの調教指令
SELECTCASE TFLAG:91
	CASE 9, 18, 55, 67, 70 TO 74
		LOCAL:(TFLAG:91) -= 3 + RAND:5
ENDSELECT

;────────────────────────────────────
;その他変動や実行不可能の判定
;────────────────────────────────────
;お仕置きモード
IF TFLAG:69
	LOCAL:74 += TFLAG:69 / 2
	LOCAL:67 += TFLAG:69 / 2
ENDIF

;不可能判定とカウンタ値の下限チェック
CALL ACT_ABLE9
SIF !RESULT || LOCAL:9 < -99
	LOCAL:9 = -99
CALL ACT_ABLE18
SIF !RESULT || LOCAL:18 < -99
	LOCAL:18 = -99
CALL ACT_ABLE55
SIF !RESULT || LOCAL:55 < -99
	LOCAL:55 = -99
CALL ACT_ABLE67
SIF !RESULT || LOCAL:67 < -99
	LOCAL:67 = -99
FOR LOCAL:900, 70, 75
	CALLFORM ACT_ABLE{LOCAL:900}
	SIF !RESULT || LOCAL:(LOCAL:900) < -99
		LOCAL:(LOCAL:900) = -99
NEXT

;────────────────────────────────────
;最終判定
;────────────────────────────────────
SELECTCASE MAX(LOCAL:70, LOCAL:71, LOCAL:72, LOCAL:73, LOCAL:74, LOCAL:9, LOCAL:18, LOCAL:55, LOCAL:67)
;ここには来ないはず
;	CASE -99
;		PRINTL (異常カウンタ異常)
;		TFLAG:90 = 70
	CASE LOCAL:70
		TFLAG:90 = 70
	CASE LOCAL:71
		TFLAG:90 = 71
	CASE LOCAL:55
		TFLAG:90 = 55
	CASE LOCAL:9
		TFLAG:90 = 9
	CASE LOCAL:18
		TFLAG:90 = 18
	CASE LOCAL:72
		TFLAG:90 = 72
	CASE LOCAL:73
		TFLAG:90 = 73
	CASE LOCAL:74
		TFLAG:90 = 74
	CASE LOCAL:67
		TFLAG:90 = 67
ENDSELECT

;デバッグ＆調整用カウンタ
IF FLAG:4
	PRINTFORML 　　异常：Fist[{LOCAL:70,3}]/ＡFi[{LOCAL:71,3}]/両Fi[{LOCAL:72,3}]/放置[{LOCAL:73,3}]/公衆[{LOCAL:74,3}]
	PRINTFORML 　　　　　嗤う[{LOCAL:9,3}]/足で[{LOCAL:18,3}]/足舐[{LOCAL:55,3}]/木馬[{LOCAL:67,3}]
ENDIF


;-----------------------------------------------------------
;異常 の実行判定
;-----------------------------------------------------------
@ACTM_ABLE7
CALL ACT_ABLE9
SIF RESULT
	RETURN 1
CALL ACT_ABLE18
SIF RESULT
	RETURN 1
CALL ACT_ABLE55
SIF RESULT
	RETURN 1
CALL ACT_ABLE67
SIF RESULT
	RETURN 1
FOR LOCAL, 70, 75
	CALLFORM ACT_ABLE{LOCAL}
	SIF RESULT
		RETURN 1
NEXT
RETURN 0
