﻿;エストの部屋
@DUNGEON_MAP_PRODUCE_ROOM
REPEAT 100
A = 1800 + COUNT
FLAG:A = 0
REND

;ビット覚書
;1　侵入不可
;2　不可視
;4　マスター現在位置
;8　敵現在位置
;16　アイテム位置
;32　上り階段位置
;64　下り階段位置
;128　イベント
;256～ （予定）罠パネル、水辺パネル（MASTER鈍足）、移動可視界無しのパネルなんかもあれば面白いかもしれない。

REPEAT 100
A:COUNT = 0
REND

;侵入不可パネル生成
REPEAT 100
SIF COUNT % 10 < 3 || COUNT % 10 > 5 
	FLAG:(1800 + COUNT) |= 1
SIF COUNT == 73 || COUNT == 75
	FLAG:(1800 + COUNT) |= 1
REND


;マスター現在位置
FLAG:1894 |= 4

;イベント
FLAG:1874 |= 256
FLAG:1874 |= 128

