﻿;-------------------------------------------------
;汎用関数置き場
;道具関連(調教道具分類系など)
;-------------------------------------------------

;-------------------------------------------------
;関数名:ITEM_TYPE
;概　要:調教道具分類関数
;引　数:ARG…ITEM番号
;戻り値:道具分類番号
;       (0.非表示/1.CVAB系/2.ＳＭ系/3.その他)
;備　考:文中関数
;調教道具分類テーブル。調教道具の分類番号を返す。
;調教道具整頓表示に用いる
;
;名前が若干DITEMTYPEとややこしいかもしれない
;-------------------------------------------------
@ITEM_TYPE(ARG)
#FUNCTION
SELECTCASE ARG
	;0.非表示
	CASE 50 TO 60, IS >= 100
		RETURNF 0
	;1.CVAB系
	CASE 0 TO 2, 11 TO 14, 32, 41
		RETURNF 1
	;2.ＳＭ系
	CASE 20 TO 22, 30, 31, 33
		RETURNF 2
ENDSELECT
;3.その他
RETURNF 3


;-------------------------------------------------
;関数名:ITEM_TYPENAME
;概　要:調教道具分類名取得関数
;引　数:ARG…調教道具分類番号
;戻り値:調教道具分類を表す文字列
;       (0.非表示/1.CVAB系/2.ＳＭ系/3.その他)
;備　考:文中関数
;調教道具の分類名を返す。調教道具整頓表示に用いる
;-------------------------------------------------
@ITEM_TYPENAME(ARG)
#FUNCTIONS
SIF !STRLENS(LOCALS)
	SPLIT "非表示/CVAB系/ＳＭ系/其他", "/", LOCALS
RETURNF LOCALS:ARG
