﻿@ITEM_SHOW_SHOP
SIF !STRLENS(LOCALS:5)
	SPLIT "/////DAYDREAMER_SHOP//MINTTEA/ROSETEA/EARLGREY/CHAI/BLEND/ROYAL/LONELYPERFUME/STONE/FIRE", "/", LOCALS
CALL PRINT_BASEL("体力", MASTER, 0)
CALL PRINT_BASEL("气力", MASTER, 1)
FOR LOCAL, 505, 600
	SIF LOCAL != 506 && ITEM:LOCAL
		PRINTFORML [{LOCAL}] - %ITEMNAME:LOCAL, 18, LEFT% × {ITEM:LOCAL}
NEXT
PRINTL [100] - 返回
PRINTL [101] - 背包整理
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 100
		RETURN 0
	CASE 101
		CALL BACKPACK
	CASE 505, 507 TO 599
		SIF !ITEM:RESULT
			GOTO ERROR
		CALLFORM ITEM_USE_%LOCALS:(RESULT - 500)%
	CASEELSE
		$ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
RESTART


@ITEM_USE_DAYDREAMER_SHOP
IF FLAG:1701
	PRINTL 拠点に帰還しますか？
	PRINTL [0] はい
	PRINTL [1] いいえ
	CALL INPUT_SELECT_2
	IF R == 1
		CALL ITEM_SHOW_SHOP
	ELSE
		IF FLAG:1701 == 1
			ITEM:505 -= 1
			PRINTW 拠点に帰還しました。
			FLAG:1701 = 0
			A = CHARANUM - 1
			REPEAT FLAG:1703
				IF TALENT:A:170
					CFLAG:A:92 = 1
				ELSE
					DELCHARA A
				ENDIF
				A -= 1
			REND
				TARGET = TARGET:10
				ASSI = ASSI:10
				ASSI:1 = ASSI:11
				ASSI:2 = ASSI:12
		ELSE
			ITEM:505 -= 1
			PRINTW 拠点に帰還しました。
			FLAG:1701 = 0
			FOR LOCAL,0, FLAG:1703
				CFLAG:(CHARANUM - 1 - LOCAL):92 = 1
			NEXT
			TARGET = TARGET:10
			ASSI = ASSI:10
			ASSI:1 = ASSI:11
			ASSI:2 = ASSI:12
		ENDIF
	ENDIF
ELSE
	PRINTL 梦魔を迷宮に送還します。
	PRINTL 誰に使いますか？
	PRINTL [0] - 戻る
	REPEAT CHARANUM
		SIF COUNT == 0
			CONTINUE
		SIF CFLAG:COUNT:92
			CONTINUE
		PRINTFORML [{COUNT}] - %CALLNAME:COUNT%
	REND
	$INPUT_LOOP
	INPUT
	IF RESULT < 0 || RESULT >= CHARANUM
		GOTO INPUT_LOOP
	ELSEIF CFLAG:RESULT:92
		GOTO INPUT_LOOP
	ELSEIF RESULT == 0
		RETURN 0
	ELSEIF CFLAG:RESULT:42 || TALENT:RESULT:2
		PRINTFORMW %CALLNAME:RESULT%は大事な家族だ
		GOTO INPUT_LOOP
	ELSEIF TALENT:RESULT:170 == 0
		PRINTFORMW %CALLNAME:RESULT%を迷宮に送還しました
		;調教者の情報を取得
		IF RESULT == TARGET || TARGET == -1
			T = -1
		ELSE
			T = CFLAG:TARGET:91
		ENDIF
		;助手の情報を取得
		IF RESULT == ASSI || ASSI == -1
			N:1 = -1
		ELSE
			N:1 = CFLAG:ASSI:91
		ENDIF
		IF RESULT == ASSI:2 || ASSI:2 == -1
			N:2 = -1
		ELSE
			N:2 = CFLAG:(ASSI:2):91
		ENDIF
		DELCHARA RESULT
			;対象を戻す
		TARGET = -1
		REPEAT CHARANUM
			SIF COUNT == 0
				CONTINUE
			SIF CFLAG:COUNT:91 == T
				TARGET = COUNT
		REND
		;助手を戻す
		ASSI = -1
		ASSI:1 = -1
		ASSI:2 = -1
		REPEAT CHARANUM
			SIF COUNT == 0
				CONTINUE
			IF CFLAG:COUNT:91 == N:1
				ASSI = COUNT
				ASSI:1 = COUNT
			ELSEIF CFLAG:COUNT:91 == N:2
				ASSI:2 = COUNT
			ENDIF
		REND
		IF TARGET == -1
			ASSI = -1
			ASSI:1 = -1
			ASSI:2 = -1
		ENDIF
		SIF ASSI == -1
			ASSI:2 = -1
		ITEM:505 -= 1
	;ユニークはCFLAG:92がたつだけで消えない
	ELSEIF TALENT:RESULT:170
		PRINTFORMW %CALLNAME:RESULT%を迷宮に送還しました。
		CFLAG:RESULT:92 = 1
		CFLAG:RESULT:93 = 1
		IF RESULT == TARGET
			TARGET = -1
		ELSEIF RESULT == ASSI
			ASSI = -1
			ASSI:1 = -1
			ASSI:2 = -1
		ELSEIF RESULT == ASSI:2
			ASSI:2 = -1
		ENDIF
		ITEM:505 -= 1
	ENDIF
ENDIF


@ITEM_STORAGE
LOCAL:1 = SUMARRAY(ITEM, 500, 600)
DRAWLINE
PRINTFORML 持有物\@ LOCAL:1 >= 20 ? (背包已经满了) # \@
FOR LOCAL, 0, 100
	SIF ITEM:(500 + LOCAL)
		PRINTFORML [{LOCAL, 3}] - %ITEMNAME:(500 + LOCAL), 18, LEFT% × {ITEM:(500 + LOCAL), 2}
NEXT
PRINTL 仓库
FOR LOCAL, 0, 100
	SIF FLAG:(3000 + LOCAL)
		PRINTFORML [{100 + LOCAL}] - %ITEMNAME:(500 + LOCAL), 18, LEFT% × {FLAG:(3000 + LOCAL), 2}
NEXT
PRINTL [200] - 返回
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0 TO 99
		SIF !ITEM:(500 + RESULT)
			GOTO ERROR
		ITEM:(500 + RESULT)--
		FLAG:(3000 + RESULT)++
	CASE 100 TO 199
		SIF !FLAG:(2900 + RESULT) || LOCAL:1 >= 20
			GOTO ERROR
		ITEM:(400 + RESULT)++
		FLAG:(2900 + RESULT)--
	CASE 200
		RETURN 0
	CASEELSE
		$ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
RESTART


@ITEMSALE
PRINTL [0] - 返回
PRINTL [1] - 出售调教道具
;PRINTL [2] - 消費アイテムの売却
CALL INPUT_SELECT_2
IF R == 0
	RETURN 0
ELSEIF R == 1
	PRINTFORML 持有金${MONEY}
	A = 0
	B = 0
	REPEAT 50
		IF FLAG:(1600 + COUNT) > 1
				PRINTFORM %ITEMNAME:COUNT%×{FLAG:(1600 + COUNT) - 1 }
			IF COUNT == 0 || COUNT == 1 || COUNT == 12 || COUNT == 13 || COUNT == 14 || COUNT == 20 || COUNT == 21 || COUNT == 22 || COUNT == 31
				PRINTFORML  ${500 * (FLAG:(1600 + COUNT) - 1)}
				B += 500 * (FLAG:(1600 + COUNT) - 1)
			ELSEIF COUNT == 2 || COUNT == 3 || COUNT == 11 || COUNT == 30 || COUNT == 42
				PRINTFORML  ${750 * (FLAG:(1600 + COUNT) - 1)}
				B += 750 * (FLAG:(1600 + COUNT) - 1)
			ELSEIF COUNT == 32 || COUNT == 41
				PRINTFORML  ${1000 * (FLAG:(1600 + COUNT) - 1)}
				B += 1000 * (FLAG:(1600 + COUNT) - 1)
			ELSEIF COUNT == 33 || COUNT == 40
				PRINTFORML  ${1500 * (FLAG:(1600 + COUNT) - 1)}
				B += 1500 * (FLAG:(1600 + COUNT) - 1)
			ENDIF
			A += 1
		ENDIF
	REND
	IF A == 0
		PRINTW 没有多余的道具
		RESTART
	ELSE
		PRINTFORML 将剩余的调教道具以${B}卖掉吗
		PRINTL [0] - 是
		PRINTL [1] - 否
		CALL INPUT_SELECT_2
		IF R == 1
			RESTART
		ELSE
			MONEY += B
			REPEAT 50
				SIF FLAG:(1600 + COUNT)
					FLAG:(1600 + COUNT) = 1
			REND
		ENDIF
	ENDIF
ELSEIF R == 2
	$ITEMSHOW
	DRAWLINE
	PRINTFORML 持有金${MONEY}
	PRINTL 要卖掉什么？
	REPEAT 100
		IF ITEM:(500 + COUNT)
			PRINTFORM [{COUNT}] %ITEMNAME:(500 + COUNT)% 
			IF COUNT == 0 || COUNT == 2 || (COUNT >= 7 && COUNT <= 10)
				PRINTFORM $250
			ELSEIF COUNT == 4
				PRINTFORM $500
			ELSEIF COUNT == 6 || COUNT == 13
				PRINTFORM $1000
			ELSEIF COUNT == 1 || COUNT == 3
				PRINTFORM $750
			ELSEIF COUNT == 5 || COUNT == 11 || COUNT == 12 || COUNT == 14 || COUNT == 15
				PRINTFORM $1500
			ENDIF
			PRINTFORML  ×{ITEM:(500 + COUNT)}　
		ENDIF
	REND
	PRINTL [100] - 返回
	INPUT
	IF RESULT > 100 || RESULT < 0
		GOTO ITEMSHOW
	ELSEIF RESULT == 100
		RESTART
	ELSEIF ITEM:(500 + RESULT)
		IF RESULT == 0 || RESULT == 2 || (RESULT >= 7 && RESULT <= 10)
			PRINTFORMW $250で売りました
			MONEY += 250
			ITEM:(500 + RESULT) -= 1
		ELSEIF RESULT == 4
			PRINTFORMW $500で売りました
			MONEY += 500
			ITEM:(500 + RESULT) -= 1
		ELSEIF RESULT == 6 || RESULT == 13
			PRINTFORMW $1000で売りました
			MONEY += 1000
			ITEM:(500 + RESULT) -= 1
		ELSEIF RESULT == 1 || RESULT == 3
			PRINTFORMW $750で売りました
			MONEY += 750
			ITEM:(500 + RESULT) -= 1
		ELSEIF RESULT == 5 || RESULT == 11 || RESULT == 12 || RESULT == 14 || RESULT == 15
			PRINTFORMW $1500で売りました
			MONEY += 1500
			ITEM:(500 + RESULT) -= 1
		ENDIF
		GOTO ITEMSHOW
	ENDIF
ENDIF


@PRESENT
PRINT 目前
SELECTCASE EQUIP:MASTER:11
	CASE 1
		PRINTL 带着【奴隶项圈】
	CASEELSE
		PRINTL 什么都没带
ENDSELECT
PRINTL [0] - 返回
SIF EQUIP:MASTER:10 & 1
	PRINTFORML [1] - 奴隶项圈   \@ EQUIP:MASTER:11 == 1 ? 装备中 # \@
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
		RETURN 0
	CASE 1
		EQUIP:MASTER:11 = EQUIP:MASTER:11 != 1
	CASEELSE
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
RESTART
