﻿
;FLAG:1742のビット解説
;1=バトル終了（マスターが移動後にイベントパネルを踏んだのか、バトルが終わってそのままの場所にいるのかの判定）
;2=逃走
;4=お持ち帰り
;8=初めて21階に到着

;初期設定
@DUNGEON_FOREST
;ダンジョンタイプ
FLAG:1709 = 2
;ダンジョン難易度補正
FLAG:1705 = FLAG:1704
IF FLAG:1704 == 1
	CALL DUNGEON_MAP_PRODUCE_FOREST
ELSE
	CALL DUNGEON_MAP_PRODUCE_ELEVATOR
ENDIF


;階層構造
@DUNGEON_FOREST_STAIRS
FLAG:1704 += 1
FLAG:1705 += 1

IF !(FLAG:1704 % 5)
	CALL DUNGEON_MAP_PRODUCE_ELEVATOR
ELSE
	CALL DUNGEON_MAP_PRODUCE_FOREST
ENDIF


;イベントパネルを踏むとここが呼び出されます。
@DUNGEON_EVENT_ELTUM
;5階ごとのワープ
IF !(FLAG:1704 % 5)
	IF FLAG:1722 < FLAG:1704 / 5
		PRINTW 転送機を稼動させた。
		FLAG:1722 = FLAG:1704 / 5
	ELSE
		PRINTL 転送機はすでに稼動している。
		PRINTL 拠点に帰還しますか？
		PRINTL [1] - はい
		PRINTL [2] - いいえ
		DRAWLINE
		INPUT
		IF RESULT == 1
			CALL ESCAPE_DUNGEON
			RETURN 0
		ELSE
			;移動メニューに移ります
			FLAG:1711 = 1
		ENDIF
	ENDIF
ELSE
	CALL DUNGEON_RANDOM_EVENT
	FLAG:M -= 128
ENDIF

