﻿;-------------------------------------------------
;汎用関数置き場
;パラメータ関連(ABL計算など)
;
;そのうちA:30とかB:2とかをなんとかする
;-------------------------------------------------

;-------------------------------------------------
;関数名:ACT_FROM
;概　要:MASTERに対して愛撫を行うときの計算を行う関数
;引　数:ARG:0…MASTERに愛撫を行うキャラの登録番号(TARGET/ASSI/ASSI:1/ASSI:2)
;      :ARG:1…行う愛撫の種類
;              (1.Ｃ愛撫/2.Ｖ愛撫/3.Ａ愛撫/4.Ｂ愛撫/5.Ｖ性交/6.Ａ性交/7.Ｖ性交奉仕/8.Ａ性交奉仕/11.Ｃ道具/12.Ｖ道具/13.Ａ道具/14.Ｂ道具)
;戻り値:計算結果
;備　考:式中関数
;@ABL_REVISIONにおける、S:1～S:8・S:11～S:14・S:21～S:27・S:31～S:34の代替
;
;技巧-感覚:  0   1   2   3   4   5   6   7   8   9  10
;　 基本値:100 121 144 169 196 225 256 289 324 361 400
;-------------------------------------------------
@ACT_FROM(ARG:0, ARG:1)
#FUNCTION
SELECTCASE ARG:1
	;Ｃ愛撫/Ｖ愛撫/Ａ愛撫/Ｂ愛撫
	CASE 1 TO 4
		LOCAL = MAX(ABL:(ARG:0):2 + ABL:(ARG:0):21 - ABL:MASTER:(2 + ARG:1), MARK:(ARG:0):2)
	;Ｖ性交/Ａ性交
	CASE 5, 6
		LOCAL = MAX(ABL:(ARG:0):2 + ABL:(ARG:0):23 - ABL:MASTER:(ARG:1 - 1), MARK:(ARG:0):2)
	;Ｖ性交奉仕/Ａ性交奉仕
	CASE 7, 8
		LOCAL = MAX(ABL:(ARG:0):2 + ABL:(ARG:0):23 + ABL:(ARG:0):(ARG:1 - 3) - ABL:MASTER:3, MARK:(ARG:0):2)
	;Ｃ道具/Ｖ道具/Ａ道具/Ｂ道具
	CASE 11 TO 14
		LOCAL = MAX(ABL:(ARG:0):2 + ABL:(ARG:0):22 - ABL:MASTER:(ARG:1 - 8), MARK:(ARG:0):2)
	;正体不明
	CASEELSE
		RETURNF 0
ENDSELECT
RETURNF POWER(LOCAL + 10, 2) * LIMIT(MAX(CFLAG:(ARG:0):0 * 10 / (CFLAG:MASTER:0 + 1), MIN(CFLAG:(ARG:0):2 / 200, 30)), 10, 1000) / 10


;-------------------------------------------------
;関数名:ACT_TO
;概　要:MASTERが愛撫を行うときの計算を行う関数
;引　数:ARG:0…MASTERが愛撫を行う対象キャラの登録番号(TARGET/ASSI/ASSI:1/ASSI:2)
;      :ARG:1…行う愛撫の種類
;              (1.Ｃ愛撫/2.Ｖ愛撫/3.Ａ愛撫/4.Ｂ愛撫/5.Ｖ性交/6.Ａ性交/7.Ｖ性交奉仕/8.Ａ性交奉仕)
;戻り値:計算結果
;備　考:式中関数
;@ABL_REVISIONにおける、S:41・S:43～S:48・S:51・S:53～S:58の代替
;
;技巧-感覚:  0   1   2   3   4   5   6   7   8   9  10
;　 基本値:100 121 144 169 196 225 256 289 324 361 400
;-------------------------------------------------
@ACT_TO(ARG:0, ARG:1)
#FUNCTION
SELECTCASE ARG:1
	;Ｃ愛撫/Ｖ愛撫/Ａ愛撫/Ｂ愛撫
	CASE 1 TO 4
		LOCAL = MAX(2 * ABL:MASTER:2 - ABL:(ARG:0):(2 + ARG:1), MARK:(ARG:0):2)
	;Ｖ性交/Ａ性交
	CASE 5, 6
		LOCAL = MAX(ABL:MASTER:2 + ABL:MASTER:(ARG:1 - 1) - ABL:(ARG:0):3, MARK:(ARG:0):2)
	;Ｖ性交奉仕/Ａ性交奉仕
	CASE 7, 8
		LOCAL = MAX(2 * ABL:MASTER:2 - ABL:(ARG:0):(ARG:1 - 3), MARK:(ARG:0):2)
	;正体不明
	CASEELSE
		RETURNF 0
ENDSELECT
RETURNF POWER(LOCAL + 10, 2) * LIMIT(MAX(CFLAG:MASTER:0 * 10 / (CFLAG:(ARG:0):0 + 1), MIN(CFLAG:(ARG:0):9 / 200, 30)), 10, 1000) * (FLAG:3185 + 100) / 1000


;-------------------------------------------------
;関数名:GET_ABL
;概　要:補正能力値取得関数
;引　数:ARG:0…キャラ登録番号
;       ARG:1…ABL番号
;戻り値:補正後のABL値
;備　考:式中関数
;@ABL_REVISIONにおける、A:1～A:29・B:1～B:30・C:1～C:29の代替
;-------------------------------------------------
@GET_ABL(ARG:0, ARG:1)
#FUNCTION
RETURNF 100 - 500 / (5 + ABL:(ARG:0):(ARG:1))


;-------------------------------------------------
;関数名:GET_REVISION
;概　要:汎用補正値取得関数
;引　数:ARG:0…参照する数値
;       ARG:1…上限
;       ARG:2…上昇率
;戻り値:補正後の値
;備　考:式中関数
;ある数値を、上限値に漸近していく数値に変換したいときに
;ARG:2が小さいとすぐに上限に達する、大きいと逆になかなか上限まで近づかない
;-------------------------------------------------
@GET_REVISION(ARG:0, ARG:1, ARG:2)
#FUNCTION
RETURNF ARG:1 - ARG:2 * ARG:1 / (ARG:2 + ARG:0)


;-------------------------------------------------
;関数名:GET_TRAINLV
;概　要:補正調教レベル取得関数
;引　数:ARG…キャラ登録番号
;戻り値:補正後の調教レベル
;備　考:式中関数
;@ABL_REVISIONにおける、A:30・C:30の代替
;-------------------------------------------------
@GET_TRAINLV(ARG)
#FUNCTION
RETURNF 50 - 2000 / (40 + CFLAG:ARG:0)
