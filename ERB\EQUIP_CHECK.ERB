﻿;────────────────────────────────────
;バイブなど付けっぱなしアイテムのチェック
;────────────────────────────────────
@EQUIP_CHECK
CALL ABL_REVISION
;インフレ仕様
;ABLとAとの対応、だいたい元値の10倍程度、100を超えない値
;	ABL	A
;	0	0
;	1	16.67
;	2	28.57
;	3	37.5
;	4	44.44
;	5	50
;	6	54.55
;	7	58.33
;	8	61.54
;	9	64.29
;	10	66.67

;────────────────────────────────────
;ローション（潤滑追加）
;────────────────────────────────────
IF TEQUIP:10
	IF PALAM:4 < 500
		UP:4 += 200
	ELSEIF PALAM:4 < 1200
		UP:4 += 100
	ELSEIF PALAM:4 < 2500
		UP:4 += 50
	ELSE
		UP:4 += 10
	ENDIF
ENDIF

;────────────────────────────────────
;媚薬（快ＣＶＡＢ、欲情追加、理性）
;────────────────────────────────────
IF TEQUIP:10
	IF PALAM:5 < 1000
		UP:5 += 500
		LOSEBASE:95 += 25
	ELSEIF PALAM:5 < 2000
		UP:5 += 250
		LOSEBASE:95 += 50
	ELSEIF PALAM:5 < 3000
		UP:5 += 150
		LOSEBASE:95 += 100
	ELSE
		UP:5 += 50
		TFLAG:130 += 200
	ENDIF
	TIMES SOURCE:0 , 1.40
	TIMES SOURCE:1 , 1.40
	TIMES SOURCE:2 , 1.40
	TIMES SOURCE:3 , 1.40
ENDIF

;────────────────────────────────────
;バイブ装着中（快Ｖ、中毒充足、トラウマ）
;────────────────────────────────────
IF TEQUIP:20 && TFLAG:90 != 21
	A = 100 * (2 + ITEM:9 + TALENT:59) / 2
	SOURCE:1 += A
	SIF ABL:MASTER:4 > 2
		SOURCE:23 += B:4 * (B:4 - 20) * (ITEM:9 + 1) / 10 + A / 5
	SIF CFLAG:MASTER:8 == 21
		SOURCE:24 += 20 + A:22 + ITEM:9 * 100 + (TALENT:59 + 1) * A:2 * A:22 / 20
ENDIF

;────────────────────────────────────
;アナルバイブ装着中（快Ａ、逸脱、中毒充足）
;────────────────────────────────────
IF TEQUIP:25 && TFLAG:90 != 22
	A = 150 * (2 + ITEM:9 + TALENT:59) / 2
	SOURCE:2 += A
	;経験不足
	IF EXP:MASTER:2 < 1
		SOURCE:22 += 1500
	ELSEIF EXP:MASTER:2 < 6
		SOURCE:22 += 800 - EXP:MASTER:2 * 100
	ELSEIF EXP:MASTER:2 < 11
		SOURCE:22 += 200 - EXP:MASTER:2 * 15
	ELSEIF EXP:MASTER:2 < 21
		SOURCE:22 += 40 - EXP:MASTER:2 * 2
	ENDIF
	SIF ABL:MASTER:5 > 2
		SOURCE:23 += (B:5 + 1) * (B:5 - 20) * (ITEM:9 + 1) / 4 + A / 5
	TFLAG:130 += 50
ENDIF

;────────────────────────────────────
;アナルビーズ装着中（快Ａ、逸脱、中毒充足）
;────────────────────────────────────
IF TEQUIP:26 && TFLAG:90 != 23
	A = 100 * (2 + ITEM:9 + TALENT:59 + TEQUIP:26) / 3
	SOURCE:2 += A
	;経験不足
	IF EXP:MASTER:2 < 1
		SOURCE:22 += 500
	ELSEIF EXP:MASTER:2 < 6
		SOURCE:22 += 300 - EXP:MASTER:2 * 20
	ELSEIF EXP:MASTER:2 < 11
		SOURCE:22 += 90 - EXP:MASTER:2 * 6
	ELSEIF EXP:MASTER:2 < 21
		SOURCE:22 += 20 - EXP:MASTER:2 * 2
	ENDIF
	SIF ABL:MASTER:5 > 2
		SOURCE:23 += (B:5 + 10) * (B:5 - 20) / 4 + A / 5
	TFLAG:130 += 30
ENDIF

;────────────────────────────────────
;浣腸＋アナルプラグ装着中
;────────────────────────────────────
IF TEQUIP:27 && TFLAG:90 != 68
	A = 20 * (CFLAG:MASTER:0 + 1) + (B:2 - 30) * B:11 * (TALENT:59 + TALENT:MASTER:77 + TEQUIP:27) * 30 / 100
	SIF A < 0
		A = 0
	B = 200 + TEQUIP:27 * 100
	SOURCE:2 += A
	SOURCE:13 += B
	SOURCE:14 += 50 + TEQUIP:27 * 100
	C = A / 2 + B / 2 - CFLAG:MASTER:0 * 20 - B:11 * 10
	SIF C < 0
		C = 0
	SOURCE:22 += C
	SIF ABL:MASTER:11 > 3
		SOURCE:23 += (B:11 - 30) * B:5 * 25 * A / 10000
ENDIF

;────────────────────────────────────
;クリキャップ装着中（快Ｃ、中毒充足）
;────────────────────────────────────
IF TEQUIP:30 && TFLAG:90 != 24
	A = 100 * (2 + ITEM:9 + TALENT:59) / 2
	SOURCE:0 += A
	SIF ABL:MASTER:3 > 1
		SOURCE:23 += B:3 * (B:3 - 10) * (ITEM:9 + 1) / 5 + A / 5
ENDIF

;────────────────────────────────────
;オナホール装着中（快Ｃ、中毒充足）
;────────────────────────────────────
IF TEQUIP:31 && TFLAG:90 != 24
	A = 150 * (2 + ITEM:9 + TALENT:59) / 2
	SOURCE:0 += A
	SIF ABL:MASTER:3 > 1
		SOURCE:23 += B:3 * (B:3 - 10) * (ITEM:9 + 1) / 5 + A / 5
	TFLAG:130 += 100
ENDIF

;────────────────────────────────────
;二プルキャップ装着中（快Ｂ、中毒充足）
;────────────────────────────────────
IF TEQUIP:35 && TFLAG:90 != 25
	A = 100 * (2 + ITEM:9 + TALENT:59) / 2
	SOURCE:3 += A
	SIF ABL:MASTER:6 > 2
		SOURCE:23 += (B:6 - 10) * (B:6 - 20) * (ITEM:9 + 1) * 35 / 100 + A / 5
	TFLAG:130 += 30
ENDIF

;────────────────────────────────────
;搾乳器装着中（快Ｂ、痛み、逸脱、中毒充足）
;────────────────────────────────────
IF TEQUIP:36 && TFLAG:90 != 26
	A = 0
	;経験を見る
	IF EXP:MASTER:6 < 1
		A = A:22
		SOURCE:13 += 800
		SOURCE:22 += 1200
	ELSEIF EXP:MASTER:6 < 6
		A = 25 + A:22 + ITEM:9 * 25 + (TALENT:59 + 1) * A:2 / 5
		SOURCE:13 += 650
		SOURCE:22 += 900
	ELSEIF EXP:MASTER:6 < 13
		A = 50 + A:22 * 15 / 10 + ITEM:9 * 50 + (TALENT:59 + 1) * A:2 * 3 / 10
		SOURCE:13 += 450
		SOURCE:22 += 600
	ELSEIF EXP:MASTER:6 < 21
		A = 75 + A:22 * 15 / 10 + ITEM:9 * 100 + (TALENT:59 + 1) * A:2 / 2
		SOURCE:13 += 300
		SOURCE:22 += 300
	ELSE
		A = 100 + A:22 * 2 + ITEM:9 * 150 + (TALENT:59 + 1) * A:2
		SOURCE:13 += 150
		SIF ABL:MASTER:6 > 1
			SOURCE:23 += (B:6 + 10) * (B:6 - 10) * (ITEM:9 + 1) / 5 + A / 5
	ENDIF
	SOURCE:3 += A
	TFLAG:130 += 30
ENDIF

;────────────────────────────────────
;縄、蔦、蛇体で緊縛中（拘束、中毒充足）
;────────────────────────────────────
IF (TEQUIP:40 && TFLAG:90 != 63) || TEQUIP:46 || TEQUIP:47
	A = 200 + (B:3 / 10 + A:30) * (A:2 + 10) * (TALENT:58 * 3 + 2) / 2
	SOURCE:14 += A
	SOURCE:23 += B:11 * (B:16 - 10) * (B:16 + 10) * A / 100000
	TFLAG:130 += (B:11 - 20) * 3
ENDIF

;────────────────────────────────────
;アイマスク装着中（拘束、中毒充足、トラウマ）
;────────────────────────────────────
IF TEQUIP:41 && TFLAG:90 != 64
	A = 100 + (A:26 / 10 + A:30) * (A:2 + 10) * (TALENT:58 + 2) / 2
	B = 0
	SOURCE:14 += A
	SIF ABL:MASTER:16 > 1
		B = B:11 * (B:16 - 10) * (B:16 + 10) * A / 100000
	SOURCE:23 += B
	C = A / 2 - B
	SIF C > 0
		SOURCE:24 += C
	SIF CFLAG:MASTER:8 == 64
		TIMES SOURCE:24 , 1.50
	TFLAG:130 += (B:11 - 20) * 2
ENDIF

;────────────────────────────────────
;ボールギャグ装着中（拘束、中毒充足、トラウマ）
;────────────────────────────────────
IF TEQUIP:42 && TFLAG:90 != 65
	A = 350 + (A:26 / 10 + A:30) * 10 * (TALENT:58 + 1)
	B = 0
	SOURCE:14 += A
	SIF ABL:MASTER:16 > 1
		B = B:11 * (B:16 - 10) * (B:16 + 10) * A / 100000
	SOURCE:23 += B
	C = A / 2 - B
	SIF C > 0
		SOURCE:24 += C
	SIF CFLAG:MASTER:8 == 65
		TIMES SOURCE:24 , 1.50
	TFLAG:130 += (B:11 - 20)
ENDIF

;────────────────────────────────────
;三角木馬乗馬中（快Ｖ、情愛逆、痛み、拘束、逸脱、中毒充足、トラウマ）
;────────────────────────────────────
IF TEQUIP:43
	A = 0
	B = 1000 + A:26 * A:2 / 2 + A:30 * (TALENT:83 * 3 + 2) * 20
	C = 50 + (MARK:3 + 1) * (A:2 + 10) 
	D = 1800 + B / 2 - CFLAG:MASTER:0 * 50 - B:11 * 20 - B:15 * 60
	E = 0
	F = 0
	G = 0
	SIF ABL:MASTER:11 > 3
		A = (B:11 - 30) * B:15 * (TALENT:MASTER:77 + 2) / 2
	SOURCE:1 += A
	SOURCE:13 += B
	SOURCE:14 += C
	SIF D < 0
		D = 0
	SOURCE:22 += D
	SIF ABL:MASTER:15 > 3
		E = B:11 * (B:15 - 30) * (B:15 + 10) * B / 80000
	SOURCE:23 += E
	F = B / 2 + C / 5 - E
	SOURCE:24 += F
	SIF CFLAG:MASTER:8 == 67
		TIMES SOURCE:24 , 1.50
	G = E - B - D - F
	SIF G < 0
		SOURCE:11 += G
	TFLAG:130 += (B:11 - 20) * 5
ENDIF
;────────────────────────────────────
;顔面騎乗中（快Ｃ、中毒充足）
;────────────────────────────────────
IF TEQUIP:44
	SOURCE:21 += 100
	SOURCE:40 += 150 + S:41 * B:30 * ( 3 + TALENT:MASTER:52 + TALENT:MASTER:54 + TALENT:MASTER:161) / 500
ENDIF
;────────────────────────────────────
;顔面騎乗アナル（快Ｃ、中毒充足）
;────────────────────────────────────
IF TEQUIP:45
	SOURCE:21 += 300
	SOURCE:42 += 150 + S:43 * B:30 * ( 3 + TALENT:MASTER:52 + TALENT:MASTER:54 + TALENT:MASTER:161) / 500
ENDIF
;────────────────────────────────────
;野外プレイ中（拘束、露出、逸脱、トラウマ）
;────────────────────────────────────
IF TEQUIP:52 && TFLAG:90 != 44
	SOURCE:14 += 50 + MARK:3 * 15
	SOURCE:20 += 300
	SIF TEQUIP:69 != 2
		SOURCE:20 += TEQUIP:69 * 200
	A = 150 + SOURCE:14 - B:8 * 3 - CFLAG:MASTER:0 * 10
	B = TALENT:MASTER:10 * 150 - TALENT:MASTER:22 * 50 + TALENT:MASTER:32 * 150 - TALENT:MASTER:33 * 50 + TALENT:MASTER:34 * 250 - TALENT:MASTER:35 * 150
	IF A > 0
		SOURCE:22 += A
		B += A
	ENDIF
	SIF B > 0
		SOURCE:24 += B
	SIF CFLAG:MASTER:8 == 44
		TIMES SOURCE:24 , 1.50
		TFLAG:130 += B:8 * 5
ENDIF

;────────────────────────────────────
;羞恥プレイ中（露出）
;────────────────────────────────────
IF TEQUIP:56 && TFLAG:90 != 43
	SOURCE:20 += 200
	SIF TEQUIP:69 != 2
		SOURCE:20 += TEQUIP:69 * 150
		TFLAG:130 += B:8 * 3
ENDIF

;────────────────────────────────────
;調教対象が自慰中（快Ｃ、快Ｖ、快Ａ、快Ｂ、性行動、露出、逸脱、中毒充足）
;────────────────────────────────────
;このターンで自慰の命令を出した場合は拒否される可能性があるので処理を後回しします
IF (TEQUIP:69 == 1 || TEQUIP:69 == 3) && TFLAG:90 != 40
	A = 20 + B:1 * (B:2 + 10) * CFLAG:MASTER:0 / 50 + B:2 * 25 * (TALENT:MASTER:57 + 1) / 10
	B = 0
	C = 0
	D = 0
	E = A
	;バイブ使用
	IF TEQUIP:20
		B = 50 + B:1 * (CFLAG:MASTER:0 + 1) / 2 + (TALENT:MASTER:59 + 1) * B:2 * (ITEM:9 + 1) * 3
		SOURCE:1 += B
		TIMES A , 0.80
	ENDIF
	;アナルバイブ使用
	IF TEQUIP:25 && ABL:MASTER:5 > 1
		C = (B:1 + 10) * (CFLAG:MASTER:0 + 1) * 6 / 10 + (TALENT:MASTER:59 + 1) * B:2 * (ITEM:9 + 1) * 25 / 10
		SOURCE:2 += C
		TIMES E , 0.60
	ENDIF
	SOURCE:0 += A
	SIF TALENT:MASTER:122 == 0
		SOURCE:3 += E
	SOURCE:12 += 50 + B:2 * 2 + CFLAG:MASTER:0 * 10 + TEQUIP:20 * 50 + TEQUIP:25 * 50
	D = 150 + A:30 * 10 + TEQUIP:52 * 200 + TEQUIP:56 * 150 + A / 2 + B / 3 + C / 3 + E / 2
	SOURCE:20 += D
	SOURCE:22 += D / 2 - B:8 * 2 - CFLAG:MASTER:0 * 10
	SOURCE:23 += (A + E + B + C + D) * (50 + B:12) / 500
	TFLAG:130 += 100
ENDIF

;────────────────────────────────────
;調教者が自慰中（逸脱、欲情追加、快ｃ、快ｖ、快ｂ）
;────────────────────────────────────
IF TEQUIP:69 > 1 && TFLAG:90 != 42
	A = 50 + A:1 * A:2 * A:30 / 50 + B:2 * 25 * (TALENT:MASTER:57 + 1) / 10
	B = A
	C = 0
	;バイブ使用
	IF ITEM:0 && TALENT:0 == 0 && TALENT:122 == 0
		C = 50 + A:1 * (A:30 + 1) / 2 + (TALENT:59 + 1) * A:2 * (ITEM:9 + 1) * 3
		TIMES A , 0.80
	ENDIF
	D = A + B + C
	SOURCE:40 += A
	SOURCE:41 += C
	SIF TALENT:122 == 0
		SOURCE:43 += B
	;調教レベルと欲望を見る
	IF CFLAG:MASTER:0 < 1 || ABL:MASTER:1 < 1
		TIMES D , 0.10
	ELSEIF CFLAG:MASTER:0 < 3 || ABL:MASTER:1 < 2
		TIMES D , 0.25
	ELSEIF CFLAG:MASTER:0 < 5 || ABL:MASTER:1 < 3
		TIMES D , 0.40
	ELSEIF CFLAG:MASTER:0 < 8 || ABL:MASTER:1 < 4
		TIMES D , 0.60
	ELSEIF CFLAG:MASTER:0 < 11 || ABL:MASTER:1 < 5
		TIMES D , 0.85
	ENDIF
	UP:5 += D / 5
	;逸脱の計算、調教のプログレスに応じて軽減
	D -= B:8 * 15 / 10 - CFLAG:MASTER:0 * 5
	SIF D > 0
		SOURCE:22 += D
	TFLAG:130 += 50
ENDIF

;────────────────────────────────────
;調教者挿入中（快Ｃ、快Ｖ、快Ａ、接触、情愛、性行動、痛み、露出、逸脱、中毒充足、トラウマ、快ｃ、快ｖ）
;────────────────────────────────────
;このターンの行動も性交ならACT_APPLY.ERBで処理します。会話や胸愛撫など挿入しながら行う行動だけここを見ます
IF TEQUIP:70 && TFLAG:80 != 3
	A = 0
	B = 0
	C = 0
	IF TEQUIP:70 == 1
		A = (300 + S:5 * 3 / 2) / 2
		B = (200 + S:45 * 3 / 2) / 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.60
			TIMES B , 0.80
			C += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.75
			TIMES B , 0.90
			C += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.90
			TIMES B , 0.95
			C += 100
		ENDIF
		;経験不足
		IF EXP:MASTER:1 < 2
			TIMES A , 0.30
			C += 1000
			SOURCE:22 += 1000
		ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
			TIMES A , 0.80
			C += 150
			SOURCE:22 += 250
		ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
			TIMES A , 0.95
			C += 50
			SOURCE:22 += 50
		ENDIF
		SOURCE:1 += A
		SIF TALENT:121 || TALENT:122
			SOURCE:40 += B
		SOURCE:10 += 250 + A:30 * 10
		SOURCE:12 += 100 + A:30 * 2
		SOURCE:13 += C
		IF CFLAG:MASTER:8 == 30
			SOURCE:24 += SOURCE:10
			TIMES SOURCE:24 , 1.50
		ENDIF
		SOURCE:11 += A / 5 - SOURCE:24 - C
		SIF SOURCE:11 > 0 && ABL:MASTER:4 > 2
			SOURCE:23 += B:4 * (B:4 - 20) * SOURCE:11 / 100 + A / 5
		SOURCE:11 += SOURCE:23 / 5
	ELSEIF TEQUIP:70 == 2
		A = (300 + S:5 * 3 / 2) / 2
		B = (300 + S:45) / 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.50
			TIMES B , 0.60
			C += 750
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.70
			TIMES B , 0.80
			C += 400
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.85
			TIMES B , 0.95
			C += 150
		ENDIF
		;経験不足
		IF EXP:MASTER:1 < 2
			TIMES A , 0.30
			C += 1200
			SOURCE:22 += 1500
		ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
			TIMES A , 0.70
			C += 350
			SOURCE:22 += 750
		ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
			TIMES A , 0.90
			C += 90
			SOURCE:22 += 250
		ENDIF
		SOURCE:1 += A
		SIF TALENT:121 || TALENT:122
			SOURCE:40 += B
		SOURCE:10 += 200 + A:30 * 10
		SOURCE:12 += 90 + A:30 * 2
		SOURCE:13 += C
		IF CFLAG:MASTER:8 == 31
			SOURCE:24 += SOURCE:10
			TIMES SOURCE:24 , 1.50
		ENDIF
		SOURCE:11 += A / 7 - SOURCE:24
		SIF SOURCE:11 > 0 && ABL:MASTER:4 > 2
			SOURCE:23 += B:4 * (B:4 - 20) * SOURCE:11 / 100 + A / 5
		SOURCE:11 += SOURCE:23 / 7
	ELSEIF TEQUIP:70 == 3
		A = (100 + S:5 * 2) / 2
		B = (100 + S:45 * 2) / 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.60
			TIMES B , 0.65
			C += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.80
			TIMES B , 0.80
			C += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.95
			TIMES B , 0.90
			C += 50
		ENDIF
		;経験不足
		IF EXP:MASTER:1 < 2
			TIMES A , 0.35
			C += 700
			SOURCE:22 += 1200
		ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
			TIMES A , 0.75
			C += 200
			SOURCE:22 += 700
		ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
			TIMES A , 0.95
			C += 30
			SOURCE:22 += 150
		ENDIF
		SOURCE:1 += A
		SIF TALENT:121 || TALENT:122
			SOURCE:40 += B
		SOURCE:10 += 200 + CFLAG:MASTER:0 * 10
		SOURCE:12 += 90 + CFLAG:MASTER:0 * 2
		SOURCE:13 += C
		IF CFLAG:MASTER:8 == 32
			SOURCE:24 += SOURCE:10
			TIMES SOURCE:24 , 1.50
		ENDIF
		SOURCE:11 += A / 5 - SOURCE:24 - C
		SIF SOURCE:11 > 0 && ABL:MASTER:4 > 2
			SOURCE:23 += B:4 * (B:4 - 20) * SOURCE:11 / 100 + A / 5
		SOURCE:11 += SOURCE:23 / 7 + SOURCE:12 / 5
	ELSEIF TEQUIP:70 == 4
		A = (100 + S:5) / 2
		B = (100 + S:45 * 2 / 3) / 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.60
			TIMES B , 0.60
			C += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.75
			TIMES B , 0.75
			C += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.90
			TIMES B , 0.90
			C += 100
		ENDIF
		;経験不足
		IF EXP:MASTER:1 < 2
			TIMES A , 0.30
			C += 1200
			SOURCE:22 += 1200
		ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
			TIMES A , 0.80
			C += 250
			SOURCE:22 += 350
		ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
			TIMES A , 0.95
			C += 50
			SOURCE:22 += 100
		ENDIF
		SOURCE:1 += A
		SIF TALENT:121 || TALENT:122
			SOURCE:40 += B
		SOURCE:10 += 400 + A:30 * 8 + CFLAG:MASTER:0 * 8
		SOURCE:12 += 130 + A:30 * 2 + CFLAG:MASTER:0 * 2
		SOURCE:13 += C
		IF CFLAG:MASTER:8 == 33
			SOURCE:24 += SOURCE:10
			TIMES SOURCE:24 , 1.50
		ENDIF
		SOURCE:11 += A / 4 - SOURCE:24 - C * 2
		SIF SOURCE:11 > 0 && ABL:MASTER:4 > 2
			SOURCE:23 += B:4 * (B:4 - 20) * SOURCE:11 / 100 + A / 5
		SOURCE:11 += SOURCE:23 / 5
	ELSEIF TEQUIP:70 == 5
		A = (50 + S:5) / 2
		B = (150 + S:45 / 2) / 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.50
			TIMES B , 0.55
			C += 750
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.70
			TIMES B , 0.75
			C += 400
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.85
			TIMES B , 0.90
			C += 150
		ENDIF
		;経験不足
		IF EXP:MASTER:1 < 2
			TIMES A , 0.30
			C += 1250
			SOURCE:22 += 1600
		ELSEIF EXP:MASTER:1 < 6 && EXP:MASTER:7 < 5
			TIMES A , 0.70
			C += 380
			SOURCE:22 += 800
		ELSEIF EXP:MASTER:1 < 11 && EXP:MASTER:7 < 9
			TIMES A , 0.90
			C += 100
			SOURCE:22 += 350
		ENDIF
		SOURCE:1 += A
		SIF TALENT:121 || TALENT:122
			SOURCE:40 += B
		SOURCE:10 += 500 + A:30 * 10
		SOURCE:12 += 150 + A:30 * 2
		SOURCE:13 += C
		;乱れ牡丹の処理
		SIF TEQUIP:56
			SOURCE:20 += 200 + A / 5
		IF CFLAG:MASTER:8 == 34
			SOURCE:24 += SOURCE:10
			TIMES SOURCE:24 , 1.50
		ENDIF
		SOURCE:11 += A / 7 - SOURCE:24
		SIF SOURCE:11 > 0 && ABL:MASTER:4 > 2
			SOURCE:23 += B:4 * (B:4 - 20) * SOURCE:11 / 100 + A / 5
		SOURCE:11 += SOURCE:23 / 7
	ELSEIF TEQUIP:70 == 6
		A = (300 + S:6 * 2) / 2
		B = (500 + S:46 * 2) / 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.40
			TIMES B , 0.30
			C += 1500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.65
			TIMES B , 0.55
			C += 900
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.80
			TIMES B , 0.75
			C += 250
		ENDIF
		;経験不足
		IF EXP:MASTER:2 < 2
			TIMES A , 0.40
			C += 1500
			SOURCE:22 += 2500
			SOURCE:24 += 1000
		ELSEIF EXP:MASTER:2 < 6 && EXP:MASTER:7 < 11
			TIMES A , 0.55
			C += 1000
			SOURCE:22 += 1500
			SOURCE:24 += 500
		ELSEIF EXP:MASTER:2 < 13 && EXP:MASTER:7 < 21
			TIMES A , 0.70
			C += 600
			SOURCE:22 += 500
			SOURCE:24 += 100
		ELSEIF EXP:MASTER:2 < 26 && EXP:MASTER:7 < 31
			TIMES A , 0.85
			C += 200
			SOURCE:22 += 150
		ENDIF
		SOURCE:2 += A
		SIF TALENT:121 || TALENT:122
			SOURCE:40 += B
		SOURCE:10 += 200 + A:30 * 10
		SOURCE:12 += 40 + A:30 * 2
		SOURCE:13 += C
		IF CFLAG:MASTER:8 == 35
			SOURCE:24 += SOURCE:10 + SOURCE:22
			TIMES SOURCE:24 , 1.50
		ENDIF
		SOURCE:11 += A / 5 - C / 5 - SOURCE:24 / 5
		SIF SOURCE:11 > 0 && ABL:MASTER:5 > 4
			SOURCE:23 += (B:5 + 10) * (B:5 - 30) * SOURCE:11 / 100 + A / 5
		SOURCE:11 += SOURCE:23 / 5
	ENDIF
TFLAG:179 = 2

ENDIF
;────────────────────────────────────
;調教対象挿入中（快Ｃ、快Ｖ、快Ａ、接触、情愛、性行動、痛み、露出、逸脱、中毒充足、トラウマ、快ｃ、快ｖ）
;────────────────────────────────────
;このターンの行動も性交ならACT_APPLY.ERBで処理します。会話や胸愛撫など挿入しながら行う行動だけここを見ます
IF TEQUIP:71 && TFLAG:80 != 10
	A = 0
	B = 0
	C = 0
	IF TEQUIP:71 == 1
		A = 100 + S:7 * 3 / 2
		B = 300 + S:47 * 3 / 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.60
			TIMES B , 0.80
			C += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.75
			TIMES B , 0.90
			C += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.90
			TIMES B , 0.95
			C += 100
		ENDIF
		SOURCE:0 += B
		SOURCE:41 += A
		SOURCE:10 += 250 + A:30 * 10
		SOURCE:12 += 100 + A:30 * 2
		SOURCE:11 += B / 5 - SOURCE:24 - C
		SIF SOURCE:11 > 0 && ABL:MASTER:3 > 2
			SOURCE:23 += B:3 * (B:3 - 20) * SOURCE:11 / 100 + B / 5
		SOURCE:11 += SOURCE:23 / 5
	ELSEIF TEQUIP:71 == 2
		A = 300 + S:7
		B = 300 + S:47 * 3 / 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.60
			TIMES B , 0.80
			C += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.75
			TIMES B , 0.90
			C += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.90
			TIMES B , 0.95
			C += 100
		ENDIF
		SOURCE:0 += B
		SOURCE:41 += A
		SOURCE:10 += 200 + A:30 * 10
		SOURCE:12 += 90 + A:30 * 2
		SOURCE:11 += B / 7 - SOURCE:24 - C
		SIF SOURCE:11 > 0 && ABL:MASTER:3 > 2
			SOURCE:23 += B:3 * (B:3 - 20) * SOURCE:11 / 100 + B / 5
		SOURCE:11 += SOURCE:23 / 7
	ELSEIF TEQUIP:71 == 3
		A = 300 + 2 * S:7
		B = 100 + S:47 * 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.70
			TIMES B , 0.80
			C += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.80
			TIMES B , 0.90
			C += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.90
			TIMES B , 0.95
			C += 100
		ENDIF
		SOURCE:0 += B
		SOURCE:41 += A
		SOURCE:10 += 200 + A:30 * 10
		SOURCE:12 += 40 + A:30 * 2
		SOURCE:11 += B / 5 - SOURCE:24 - C
		SIF SOURCE:11 > 0 && ABL:MASTER:3 > 2
			SOURCE:23 += B:3 * (B:3 - 20) * SOURCE:11 / 100 + B / 5
		SOURCE:11 += SOURCE:23 / 5
	ELSEIF TEQUIP:71 == 4
		A = 300 + S:7
		B = 50 + S:44 / 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.60
			TIMES B , 0.65
			C += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.80
			TIMES B , 0.80
			C += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.90
			TIMES B , 0.95
			C += 100
		ENDIF
		SOURCE:0 += B
		SOURCE:41 += A
		SOURCE:10 += 200 + A:30 * 10
		SOURCE:12 += 40 + A:30 * 2
		SOURCE:11 += B / 5 - SOURCE:24 - C
		SIF SOURCE:11 > 0 && ABL:MASTER:3 > 2
			SOURCE:23 += B:3 * (B:3 - 20) * SOURCE:11 / 100 + B / 5
		SOURCE:11 += SOURCE:23 / 5
	ELSEIF TEQUIP:71 == 5
		A = 100 + S:7
		B = 50 + S:41
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.50
			TIMES B , 0.55
			C += 500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.70
			TIMES B , 0.75
			C += 200
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.85
			TIMES B , 0.90
			C += 100
		ENDIF
		SOURCE:0 += B
		SOURCE:41 += A
		SOURCE:10 += 200 + A:30 * 10
		SOURCE:12 += 40 + A:30 * 2
		;乱れ牡丹の処理
		SIF TEQUIP:56
			SOURCE:20 += 200 + A / 5
		SOURCE:11 += B / 7 - SOURCE:24 - C
		SIF SOURCE:11 > 0 && ABL:MASTER:3 > 2
			SOURCE:23 += B:3 * (B:3 - 20) * SOURCE:11 / 100 + B / 5
		SOURCE:11 += SOURCE:23 / 7
	ELSEIF TEQUIP:71 == 6
		A = 500 + S:8 * 2
		B = 300 + S:48 * 2
		;潤滑不足
		IF PALAM:4 < 100 && TEQUIP:10 < 3
			TIMES A , 0.30
			TIMES B , 0.40
			C += 1500
		ELSEIF PALAM:4 < 250 && TEQUIP:10 < 2
			TIMES A , 0.55
			TIMES B , 0.65
			C += 900
		ELSEIF PALAM:4 < 500 && TEQUIP:10 < 1
			TIMES A , 0.75
			TIMES B , 0.80
			C += 250
		ENDIF
		SOURCE:42 += A
		SOURCE:0 += B
		SOURCE:10 += 200 + A:30 * 10
		SOURCE:12 += 40 + A:30 * 2
		SOURCE:11 += A / 5 - C / 5 - SOURCE:24 / 5
		SIF SOURCE:11 > 0 && ABL:MASTER:5 > 4
			SOURCE:23 += (B:5 + 10) * (B:5 - 30) * SOURCE:11 / 100 + A / 5
		SOURCE:11 += SOURCE:23 / 5
	ENDIF
ENDIF
SIF TEQUIP:70 || TEQUIP:71
	PALAM:4 += 50
;────────────────────────────────────
;触手調教中（未実装）
;────────────────────────────────────
;────────────────────────────────────
;スキュラ触手調教中
;────────────────────────────────────
IF TEQUIP:91
	SOURCE:0 += 2 * A:21 + 2 * A:2 + A:2 * TALENT:TARGET:57
	TFLAG:130 += A:2 + A:21 + TALENT:TARGET:57 * 50
ELSEIF TEQUIP:92
	SOURCE:2 += 25 * A:21 / 10 + 25 * A:2 / 10 + A:2 * TALENT:TARGET:57
	TFLAG:130 += (B:5 * 10 + B:11 * A:2) / 10
ELSEIF TEQUIP:93
	SOURCE:3 += 2 * A:21 + A:2 + A:2 * TALENT:TARGET:57
	TFLAG:130 += (A:2 + A:21 + TALENT:TARGET:57 * 50) / 2
ELSEIF TEQUIP:94
	SOURCE:1 +=  A:21 * 15 / 10 + (TALENT:TARGET:57 + 1) * A:2 * A:21 * 5 / 100
	TFLAG:130 += (A:2 + A:21 + TALENT:TARGET:57 * 50) / 2
ENDIF

;────────────────────────────────────
;C愛撫中（暫定）
;────────────────────────────────────
IF TEQUIP:100
	SOURCE:0 += 40 + S:1 * (2 + TALENT:57) / 10
	SOURCE:10 += 50 + A:30 * 3
	SOURCE:12 += 30 + SOURCE:0 / 20 + SOURCE:3 / 20
	SOURCE:11 += SOURCE:10 / 4 + A:7 * 3
	TFLAG:130 += 20 + (A:2 + A:21 + TALENT:57 * 50) / 2
ENDIF

