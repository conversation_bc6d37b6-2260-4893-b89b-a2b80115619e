﻿;────────────────────────────────────
;調教者の能力上昇
;────────────────────────────────────
;欲望
@ABLUP_1T
LOCAL = 0
SELECTCASE ABL:TARGET:1
	CASE 0
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 120 && CFLAG:0 > 0 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > 0
	CASE 1
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 400 && CFLAG:0 > 2 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > (30 + 8 * TALENT:71 - 8 * TALENT:70)
	CASE 2
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 1200 && CFLAG:0 > 4 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > (60 + 15 * TALENT:71 - 15 * TALENT:70)
	CASE 3
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 2500 && CFLAG:0 > 7 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > (120 + 30 * TALENT:71 - 30 * TALENT:70)
	CASE 4
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 4000 && CFLAG:0 > 10 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > (200 + 50 * TALENT:71 - 50 * TALENT:70)
	CASE IS < 99
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 4000 && CFLAG:0 > 10 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > (200 + 50 * TALENT:71 - 50 * TALENT:70) * (2 + ABL:1 - 4) * (10 + ABL:1 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(1)


;技巧、奉仕で調教者を気持ちよくするとあがる
@ABLUP_2T
LOCAL = 0
SELECTCASE ABL:TARGET:2
	CASE 0
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 100 - TALENT:50 * 20 + TALENT:51 * 20 && EXP:63
	CASE 1
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 500 - TALENT:50 * 90 + TALENT:51 * 90 && EXP:22 + EXP:23 + EXP:7 > 30 - TALENT:50 * 2 + TALENT:51 * 2 && EXP:63 > 20 - TALENT:50 * 5 + TALENT:51 * 5
	CASE 2
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 1000 - TALENT:50 * 200 + TALENT:51 * 200 && EXP:22 + EXP:23 + EXP:7 > 70 - TALENT:50 * 5 + TALENT:51 * 5 && EXP:63 > 40 - TALENT:50 * 10 + TALENT:51 * 10
	CASE 3
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 1500 - TALENT:50 * 400 + TALENT:51 * 400 && EXP:22 + EXP:23 + EXP:7 > 140 - TALENT:50 * 12 + TALENT:51 * 12 && EXP:63 > 60 - TALENT:50 * 15 + TALENT:51 * 15
	CASE 4
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 2000 - TALENT:50 * 750 + TALENT:51 * 750 && EXP:22 + EXP:23 + EXP:7 > 200 - TALENT:50 * 25 + TALENT:51 * 25 && EXP:63 > 100 - TALENT:50 * 20 + TALENT:51 * 20
	CASE IS < 99
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > (2000 - TALENT:50 * 750 + TALENT:51 * 750) * (5 + (ABL:2 - 4)) / 5 && EXP:22 + EXP:23 + EXP:7 > (200 - TALENT:50 * 25 + TALENT:51 * 25) * (2 + ABL:2 - 4) * (10 + ABL:2 - 5) / 20 && EXP:63 > (150 - TALENT:50 * 30 + TALENT:51 * 30) * (2 + ABL:2 - 4) * (10 + ABL:2 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(2)


;Ｃ感覚
@ABLUP_3T
LOCAL = 0
SELECTCASE ABL:TARGET:3
	CASE 0
		LOCAL = UP:40 / 2 > 50 - TALENT:100 * 20 + TALENT:101 * 20 && EXP:0 > 3 - TALENT:100 + TALENT:101
	CASE 1
		LOCAL = UP:40 / 2 > 250 - TALENT:100 * 60 + TALENT:101 * 60 && EXP:0 > 15 - TALENT:100 * 3 + TALENT:101 * 3
	CASE 2
		LOCAL = UP:40 / 2 > 800 - TALENT:100 * 150 + TALENT:101 * 150 && EXP:0 > 32 - TALENT:100 * 6 + TALENT:101 * 6 && EXP:4 > 0
	CASE 3
		LOCAL = UP:40 / 2 > 1600 - TALENT:100 * 400 + TALENT:101 * 400 && EXP:0 > 60 - TALENT:100 * 12 + TALENT:101 * 12
	CASE 4
		LOCAL = UP:40 / 2 > 2800 - TALENT:100 * 750 + TALENT:101 * 750 && EXP:0 > 100 - TALENT:100 * 20 + TALENT:101 * 20 && ABL:1 > 3 - TALENT:100 + TALENT:101
	CASE IS < 99
		LOCAL = UP:40 / 2 > 2800 - TALENT:100 * 750 + TALENT:101 * 750 && EXP:0 > (100 - TALENT:100 * 20 + TALENT:101 * 20) * (2 + ABL:3 - 4) * (10 + ABL:3 - 5) / 20 && ABL:1 > ABL:3 - 2 - TALENT:100 + TALENT:101
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(3)


;Ｖ感覚
@ABLUP_4T
LOCAL = 0
SELECTCASE ABL:TARGET:4
	CASE 0
		LOCAL = UP:41 / 2 > 50 - TALENT:102 * 20 + TALENT:103 * 20 && EXP:1 > 5 - TALENT:102 + TALENT:103
	CASE 1
		LOCAL = UP:41 / 2 > 250 - TALENT:102 * 60 + TALENT:103 * 60 && EXP:1 > 20 - TALENT:102 * 3 + TALENT:103 * 3
	CASE 2
		LOCAL = UP:41 / 2 > 800 - TALENT:102 * 150 + TALENT:103 * 150 && EXP:1 > 40 - TALENT:102 * 6 + TALENT:103 * 6 && EXP:7 > 5 - TALENT:102 + TALENT:103
	CASE 3
		LOCAL = UP:41 / 2 > 1600 - TALENT:102 * 400 + TALENT:103 * 400 && EXP:1 > 80 - TALENT:102 * 12 + TALENT:103 * 12 && EXP:7 > 15 - 3 * TALENT:102 + 3 * TALENT:103
	CASE 4
		LOCAL = UP:41 / 2 > 2800 - TALENT:102 * 750 + TALENT:103 * 750 && EXP:1 > 150 - TALENT:102 * 20 + TALENT:103 * 20 && EXP:7 > 30 - TALENT:102 * 5 + TALENT:103 * 5 
	CASE IS < 99
		LOCAL = UP:41 / 2 > 2800 - TALENT:102 * 750 + TALENT:103 * 750 && EXP:1 > (150 - TALENT:102 * 20 + TALENT:103 * 20) * (2 + ABL:4 - 4) * (10 + ABL:4 - 5) / 20 && EXP:7 > (30 - TALENT:102 * 5 + TALENT:103 * 5 ) * (2 + ABL:4 - 4) * (10 + ABL:4 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(4)


;Ａ感覚
@ABLUP_5T
LOCAL = 0
SELECTCASE ABL:TARGET:5
	CASE 0
		LOCAL = UP:42 / 2 > 50 - TALENT:104 * 20 + TALENT:105 * 20 && EXP:2 > 3 - TALENT:104 + TALENT:105
	CASE 1
		LOCAL = UP:42 / 2 > 250 - TALENT:104 * 60 + TALENT:105 * 60 && EXP:2 > 15 - TALENT:104 * 3 + TALENT:105 * 3
	CASE 2
		LOCAL = UP:42 / 2 > 800 - TALENT:104 * 150 + TALENT:105 * 150 && EXP:2 > 32 - TALENT:104 * 6 + TALENT:105 * 6 && EXP:50 > 0
	CASE 3
		LOCAL = UP:42 > 1600 - TALENT:104 * 400 + TALENT:105 * 400 && EXP:2 > 60 - TALENT:104 * 12 + TALENT:105 * 12
	CASE 4
		LOCAL = UP:42 > 2800 - TALENT:104 * 750 + TALENT:105 * 750 && EXP:2 > 100 - TALENT:104 * 20 + TALENT:105 * 20 && EXP:50 > 3
	CASE IS < 99
		LOCAL = UP:42 > 2800 - TALENT:104 * 750 + TALENT:105 * 750 && EXP:2 + 10 * EXP:53 > (100 - TALENT:104 * 20 + TALENT:105 * 20) * (2 + ABL:5 - 4) * (10 + ABL:5 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(5)


;Ｂ感覚
@ABLUP_6T
LOCAL = 0
SELECTCASE ABL:TARGET:6
	CASE 0
		LOCAL = UP:43 / 2 > 50 - TALENT:106 * 20 + TALENT:107 * 20 && EXP:3 > 3 - TALENT:106 + TALENT:107
	CASE 1
		LOCAL = UP:43 / 2 > 250 - TALENT:106 * 60 + TALENT:107 * 60 && EXP:3 > 15 - TALENT:106 * 3 + TALENT:107 * 3
	CASE 2
		LOCAL = UP:43 / 2 > 800 - TALENT:106 * 150 + TALENT:107 * 150 && EXP:3 > 32 - TALENT:106 * 6 + TALENT:107 * 6 && ABL:1 > 1
	CASE 3
		LOCAL = UP:43 / 2 > 1600 - TALENT:106 * 400 + TALENT:107 * 400 && EXP:3 > 60 - TALENT:106 * 12 + TALENT:107 * 12
	CASE 4
		LOCAL = UP:43 / 2 > 2800 - TALENT:106 * 750 + TALENT:107 * 750 && EXP:3 > 100 - TALENT:106 * 20 + TALENT:107 * 20 && ABL:1 > 3 - TALENT:106 + TALENT:107
	CASE IS < 99
		LOCAL = UP:43 / 2 > 2800 - TALENT:106 * 750 + TALENT:107 * 750 && EXP:3 > (100 - TALENT:106 * 20 + TALENT:107 * 20) * (2 + ABL:6 - 4) * (10 + ABL:6 - 5) / 20 && ABL:1 > ABL:6 - 2 - TALENT:106 + TALENT:107
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(6)


;奉仕精神、奉仕する時の達成ソースであがる
@ABLUP_7T
LOCAL = 0
SELECTCASE ABL:TARGET:7
	CASE 0
		LOCAL = SOURCE:31 > 100 - TALENT:63 * 20 + TALENT:65 * 20 && CFLAG:0 > 0 && EXP:21
	CASE 1
		LOCAL = SOURCE:31 > 300 - TALENT:63 * 50 + TALENT:65 * 50 && CFLAG:0 > 2 && EXP:21 > 10 + 3 * TALENT:65 - 3 * TALENT:63
	CASE 2
		LOCAL = SOURCE:31 > 700 - TALENT:63 * 100 + TALENT:65 * 100 && CFLAG:0 > 4 && EXP:21 > 30 + 6 * TALENT:65 - 6 * TALENT:63
	CASE 3
		LOCAL = SOURCE:31 > 1200 - TALENT:63 * 160 + TALENT:65 * 160 && CFLAG:0 > 7 && EXP:21 > 75 + 15 * TALENT:65 - 15 * TALENT:63
	CASE 4
		LOCAL = SOURCE:31 > 2000 - TALENT:63 * 300 + TALENT:65 * 300 && CFLAG:0 > 10 && EXP:21 > 150 + 30 * TALENT:65 - 30 * TALENT:63
	CASE IS < 99
		LOCAL = SOURCE:31 > 2000 - TALENT:63 * 300 + TALENT:65 * 300 && CFLAG:0 > 10 + 3 * (ABL:7 - 4) && EXP:21 > (150 + 30 * TALENT:65 - 30 * TALENT:63) * (2 + ABL:7 - 4) * (10 + ABL:7 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(7)


;あがる法則が思いつきません…一箇所だけ（調教者の行動判定）調教者の露出癖を参照してるから放置しても大丈夫かな？
;露出癖
@ABLUP_8T
LOCAL = 0
SELECTCASE ABL:TARGET:8
	CASE 0
		LOCAL = 0
	CASE 1
		LOCAL = 0
	CASE 2
		LOCAL = 0
	CASE 3
		LOCAL = 0
	CASE 4
		LOCAL = 0
	CASE IS < 99
		LOCAL = 0
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(8)


;ちょっと手抜きで調教対象のあがる法則をそのまま流用
;レズっ気
@ABLUP_9T
LOCAL = 0
SELECTCASE ABL:TARGET:9
	CASE 0
		LOCAL = SOURCE:11 + SOURCE:32 > 100 - TALENT:81 * 20 && EXP:40 > 20 - TALENT:81 * 10
	CASE 1
		LOCAL = SOURCE:11 + SOURCE:32 > 400 - TALENT:81 * 65 && EXP:40 > 60 - TALENT:81 * 15
	CASE 2
		LOCAL = SOURCE:11 + SOURCE:32 > 900 - TALENT:81 * 150 && EXP:40 > 150 - TALENT:81 * 32
	CASE 3
		LOCAL = SOURCE:11 + SOURCE:32 > 1750 - TALENT:81 * 350 && EXP:40 > 350 - TALENT:81 * 66
	CASE 4
		LOCAL = SOURCE:11 + SOURCE:32 > 3000 - TALENT:81 * 600 && EXP:40 > 600 - TALENT:81 * 120
	CASE IS < 99
		LOCAL = SOURCE:11 + SOURCE:32 > 3000 - TALENT:81 * 600 && EXP:40 > (600 - TALENT:81 * 120) * (2 + ABL:9 - 4) * (10 + ABL:9 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(9)


;ＢＬっ気
@ABLUP_10T
LOCAL = 0
SELECTCASE ABL:TARGET:10
	CASE 0
		LOCAL = SOURCE:11 + SOURCE:32 > 100 - TALENT:81 * 20 && EXP:41 > 20 - TALENT:81 * 10
	CASE 1
		LOCAL = SOURCE:11 + SOURCE:32 > 400 - TALENT:81 * 65 && EXP:41 > 60 - TALENT:81 * 15
	CASE 2
		LOCAL = SOURCE:11 + SOURCE:32 > 900 - TALENT:81 * 150 && EXP:41 > 150 - TALENT:81 * 32
	CASE 3
		LOCAL = SOURCE:11 + SOURCE:32 > 1750 - TALENT:81 * 350 && EXP:41 > 350 - TALENT:81 * 66
	CASE 4
		LOCAL = SOURCE:11 + SOURCE:32 > 3000 - TALENT:81 * 600 && EXP:41 > 600 - TALENT:81 * 120
	CASE IS < 99
		LOCAL = SOURCE:11 + SOURCE:32 > 3000 - TALENT:81 * 600 && EXP:41 > (600 - TALENT:81 * 120) * (2 + ABL:10 - 4) * (10 + ABL:10 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(10)


;精液中毒
@ABLUP_13T
LOCAL = 0
SELECTCASE ABL:TARGET:13
	CASE 0
		LOCAL = ABL:1 > 0 && EXP:20 > 15
	CASE 1
		LOCAL = ABL:1 > 1 && EXP:20 > 30
	CASE 2
		LOCAL = ABL:1 > 2 && EXP:20 > 60
	CASE 3
		LOCAL = ABL:1 > 3 && EXP:20 > 120
	CASE 4
		LOCAL = ABL:1 > 4 && EXP:20 > 240
	CASE IS < 99
		LOCAL = ABL:1 > ABL:13 && EXP:20 > 240 * (2 + ABL:13 - 4) * (10 + ABL:13 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(13)


;レズ中毒
@ABLUP_14T
LOCAL = 0
SELECTCASE ABL:TARGET:14
	CASE 0
		LOCAL = CFLAG:2 > 499 && EXP:40 > 100 - TALENT:81 * 20 && ABL:9 > 0
	CASE 1
		LOCAL = CFLAG:2 > 999 && EXP:40 > 200 - TALENT:81 * 40 && ABL:9 > 1
	CASE 2
		LOCAL = CFLAG:2 > 1499 && EXP:40 > 300 - TALENT:81 * 60 && ABL:9 > 2
	CASE 3
		LOCAL = CFLAG:2 > 1999 && EXP:40 > 400 - TALENT:81 * 80 && ABL:9 > 3
	CASE 4
		LOCAL = CFLAG:2 > 2499 && EXP:40 > 500 - TALENT:81 * 100 && ABL:9 > 4
	CASE IS < 99
		LOCAL = CFLAG:2 > 2499 + (ABL:14 - 5) * 500 && EXP:40 > (500 - TALENT:81 * 100) * (2 + ABL:14 - 4) * (10 + ABL:14 - 5) / 20 && ABL:9 > ABL:14
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(14)


;────────────────────────────────────
;調教能力の上昇
;────────────────────────────────────
;会話
@ABLUP_20T
LOCAL = 0
SELECTCASE ABL:TARGET:20
	CASE 0
		LOCAL = UP:6 + UP:8 * 2 + UP:7 - UP:9 > 150 - TALENT:50 * 20 + TALENT:51 * 20 && EXP:26 > 5
	CASE 1
		LOCAL = UP:6 + UP:8 * 2 + UP:7 - UP:9 > 350 - TALENT:50 * 50 + TALENT:51 * 50 && CFLAG:0 > 1 && EXP:26 > 10
	CASE 2
		LOCAL = UP:6 + UP:8 * 2 + UP:7 - UP:9 > 550 - TALENT:50 * 90 + TALENT:51 * 90 && CFLAG:0 > 3 - TALENT:50 + TALENT:51 && EXP:26 > 20
	CASE 3
		LOCAL = UP:6 + UP:8 * 2 + UP:7 - UP:9 > 750 - TALENT:50 * 140 + TALENT:51 * 140 && CFLAG:0 > 5 - TALENT:50 + TALENT:51 && EXP:26 > 30
	CASE 4
		LOCAL = UP:6 + UP:8 * 2 + UP:7 - UP:9 > 1000 - TALENT:50 * 200 + TALENT:51 * 200 && CFLAG:0 > 8 - TALENT:50 * 2 + TALENT:51 * 2 && EXP:26 > 40
	CASE IS < 99
		LOCAL = UP:6 + UP:8 * 2 + UP:7 - UP:9 > (1000 - TALENT:50 * 200 + TALENT:51 * 200) * (5 + (ABL:20 - 4)) / 5 && EXP:26 > 80 * (2 + ABL:20 - 4) * (10 + ABL:20 - 5) / 50
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(20)


;愛撫
@ABLUP_21T
LOCAL = 0
SELECTCASE ABL:TARGET:21
	CASE 0
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:13 > 400 - TALENT:50 * 40 + TALENT:51 * 40
	CASE 1
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:13 > 1000 - TALENT:50 * 100 + TALENT:51 * 100 && CFLAG:0 > 1
	CASE 2
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:13 > 1500 - TALENT:50 * 200 + TALENT:51 * 200 && CFLAG:0 > 3 - TALENT:50 + TALENT:51
	CASE 3
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:13 > 2500 - TALENT:50 * 300 + TALENT:51 * 300 && CFLAG:0 > 5 - TALENT:50 + TALENT:51
	CASE 4
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:13 > 4000 - TALENT:50 * 400 + TALENT:51 * 400 && CFLAG:0 > 8 - TALENT:50 * 2 + TALENT:51 * 2
	CASE IS < 99
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:13 > (4000 - TALENT:50 * 400 + TALENT:51 * 400) * (5 + (ABL:21 - 4)) / 5 && CFLAG:0 > ABL:21 * ABL:21 * 4 / 10 - TALENT:50 * 2 + TALENT:51 * 2
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(21)


;道具
@ABLUP_22T
LOCAL = 0
SELECTCASE ABL:TARGET:22
	CASE 0
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:12 > 200 - TALENT:50 * 40 + TALENT:51 * 40 && EXP:24 > 2
	CASE 1
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:12 > 700 - TALENT:50 * 100 + TALENT:51 * 100 && EXP:24 > 5
	CASE 2
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:12 > 1500 - TALENT:50 * 200 + TALENT:51 * 200 && EXP:24 > 15 - TALENT:50 * 2 + TALENT:51 * 2
	CASE 3
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:12 > 2500 - TALENT:50 * 300 + TALENT:51 * 300 && EXP:24 > 30 - TALENT:50 * 5 + TALENT:51 * 5
	CASE 4
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:12 > 4000 - TALENT:50 * 400 + TALENT:51 * 400 && EXP:24 > 55 - TALENT:50 * 8 + TALENT:51 * 8
	CASE IS < 99
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 - UP:12 > (4000 - TALENT:50 * 400 + TALENT:51 * 400) * (5 + (ABL:22 - 4)) / 5 && EXP:24 > (55 - TALENT:50 * 8 + TALENT:51 * 8) * (2 + ABL:22 - 4) * (10 + ABL:22 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(22)


;性交
@ABLUP_23T
LOCAL = 0
SELECTCASE ABL:TARGET:23
	CASE 0
		LOCAL = UP:0 + UP:1 + UP:2 + UP:5 + UP:8 - UP:7 - UP:13 > 200 - TALENT:50 * 40 + TALENT:51 * 40 && EXP:7 > 3
	CASE 1
		LOCAL = UP:0 + UP:1 + UP:2 + UP:5 + UP:8 - UP:7 - UP:13 > 700 - TALENT:50 * 100 + TALENT:51 * 100 && EXP:7 > 15 - TALENT:50 + TALENT:51
	CASE 2
		LOCAL = UP:0 + UP:1 + UP:2 + UP:5 + UP:8 - UP:7 - UP:13 > 1500 - TALENT:50 * 200 + TALENT:51 * 200 && EXP:7 > 30 - TALENT:50 * 2 + TALENT:51 * 2
	CASE 3
		LOCAL = UP:0 + UP:1 + UP:2 + UP:5 + UP:8 - UP:7 - UP:13 > 2500 - TALENT:50 * 300 + TALENT:51 * 300 && EXP:7 > 50 - TALENT:50 * 4 + TALENT:51 * 4
	CASE 4
		LOCAL = UP:0 + UP:1 + UP:2 + UP:5 + UP:8 - UP:7 - UP:13 > 4000 - TALENT:50 * 400 + TALENT:51 * 400 && EXP:7 > 80 - TALENT:50 * 6 + TALENT:51 * 6
	CASE IS < 99
		LOCAL = UP:0 + UP:1 + UP:2 + UP:5 + UP:8 - UP:7 - UP:13 > (4000 - TALENT:50 * 400 + TALENT:51 * 400) * (5 + (ABL:23 - 4)) / 5 && EXP:7 > (80 - TALENT:50 * 6 + TALENT:51 * 6) * (2 + ABL:23 - 4) * (10 + ABL:23 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(23)


;羞恥
@ABLUP_24T
LOCAL = 0
SELECTCASE ABL:TARGET:24
	CASE 0
		LOCAL = UP:5 + UP:11 - UP:13 > 150 - TALENT:50 * 30 + TALENT:51 * 30
	CASE 1
		LOCAL = UP:5 + UP:11 - UP:13 > 400 - TALENT:50 * 80 + TALENT:51 * 80 && CFLAG:0 > 1
	CASE 2
		LOCAL = UP:5 + UP:11 - UP:13 > 1000 - TALENT:50 * 140 + TALENT:51 * 140 && CFLAG:0 > 3 - TALENT:50 + TALENT:51
	CASE 3
		LOCAL = UP:5 + UP:11 - UP:13 > 1800 - TALENT:50 * 220 + TALENT:51 * 220 && CFLAG:0 > 5 - TALENT:50 + TALENT:51
	CASE 4
		LOCAL = UP:5 + UP:11 - UP:13 > 3000 - TALENT:50 * 350 + TALENT:51 * 350 && CFLAG:0 > 8 - TALENT:50 * 2 + TALENT:51 * 2
	CASE IS < 99
		LOCAL = UP:5 + UP:11 - UP:13 > (3000 - TALENT:50 * 350 + TALENT:51 * 350) * (5 + (ABL:24 - 4)) / 5 && CFLAG:0 > ABL:24 * ABL:24 * 4 / 10 - TALENT:50 * 2 + TALENT:51 * 2
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(24)


;奉仕
@ABLUP_25T
LOCAL = 0
SELECTCASE ABL:TARGET:25
	CASE 0
		LOCAL = UP:6 + UP:8 - UP:9 > 150 - TALENT:50 * 30 + TALENT:51 * 30
	CASE 1
		LOCAL = UP:6 + UP:8 - UP:9 > 400 - TALENT:50 * 80 + TALENT:51 * 80 && CFLAG:0 > 1
	CASE 2
		LOCAL = UP:6 + UP:8 - UP:9 > 1000 - TALENT:50 * 140 + TALENT:51 * 140 && CFLAG:0 > 3 - TALENT:50 + TALENT:51
	CASE 3
		LOCAL = UP:6 + UP:8 - UP:9 > 1800 - TALENT:50 * 220 + TALENT:51 * 220 && CFLAG:0 > 5 - TALENT:50 + TALENT:51
	CASE 4
		LOCAL = UP:6 + UP:8 - UP:9 > 3000 - TALENT:50 * 350 + TALENT:51 * 350 && CFLAG:0 > 8 - TALENT:50 * 2 + TALENT:51 * 2
	CASE IS < 99
		LOCAL = UP:6 + UP:8 - UP:9 > (3000 - TALENT:50 * 350 + TALENT:51 * 350) * (5 + (ABL:25 - 4)) / 5 && CFLAG:0 > ABL:25 * ABL:25 * 4 / 10 - TALENT:50 * 2 + TALENT:51 * 2
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(25)


;加虐
@ABLUP_26T
LOCAL = 0
SELECTCASE ABL:TARGET:26
	CASE 0
		LOCAL = UP:10 + UP:12 - UP:9 > 150 - TALENT:50 * 30 + TALENT:51 * 30
	CASE 1
		LOCAL = UP:10 + UP:12 - UP:9 > 400 - TALENT:50 * 80 + TALENT:51 * 80 && CFLAG:0 > 1
	CASE 2
		LOCAL = UP:10 + UP:12 - UP:9 > 1000 - TALENT:50 * 140 + TALENT:51 * 140 && CFLAG:0 > 3 - TALENT:50 + TALENT:51
	CASE 3
		LOCAL = UP:10 + UP:12 - UP:9 > 1800 - TALENT:50 * 220 + TALENT:51 * 220 && CFLAG:0 > 5 - TALENT:50 + TALENT:51
	CASE 4
		LOCAL = UP:10 + UP:12 - UP:9 > 3000 - TALENT:50 * 350 + TALENT:51 * 350 && CFLAG:0 > 8 - TALENT:50 * 2 + TALENT:51 * 2
	CASE IS < 99
		LOCAL = UP:10 + UP:12 - UP:9 > (3000 - TALENT:50 * 350 + TALENT:51 * 350) * (5 + (ABL:26 - 4)) / 5 && CFLAG:0 > ABL:26 * ABL:26 * 4 / 10 - TALENT:50 * 2 + TALENT:51 * 2
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(26)


;異常
@ABLUP_27T
LOCAL = 0
SELECTCASE ABL:TARGET:27
	CASE 0
		LOCAL = SOURCE:22 > 200 - TALENT:50 * 40 + TALENT:51 * 40 && EXP:50 > 1
	CASE 1
		LOCAL = SOURCE:22 > 500 - TALENT:50 * 100 + TALENT:51 * 100 && EXP:50 > 3 - TALENT:50 + TALENT:51
	CASE 2
		LOCAL = SOURCE:22 > 1200 - TALENT:50 * 240 + TALENT:51 * 240 && EXP:50 > 8 - TALENT:50 * 2 + TALENT:51 * 2
	CASE 3
		LOCAL = SOURCE:22 > 2000 - TALENT:50 * 400 + TALENT:51 * 400 && EXP:50 > 15 - TALENT:50 * 3 + TALENT:51 * 3
	CASE 4
		LOCAL = SOURCE:22 > 3500 - TALENT:50 * 700 + TALENT:51 * 700 && EXP:50 > 25 - TALENT:50 * 4 + TALENT:51 * 4
	CASE IS < 99
		LOCAL = SOURCE:22 > (3500 - TALENT:50 * 700 + TALENT:51 * 700) * (5 + (ABL:27 - 4)) / 5 && EXP:50 > (25 - TALENT:50 * 4 + TALENT:51 * 4) * (2 + ABL:27 - 4) * (10 + ABL:27 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(27)


;使役、放置放置～
@ABLUP_28T
LOCAL = 0
SELECTCASE ABL:TARGET:28
	CASE 0
		LOCAL = 0
	CASE 1
		LOCAL = 0
	CASE 2
		LOCAL = 0
	CASE 3
		LOCAL = 0
	CASE 4
		LOCAL = 0
	CASE IS < 99
		LOCAL = 0
ENDSELECT
SIF LOCAL
	CALL ABLUP_TARGET(28)


@ABLUP_TARGET(ARG)
ABL:TARGET:ARG++
CALL KOJO_EVENT(24, ARG)
PRINTFORMW %CALLNAME:TARGET%的%ABLNAME:ARG%\@ 20 <= ARG && ARG <= 29 ? 技能 # \@变成了\@ ABL:TARGET:ARG > 9 ? {ABL:TARGET:ARG} # %TOFULL(TOSTR(ABL:TARGET:ARG))% \@
