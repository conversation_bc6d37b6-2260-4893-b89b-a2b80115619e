﻿;初期設定
@DUNGEON_CAVE
;ダンジョンタイプ
FLAG:1709 = 1
;ダンジョン難易度補正
FLAG:1705 = FLAG:1704
IF FLAG:1704 == 1
CALL DUNGEON_MAP_PRODUCE_CAVE
ELSE
CALL DUNGEON_MAP_PRODUCE_ELEVATOR
ENDIF

;階層構造
@DUNGEON_CAVE_STAIRS
FLAG:1704 += 1
FLAG:1705 += 1
IF FLAG:1704 % 5 == 0
	CALL DUNGEON_MAP_PRODUCE_ELEVATOR
ELSE
	CALL DUNGEON_MAP_PRODUCE_CAVE
ENDIF

;イベントパネルを踏むとここが呼び出されます。
@DUNGEON_EVENT_RYUDUF
;FLAG:1741のビット解説
;1=アイテムゲットイベント
;2=

IF FLAG:1704 == 1 && !(FLAG:1741 & 1)
	PRINTW 
	PRINTFORML 迷宮の探索を開始した%CALLNAME:MASTER%の目の前には、打ち捨てられたイタリア製戦車があった。
	PRINTFORMW 攻撃には微塵の役にも立ちそうにないが、逃げ足は異常に速そうだ。
	PRINTL 
	PRINTL 戦車の中からは力の限りふれば敵をひるませ逃亡に役立ちそうな白旗（イタリア製）、
	PRINTW 食べれば勇気百倍、元気がもりもり湧いてくるピッツァ（イタリア製）も見つかった。
	PRINTW いずれも迷宮の探索には役立つだろう。
	PRINTL 
	PRINTFORMW %CALLNAME:MASTER%は見知らぬイタリア人に感謝しつつ、アイテムを頂くことにした。
	PRINTL 
	DRAWLINE
	ITEM:撤退用戦車 = 1
	ITEM:白旗 = 2
	ITEM:パスタ = 1
	FLAG:1741 |= 1
	FLAG:M -= 128
	FLAG:1711 = 1
ELSEIF FLAG:1704 % 5 == 0
	IF FLAG:1721 < FLAG:1704 / 5
		PRINTW 転送機を稼動させた。
		FLAG:1721 = FLAG:1704 / 5
	ELSE
		PRINTL 転送機はすでに稼動している。
		PRINTL 拠点に帰還しますか？
		PRINTL [1] - はい
		PRINTL [2] - いいえ
		DRAWLINE
		INPUT
		IF RESULT == 1
			CALL ESCAPE_DUNGEON
			RETURN 0
		ELSE
			;移動メニューに移ります
			FLAG:1711 = 1
		ENDIF
	ENDIF
ELSE
	CALL DUNGEON_RANDOM_EVENT
	FLAG:M -= 128
ENDIF
