﻿@PLAYER_ACT_EXTRA_PRI
CALL _PLAYER_ACT_EXTRA_SUB1(3)
CALL _PLAYER_ACT_EXTRA_SUB1(4)


@PLAYER_ACT_EXTRA_LATER
;追加愛撫の見切
SIF !GETBIT(CFLAG:MASTER:319,1)
	CALL _PLAYER_ACT_EXTRA_SUB1(0)
CALL _PLAYER_ACT_EXTRA_SUB1(1)
CALL _PLAYER_ACT_EXTRA_SUB1(2)


@_PLAYER_ACT_EXTRA_SUB1(ARG)
CALLFORM ACTM_EXTRA_ABLE{ARG}
SIF RESULT
	CALLFORM ACT_EXTRA_M{ARG}
SIF RESULT
	SETBIT TFLAG:305, ARG
TFLAG:306 = TFLAG:305


@PLAYER_ACT_EXTRA_MESSAGE
LOCAL:1 = 0
FOR LOCAL, 0, 5
	SIF GETBIT(TFLAG:305, LOCAL)
		CALL KOJO_ACT_EXTRA(LOCAL)
NEXT
FOR LOCAL, 0, 5
	IF GETBIT(TFLAG:306, LOCAL)
		SIF LOCAL:1++ == 0
			PRINTL 
		CALLFORM ACT_EXTRA_MESSAGE_M{LOCAL}
	ENDIF
NEXT
