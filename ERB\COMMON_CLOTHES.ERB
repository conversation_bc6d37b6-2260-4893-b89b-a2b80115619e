﻿;-------------------------------------------------
;汎用関数置き場
;衣装関連(衣装名など)
;-------------------------------------------------

;-------------------------------------------------
;関数名:COSPLAY
;概　要:服装名取得関数(カスタム衣装)
;引　数:ARG:0…キャラ登録番号(MASTERかTARGET)
;　　　:ARG:1…省略可。真の場合、短縮名を返す
;戻り値:服装名(文字列)
;備　考:文中関数
;キャラ登録番号から、カスタム衣装の名前を返す
;-------------------------------------------------
@COSPLAY(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE CFLAG:(ARG:0):79
	CASE 1
		RETURNF "束缚装"
	CASE 2
		RETURNF "和服"
	CASE 3
		RETURNF "女仆装"
	CASE 4
		RETURNF "中国服"
	CASE 5
		RETURNF "水手服"
	CASE 6
		RETURNF "学校泳衣"
	CASE 7
		RETURNF "兔女郎服"
	CASE 8
		RETURNF "体操服"
ENDSELECT


;-------------------------------------------------
;関数名:GLOVES
;概　要:服装名取得関数(特殊)
;引　数:ARG:0…キャラ登録番号(MASTERかTARGET)
;　　　:ARG:1…省略可。真の場合、短縮名を返す
;戻り値:服装名(文字列)
;備　考:文中関数
;キャラ登録番号から、特殊装飾品の名前を返す
;-------------------------------------------------
@GLOVES(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE CFLAG:(ARG:0):80
	;手袋
	CASE 1
		RETURNF "手套"
	;袈裟
	CASE 2
		RETURNF "袈裟"
	;ストール
	CASE 3
		RETURNF "披肩"
ENDSELECT


;-------------------------------------------------
;関数名:SOCKS
;概　要:服装名取得関数(靴下)
;引　数:ARG:0…キャラ登録番号(MASTERかTARGET)
;　　　:ARG:1…省略可。真の場合、短縮名を返す
;戻り値:服装名(文字列)
;備　考:文中関数
;キャラ登録番号から、靴下の名前を返す
;-------------------------------------------------
@SOCKS(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE CFLAG:(ARG:0):81
	;靴下
	CASE 1
		RETURNF "袜子"
	;足袋
	CASE 2
		RETURNF "短布袜"
	;ニーソ
	CASE 3
		RETURNF "过膝袜"
ENDSELECT


;-------------------------------------------------
;関数名:INNER_B
;概　要:服装名取得関数(下半身下着)
;引　数:ARG:0…キャラ登録番号(MASTERかTARGET)
;　　　:ARG:1…省略可。真の場合、短縮名を返す
;戻り値:服装名(文字列)
;備　考:文中関数
;キャラ登録番号から、下半身下着の名前を返す
;-------------------------------------------------
@INNER_B(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE CFLAG:(ARG:0):82
	;パンティ
	CASE 1
		RETURNF "三角裤"
	;トランクス
	CASE 2
		RETURNF "四角裤"
	;下穿き
	CASE 3
		RETURNF "内裤"
ENDSELECT


;-------------------------------------------------
;関数名:INNER_T
;概　要:服装名取得関数(上半身下着)
;引　数:ARG:0…キャラ登録番号(MASTERかTARGET)
;　　　:ARG:1…省略可。真の場合、短縮名を返す
;戻り値:服装名(文字列)
;備　考:文中関数
;キャラ登録番号から、上半身下着の名前を返す
;-------------------------------------------------
@INNER_T(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE CFLAG:(ARG:0):83
	;ブラジャー
	CASE 1
		RETURNF "胸罩"
	;さらし
	CASE 2
		RETURNF "裹胸布"
	;シャツ
	CASE 3
		RETURNF "衬衫"
ENDSELECT


;-------------------------------------------------
;関数名:OUTER_B
;概　要:服装名取得関数(下半身上着)
;引　数:ARG:0…キャラ登録番号(MASTERかTARGET)
;　　　:ARG:1…省略可。真の場合、短縮名を返す
;戻り値:服装名(文字列)
;備　考:文中関数
;キャラ登録番号から、上半身下着の名前を返す
;-------------------------------------------------
@OUTER_B(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE CFLAG:(ARG:0):84
	;女袴
	CASE 1
		RETURNF "女袴"
	;袴
	CASE 2
		RETURNF "袴"
	;ズボン
	CASE 3
		RETURNF "裤子"
ENDSELECT


;-------------------------------------------------
;関数名:OUTER_T
;概　要:服装名取得関数(上半身上着)
;引　数:ARG:0…キャラ登録番号(MASTERかTARGET)
;　　　:ARG:1…省略可。真の場合、短縮名を返す
;戻り値:服装名(文字列)
;備　考:文中関数
;キャラ登録番号から、上半身上着の名前を返す
;-------------------------------------------------
@OUTER_T(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE CFLAG:(ARG:0):85
	;和服
	CASE 1
		RETURNF "和服"
	;ジャケット
	CASE 2
		RETURNF "夹克"
	;コート
	CASE 3
		RETURNF "外套"
ENDSELECT


;-------------------------------------------------
;関数名:DRESS
;概　要:服装名取得関数(全身上着)
;引　数:ARG:0…キャラ登録番号(MASTERかTARGET)
;　　　:ARG:1…省略可。真の場合、短縮名を返す
;戻り値:服装名(文字列)
;備　考:文中関数
;キャラ登録番号から、全身上着の名前を返す
;-------------------------------------------------
@DRESS(ARG:0, ARG:1)
#FUNCTIONS
SELECTCASE CFLAG:(ARG:0):86
	;着物
	CASE 1
		RETURNF "衣服"
	;ワンピース
	CASE 2
		RETURNF "连衣裙"
ENDSELECT
