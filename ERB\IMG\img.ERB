﻿;能力狀態欄頭像
@CHARA_FACE(ARG)
SIF !FLAG:666
RETURN 0
	IF NO:ARG == 0
		IF TALENT:ARG:男性 == 1
			HTML_PRINT "<shape type='space' param='3400'><img src='chara_M' width='2800' height='1400' ypos='1600'>"
		ELSEIF TALENT:ARG:扶她 == 1
			HTML_PRINT "<shape type='space' param='3400'><img src='chara_U' width='2800' height='1400' ypos='1600'>"
		ELSE
		HTML_PRINT "<shape type='space' param='3400'><img src='chara_F' width='2800' height='1400' ypos='1600'>"
		ENDIF
	ELSEIF NO:ARG == 1
		HTML_PRINT "<shape type='space' param='3400'><img src='chara1' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 2
		HTML_PRINT "<shape type='space' param='3400'><img src='chara2' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 3
		HTML_PRINT "<shape type='space' param='3400'><img src='chara3' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 4
		HTML_PRINT "<shape type='space' param='3400'><img src='chara4' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 5
		HTML_PRINT "<shape type='space' param='3400'><img src='chara5' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 6
		HTML_PRINT "<shape type='space' param='3400'><img src='chara6' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 7
		HTML_PRINT "<shape type='space' param='3400'><img src='chara7' width='2800' height='1400' ypos='1600'>"	
	ELSEIF NO:ARG == 8
		HTML_PRINT "<shape type='space' param='3400'><img src='chara8' width='2800' height='1400' ypos='1600'>"		
	ELSEIF NO:ARG == 9
		HTML_PRINT "<shape type='space' param='3400'><img src='chara9' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 10
		HTML_PRINT "<shape type='space' param='3400'><img src='chara10' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 11
		HTML_PRINT "<shape type='space' param='3400'><img src='chara11' width='2800' height='1400' ypos='1600'>"	
	ELSEIF NO:ARG == 12
		HTML_PRINT "<shape type='space' param='3400'><img src='chara12' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 13
		HTML_PRINT "<shape type='space' param='3400'><img src='chara13' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 14
		HTML_PRINT "<shape type='space' param='3400'><img src='chara14' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 15
		HTML_PRINT "<shape type='space' param='3400'><img src='chara15' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 16
		HTML_PRINT "<shape type='space' param='3400'><img src='chara16' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 17
		HTML_PRINT "<shape type='space' param='3400'><img src='chara17' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 18
		HTML_PRINT "<shape type='space' param='3400'><img src='chara18' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 19
		HTML_PRINT "<shape type='space' param='3400'><img src='chara19' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 20
		HTML_PRINT "<shape type='space' param='3400'><img src='chara20' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 21
		HTML_PRINT "<shape type='space' param='3400'><img src='chara21' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 22
		HTML_PRINT "<shape type='space' param='3400'><img src='chara22' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 23
		HTML_PRINT "<shape type='space' param='3400'><img src='chara23' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 24
		HTML_PRINT "<shape type='space' param='3400'><img src='chara24' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 25
		HTML_PRINT "<shape type='space' param='3400'><img src='chara25' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 26
		HTML_PRINT "<shape type='space' param='3400'><img src='chara26' width='2800' height='1400' ypos='1600'>"		
	ELSEIF NO:ARG == 27
		HTML_PRINT "<shape type='space' param='3400'><img src='chara27' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 28
		HTML_PRINT "<shape type='space' param='3400'><img src='chara28' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 29
		HTML_PRINT "<shape type='space' param='3400'><img src='chara29' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 30
		HTML_PRINT "<shape type='space' param='3400'><img src='chara30' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 31
		HTML_PRINT "<shape type='space' param='3400'><img src='chara31' width='2800' height='1400' ypos='1600'>"		
	ELSEIF NO:ARG == 32
		HTML_PRINT "<shape type='space' param='3400'><img src='chara32' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 33
		HTML_PRINT "<shape type='space' param='3400'><img src='chara33' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 34
		HTML_PRINT "<shape type='space' param='3400'><img src='chara34' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 35
		HTML_PRINT "<shape type='space' param='3400'><img src='chara35' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 36
		HTML_PRINT "<shape type='space' param='3400'><img src='chara36' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 37
		HTML_PRINT "<shape type='space' param='3400'><img src='chara37' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 38
		HTML_PRINT "<shape type='space' param='3400'><img src='chara38' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 39
		HTML_PRINT "<shape type='space' param='3400'><img src='chara39' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 40
		HTML_PRINT "<shape type='space' param='3400'><img src='chara40' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 41
		HTML_PRINT "<shape type='space' param='3400'><img src='chara41' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 42
		HTML_PRINT "<shape type='space' param='3400'><img src='chara42' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 43
		HTML_PRINT "<shape type='space' param='3400'><img src='chara43' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 44
		HTML_PRINT "<shape type='space' param='3400'><img src='chara44' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 45
		HTML_PRINT "<shape type='space' param='3400'><img src='chara45' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 46
		HTML_PRINT "<shape type='space' param='3400'><img src='chara46' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 47
		HTML_PRINT "<shape type='space' param='3400'><img src='chara47' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 48
		HTML_PRINT "<shape type='space' param='3400'><img src='chara48' width='2800' height='1400' ypos='1600'>"		
	ELSEIF NO:ARG == 49
		HTML_PRINT "<shape type='space' param='3400'><img src='chara49' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 50
		HTML_PRINT "<shape type='space' param='3400'><img src='chara50' width='2800' height='1400' ypos='1600'>"		
	ELSEIF NO:ARG == 51
		HTML_PRINT "<shape type='space' param='3400'><img src='chara51' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 52
		HTML_PRINT "<shape type='space' param='3400'><img src='chara52' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 53
		HTML_PRINT "<shape type='space' param='3400'><img src='chara53' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 54
		HTML_PRINT "<shape type='space' param='3400'><img src='chara54' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 55
		HTML_PRINT "<shape type='space' param='3400'><img src='chara55' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 56
		HTML_PRINT "<shape type='space' param='3400'><img src='chara56' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 57
		HTML_PRINT "<shape type='space' param='3400'><img src='chara57' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 58
		HTML_PRINT "<shape type='space' param='3400'><img src='chara58' width='2800' height='1400' ypos='1600'>"		
	ELSEIF NO:ARG == 59
		HTML_PRINT "<shape type='space' param='3400'><img src='chara59' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 60
		HTML_PRINT "<shape type='space' param='3400'><img src='chara60' width='2800' height='1400' ypos='1600'>"		
	ELSEIF NO:ARG == 61
		HTML_PRINT "<shape type='space' param='3400'><img src='chara61' width='2800' height='1400' ypos='1600'>"
	ELSEIF NO:ARG == 62
		HTML_PRINT "<shape type='space' param='3400'><img src='chara62' width='2800' height='1400' ypos='1600'>"
	ELSE
		HTML_PRINT "<shape type='space' param='3400'><img src='缺' width='2800' height='1400' ypos='1600'>"	
	ENDIF

;調教狀態欄頭像
@CHARA_FACE_T
;調教者頭像
	IF NO:TARGET == 0
		IF TALENT:TARGET:男性 == 1
			HTML_PRINT "<shape type='space' param='1100'><img src='chara_M' width='1400' height='700' ypos='-250'>"
		ELSEIF TALENT:TARGET:扶她 == 1
			HTML_PRINT "<shape type='space' param='1100'><img src='chara_U' width='1400' height='700' ypos='-250'>"
		ELSE
			HTML_PRINT "<shape type='space' param='1100'><img src='chara_F' width='1400' height='700' ypos='-250'>"
		ENDIF	
	ELSEIF NO:TARGET == 1
		HTML_PRINT "<shape type='space' param='1100'><img src='chara1' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 2
		HTML_PRINT "<shape type='space' param='1100'><img src='chara2' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 3
		HTML_PRINT "<shape type='space' param='1100'><img src='chara2' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 4
		HTML_PRINT "<shape type='space' param='1100'><img src='chara4' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 5
		HTML_PRINT "<shape type='space' param='1100'><img src='chara5' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 6
		HTML_PRINT "<shape type='space' param='1100'><img src='chara6' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 7
		HTML_PRINT "<shape type='space' param='1100'><img src='chara7' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 8
		HTML_PRINT "<shape type='space' param='1100'><img src='chara8' width='1400' height='700' ypos='-250'>"	
	ELSEIF NO:TARGET == 9
		HTML_PRINT "<shape type='space' param='1100'><img src='chara9' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 10
		HTML_PRINT "<shape type='space' param='1100'><img src='chara10' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 11
		HTML_PRINT "<shape type='space' param='1100'><img src='chara11' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 12
		HTML_PRINT "<shape type='space' param='1100'><img src='chara12' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 13
		HTML_PRINT "<shape type='space' param='1100'><img src='chara13' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 14
		HTML_PRINT "<shape type='space' param='1100'><img src='chara14' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 15
		HTML_PRINT "<shape type='space' param='1100'><img src='chara15' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 16
		HTML_PRINT "<shape type='space' param='1100'><img src='chara16' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 17
		HTML_PRINT "<shape type='space' param='1100'><img src='chara17' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 18
		HTML_PRINT "<shape type='space' param='1100'><img src='chara18' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 19
		HTML_PRINT "<shape type='space' param='1100'><img src='chara19' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 20
		HTML_PRINT "<shape type='space' param='1100'><img src='chara20' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 21
		HTML_PRINT "<shape type='space' param='1100'><img src='chara21' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 22
		HTML_PRINT "<shape type='space' param='1100'><img src='chara22' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 23
		HTML_PRINT "<shape type='space' param='1100'><img src='chara23' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 24
		HTML_PRINT "<shape type='space' param='1100'><img src='chara24' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 25
		HTML_PRINT "<shape type='space' param='1100'><img src='chara25' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 26
		HTML_PRINT "<shape type='space' param='1100'><img src='chara26' width='1400' height='700' ypos='-250'>"		
	ELSEIF NO:TARGET == 27
		HTML_PRINT "<shape type='space' param='1100'><img src='chara27' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 28
		HTML_PRINT "<shape type='space' param='1100'><img src='chara28' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 29
		HTML_PRINT "<shape type='space' param='1100'><img src='chara29' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 30
		HTML_PRINT "<shape type='space' param='1100'><img src='chara30' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 31
		HTML_PRINT "<shape type='space' param='1100'><img src='chara31' width='1400' height='700' ypos='-250'>"		
	ELSEIF NO:TARGET == 32
		HTML_PRINT "<shape type='space' param='1100'><img src='chara32' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 33
		HTML_PRINT "<shape type='space' param='1100'><img src='chara33' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 34
		HTML_PRINT "<shape type='space' param='1100'><img src='chara34' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 35
		HTML_PRINT "<shape type='space' param='1100'><img src='chara35' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 36
		HTML_PRINT "<shape type='space' param='1100'><img src='chara36' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 37
		HTML_PRINT "<shape type='space' param='1100'><img src='chara37' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 38
		HTML_PRINT "<shape type='space' param='1100'><img src='chara38' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 39
		HTML_PRINT "<shape type='space' param='1100'><img src='chara39' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 40
		HTML_PRINT "<shape type='space' param='1100'><img src='chara40' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 41
		HTML_PRINT "<shape type='space' param='1100'><img src='chara41' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 42
		HTML_PRINT "<shape type='space' param='1100'><img src='chara42' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 43
		HTML_PRINT "<shape type='space' param='1100'><img src='chara43' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 44
		HTML_PRINT "<shape type='space' param='1100'><img src='chara44' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 45
		HTML_PRINT "<shape type='space' param='1100'><img src='chara45' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 46
		HTML_PRINT "<shape type='space' param='1100'><img src='chara46' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 47
		HTML_PRINT "<shape type='space' param='1100'><img src='chara47' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 48
		HTML_PRINT "<shape type='space' param='1100'><img src='chara48' width='1400' height='700' ypos='-250'>"		
	ELSEIF NO:TARGET == 49
		HTML_PRINT "<shape type='space' param='1100'><img src='chara49' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 50
		HTML_PRINT "<shape type='space' param='1100'><img src='chara50' width='1400' height='700' ypos='-250'>"		
	ELSEIF NO:TARGET == 51
		HTML_PRINT "<shape type='space' param='1100'><img src='chara51' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 52
		HTML_PRINT "<shape type='space' param='1100'><img src='chara52' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 53
		HTML_PRINT "<shape type='space' param='1100'><img src='chara53' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 54
		HTML_PRINT "<shape type='space' param='1100'><img src='chara54' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 55
		HTML_PRINT "<shape type='space' param='1100'><img src='chara55' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 56
		HTML_PRINT "<shape type='space' param='1100'><img src='chara56' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 57
		HTML_PRINT "<shape type='space' param='1100'><img src='chara57' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 58
		HTML_PRINT "<shape type='space' param='1100'><img src='chara58' width='1400' height='700' ypos='-250'>"		
	ELSEIF NO:TARGET == 59
		HTML_PRINT "<shape type='space' param='1100'><img src='chara59' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 60
		HTML_PRINT "<shape type='space' param='1100'><img src='chara60' width='1400' height='700' ypos='-250'>"		
	ELSEIF NO:TARGET == 61
		HTML_PRINT "<shape type='space' param='1100'><img src='chara61' width='1400' height='700' ypos='-250'>"
	ELSEIF NO:TARGET == 62
		HTML_PRINT "<shape type='space' param='1100'><img src='chara62' width='1400' height='700' ypos='-250'>"
		
	ELSE
		HTML_PRINT "<shape type='space' param='1100'><img src='空' width='1400' height='700' ypos='-250'>"	
	ENDIF
;助手1頭像
IF ASSI:1 > 0
	IF NO:(ASSI:1) == 0
		IF TALENT:ASSI:男性 == 1
			HTML_PRINT "<shape type='space' param='2700'><img src='chara_M' width='1400' height='700' ypos='-350'>"
		ELSEIF TALENT:ASSI:扶她 == 1
			HTML_PRINT "<shape type='space' param='2700'><img src='chara_U' width='1400' height='700' ypos='-350'>"
		ELSE
			HTML_PRINT "<shape type='space' param='2700'><img src='chara_F' width='1400' height='700' ypos='-350'>"
		ENDIF
	ELSEIF NO:(ASSI:1) == 1
			HTML_PRINT "<shape type='space' param='2700'><img src='chara1' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 2
			HTML_PRINT "<shape type='space' param='2700'><img src='chara2' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 3
			HTML_PRINT "<shape type='space' param='2700'><img src='chara3' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 4
			HTML_PRINT "<shape type='space' param='2700'><img src='chara4' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 5
			HTML_PRINT "<shape type='space' param='2700'><img src='chara5' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 6
			HTML_PRINT "<shape type='space' param='2700'><img src='chara6' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 7
			HTML_PRINT "<shape type='space' param='2700'><img src='chara7' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 8
			HTML_PRINT "<shape type='space' param='2700'><img src='chara8' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 9
			HTML_PRINT "<shape type='space' param='2700'><img src='chara9' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 10
			HTML_PRINT "<shape type='space' param='2700'><img src='chara10' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 11
			HTML_PRINT "<shape type='space' param='2700'><img src='chara11' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 12
			HTML_PRINT "<shape type='space' param='2700'><img src='chara12' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 13
			HTML_PRINT "<shape type='space' param='2700'><img src='chara13' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 14
			HTML_PRINT "<shape type='space' param='2700'><img src='chara14' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 15
		HTML_PRINT "<shape type='space' param='2700'><img src='chara15' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 16
		HTML_PRINT "<shape type='space' param='2700'><img src='chara16' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 17
		HTML_PRINT "<shape type='space' param='2700'><img src='chara17' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 18
		HTML_PRINT "<shape type='space' param='2700'><img src='chara18' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 19
		HTML_PRINT "<shape type='space' param='2700'><img src='chara19' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 20
		HTML_PRINT "<shape type='space' param='2700'><img src='chara20' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 21
		HTML_PRINT "<shape type='space' param='2700'><img src='chara21' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 22
		HTML_PRINT "<shape type='space' param='2700'><img src='chara22' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 23
		HTML_PRINT "<shape type='space' param='2700'><img src='chara23' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 24
		HTML_PRINT "<shape type='space' param='2700'><img src='chara24' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 25
		HTML_PRINT "<shape type='space' param='2700'><img src='chara25' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 26
		HTML_PRINT "<shape type='space' param='2700'><img src='chara26' width='1400' height='700' ypos='-350'>"		
	ELSEIF NO:(ASSI:1) == 27
		HTML_PRINT "<shape type='space' param='2700'><img src='chara27' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 28
		HTML_PRINT "<shape type='space' param='2700'><img src='chara28' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 29
		HTML_PRINT "<shape type='space' param='2700'><img src='chara29' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 30
		HTML_PRINT "<shape type='space' param='2700'><img src='chara30' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 31
		HTML_PRINT "<shape type='space' param='2700'><img src='chara31' width='1400' height='700' ypos='-350'>"		
	ELSEIF NO:(ASSI:1) == 32
		HTML_PRINT "<shape type='space' param='2700'><img src='chara32' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 33
		HTML_PRINT "<shape type='space' param='2700'><img src='chara33' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 34
		HTML_PRINT "<shape type='space' param='2700'><img src='chara34' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 35
		HTML_PRINT "<shape type='space' param='2700'><img src='chara35' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 36
		HTML_PRINT "<shape type='space' param='2700'><img src='chara36' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 37
		HTML_PRINT "<shape type='space' param='2700'><img src='chara37' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 38
		HTML_PRINT "<shape type='space' param='2700'><img src='chara38' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 39
		HTML_PRINT "<shape type='space' param='2700'><img src='chara39' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 40
		HTML_PRINT "<shape type='space' param='2700'><img src='chara40' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 41
		HTML_PRINT "<shape type='space' param='2700'><img src='chara41' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 42
		HTML_PRINT "<shape type='space' param='2700'><img src='chara42' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 43
		HTML_PRINT "<shape type='space' param='2700'><img src='chara43' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 44
		HTML_PRINT "<shape type='space' param='2700'><img src='chara44' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 45
		HTML_PRINT "<shape type='space' param='2700'><img src='chara45' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 46
		HTML_PRINT "<shape type='space' param='2700'><img src='chara46' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 47
		HTML_PRINT "<shape type='space' param='2700'><img src='chara47' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 48
		HTML_PRINT "<shape type='space' param='2700'><img src='chara48' width='1400' height='700' ypos='-350'>"		
	ELSEIF NO:(ASSI:1) == 49
		HTML_PRINT "<shape type='space' param='2700'><img src='chara49' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 50
		HTML_PRINT "<shape type='space' param='2700'><img src='chara50' width='1400' height='700' ypos='-350'>"		
	ELSEIF NO:(ASSI:1) == 51
		HTML_PRINT "<shape type='space' param='2700'><img src='chara51' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 52
		HTML_PRINT "<shape type='space' param='2700'><img src='chara52' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 53
		HTML_PRINT "<shape type='space' param='2700'><img src='chara53' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 54
		HTML_PRINT "<shape type='space' param='2700'><img src='chara54' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 55
		HTML_PRINT "<shape type='space' param='2700'><img src='chara55' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 56
		HTML_PRINT "<shape type='space' param='2700'><img src='chara56' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 57
		HTML_PRINT "<shape type='space' param='2700'><img src='chara57' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 58
		HTML_PRINT "<shape type='space' param='2700'><img src='chara58' width='1400' height='700' ypos='-350'>"		
	ELSEIF NO:(ASSI:1) == 59
		HTML_PRINT "<shape type='space' param='2700'><img src='chara59' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 60
		HTML_PRINT "<shape type='space' param='2700'><img src='chara60' width='1400' height='700' ypos='-350'>"		
	ELSEIF NO:(ASSI:1) == 61
		HTML_PRINT "<shape type='space' param='2700'><img src='chara61' width='1400' height='700' ypos='-350'>"
	ELSEIF NO:(ASSI:1) == 62
		HTML_PRINT "<shape type='space' param='2700'><img src='chara62' width='1400' height='700' ypos='-350'>"
	ELSE
			HTML_PRINT "<shape type='space' param='2700'><img src='缺' width='1400' height='700' ypos='-350'>"
	ENDIF
ELSE
	HTML_PRINT "<shape type='space' param='2700'><img src='空' width='1400' height='700' ypos='-350'>"
ENDIF
;助手2頭像
IF ASSI:2 > 0
	IF NO:(ASSI:2) == 0
		IF TALENT:ASSI:男性 == 1
			HTML_PRINT "<shape type='space' param='4250'><img src='chara_M' width='1400' height='700' ypos='-450'>"
		ELSEIF TALENT:ASSI:扶她 == 1
			HTML_PRINT "<shape type='space' param='4250'><img src='chara_U' width='1400' height='700' ypos='-450'>"
		ELSE
			HTML_PRINT "<shape type='space' param='4250'><img src='chara_F' width='1400' height='700' ypos='-450'>"
		ENDIF
	ELSEIF NO:(ASSI:2) == 1
			HTML_PRINT "<shape type='space' param='4250'><img src='chara1' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 2
			HTML_PRINT "<shape type='space' param='4250'><img src='chara2' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 3
			HTML_PRINT "<shape type='space' param='4250'><img src='chara3' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 4
			HTML_PRINT "<shape type='space' param='4250'><img src='chara4' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 5
			HTML_PRINT "<shape type='space' param='4250'><img src='chara5' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 6
			HTML_PRINT "<shape type='space' param='4250'><img src='chara6' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 7
			HTML_PRINT "<shape type='space' param='4250'><img src='chara7' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 8
			HTML_PRINT "<shape type='space' param='4250'><img src='chara8' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 9
			HTML_PRINT "<shape type='space' param='4250'><img src='chara9' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 10
			HTML_PRINT "<shape type='space' param='4250'><img src='chara10' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 11
			HTML_PRINT "<shape type='space' param='4250'><img src='chara11' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 12
			HTML_PRINT "<shape type='space' param='4250'><img src='chara12' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 13
			HTML_PRINT "<shape type='space' param='4250'><img src='chara13' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 14
			HTML_PRINT "<shape type='space' param='4250'><img src='chara14' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 15
		HTML_PRINT "<shape type='space' param='4250'><img src='chara15' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 16
		HTML_PRINT "<shape type='space' param='4250'><img src='chara16' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 17
		HTML_PRINT "<shape type='space' param='4250'><img src='chara17' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 18
		HTML_PRINT "<shape type='space' param='4250'><img src='chara18' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 19
		HTML_PRINT "<shape type='space' param='4250'><img src='chara19' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 20
		HTML_PRINT "<shape type='space' param='4250'><img src='chara20' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 21
		HTML_PRINT "<shape type='space' param='4250'><img src='chara21' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 22
		HTML_PRINT "<shape type='space' param='4250'><img src='chara22' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 23
		HTML_PRINT "<shape type='space' param='4250'><img src='chara23' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 24
		HTML_PRINT "<shape type='space' param='4250'><img src='chara24' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 25
		HTML_PRINT "<shape type='space' param='4250'><img src='chara25' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 26
		HTML_PRINT "<shape type='space' param='4250'><img src='chara26' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 27
		HTML_PRINT "<shape type='space' param='4250'><img src='chara27' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 28
		HTML_PRINT "<shape type='space' param='4250'><img src='chara28' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 29
		HTML_PRINT "<shape type='space' param='4250'><img src='chara29' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 30
		HTML_PRINT "<shape type='space' param='4250'><img src='chara30' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 31
		HTML_PRINT "<shape type='space' param='4250'><img src='chara31' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 32
		HTML_PRINT "<shape type='space' param='4250'><img src='chara32' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 33
		HTML_PRINT "<shape type='space' param='4250'><img src='chara33' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 34
		HTML_PRINT "<shape type='space' param='4250'><img src='chara34' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 35
		HTML_PRINT "<shape type='space' param='4250'><img src='chara35' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 36
		HTML_PRINT "<shape type='space' param='4250'><img src='chara36' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 37
		HTML_PRINT "<shape type='space' param='4250'><img src='chara37' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 38
		HTML_PRINT "<shape type='space' param='4250'><img src='chara38' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 39
		HTML_PRINT "<shape type='space' param='4250'><img src='chara39' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 40
		HTML_PRINT "<shape type='space' param='4250'><img src='chara40' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 41
		HTML_PRINT "<shape type='space' param='4250'><img src='chara41' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 42
		HTML_PRINT "<shape type='space' param='4250'><img src='chara42' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 43
		HTML_PRINT "<shape type='space' param='4250'><img src='chara43' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 44
		HTML_PRINT "<shape type='space' param='4250'><img src='chara44' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 45
		HTML_PRINT "<shape type='space' param='4250'><img src='chara45' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 46
		HTML_PRINT "<shape type='space' param='4250'><img src='chara46' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 47
		HTML_PRINT "<shape type='space' param='4250'><img src='chara47' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 48
		HTML_PRINT "<shape type='space' param='4250'><img src='chara48' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 49
		HTML_PRINT "<shape type='space' param='4250'><img src='chara49' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 50
		HTML_PRINT "<shape type='space' param='4250'><img src='chara50' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 51
		HTML_PRINT "<shape type='space' param='4250'><img src='chara51' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 52
		HTML_PRINT "<shape type='space' param='4250'><img src='chara52' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 53
		HTML_PRINT "<shape type='space' param='4250'><img src='chara53' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 54
		HTML_PRINT "<shape type='space' param='4250'><img src='chara54' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 55
		HTML_PRINT "<shape type='space' param='4250'><img src='chara55' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 56
		HTML_PRINT "<shape type='space' param='4250'><img src='chara56' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 57
		HTML_PRINT "<shape type='space' param='4250'><img src='chara57' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 58
		HTML_PRINT "<shape type='space' param='4250'><img src='chara58' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 59
		HTML_PRINT "<shape type='space' param='4250'><img src='chara59' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 60
		HTML_PRINT "<shape type='space' param='4250'><img src='chara60' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 61
		HTML_PRINT "<shape type='space' param='4250'><img src='chara61' width='1400' height='700' ypos='-450'>"
	ELSEIF NO:(ASSI:2) == 62
		HTML_PRINT "<shape type='space' param='4250'><img src='chara62' width='1400' height='700' ypos='-450'>"
	ELSE
			HTML_PRINT "<shape type='space' param='4250'><img src='缺' width='1400' height='700' ypos='-450'>"
	ENDIF
ELSE
	HTML_PRINT "<shape type='space' param='4250'><img src='空' width='1400' height='700' ypos='-450'>"
ENDIF
