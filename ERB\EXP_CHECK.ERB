﻿;────────────────────────────────────
;経験を行動やコマンドから分離してここでまとめて処理するの実験です
;────────────────────────────────────
@EXP_CHECK
;────────────────────────────────────
;快ＣＶＡＢ経験
;────────────────────────────────────
;1(最低保証) + UP補正(MASTER:1000UPで+1,10000UPで+4、TARGET:500UPで+1,その後1000UPごとに+1) * レベル補正（対手とのレベル差が大きいほど補正）
IF UP:0
	LOCAL = (SQRT(UP:0) + 10) / 30
	LOCAL = LOCAL * MAX(100 * (CFLAG:0 + 10) / (CFLAG:MASTER:0 + 10) , 10) / 100
	SIF NOWEX:0
		LOCAL = (LOCAL + 1) * 3 / 2
	LOCAL = LOCAL < 1 ? 1 # LOCAL
	EXP:MASTER:0 += LOCAL
	PRINTFORML Ｃ经验+{LOCAL}
ENDIF
IF UP:1
	LOCAL = (SQRT(UP:1) + 10) / 30
	LOCAL = LOCAL * MAX(100 * (CFLAG:0 + 10) / (CFLAG:MASTER:0 + 10) , 10) / 100
	SIF NOWEX:1
		LOCAL = (LOCAL + 1) * 3 / 2	
	LOCAL = LOCAL < 1 ? 1 # LOCAL
	EXP:MASTER:1 += LOCAL
	PRINTFORML Ｖ经验+{LOCAL}
ENDIF
IF UP:2
	LOCAL = (SQRT(UP:2) + 10) / 30
	LOCAL = LOCAL * MAX(100 * (CFLAG:0 + 10) / (CFLAG:MASTER:0 + 10) , 10) / 100
	SIF NOWEX:2
		LOCAL = (LOCAL + 1) * 3 / 2	
	LOCAL = LOCAL < 1 ? 1 # LOCAL
	EXP:MASTER:2 += LOCAL
	PRINTFORML Ａ经验+{LOCAL}
ENDIF
IF UP:3
	LOCAL = 1 + (SQRT(UP:3) + 10) / 30
	LOCAL = LOCAL * MAX(100 * (CFLAG:0 + 10) / (CFLAG:MASTER:0 + 10) , 10) / 100
	SIF NOWEX:3
		LOCAL = (LOCAL + 1) * 3 / 2	
	LOCAL = LOCAL < 1 ? 1 # LOCAL
	EXP:MASTER:3 += LOCAL
	PRINTFORML Ｂ经验+{LOCAL}
ENDIF

IF UP:40
	LOCAL = 1 + (UP:40 + 500) / 1000
	LOCAL = LOCAL * MAX(100 * (CFLAG:MASTER:0 + 10) / (CFLAG:0 + 10) , 100) / 100
	SIF NOWEX:40
		LOCAL = (LOCAL + 1) * 3 / 2	
	EXP:0 += LOCAL
	PRINTFORML ｃ经验+{LOCAL}
ENDIF
IF UP:41
	LOCAL = 1 + (UP:41 + 500) / 1000
	LOCAL = LOCAL * MAX(100 * (CFLAG:MASTER:0 + 10) / (CFLAG:0 + 10) , 100) / 100
	SIF NOWEX:41
		LOCAL = (LOCAL + 1) * 3 / 2	
	EXP:1 += LOCAL
	PRINTFORML ｖ经验+{LOCAL}
ENDIF
IF UP:42
	LOCAL = 1 + (UP:42 + 500) / 1000
	LOCAL = LOCAL * MAX(100 * (CFLAG:MASTER:0 + 10) / (CFLAG:0 + 10) , 100) / 100
	SIF NOWEX:42
		LOCAL = (LOCAL + 1) * 3 / 2	
	EXP:2 += LOCAL
	PRINTFORML ａ经验+{LOCAL}
ENDIF
IF UP:43
	LOCAL = 1 + (UP:43 + 500) / 1000
	LOCAL = LOCAL * MAX(100 * (CFLAG:MASTER:0 + 10) / (CFLAG:0 + 10) , 100) / 100
	SIF NOWEX:43
		LOCAL = (LOCAL + 1) * 3 / 2	
	EXP:3 += LOCAL
	PRINTFORML ｂ经验+{LOCAL}
ENDIF

;────────────────────────────────────
;処女丧失のチェック
;────────────────────────────────────
IF TALENT:MASTER:0
	IF (TEQUIP:70 && TEQUIP:70 < 6) || TEQUIP:20 || EXP:MASTER:1 > 0
		PRINTW 处女丧失
		IF !GETBIT(CFLAG:300, 57)
			PRINTL 【处女丧失】屈服点+10
			CFLAG:301 += 10
			SETBIT CFLAG:300 , 57
		ENDIF
		TALENT:MASTER:0 = 0
		;処女丧失フラグ(今回のコマンド)
		;TFLAG:3 = 1
		;調教対象の処女童貞丧失フラグ(アクション内/調教内。口上向け)
		TFLAG:301 |= 1
		TFLAG:303 |= 1
		IF EXP:MASTER:1 == 0
			EXP:MASTER:1 += 1
			PRINTL Ｖ经验+1
		ENDIF
	ENDIF
ENDIF

IF ((TEQUIP:71 && TEQUIP:71 < 6) || TFLAG:105 == 2 || TFLAG:105 == 4) && TALENT:0
	PRINTFORMW 处女丧失（%CALLNAME:TARGET%）
	TALENT:0 = 0
	;調教者の処女童貞丧失フラグ(アクション内/調教内。口上向け)
	TFLAG:302 |= 1
	TFLAG:304 |= 1
	IF EXP:1 == 0
		EXP:1 += 1
		PRINTL ｖ经验+1
	ENDIF
ENDIF


;────────────────────────────────────
;童貞丧失のチェック
;────────────────────────────────────
IF TALENT:MASTER:1
	IF TEQUIP:71 || (TFLAG:90 == 74 && SOURCE:0 > 500) || TFLAG:105 == 2 || TFLAG:105 == 4|| TFLAG:105 == 6 || TFLAG:105 == 8
		PRINTW 童贞丧失
		IF !GETBIT(CFLAG:300, 58)
			PRINTL 【童贞丧失】屈服点+10
			CFLAG:301 += 10
			SETBIT CFLAG:300 , 58
		ENDIF
		TALENT:MASTER:1 = 0
		;童貞丧失フラグ(今回のコマンド)
		;TFLAG:3 = 2
		;調教対象の処女童貞丧失フラグ
		TFLAG:301 |= 2
		TFLAG:303 |= 2
	ENDIF
ENDIF

IF (TEQUIP:70 || TFLAG:105 == 1 || TFLAG:105 == 3 || TFLAG:105 == 5 || TFLAG:105 == 7) && TALENT:1
	PRINTFORMW 童贞丧失（%CALLNAME:TARGET%）
	TALENT:1 = 0
	;調教者の処女童貞丧失フラグ
	TFLAG:302 |= 2
	TFLAG:304 |= 2
ENDIF


;────────────────────────────────────
;絶頂経験
;────────────────────────────────────
A = NOWEX:0 + NOWEX:1 + NOWEX:2 + NOWEX:3
EXP:MASTER:4 += A
EXP:63 += A
IF A > 0
	PRINT 绝顶经验+
	PRINTVL A
ENDIF

B = NOWEX:40 + NOWEX:41 + NOWEX:42 + NOWEX:43
SIF B
	TFLAG:403 = 2 + RAND:3
EXP:4 += B
EXP:MASTER:63 += B
IF B > 0
	PRINTFORM （%CALLNAME:TARGET%）绝顶经验+
	PRINTVL B
ENDIF


;────────────────────────────────────
;射精/噴乳経験
;────────────────────────────────────
IF NOWEX:10
	EXP:MASTER:6 += NOWEX:10
	PRINT 喷乳经验+
	PRINTVL NOWEX:10
ENDIF
IF NOWEX:11
	EXP:MASTER:5 += NOWEX:11
	PRINT 射精经验+
	PRINTVL NOWEX:11
ENDIF
IF NOWEX:50
	EXP:6 += NOWEX:50
	PRINTFORM （%CALLNAME:TARGET%）喷乳经验+
	PRINTVL NOWEX:50
ENDIF
IF NOWEX:51
	EXP:5 += NOWEX:51
	PRINTFORM （%CALLNAME:TARGET%）射精经验+
	PRINTVL NOWEX:51
ENDIF


;────────────────────────────────────
;性交経験/性知識/自慰経験/被写経験（性知識をどう扱うかはまだわかりませんがここでも保留しておきましょう。今は会話の知識だけで上がります）
;────────────────────────────────────
IF TEQUIP:70 || TEQUIP:71
	EXP:MASTER:7 += 1
	EXP:7 += 1
	IF ASSI:1 > 0 && CFLAG:(ASSI:1):172 == 1
		EXP:(ASSI:1):7 += 1
		PRINTFORML （三人）性交经验+1
	ELSE
		PRINTL （二人）性交经验+1
	ENDIF
ENDIF

IF TFLAG:90 == 2
	Z = 1 + RAND:3
	EXP:MASTER:8 += Z
	PRINT 性知识+
	PRINTVL Z
ENDIF

IF TEQUIP:69 == 1
	EXP:MASTER:10 += 1
	EXP:MASTER:11 += 1
	PRINTL 自慰经验+1
ELSEIF TEQUIP:69 == 2
	EXP:10 += 1
	PRINTFORML （%CALLNAME:TARGET%）自慰经验+1
ELSEIF TEQUIP:69 == 3
	EXP:MASTER:10 += 1
	EXP:MASTER:11 += 1
	EXP:10 += 1
	PRINTL （二人）自慰经验+1
ENDIF

IF TFLAG:190 == 52
	EXP:MASTER:13 += 1
	PRINTL 被拍经验+1
ENDIF


;────────────────────────────────────
;放尿経験/精液経験/フェラ経験/手淫経験/道具使用経験
;────────────────────────────────────
IF TFLAG:90 == 45 && (TFLAG:94 == 1 || TEQUIP:94 == 2)
	EXP:MASTER:12 += 1
	PRINTL 放尿经验+1
ELSEIF TFLAG:32 > 0
	EXP:MASTER:12 += 1
	PRINTL 放尿经验+1
ENDIF

IF TFLAG:30 && TFLAG:35 && TFLAG:30 != 1 && TFLAG:35 != 1
	EXP:MASTER:20 += LOSEBASE:2 / 300
	EXP:20 += LOSEBASE:92 / 300
	PRINT 精液经验+
	PRINTVL LOSEBASE:2 / 300
	PRINTFORM （%CALLNAME:TARGET%）精液经验+
	PRINTVL LOSEBASE:92 / 300
ELSEIF TFLAG:30 && TFLAG:30 != 1
	EXP:20 += LOSEBASE:92 / 300
	PRINTFORM （%CALLNAME:TARGET%）精液经验+
	PRINTVL LOSEBASE:92 / 300
ELSEIF TFLAG:35 && TFLAG:35 != 1
	EXP:MASTER:20 += LOSEBASE:2 / 300
	PRINT 精液经验+
	PRINTVL LOSEBASE:2 / 300
ENDIF

;奉仕系能力をうまく上がらせるため愛撫も手淫、クンニも口淫経験として扱います
IF TFLAG:90 == 51 || TFLAG:90 == 52
	EXP:MASTER:22 += 1
	PRINTL 口淫经验+1
ENDIF
IF TFLAG:90 == 12 || TFLAG:90 == 17 || (TFLAG:90 == 11 && TFLAG:93 == 1) || (ASSI:1 > 0 && CFLAG:(ASSI:1):190 == 53 )|| (ASSI:2 > 0 && CFLAG:(ASSI:2):190 == 53)
	EXP:22 += 1
	IF ASSI:1 > 0 && CFLAG:(ASSI:1):172 == 1
		EXP:(ASSI:1):22 += 1
		PRINTFORML （%CALLNAME:TARGET%・%CALLNAME:(ASSI:1)%）口淫经验+1
	ELSE
		PRINTFORML （%CALLNAME:TARGET%）口淫经验+1
	ENDIF
ENDIF
IF TFLAG:90 == 15
	EXP:25 += 1
	EXP:MASTER:25 += 1
	IF ASSI:1 > 0 && CFLAG:(ASSI:1):172 == 1
		EXP:(ASSI:1):25 += 1
		PRINTL （三人）接吻经验+1
	ELSE
		PRINTL （二人）接吻经验+1
	ENDIF
ENDIF
IF TFLAG:90 == 50 || (TFLAG:90 == 11 && TFLAG:93 == 2)
	EXP:MASTER:23 += 1
	PRINTL 手淫经验+1
ENDIF
IF TFLAG:90 == 10 || TFLAG:90 == 13 || (TFLAG:11 && TFLAG:93 != 1)
	EXP:23 += 1
	IF ASSI:1 > 0 && CFLAG:(ASSI:1):172 == 1
		EXP:(ASSI:1):23 += 1
		PRINTFORML （%CALLNAME:TARGET%・%CALLNAME:(ASSI:1)%）手淫经验+1
	ELSE
		PRINTFORML （%CALLNAME:TARGET%）手淫经验+1
	ENDIF
ENDIF

;自慰でバイブ使用も計算します
IF TEQUIP:69 && TFLAG:94 < 4
	IF TEQUIP:20 || TEQUIP:25 || TEQUIP:26
		EXP:MASTER:24 += 1
		PRINTL 道具使用经验+1
	ENDIF
ENDIF
IF TFLAG:80 == 0 && SELECTCOM > 0 && SELECTCOM < 10
	EXP:26 += 1
	EXP:MASTER:26 += 1
	PRINTFORML （二人）聊天经验+1
ENDIF

;────────────────────────────────────
;奉仕快楽経験/苦痛快楽経験/恥辱快楽経験
;────────────────────────────────────
IF ((TFLAG:80 == 5 || TFLAG:80 == 10  || TEQUIP:70 == 3 ) && TFLAG:94 == 2) && UP:0 + UP:1 + UP:2 + UP:3 + UP:5 > 1000
	EXP:MASTER:21 += 1
	PRINTL 侍奉快乐经验+1
ENDIF
IF TFLAG:80 == 1
	IF UP:40 + UP:41 + UP:42 + UP:43 + UP:8 > 1000 || (BASE:8 > BASE:7 && UP:9 < UP:6 + UP:8)
		EXP:21 += 1
		PRINTFORML （%CALLNAME:TARGET%）侍奉快乐经验+1
	ENDIF
ENDIF
IF UP:10 > 200 
	IF (UP:0 + UP:1 + UP:2 + UP:3 > UP:12) && (UP:0 + UP:1 + UP:2 + UP:3 > 500)
		EXP:MASTER:30 += 1
		PRINTL 痛苦快乐经验+1
	ELSEIF (UP:5 + UP:10 + UP:12 > 1500 - EXP:50 * 50) && (EXP:50 > RAND:5)
		EXP:MASTER:30 += 1
		PRINTL 痛苦快乐经验+1
	ENDIF
ENDIF
IF UP:11 > 200 
	IF (UP:0 + UP:1 + UP:2 + UP:3 > UP:12) && (UP:0 + UP:1 + UP:2 + UP:3 > 500)
		EXP:MASTER:31 += 1
		PRINTL 恥辱快乐经验+1
	ENDIF
ENDIF
;────────────────────────────────────
;愛情経験（）
;────────────────────────────────────
IF UP:8 > 5000 || SELECTCOM == 43
	EXP:32 += 1
	EXP:MASTER:32 += 1
	PRINTL (二人)爱情经验+1
ENDIF
;────────────────────────────────────
;レズ経験/ＢＬ経験
;────────────────────────────────────
;接触が多いほど多く経験値が入ります
IF TALENT:MASTER:122 == 0 && TALENT:122 == 0
	L = 1 + SOURCE:10 / 200
	SIF L > 10
		L = 10
	EXP:MASTER:40 += L
	EXP:40 += L
	PRINT （二人）百合经验+
	PRINTVL L
ELSEIF TALENT:MASTER:122 && TALENT:122
	G = 1 + SOURCE:10 / 200
	SIF G > 10
		G = 10
	EXP:MASTER:40 += G
	EXP:40 += G
	PRINT （二人）ＢＬ经验+
	PRINTVL G
ENDIF


;────────────────────────────────────
;緊縛経験/拡張経験
;────────────────────────────────────
B = 0
SIF TEQUIP:40 || TEQUIP:46 || TEQUIP:47
	B += 1
SIF TEQUIP:40 > 4
	B += 1
SIF TEQUIP:41
	B += 1
SIF TEQUIP:42
	B += 1
SIF TEQUIP:43
	B += 1
IF B > 0
	EXP:MASTER:51 += B
	PRINT 紧缚经验+
	PRINTVL B
ENDIF

IF TFLAG:94 != 4
	IF TFLAG:90 == 70
		EXP:MASTER:52 += 1
		PRINTL Ｖ扩张经验+1
	ELSEIF TFLAG:90 == 71
		EXP:MASTER:53 += 1
		PRINTL Ａ扩张经验+1
	ELSEIF TFLAG:90 == 72
		EXP:MASTER:52 += 1
		EXP:MASTER:53 += 1
		PRINTL Ｖ扩张＆Ａ扩张经验+1
	ENDIF
ENDIF


;────────────────────────────────────
;触手経験
;────────────────────────────────────
IF TEQUIP:90
	T = 1
	EXP:MASTER:55 += T
	PRINT 触手经验+
	PRINTVL T
ELSEIF TEQUIP:91 || TEQUIP:92 || TEQUIP:93 || TEQUIP:94
	T = 1
	EXP:MASTER:55 += T
	PRINT 触手经验+
	PRINTVL T
ENDIF

;────────────────────────────────────
;異常経験/屈服経験/恐怖経験/露出経験
;────────────────────────────────────
IF SOURCE:22 > 200 + CFLAG:MASTER:0 * 50 + MARK:2 * 50 + EXP:MASTER:50 * 20
	EXP:MASTER:50 += 1
	PRINTL 异常经验+1
ENDIF

IF UP:6 > 100 + EXP:MASTER:60 * 10 + MARK:2 * 20
	EXP:MASTER:60 += 1
	PRINTL 屈服经验+1
ENDIF

IF UP:7 > 200 + EXP:MASTER:61 * 10 + MARK:MASTER:4 * 20
	EXP:MASTER:61 += 1
	PRINTL 恐怖经验+1
ENDIF

IF SOURCE:20 > 200 + EXP:MASTER:62 * 10 + CFLAG:MASTER:0 * 20
	EXP:MASTER:62 += 1
	PRINTL 露出经验+1
ENDIF
SIF ASSI:1 > 0
	PRINTL 
FOR LOCAL, 1, 3
	IF ASSI:LOCAL > 0
		;ｃ経験
		IF CFLAG:(ASSI:LOCAL):190 == 2 || CFLAG:(ASSI:LOCAL):190 == 41 || CFLAG:(ASSI:LOCAL):190 == 32
			EXP:(ASSI:LOCAL):0 += 1
			PRINTFORML （%CALLNAME:(ASSI:LOCAL)%）ｃ经验+1
		ENDIF
		;ｖ経験
		IF CFLAG:(ASSI:LOCAL):190 == 6
			EXP:(ASSI:LOCAL):1 += 1
			PRINTFORML （%CALLNAME:(ASSI:LOCAL)%）ｖ经验+1
		ENDIF
		;ｂ経験
		IF CFLAG:(ASSI:LOCAL):190 == 3
			EXP:(ASSI:LOCAL):3 += 1
			PRINTFORML （%CALLNAME:(ASSI:LOCAL)%）ｂ经验+1
		ENDIF
		IF CFLAG:(ASSI:LOCAL):190 == 6 || CFLAG:(ASSI:LOCAL):190 == 32
			EXP:(ASSI:LOCAL):7 += 1
			EXP:MASTER:7 += 1
			PRINTFORML （%CALLNAME:(ASSI:LOCAL)%・%CALLNAME:MASTER%）性交经验+1
		ENDIF
		IF CFLAG:(ASSI:LOCAL):190 == 50
			EXP:(ASSI:LOCAL):10 += 1
			PRINTFORML （%CALLNAME:(ASSI:LOCAL)%）自慰经验+1
		ENDIF
		IF TFLAG:30 && TFLAG:30 != 1 && !(TEQUIP:71 || TFLAG:105 == 2 || TFLAG:105 == 4 || TFLAG:105 == 6 || TFLAG:105 == 8)
			EXP:(ASSI:LOCAL):20 += 1
			PRINTFORML （%CALLNAME:(ASSI:LOCAL)%）精液经验+1
		ENDIF
		IF CFLAG:(ASSI:LOCAL):190== 1 || CFLAG:(ASSI:LOCAL):190 == 3 || CFLAG:(ASSI:LOCAL):190 == 11 || CFLAG:(ASSI:LOCAL):190 == 21
			EXP:(ASSI:LOCAL):22 += 1
			PRINTFORML （%CALLNAME:(ASSI:LOCAL)%）口淫经验+1
		ENDIF
		IF CFLAG:(ASSI:LOCAL):190 == 7 || CFLAG:(ASSI:LOCAL):190 == 10 || CFLAG:(ASSI:LOCAL):190 == 20 || CFLAG:(ASSI:LOCAL):190 == 30
			EXP:(ASSI:LOCAL):23 += 1
			PRINTFORML （%CALLNAME:(ASSI:LOCAL)%）手淫经验+1
		ENDIF
		IF CFLAG:(ASSI:LOCAL):190 == 5 || CFLAG:(ASSI:LOCAL):190 == 12 || CFLAG:(ASSI:LOCAL):190 == 13 || CFLAG:(ASSI:LOCAL):190 == 22 || CFLAG:(ASSI:LOCAL):190 == 31
			EXP:(ASSI:LOCAL):24 += 1
			PRINTFORML （%CALLNAME:(ASSI:LOCAL)%）道具使用经验+1
		ENDIF
		IF CFLAG:(ASSI:LOCAL):190 == 40 || CFLAG:(ASSI:LOCAL):190 == 53
			EXP:(ASSI:LOCAL):25 += 1
			EXP:25 += 1
			PRINTFORML （%CALLNAME:TARGET%・%CALLNAME:(ASSI:LOCAL)%）接吻经验+1
		ENDIF
		SIF MASTER_EX(4 * LOCAL,10)
			EXP:(ASSI:LOCAL):63 += 1
	ENDIF
NEXT
