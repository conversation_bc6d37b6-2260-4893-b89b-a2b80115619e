﻿;────────────────────────────────────
;コマンド終了の処理
;────────────────────────────────────
@EVENTCOMEND
CALL ABL_REVISION
;────────────────────────────────────
;母乳と写真の計算
;────────────────────────────────────
SIF TEQUIP:36 && TALENT:MASTER:114 == 0
	CALL MILK_CHECK
SIF TFLAG:190 == 52
	CALL SELL_PHOTO

;────────────────────────────────────
;好感度の計算（調教対象）
;────────────────────────────────────
A = TFLAG:95 + TALENT:91 * (RAND:3 + 1)

SIF UP:0 > 100
	A += 1 + UP:0 / 1000
SIF UP:1 > 100
	A += 1 + UP:1 / 1000
SIF UP:2 > 100
	A += 1 + UP:2 / 1000
SIF UP:3 > 100
	A += 1 + UP:3 / 1000

SIF A > 0
	A = A * (10 + B:30) * (2 + CFLAG:MASTER:0 / 3) * (11 - MARK:3) * (3 + TALENT:MASTER:13 - TALENT:MASTER:11 + TALENT:91 + TALENT:92 - TALENT:93) / 12000
SIF A < 0
	A = A * (1 + MARK:3) * (100 - B:30) * (3 + TALENT:MASTER:11 - TALENT:MASTER:13 + TALENT:MASTER:16 - TALENT:90) / 1000
SIF A < -5
	A = -5
SIF A > 40
	A = 40

CFLAG:2 += A
IF A != 0
	IF A > 0
		PRINTFORM 对%CALLNAME:TARGET%的好感度+
		PRINTV A
	ELSE
		PRINTFORM 对%CALLNAME:TARGET%的好感度-
		PRINTV A * -1
	ENDIF
	PRINTFORML 　(当前值{CFLAG:2})
ENDIF


;────────────────────────────────────
;好感度の計算（調教者）
;────────────────────────────────────
D = BASE:8 / 200 + (UP:40 + UP:41 + UP:42 + UP:43) / 250 + (UP:5 + UP:6 - UP:9) / 150 + (SOURCE:11 + SOURCE:31) / 100 + (NOWEX:40 + NOWEX:41 + NOWEX:42 + NOWEX:43 + NOWEX:50 + NOWEX:51) * 2

SIF A > 0 && TFLAG:94 < 3
	D += RAND:A * (3 + TALENT:MASTER:91 * 2 + TALENT:MASTER:92 - TALENT:MASTER:93 + TALENT:MASTER:25 - TALENT:MASTER:24) / 8

REPEAT 4
	Z = 40 + COUNT
	SIF UP:Z > 100
		D += 1
REND

SIF TALENT:MASTER:91
	D += 1

;苛立ちと相殺
IF BASE:7 > 0
	IF BASE:7 - D / 3 > BASE:7 / 2
		BASE:7 -= D / 3
	ELSEIF BASE:7 - D / 3 > 0
		BASE:7 /= 2
	ELSE
		BASE:7 /= 3
	ENDIF
	D = D * (MAXBASE:7 - BASE:7) / MAXBASE:7
ENDIF

Z = (CFLAG:2 - 500) / 200
SIF Z > 5
	Z = 5
SIF Z < -5
	Z = -5
SIF D > 0
	D = D * (10 + B:30) * (6 + Z + TALENT:MASTER:91 * 2 + TALENT:MASTER:92 - TALENT:MASTER:93) / ((30 + MARK:MASTER:4) * 10)
SIF D < 0
	D = D * (100 - B:30) * (3 + MARK:3 + TALENT:MASTER:11 * 2 - TALENT:MASTER:13) / ((20 + CFLAG:MASTER:0 * 2 + Z * 3) * 10)
SIF D > 0
	D = D * (100 + FLAG:3186) / 100
SIF D < -5
	D = -5
SIF D > 40 * (100 + FLAG:3186) / 100
	D = 40 * (100 + FLAG:3186) / 100


CFLAG:9 += D
IF D != 0
	IF D
		IF TALENT:MASTER:132 == 1
			PRINTFORML 对%CALLNAME:MASTER%的好感度似乎上升了
		ELSEIF TALENT:MASTER:132 == 2
			PRINTFORM 对%CALLNAME:MASTER%的好感度+
			PRINTV D
		ENDIF
	ELSE
		IF TALENT:MASTER:132 == 1
			PRINTFORML 对%CALLNAME:MASTER%的好感度似乎下降了
		ELSEIF TALENT:MASTER:132 == 2
			PRINTFORM 对%CALLNAME:MASTER%的好感度-
			PRINTV D * -1
		ENDIF
	ENDIF
	SIF TALENT:MASTER:132 == 2
		PRINTFORML 　(当前值{CFLAG:9})
ENDIF

;────────────────────────────────────
;経験値の計算（調教対象）
;────────────────────────────────────
B = TFLAG:96

;欲情、恭順、反抗、抑鬱の上昇値を見る
SIF UP:5 > 200
	B += 2
SIF UP:5 > 500
	B += 3 + RAND:2
SIF UP:5 > 1200
	B += 5
SIF UP:5 > 2000
	B += 8
SIF UP:8 > 200
	B += 2 + RAND:2
SIF UP:8 > 500
	B += 5
SIF UP:8 > 1200
	B += 7
SIF UP:8 > 2000
	B += 11
SIF UP:9 > 500
	B -= 2
SIF UP:9 > 1200
	B -= 4
SIF UP:9 > 2000
	B -= 6
SIF UP:11 > 200
	B += RAND:4
SIF UP:11 > 500
	B += RAND:8
SIF UP:11 > 1200
	B += RAND:12
SIF UP:11 > 2000
	B += RAND:16
SIF UP:13 > 500
	B -= 3
SIF UP:13 > 1200
	B -= 5

;奉仕の追加経験値
IF TFLAG:80 == 5 || TEQUIP:60 == 1 || TEQUIP:60 == 3
	SIF TFLAG:94 == 1
		B += 4
	SIF TFLAG:94 == 2
		B += 7
ENDIF

;従順と習得早い/遅いの補正
B = (B + 1) * (50 + B:30 * 2) * (2 + TALENT:MASTER:50 - TALENT:MASTER:51) / 120
SIF NOWEX:0
	B *= 2
SIF NOWEX:1
	B *= 2
SIF NOWEX:2
	B *= 2
SIF NOWEX:3
	B *= 2
SIF NOWEX:40
	B *= 3
SIF NOWEX:41
	B *= 3
SIF NOWEX:42
	B *= 3
SIF NOWEX:43
	B *= 3

B = B * (CFLAG:0 + 5) / (CFLAG:MASTER:0 + 5)

SIF B < 1
	B = 1
IF FLAG:3134 == 1
	TIMES B, 1.50
ELSEIF FLAG:3134 == 2
	TIMES B , 2.00
ENDIF
IF B > 0
	CFLAG:MASTER:1 += B
	PRINTFORM %CALLNAME:MASTER%的经验值+
	PRINTVL B
ENDIF


;────────────────────────────────────
;経験値の計算（調教者）
;────────────────────────────────────
;各パラメーターの上昇値を見る
C = 0
REPEAT 50
	SIF UP:COUNT > 500 && COUNT != 4
		C += 3
	SIF UP:COUNT > 1200 && COUNT != 4
		C += 3
	SIF UP:COUNT > 2500 && COUNT != 4
		C += 3
REND

;屈服と恭順は高く評価され、反抗と抑鬱は負の要素とする
SIF UP:6 > 200
	C += RAND:2
SIF UP:6 > 500
	C += 1
SIF UP:6 > 1200
	C += 1
SIF UP:6 > 2500
	C += 1
SIF UP:8 > 200
	C += RAND:2
SIF UP:8 > 500
	C += 1
SIF UP:8 > 1200
	C += 1
SIF UP:8 > 2500
	C += 1
SIF UP:9 > 200
	C -= 1
SIF UP:9 > 500
	C -= 2
SIF UP:9 > 1200
	C -= 3
SIF UP:9 > 2500
	C -= 3
SIF UP:9 > 200
	C -= 1
SIF UP:13 > 500
	C -= 2
SIF UP:13 > 1200
	C -= 3
SIF UP:13 > 2500
	C -= 3

;調教対象の反応による変動
SIF TFLAG:94 == 1
	C += 2
SIF TFLAG:94 == 2
	C += 5
SIF TFLAG:94 == 3
	C += 1
SIF TFLAG:94 == 4
	C -= 3
SIF TFLAG:94 == 5
	C -= 1

SIF NOWEX:0
	B *= 3
SIF NOWEX:1
	B *= 3
SIF NOWEX:2
	B *= 3
SIF NOWEX:3
	B *= 3
SIF NOWEX:40
	B *= 2
SIF NOWEX:41
	B *= 2
SIF NOWEX:42
	B *= 2
SIF NOWEX:43
	B *= 2
B += LOSEBASE:92 / 100

B = B * (CFLAG:MASTER:0 + 5) / (CFLAG:0 + 5)

;習得早い/遅いの補正
C = C * (3 + TALENT:50 * 2 - TALENT:51 * 2) / 2

SIF C > -5 && C < 1
	C = 1
IF CFLAG:MASTER:15 == 4
	IF CFLAG:302
		C *= 2
	ELSE
		C /= 2
	ENDIF
ENDIF
IF C > 0
	CFLAG:1 += C
	PRINTFORM %CALLNAME:TARGET%的经验值+
	PRINTVL C
ENDIF


;────────────────────────────────────
;罪悪感とアライメント変動
;────────────────────────────────────
;いわゆるカルマ値
A = 0

;恐怖と抑鬱を見る
SIF UP:7 + UP:13 > 1000
	A += 1 + RAND:2
SIF UP:7 + UP:13 > 600
	A += 1
SIF UP:7 + UP:13 > 300
	A += 1

;サド、お仕置きモード、調教対象の反抗が激しい、一つでも満たせば苦痛と不快はカルマに加算しません
IF TALENT:83 == 0 && MARK:3 * 150 < UP:9 && TFLAG:69 == 0
	SIF UP:10 + UP:12 > 1200
		A += 1 + RAND:2
	SIF UP:10 + UP:12 > 900
		A += 1 + RAND:2
	SIF UP:10 + UP:12 > 600
		A += 1
	SIF UP:10 + UP:12 > 300
		A += 1
ENDIF

;優しい性格ならハードなメニューをこなしただけでも罪悪感
SIF (TALENT:14 || TALENT:87) && (TFLAG:70 > 3 || TFLAG:80 == 6 || TFLAG:80 == 7 || TFLAG:80 == 8)
	A += 2

SIF TALENT:14 || TALENT:87
	TIMES A , 1.50
SIF TALENT:16 || TALENT:83 || TALENT:86
	TIMES A , 0.50

CFLAG:5 += A

;罪悪感の消滅、これも調教中では下がるスピードがかなり遅いです
IF CFLAG:5 > 100
	IF A < 1
		CFLAG:5 -= 3
	ELSEIF A < 3
		CFLAG:5 -= 2
	ELSEIF A < 6
		CFLAG:5 -= 1
	ENDIF
ELSEIF CFLAG:5 > 60
	IF A < 1
		CFLAG:5 -= 2
	ELSEIF A < 3
		CFLAG:5 -= 1
	ELSEIF A < 6
		CFLAG:5 -= RAND:2
	ENDIF
ELSEIF CFLAG:5 > 30
	IF A < 1
		CFLAG:5 -= 1
	ELSEIF A < 3
		CFLAG:5 -= RAND:2
	ENDIF
ELSE
	SIF A < 1
		CFLAG:5 -= RAND:2
ENDIF
SIF CFLAG:5 < 0
	CFLAG:5 = 0

A = 0
B = 0
C = 0
D = 0
E = 0
;MASTERの応答で基本値が決まる
IF TFLAG:125 == 1
	A = 500
ELSEIF TFLAG:125 == 2
	A = 200
ELSEIF TFLAG:125 == 3
	A = -200
ELSEIF TFLAG:125 == 4
	A = -500
ELSEIF TFLAG:125 == 5
	IF CFLAG:6 > 0
		A = 200
	ELSE
		A = -200
	ENDIF
ELSEIF TFLAG:125 == 6
	IF CFLAG:6 > 0
		A = -200
	ELSE
		A = 200
	ENDIF
ELSE
	A = 0
ENDIF

;TFLAG:402の変動
IF A > 0
	IF TFLAG:402 >= 0
		TFLAG:402 += 1
	ELSE
		TFLAG:402 = 0
	ENDIF
ELSEIF A < 0
	IF TFLAG:402 <= 0
		TFLAG:402 -= 1
	ELSE
		TFLAG:402 = 0
	ENDIF
ELSE
	TFLAG:402 = 0
ENDIF

;夢魔の性格で倍率が決まる。
;最小値50(Mデレ)、最大値150(Sツン)、素質変動しない/させないキャラではB = 100
B = (CFLAG:150 + CFLAG:152 + 200) /2

;MデレならAが+の方向に補正がかかる
IF A < 0
	A = A * B / 100
ELSE
	A = A * (200 - B) / 100
ENDIF
;ビッチならより極端な値をとる。(最大25％の増減)
A = A * (CFLAG:153 + 300) / 300

;平時アライメントから遠ざかる、またアライメントが極端な値の時、変動は-に補正
;CFLAG:90 = 0, CFLAG:90 = CFLAG:6のとき C = 10,差が20の時 C = 5,40の時 C = 10 / 3

D = 20 - ABS(CFLAG:6) / 5
E = D + ABS(CFLAG:90 / 10 - CFLAG:6)
SIF E <= 0
	E = 1
SIF D < 0
	D = 0

IF  (CFLAG:90 / 10 > CFLAG:6 && A < 0) || (CFLAG:90 / 10 < CFLAG:6 && A > 0)
	C = 10 * D / E
ELSE
	C = 10
ENDIF

A = A * C / 10
CFLAG:6 += A / 100

;────────────────────────────────────
;お仕置き関連の処理
;────────────────────────────────────
;苛立ちとこのターンの反抗をお仕置きポイントに加算します
IF BASE:7 > BASE:8 || (BASE:7 > 0 && UP:9 > 0 && RAND:3 > 0)
	K = BASE:7 / (30 + RAND:5) + UP:9 / 100
	SIF K > 20
		K = 20
	SIF K < 1
		K = 1
	TFLAG:68 += K
ELSE
	TFLAG:68 -= 1 + RAND:3
	SIF TFLAG:68 < 0
		TFLAG:68 = 0
ENDIF

;お仕置きモードの発生と解消
IF TFLAG:69 > 0
	;調教対象を痛めたり屈服させたりでいらいら解消。この場合簡単に降伏させるより無理やり折らせたほうが気持ち良いと思いますので、恭順の比重を屈服より低く設定しました
	K = UP:6 / 15 + UP:7 / 30 + UP:8 / 30 - UP:9 / 20 + UP:10 / 20 + UP:11 / 75 + UP:12 / 60 + UP:13 / 75
	BASE:7 -= K
	BASE:8 += K / 2
	;お仕置き宣言の場合はこのターン決してお仕置きモード解消しません（哀願を聞き入れた場合だけが例外）
	IF TFLAG:90 != 7 || TFLAG:94 == 3
		;調教対象の降伏、調教者の満足、二つの条件のどれかを満たしたらお仕置きモード解消
		IF (UP:6 + UP:7 + UP:8 > UP:9 + UP:12 + UP:13) || (BASE:8 - BASE:7 > 100 + RAND:50)
			TFLAG:68 -= TFLAG:68 / 2 - 5
			BASE:7 /= 2
			SIF TFLAG:68 > 30
				TFLAG:68 = 30
			SIF TFLAG:68 < 0
				TFLAG:68 = 0
			TFLAG:69 = 0
		ENDIF
	ENDIF
;ちょっと複雑な計算をしてるみたいだが、お仕置きポイントを100まで貯めればお仕置きモードという認識で大丈夫です。
ELSEIF TFLAG:68 + RAND:40 - RAND:10 > 100 + TALENT:86 * 3 - TALENT:87 * RAND:5 - TALENT:83 * 5 - TALENT:15 * (1 + RAND:3) + TALENT:17 * (1 + RAND:2)
	TFLAG:69 = 1
	TFLAG:68 /= 2
	TEQUIP:37 = 0
	TEQUIP:70 = 0
	TEQUIP:71 = 0
ENDIF
IF TFLAG:77 == 2
	TFLAG:69 = 1
ENDIF
;────────────────────────────────────
;TEQUIP処理
;────────────────────────────────────
CALL EVENT_EQUIP

;────────────────────────────────────
;勃起計算
;────────────────────────────────────
;残り精力が少ないと勃起しにくくなるように
LOCAL = 0
IF TALENT:MASTER:121 || TALENT:MASTER:122
	;理性
	SIF BASE:MASTER:5 < 500
		LOCAL -= 2
	SIF BASE:MASTER:5 < 200
		LOCAL -= 2
	SIF BASE:MASTER:5 <= 0
		LOCAL -= 2
	;媚薬
	SIF TEQUIP:11
		LOCAL -= 5
	;敏感
	SIF TFLAG:373
		LOCAL -= 5
	;残り精力
	SIF BASE:MASTER:2 < MAXBASE:MASTER:2 /2
		LOCAL += 3
	SIF BASE:MASTER:2 < MAXBASE:MASTER:2 /4
		LOCAL += 3
	IF BASE:MASTER:2 == 0
		LOCAL += 4
		TFLAG:132 = 1
	ENDIF
	;残り体力
	IF BASE:MASTER:0 < MAXBASE:MASTER:0 /4
		LOCAL += 5
	ELSEIF BASE:MASTER:0 < MAXBASE:MASTER:0 /2
		LOCAL += 2
	ENDIF
	;残り気力
	IF BASE:MASTER:1 < MAXBASE:MASTER:1 /4
		LOCAL += 5
	ELSEIF BASE:MASTER:1 < MAXBASE:MASTER:1 /2
		LOCAL += 2
	ENDIF
	
	IF TFLAG:131 > 800
		LOCAL = LOCAL + (20 - LOCAL) * (TFLAG:131 - 800) / 700
	ENDIF
	SIF LOCAL > 20
		LOCAL = 20
ENDIF
TFLAG:134 = TFLAG:131
TFLAG:131 += TFLAG:130 * (20 - LOCAL ) / 20
SIF SELECTCOM == 60
	TFLAG:131 += 200
;射精処理
IF !TFLAG:166 || TFLAG:166 == 1000
	IF TFLAG:30
		IF	TFLAG:30 ==1
			TFLAG:131 = A:2 * 3
		ELSEIF	TFLAG:30 == 2 || TFLAG:30 > 3
			TFLAG:131 = TFLAG:131 / 3 + A:2 * 5
		ELSEIF	TFLAG:30 == 3
			TFLAG:131 = TFLAG:131 / 5 + A:2 * 3
		ENDIF
		IF TEQUIP:11
			TFLAG:131 = TFLAG:131 * 5 / 4
		ENDIF
		SIF TEQUIP:71 && TFLAG:106 && TFLAG:131 < 1000
			TFLAG:131 = 1000
	ENDIF
ENDIF
;────────────────────────────────────
;疲弊の処理、ゲージの負数防止
;────────────────────────────────────
;休憩などで疲弊が負数になったらここでリセット
SIF TFLAG:62 < 0
	TFLAG:62 = 0
SIF TFLAG:63 < 0
	TFLAG:63 = 0

IF BASE:MASTER:0 <= 0
	PRINTFORMW （由于激烈的调教，%CALLNAME:MASTER%似乎消耗了很多。）
	TFLAG:63 += 1 + RAND:3
	BASE:MASTER:0 = 0
ELSEIF BASE:MASTER:0 < 200
	PRINTFORMW （由于激烈的调教，%CALLNAME:MASTER%似乎有些消耗了。）
	TFLAG:63 += 1
ENDIF

IF BASE:0 <= 0
	PRINTFORMW （由于激烈的调教，%CALLNAME:TARGET%似乎消耗了很多。）
	TFLAG:62 += 1 + RAND:3
	BASE:0 = 0
ELSEIF BASE:0 < 200
	PRINTFORMW （由于激烈的调教，%CALLNAME:TARGET%似乎有些消耗了。）
	TFLAG:62 += 1
ENDIF

SIF BASE:MASTER:1 < 0
	BASE:MASTER:1 = 0
SIF BASE:MASTER:2 < 0
	BASE:MASTER:2 = 0
SIF BASE:MASTER:3 < 0
	BASE:MASTER:3 = 0
SIF BASE:MASTER:4 < 0
	BASE:MASTER:4 = 0
SIF BASE:MASTER:5 < 0
	BASE:MASTER:5 = 0
SIF TFLAG:131 < 0
	TFLAG:131 = 0
SIF BASE:1 < 0
	BASE:1 = 0
SIF BASE:2 < 0
	BASE:2 = 0
SIF BASE:3 < 0
	BASE:3 = 0
SIF BASE:4 < 0
	BASE:4 = 0
SIF BASE:5 < 0
	BASE:5 = 0
SIF BASE:6 < 0
	BASE:6 = 0
SIF BASE:7 < 0
	BASE:7 = 0
SIF BASE:8 < 0
	BASE:8 = 0


;────────────────────────────────────
;大満足ボーナス
;────────────────────────────────────
IF BASE:8 >= MAXBASE:8
	PRINTL 
	;大満足口上を呼ぶ
	RESULT = 0
	CALL KOJO_EVENT(21)
	SIF RESULT
		PRINTL 
	PRINTFORMW %CALLNAME:TARGET%大人非常满足
	CFLAG:202 = 1
	BASE:8 = 0
	;満足のゲージを伸ばして次回のボーナスを取得するにはもっと頑張らなくちゃ
	MAXBASE:8 += (7000 - MAXBASE:8) / 10
	TFLAG:67 += 1
;ある程度満足度ためている場合はターンごと自然消耗
ELSEIF BASE:8 > MAXBASE:8 / 3
	IF SOURCE:31 > 0
		BASE:8 -= MAXBASE:8 / 50
	ELSE
		BASE:8 -= MAXBASE:8 / 30
	ENDIF
	SIF BASE:8 < 0
		BASE:8 = 0
ENDIF

;────────────────────────────────────
;体力、気力、理性の自然回復、ゲージの限界突破防止
;────────────────────────────────────
;このターンで激しい消耗がなければ体力と気力は少しずつ自然回復
;ダンジョン探索中は自然回復なし
IF FLAG:1700 == 0
	IF LOSEBASE:90 < 50
		E = 25
	ELSEIF LOSEBASE:90 < 100
		E = 20
	ELSEIF LOSEBASE:90 < 150
		E = 15
	ELSEIF LOSEBASE:90 < 200
		E = 10
	ELSEIF LOSEBASE:90 < 250
		E = 5
	ENDIF

	IF LOSEBASE:91 < 20
		F = 15
	ELSEIF LOSEBASE:91 < 50
		F = 12
	ELSEIF LOSEBASE:91 < 80
		F = 9
	ELSEIF LOSEBASE:91 < 120
		F = 6
	ELSEIF LOSEBASE:91 < 160
		F = 3
	ENDIF

	IF LOSEBASE:0 < 25
		G = 25
	ELSEIF LOSEBASE:0 < 50
		G = 20
	ELSEIF LOSEBASE:0 < 75
		G = 15
	ELSEIF LOSEBASE:0 < 100
		G = 10
	ELSEIF LOSEBASE:0 < 125
		G = 5
	ENDIF

	IF LOSEBASE:1 < 10
		H = 15
	ELSEIF LOSEBASE:1 < 25
		H = 12
	ELSEIF LOSEBASE:1 < 40
		H = 9
	ELSEIF LOSEBASE:1 < 60
		H = 6
	ELSEIF LOSEBASE:1 < 80
		H = 3
	ENDIF

	IF TALENT:MASTER:112
		TIMES E , 2.00
		TIMES F , 2.00
	ELSEIF TALENT:MASTER:113
		TIMES E , 0.70
		TIMES F , 0.70
	ENDIF

	IF TALENT:112
		TIMES G , 2.00
		TIMES H , 2.00
	ELSEIF TALENT:113
		TIMES G , 0.70
		TIMES H , 0.70
	ENDIF

	BASE:MASTER:0 += E
	BASE:MASTER:1 += F
	BASE:0 += G
	BASE:1 += H
ENDIF
	;理性描写
	IF TFLAG:98 == 0 || ABL:MASTER:11 > 0
		IF BASE:MASTER:5 < 200 && BASE:MASTER:5 + LOSEBASE:95 > 200
			PRINTFORML 在过度的快感下，%CALLNAME:MASTER%的视野被桃色的雾霭笼罩，脑袋里充溢着甜蜜的麻木感。
		ELSEIF BASE:MASTER:5 < 500 && BASE:MASTER:5 + LOSEBASE:95 > 500
			PRINTFORML %CALLNAME:TARGET%被给予的快感刺激的丧失了理性，被夺走了抵抗的力量……
		ENDIF
	;マゾっ気がなく苦痛
	ELSE
		IF BASE:MASTER:5 < 200 && BASE:MASTER:5 + LOSEBASE:95 > 200
			PRINTFORML 过度的剧痛完全麻痹了%CALLNAME:MASTER%的思考，眼里失去了理性。
		ELSEIF BASE:MASTER:5 < 500 && BASE:MASTER:5 + LOSEBASE:95 > 500
			PRINTFORML %CALLNAME:TARGET%给予的剧痛削减了%CALLNAME:MASTER%的理性…
		ENDIF
	ENDIF
IF FLAG:1700 == 0
	;調教対象の理性回復。もし快楽/苦痛による理性低下のフラグがあればまずそれらを解決します。状態異常の場合も回復しません
	;苦痛による理性低下。理性が一気に下がるが一ターンで解消します。つまり痛みの効果は理性ダメージ＋一ターン理性回復なしです
	IF TFLAG:98 > 0
		TFLAG:98 = 0
	;快楽による理性低下。自然解消するのは遅いですが、絶頂すると一気に解消します。つまりいったら次のターンから理性回復できます
	ELSEIF TFLAG:97 > 0
		IF UP:0 + UP:1 + UP:2 + UP:3 > 50
			TFLAG:97 -= RAND:2
		ELSE
			TFLAG:97 -= 1 + RAND:2
		ENDIF
		SIF NOWEX:0 + NOWEX:1 + NOWEX:2 + NOWEX:3 > 0
			TFLAG:97 = 0
	ELSEIF TFLAG:61 == 0
		BASE:MASTER:5 += 50 + (5 + RAND:6) * (2 + RAND:4)
	ENDIF
	;調教者の理性は状態異常でなければ自然回復
	SIF TFLAG:60 == 0
		BASE:5 += 50 + (5 + RAND:6) * (2 + RAND:4)
ENDIF

;勃起度の自然低下
IF TALENT:MASTER:121 || TALENT:MASTER:122 
	IF !TFLAG:170 && !TEQUIP:71 && !TFLAG:130
		IF TFLAG:60 == 5
		ELSEIF TFLAG:60 == 1 || TFLAG:60 == 2 || TFLAG:60 == 3
			TIMES TFLAG:131, 0.85
		ELSE
			TIMES TFLAG:131, 0.90
		ENDIF
		SIF TFLAG:132 == 1
				TIMES TFLAG:131, 0.90
	ENDIF
ELSE
	TFLAG:131 = 0
ENDIF

SIF BASE:MASTER:0 > (MAXBASE:MASTER:0) * (100 + FLAG:3188) / 100
	BASE:MASTER:0 = (MAXBASE:MASTER:0) * (100 + FLAG:3188) / 100
SIF BASE:MASTER:1 > (MAXBASE:MASTER:1) * (100 + FLAG:3189) / 100
	BASE:MASTER:1 = (MAXBASE:MASTER:1) * (100 + FLAG:3189) / 100
SIF BASE:MASTER:2 > (MAXBASE:MASTER:2) * (100 + FLAG:3190) / 100
	BASE:MASTER:2 = (MAXBASE:MASTER:2) * (100 + FLAG:3190) / 100
SIF BASE:MASTER:3 > MAXBASE:MASTER:3
	BASE:MASTER:3 = MAXBASE:MASTER:3
SIF BASE:MASTER:4 > MAXBASE:MASTER:4
	BASE:MASTER:4 = MAXBASE:MASTER:4
SIF BASE:MASTER:5 > MAXBASE:MASTER:5
	BASE:MASTER:5 = MAXBASE:MASTER:5
SIF TFLAG:131 > 1500
	TFLAG:131 = 1500

SIF BASE:0 > MAXBASE:0
	BASE:0 = MAXBASE:0
SIF BASE:1 > MAXBASE:1
	BASE:1 = MAXBASE:1
SIF BASE:2 > MAXBASE:2
	BASE:2 = MAXBASE:2
SIF BASE:3 > MAXBASE:3
	BASE:3 = MAXBASE:3
SIF BASE:4 > MAXBASE:4
	BASE:4 = MAXBASE:4
SIF BASE:5 > MAXBASE:5
	BASE:5 = MAXBASE:5
SIF BASE:6 > MAXBASE:6
	BASE:6 = MAXBASE:6
SIF BASE:7 > MAXBASE:7
	BASE:7 = MAXBASE:7



;────────────────────────────────────
;調教対象の状態変化
;────────────────────────────────────
;異常状態中なら次のターンは必ず正常状態になります(0=通常/1=疲弊/2=衰弱/3=無気力/4=朦朧/5=情欲/6=怒り/7=退屈/8=狂気)
IF TFLAG:61 > 0
	TFLAG:61 = 0
;疲弊を見る
ELSEIF TFLAG:63 > 3 + RAND:5
	IF TFLAG:63 > 5
		TFLAG:61 = 2
	ELSE
		TFLAG:61 = 1
	ENDIF
ELSEIF BASE:MASTER:1 < 0 && (UP:13 > 500 + RAND:500 || RAND:3 > 1)
	TFLAG:61 = 3
ELSEIF BASE:MASTER:0 + BASE:MASTER:1 + BASE:MASTER:5 - NOWEX:0 * 100 - NOWEX:1 * 150 - NOWEX:2 * 120 - NOWEX:3 * 80 - NOWEX:10 * 80 - NOWEX:11 * 150 < 1000
	TFLAG:61 = 4
ELSEIF (PALAM:5 > 6000 - MARK:MASTER:1 * 200 && RAND:3 > 1) || UP:5 + LOSEBASE:95 * 2 + TFLAG:97 * 100 > 2000
	TFLAG:61 = 5
ELSEIF UP:9 * 2 + UP:12 - UP:8 > 1000 + RAND:5 * 50 - MARK:3 * 20 + MARK:2 * 10 + MARK:MASTER:4 * 10
	TFLAG:61 = 6
ELSEIF UP:0 + UP:1 + UP:2 + UP:3 + UP:5 + UP:7 + UP:10 + UP:11 + UP:12 < 500 + BASE:MASTER:5 + CFLAG:MASTER:0 * (200 - MARK:2 * 10) && TFLAG:64 > 15 && RAND:3 > 1
	TFLAG:61 = 7
ELSEIF UP:12 + UP:13 > 1200 + CFLAG:MASTER:0 * 100 - MARK:MASTER:4 * (50 + RAND:50) - TALENT:MASTER:89 * 200
	TFLAG:61 = 8
ENDIF

;────────────────────────────────────
;調教者の状態変化
;────────────────────────────────────
;異常状態中なら次のターンは必ず正常状態になります
IF TFLAG:60 > 0
	TFLAG:60 = 0
;疲弊を見る
ELSEIF TFLAG:62 > 3 + RAND:5
	IF TFLAG:62 > 5
		TFLAG:60 = 2
	ELSE
		TFLAG:60 = 1
	ENDIF
ELSEIF BASE:1 < 0 && (BASE:7 < 100 + RAND:100 || RAND:3 > 1)
	TFLAG:60 = 3
ELSEIF BASE:0 + BASE:1 + BASE:5 - NOWEX:40 * 100 - NOWEX:41 * 150 - NOWEX:42 * 120 - NOWEX:43 * 80 - NOWEX:50 * 80 - NOWEX:51 * 150 < 1000
	TFLAG:60 = 4
ELSEIF PALAM:40 + PALAM:41 + PALAM:42 + PALAM:43 > 12000 && RAND:3 > 1
	TFLAG:60 = 5
ELSEIF UP:9 * 2 - UP:6 - UP:8 + BASE:7 + TFLAG:68 * 2 + TFLAG:69 * 150 > 1000 + (RAND:5 + TALENT:84 - TALENT:85) * 100 + MARK:2 * 50
	TFLAG:60 = 6
ELSEIF BASE:6 < 100 + (A:30 + 1) * (10 + RAND:5) && RAND:3 > 0
	TFLAG:60 = 7
ELSEIF TALENT:89 && RAND:5 > 3
	TFLAG:60 = 8
ENDIF
;────────────────────────────────────
;状態変化口上とテキストの表示
;────────────────────────────────────
;状態変化口上を呼ぶ
RESULT = 0
CALL KOJO_EVENT(22)
SIF RESULT
	PRINTL 
SIF TFLAG:61
	PRINTFORMW %CALLNAME:MASTER%变成了%CONDITION(TFLAG:61)%状态
;状態異常
LOCAL = MAX(300 * (1000 + BASE:MASTER:1) / (1000 + MAXBASE:MASTER:1),100)
IF (CFLAG:100 > 20 + RAND:LOCAL ||(ASSI:1 > 0 && CFLAG:(ASSI:1):100 > 30 + RAND:500) ||(ASSI:2 > 0 && CFLAG:(ASSI:2):100 > 30 + RAND:500)) && !TFLAG:371
	RESULT = 0
	CALL SKILL_BADSTATUS(0)
	IF !RESULT
		PRINTFORMW %CALLNAME:MASTER%处于安心状态
		TFLAG:371 = 2 + RAND:(CFLAG:100 + 1) / 10
	ENDIF
ENDIF
IF (CFLAG:101 > 20 + RAND:LOCAL ||(ASSI:1 > 0 && CFLAG:(ASSI:1):101 > 30 + RAND:500) ||(ASSI:2 > 0 && CFLAG:(ASSI:2):101 > 30 + RAND:500)) &&  !TFLAG:372
	RESULT = 0
	CALL SKILL_BADSTATUS(1)
	IF !RESULT
		PRINTFORMW %CALLNAME:MASTER%变得无法保持理性了
		TFLAG:372 = 2 + RAND:(CFLAG:101 + 1) / 10
	ENDIF
ENDIF
IF (CFLAG:102 > 20 + RAND:LOCAL ||(ASSI:1 > 0 && CFLAG:(ASSI:1):102 > 30 + RAND:500) ||(ASSI:2 > 0 && CFLAG:(ASSI:2):102 > 30 + RAND:500)) &&  !TFLAG:373
	RESULT = 0
	CALL SKILL_BADSTATUS(2)
	IF !RESULT
		PRINTFORMW %CALLNAME:MASTER%变得敏感了
		TFLAG:373 = 2 + RAND:(CFLAG:102 + 1) / 10
	ENDIF
ENDIF

SIF TFLAG:60
	PRINTFORMW %CALLNAME:TARGET%变成了%CONDITION(TFLAG:60)%状态

;勃起描写
IF TFLAG:131 > 1300 && TFLAG:134 < 1300
	PRINTFORML %CALLNAME:MASTER%的男性器官勃起的快要触碰到腹部了，激烈地脉动着。
ELSEIF TFLAG:131 > 1000 && TFLAG:134 < 1000
	PRINTFORML %CALLNAME:MASTER%的男性器官隆隆地抬起头。
ELSEIF TFLAG:131 > 200 && TFLAG:134 < 200
	IF TFLAG:131 - TFLAG:134 > 200
		PRINTFORML %CALLNAME:MASTER%感觉到下半身血液迅速聚集。
	ELSEIF TFLAG:131 - TFLAG:134 > 50
		PRINTFORML %CALLNAME:MASTER%感觉到下半身的血液开始聚集。
	ELSE
		PRINTFORML %CALLNAME:MASTER%感到下半身的血液慢慢地开始聚集。
	ENDIF
ENDIF
;尿意描写
IF TALENT:MASTER:116
	IF BASE:MASTER:4 > 7 * MAXBASE:MASTER:4 / 8
		PRINTFORML %CALLNAME:MASTER%强力屏住呼吸，膝盖紧闭，勉强抵御着膨胀的尿意。
	ELSEIF BASE:MASTER:4 > 2 * MAXBASE:MASTER:4 / 3
        	PRINTFORML %CALLNAME:MASTER%脸颊泛红，摩擦着大腿忍受着尿意。
	ELSEIF BASE:MASTER:4 > MAXBASE:MASTER:4 / 2
        	PRINTFORML %CALLNAME:MASTER%感到了尿意。
	ENDIF
ENDIF
;────────────────────────────────────
;薬力の消滅、薬剤のクールダウン
;────────────────────────────────────
;ローション
SIF TEQUIP:10 > 0 && TALENT:MASTER:56
	TEQUIP:10 -= 1 + RAND:2
IF TEQUIP:10 > 0 && RAND:2 > 0
	TEQUIP:10 -= 1 + RAND:2
	SIF TEQUIP:10 < 0
		TEQUIP:10 = 0
ENDIF

;媚薬
SIF TEQUIP:11 > 0 && TALENT:MASTER:56
	TEQUIP:11 -= 2 + RAND:2
IF TEQUIP:11 > 0 && RAND:2 > 0
	TEQUIP:11 -= 1 + RAND:2
	SIF TEQUIP:11 < 0
		TEQUIP:11 = 0
ENDIF
;敏感
SIF CFLAG:MASTER:220
	TEQUIP:11 += 2
;薬剤の特性は調教中であまりクールダウンしませんが、調教終了後一気にクールダウン（仕入れ）します。
SIF RAND:5 > 3
	FLAG:20 -= 1
SIF RAND:5 > 3
	FLAG:21 -= 1
SIF RAND:4 > 2
	FLAG:23 -= 1
SIF BASE:0 > 1000 && BASE:1 > 500 && RAND:3 > 0
	FLAG:24 -= 1

SIF FLAG:20 < 0
	FLAG:20 = 0
SIF FLAG:21 < 0
	FLAG:21 = 0
SIF FLAG:23 < 0
	FLAG:23 = 0
SIF FLAG:24 < 0
	FLAG:24 = 0


;────────────────────────────────────
;ヒート蓄積とクールダウンの処理。このシステムが成功できるかの鍵なんですけど、こりゃまた複雑な構造になりましたね…
;────────────────────────────────────
;調教方針のヒート蓄積
IF TEQUIP:37 == 1
ELSE
	IF TFLAG:70 == 1
		SIF TFLAG:71 > 0
			TFLAG:71 += 4
		TFLAG:71 += 8
		SIF TFLAG:76 == 1
			TFLAG:71 += 5
	ELSEIF TFLAG:70 == 2
		SIF TFLAG:72 > 0
			TFLAG:72 += 4
		TFLAG:72 += 8
		SIF TFLAG:76 == 2
			TFLAG:72 += 5
	ELSEIF TFLAG:70 == 3
		SIF TFLAG:73 > 0
			TFLAG:73 += 4
		TFLAG:73 += 8
		SIF TFLAG:76 == 3
			TFLAG:73 += 5
	ELSEIF TFLAG:70 == 4
		SIF TFLAG:74 > 0
			TFLAG:74 += 4
		TFLAG:74 += 8
		SIF TFLAG:76 == 4
			TFLAG:74 += 5
	ELSEIF TFLAG:70 == 5
		SIF TFLAG:75 > 0
			TFLAG:75 += 4
		TFLAG:75 += 8
		SIF TFLAG:76 == 5
			TFLAG:75 += 5
	ENDIF
ENDIF

;休憩のクールダウン
IF TFLAG:71 > 0
	TFLAG:71 -= 2
	SIF TFLAG:76 != 1
		TFLAG:71 -= 1
	;体力と気力の残量を見る
	IF BASE:0 + BASE:1 + BASE:MASTER:0 + BASE:MASTER:1 < 500
		TFLAG:71 -= 3
	ELSEIF BASE:0 + BASE:1 + BASE:MASTER:0 + BASE:MASTER:1 < 1000
		TFLAG:71 -= 2
	ELSEIF BASE:0 + BASE:1 + BASE:MASTER:0 + BASE:MASTER:1 < 1500
		TFLAG:71 -= 1
	ENDIF
	;蓄積された疲弊
	TFLAG:71 -= TFLAG:62 / 2
	TFLAG:71 -= TFLAG:63 / 3
	;状態を見る
	SIF TFLAG:60 == 1
		TFLAG:71 -= 1
	SIF TFLAG:60 == 2
		TFLAG:71 -= 2
	SIF TFLAG:60 == 3
		TFLAG:71 -= 1
	;回復早い/遅い
	SIF TALENT:112
		TFLAG:71 += 1
	SIF TALENT:MASTER:112
		TFLAG:71 += 1
	SIF TALENT:113
		TFLAG:71 -= 1
	SIF TALENT:MASTER:113
		TFLAG:71 -= 1
	SIF TFLAG:71 < 0
		TFLAG:71 = 0
ENDIF

;ソフトのクールダウン
IF TFLAG:72 > 0
	A = 2 + RAND:2
	SIF TFLAG:76 != 2
		A += 1
	;体力と気力の残量を見る
	IF BASE:0 + BASE:1 + BASE:MASTER:0 + BASE:MASTER:1 < 500
		A += 2
	ELSEIF BASE:0 + BASE:1 + BASE:MASTER:0 + BASE:MASTER:1 < 1000
		A += 1
	ENDIF
	;状態を見る
	SIF TFLAG:60 == 5
		A += 1
	SIF TFLAG:60 == 6
		A -= 2
	SIF TFLAG:60 == 8
		A -= 1
	;素質
	SIF TALENT:11
		A -= 1
	SIF TALENT:14
		A += 2
	SIF TALENT:15
		A -= RAND:2
	SIF TALENT:16
		A -= 1
	SIF TALENT:17
		A += RAND:2
	SIF TALENT:28
		A += 1
	SIF TALENT:77
		A -= RAND:4
	SIF TALENT:86
		A -= RAND:2
	SIF TALENT:87
		A += 2
	SIF TALENT:92
		A += RAND:2
	SIF CFLAG:0 < 5
		A += 1
	SIF CFLAG:6 > 19
		A += 1
	SIF CFLAG:6 > 49
		A += 1
	SIF A > 0
		TFLAG:72 -= A
	SIF TFLAG:72 < 0
		TFLAG:72 = 0
ENDIF

;ノーマルのクールダウン
IF TFLAG:73 > 0
	A = 5
	SIF TFLAG:76 != 3
		A += 1 + RAND:3
	;状態を見る
	SIF TFLAG:60 == 6
		A -= 1
	SIF TFLAG:60 == 7
		A -= 3
	SIF TFLAG:60 == 8
		A -= 3
	SIF BASE:7 < 400
		A -= 1
	SIF BASE:7 < 200
		A -= 1
	;素質
	SIF TALENT:14
		A += 1
	SIF TALENT:28
		A += 2
	SIF TALENT:77
		A -= RAND:2
	SIF TALENT:84
		A += 1
	SIF TALENT:88
		A += RAND:3
	SIF TALENT:91
		A += RAND:2
	SIF TALENT:MASTER:78
		A += RAND:3
	SIF CFLAG:6 > -26 && CFLAG:6 < 26
		A += 1
	SIF CFLAG:6 > -11 && CFLAG:6 < 11
		A += 1
	SIF A > 0
		TFLAG:73 -= A
	SIF TFLAG:73 < 0
		TFLAG:73 = 0
ENDIF

;ハードのクールダウン
IF TFLAG:74 > 0
	A = 1 + RAND:3
	SIF TFLAG:76 != 4
		A += 2
	;体力と気力の残量を見る
	IF BASE:0 + BASE:1 + BASE:MASTER:0 + BASE:MASTER:1 < 500
		A -= 2
	ELSEIF BASE:0 + BASE:1 + BASE:MASTER:0 + BASE:MASTER:1 < 1000
		A -= 1
	ENDIF
	;状態を見る
	SIF TFLAG:60 == 2
		A -= 1
	SIF TFLAG:60 == 3
		A -= 2
	SIF TFLAG:60 == 7
		A += 1
	SIF BASE:7 > 300
		A += 1
	SIF BASE:7 > 600
		A += 1
	;素質
	SIF TALENT:10
		A -= 1
	SIF TALENT:12
		A += 1
	SIF TALENT:15
		A += RAND:3
	SIF TALENT:16
		A += 1
	SIF TALENT:17
		A -= RAND:3
	SIF TALENT:28
		A -= 1
	SIF TALENT:32
		A -= 1
	SIF TALENT:33
		A += 1
	SIF TALENT:MASTER:77
		A += 1
	SIF TALENT:MASTER:79
		A += 2
	SIF TALENT:83
		A += 2
	SIF TALENT:84 && CFLAG:MASTER:0 < 6
		A -= 3 - CFLAG:MASTER:0 / 2
	SIF TALENT:90
		A += 1
	SIF A > 0
		TFLAG:74 -= A
	SIF TFLAG:74 < 0
		TFLAG:74 = 0
ENDIF

;異常のクールダウン
IF TFLAG:75 > 0
	A = RAND:6
	SIF TFLAG:76 != 5
		A += RAND:3
	;状態を見る
	SIF TFLAG:60 == 4
		A += 1
	SIF TFLAG:60 == 8
		A += 3
	SIF BASE:5 < 200
		A += 2
	SIF BASE:5 < 400
		A += 1
	;素質
	SIF TALENT:10
		A -= 2
	SIF TALENT:16
		A += 1
	SIF TALENT:20
		A -= 2
	SIF TALENT:21
		A += 2
	SIF TALENT:28
		A -= 3
	SIF TALENT:32
		A -= 1 + RAND:2
	SIF TALENT:33
		A += 1 + RAND:2
	SIF TALENT:MASTER:76
		A += 1
	SIF TALENT:89
		A += 4
	SIF TALENT:115
		A += 1 + RAND:2
	SIF TALENT:130
		A += 2
	SIF A > 0
		TFLAG:75 -= A
	SIF TFLAG:75 < 0
		TFLAG:75 = 0
ENDIF

;調教メニューのヒート蓄積
IF TEQUIP:37 == 1
ELSE
	;会話
	IF TFLAG:80 == 0
		SIF TFLAG:81 < 0
			TFLAG:81 = 0
		SIF TFLAG:81 > 0
			TFLAG:81 += 7
		TFLAG:81 += 8
		SIF TFLAG:79 == 0
				TFLAG:81 += 5
	;愛撫
	ELSEIF TFLAG:80 == 1
		SIF TFLAG:82 > 0
			TFLAG:82 += 7
		TFLAG:82 += 8
		SIF TFLAG:79 == 1
			TFLAG:82 += 5
	;道具
	ELSEIF TFLAG:80 == 2
		SIF TFLAG:83 > 0
			TFLAG:83 += 7
		TFLAG:83 += 8
		SIF TFLAG:79 == 2
			TFLAG:83 += 5
	;性交
	ELSEIF TFLAG:80 == 3
		SIF TFLAG:84 > 0
			TFLAG:84 += 7
		TFLAG:84 += 8
		SIF TFLAG:79 == 3
			TFLAG:84 += 10
	;羞恥
	ELSEIF TFLAG:80 == 4
		SIF TFLAG:85 > 0
			TFLAG:85 += 7
		TFLAG:85 += 8
		SIF TFLAG:79 == 4
			TFLAG:85 += 5
	;奉仕
	ELSEIF TFLAG:80 == 5
		SIF TFLAG:86 > 0
			TFLAG:86 += 7
		TFLAG:86 += 8
		SIF TFLAG:79 == 8
			TFLAG:86 += 5
	;加虐
	ELSEIF TFLAG:80 == 6
		SIF TFLAG:87 > 0
			TFLAG:87 += 7
		TFLAG:87 += 8
		SIF TFLAG:79 == 6
			TFLAG:87 += 5
	;異常
	ELSEIF TFLAG:80 == 7
		SIF TFLAG:88 > 0
			TFLAG:88 += 7
		TFLAG:88 += 8
		SIF TFLAG:79 == 7
			TFLAG:88 += 5
	;使役
	ELSEIF TFLAG:80 == 8
		SIF TFLAG:89 > 0
			TFLAG:89 += 7
		TFLAG:89 += 8
		SIF TFLAG:79 == 8
			TFLAG:89 += 5
	;性交奉仕
	ELSEIF TFLAG:80 == 10
		SIF TFLAG:350 > 0
			TFLAG:350 += 7
		TFLAG:350 += 8
		SIF TFLAG:79 == 10
			TFLAG:350 += 10
	ENDIF
ENDIF
;会話のクールダウン
IF TFLAG:81 > 0
	A = 1 + (A:20 + 10) / 20
	SIF TFLAG:79 != 0
		A += 3
	SIF BASE:0 + BASE:1 < 400
		A += 1
	SIF BASE:0 + BASE:1 < 800
		A += 1
	SIF BASE:6 < 200
		A -= 2
	SIF BASE:6 < 400
		A -= 1
	SIF BASE:6 > 800
		A += 1
	SIF TALENT:22
		A -= 2
	SIF TALENT:23
		A += 2
	SIF TALENT:24
		A -= 1
	SIF TALENT:25
		A += 1
	SIF TALENT:26
		A += RAND:2
	SIF TALENT:27
		A -= RAND:2
	SIF TALENT:92
		A += 1
	SIF TALENT:93
		A -= 1
	SIF A > 0
		TFLAG:81 -= A
	;負数になれるのは会話だけの特性、しばらく会話してないと話したくなると認知してください
	SIF TFLAG:81 < 0
		TFLAG:81 -= 1
ENDIF

;愛撫のクールダウン
IF TFLAG:82 > 0
	A = 2 + RAND:3 + (A:21 + 10) / 20
	SIF TFLAG:79 != 1
		A += 2
	SIF TFLAG:62 > 5
		A -= 1
	SIF TFLAG:62 > 0
		A -= 1
	SIF TFLAG:60 == 4
		A += RAND:2
	SIF TFLAG:60 == 6
		A -= 2
	SIF BASE:6 < 400
		A -= 1
	SIF BASE:6 > 800
		A += 1
	SIF TALENT:22
		A -= 2
	SIF TALENT:23
		A += 2
	SIF TALENT:24
		A -= 1
	SIF TALENT:25
		A += 1
	SIF TALENT:26
		A += RAND:2
	SIF TALENT:27
		A -= RAND:2
	SIF TALENT:52
		A += 1
	SIF TALENT:57
		A += 1
	SIF TALENT:63
		A += 1
	SIF TALENT:65
		A -= 1
	SIF TALENT:82 && TALENT:MASTER:122
		A -= 2
	IF TALENT:82 == 0 && ABL:9 < 1 && ABL:10 < 1
		SIF TALENT:MASTER:122 && TALENT:122
			A -= 2
		SIF TALENT:MASTER:122 == 0 && TALENT:122 == 0
			A -= 2
	ENDIF
	SIF A > 0
		TFLAG:82 -= A
	SIF TFLAG:82 < 0
		TFLAG:82 = 0
ENDIF

;道具のクールダウン
IF TFLAG:83 > 0
	A = 1 + (A:22 + 10) * 2 / 30
	SIF TFLAG:79 != 2
		A += 3
	SIF TFLAG:60 == 3
		A -= RAND:2
	SIF TFLAG:60 == 4
		A -= 1
	SIF TFLAG:60 == 5
		A -= 1
	SIF ITEM:9 > 0
		A += 1
	IF TEQUIP:20 + TEQUIP:25 + TEQUIP:26 + TEQUIP:27 + TEQUIP:30 + TEQUIP:31 + TEQUIP:35 + TEQUIP:36 == 0
		A += 3
	ELSE
		A -= 1
	ENDIF
	SIF TALENT:33
		A += RAND:2
	SIF TALENT:32
		A -= RAND:2
	SIF TALENT:59
		A += 2
	SIF TALENT:63
		A += 1
	SIF TALENT:65
		A -= 1
	SIF A > 0
		TFLAG:83 -= A
	SIF TFLAG:83 < 0
		TFLAG:83 = 0
ENDIF

;性交のクールダウン
IF TFLAG:84 > 0
	A = 1 + (A:23 + 10) / 20
	SIF TFLAG:79 != 3
		A += 1
	SIF TFLAG:60 == 3
		A -= RAND:2
	SIF TFLAG:60 == 5
		A += 2
	SIF TFLAG:60 == 7
		A -= 1
	;挿入してる間だけ速いスピードでクールダウンする
	SIF TEQUIP:70 && TFLAG:80 != 3
		A += 7
	SIF TALENT:82 && TALENT:MASTER:122
		A -= 3
	IF TALENT:82 == 0 && ABL:9 < 1 && ABL:10 < 1
		SIF TALENT:MASTER:122 && TALENT:122
			A -= 3
		SIF TALENT:MASTER:122 == 0 && TALENT:122 == 0
			A -= 3
	ENDIF
	SIF A > 0
		TFLAG:84 -= A
	SIF TFLAG:84 < 0
		TFLAG:84 = 0
ENDIF

;羞恥のクールダウン
IF TFLAG:85 > 0
	A = 2 + (A:24 + 10) / 20
	SIF TFLAG:79 != 4
		A += 2
	SIF BASE:6 < 200
		A -= 1
	SIF BASE:6 < 400
		A -= 1
	SIF BASE:6 > 800
		A += 1
	SIF TFLAG:60 == 3
		A -= 2
	SIF TFLAG:60 == 6
		A -= 2
	SIF TFLAG:60 == 7
		A -= 1
	SIF TEQUIP:41
		A += 1
	SIF TALENT:22
		A -= 1
	SIF TALENT:23
		A += 1
	SIF TALENT:29
		A += 1
	SIF TALENT:32
		A -= 2
	SIF TALENT:33
		A -= 2
	SIF TALENT:34
		A -= 2
	SIF TALENT:35
		A += 2
	SIF TALENT:60
		A += 1
	SIF TALENT:MASTER:60
		A += 1
	SIF A > 0
		TFLAG:85 -= A
	SIF TFLAG:85 < 0
		TFLAG:85 = 0
ENDIF

;奉仕のクールダウン
IF TFLAG:86 > 0
	A = 2 + (A:25 + 10) / 20
	SIF TFLAG:79 != 5
		A += 3
	SIF BASE:MASTER:1 < 200
		A -= 1
	SIF BASE:MASTER:1 < 400
		A -= 1
	SIF TFLAG:60 == 5
		A += 2
	SIF TFLAG:60 == 7
		A -= 2
	SIF TEQUIP:40 || TEQUIP:46 || TEQUIP:47
		A -= 1
	SIF TEQUIP:42
		A -= 1
	SIF TALENT:15
		A += 1
	SIF TALENT:17
		A -= 1
	SIF TALENT:22
		A -= RAND:2
	SIF TALENT:23
		A += RAND:2
	SIF TALENT:29
		A += 1
	SIF TALENT:34
		A -= 1
	SIF TALENT:35
		A += 1
	SIF TALENT:63
		A -= 1
	SIF TALENT:65
		A += 1 + RAND:2
	SIF TALENT:70
		A += 2
	SIF TALENT:71
		A -= 2
	SIF TALENT:MASTER:78
		A += 1
	SIF TALENT:82 && TALENT:MASTER:122
		A -= 1
	IF TALENT:82 == 0 && ABL:9 < 1 && ABL:10 < 1
		SIF TALENT:MASTER:122 && TALENT:122
			A -= 1
		SIF TALENT:MASTER:122 == 0 && TALENT:122 == 0
			A -= 1
	ENDIF
	SIF A > 0
		TFLAG:86 -= A
	SIF TFLAG:86 < 0
		TFLAG:86 = 0
ENDIF

;加虐のクールダウン
IF TFLAG:87 > 0
	A = 3 + (A:26 + 10) / 20
	SIF TFLAG:79 != 6
		A += 1
	SIF BASE:MASTER:0 < 500
		A -= 1
	SIF BASE:MASTER:6 < 1000
		A -= 1
	SIF BASE:7 > 800
		A += 1
	SIF BASE:7 > 600
		A += 1
	SIF TFLAG:60 == 2
		A -= 1
	SIF TFLAG:60 == 3
		A -= 2
	SIF TFLAG:60 == 6
		A += 2
	SIF TFLAG:60 == 8
		A += 1
	SIF TALENT:15
		A += 1
	SIF TALENT:17
		A -= 1
	SIF TALENT:14
		A -= 2
	SIF TALENT:16
		A += 2
	SIF TALENT:53
		A += 1
	SIF TALENT:58
		A += 1
	SIF TALENT:MASTER:77
		A += 2
	SIF TALENT:83
		A += 2
	SIF TALENT:86
		A += 1
	SIF TALENT:87
		A -= 1
	SIF A > 0
		TFLAG:87 -= A
	SIF TFLAG:87 < 0
		TFLAG:87 = 0
ENDIF

;異常行為のクールダウン
IF TFLAG:88 > 0
	A = 1 + (A:28 + 10) * 2 / 30
	SIF TFLAG:79 != 7
		A += RAND:4
	SIF BASE:0 + BASE:1 < 600
		A -= 1
	SIF BASE:0 + BASE:1 < 1200
		A -= 1
	SIF BASE:8 > 300
		A -= 1
	SIF BASE:8 > 600
		A -= 1
	SIF TFLAG:60 == 2
		A -= 3
	SIF TFLAG:60 == 3
		A -= 1
	SIF TFLAG:60 == 4
		A += 2
	SIF TFLAG:60 == 8
		A += 4
	SIF TALENT:10
		A -= 2
	SIF TALENT:12
		A += RAND:2
	SIF TALENT:20
		A -= 1
	SIF TALENT:21
		A += 1
	SIF TALENT:32
		A -= 2
	SIF TALENT:33
		A += RAND:3
	SIF TALENT:88
		A += 1
	SIF TALENT:89
		A += 3
	SIF TALENT:115
		A += RAND:2
	SIF TALENT:130
		A += 1
	SIF A > 0
		TFLAG:88 -= A
	SIF TFLAG:88 < 0
		TFLAG:88 = 0
ENDIF

;使役のクールダウン
IF TFLAG:89 > 0
	A = RAND:2 + (A:28 + 10) * 2 / 30
	SIF TFLAG:79 != 8
		A += 1
	SIF BASE:MASTER:0 + BASE:MASTER:1 < 600
		A -= 2
	SIF BASE:MASTER:0 + BASE:MASTER:1 < 1200
		A -= 2
	SIF BASE:6 < 200
		A += 1
	SIF BASE:6 < 400
		A += 1
	SIF TFLAG:60 == 5
		A -= 1
	SIF TFLAG:60 == 7
		A += 3
	SIF TFLAG:60 == 8
		A += 1
	SIF TEQUIP:90
		A += 4 + RAND:5
	SIF TALENT:22
		A += 1
	SIF TALENT:23
		A -= 1
	SIF TALENT:28
		A -= 2
	SIF TALENT:88
		A += 1
	SIF TALENT:89
		A += 2
	SIF TALENT:130
		A += 3
	SIF A > 0
		TFLAG:89 -= A
	SIF TFLAG:89 < 0
		TFLAG:89 = 0
ENDIF

;性交(奉仕)のクールダウン
IF TFLAG:350 > 0
	A = 1
	SIF TFLAG:79 != 10
		A += 1
	SIF TFLAG:60 == 3
		A += RAND:2
	SIF TFLAG:60 == 5
		A -= 3
	SIF TFLAG:60 == 7
		A += 1
	;挿入してる間だけ速いスピードでクールダウンする
	SIF TEQUIP:71 && TFLAG:80 != 10
		A += 7
	SIF TALENT:82 && TALENT:MASTER:122
		A += 3
	SIF TALENT:78
		A -= 1
	SIF TFLAG:77 == 7
		A += 5
	SIF A > 0
		TFLAG:350 -= A
	SIF TFLAG:350 < 0
		TFLAG:350 = 0
ENDIF
;────────────────────────────────────
;イかせる、焦らす、エナジードレイン
;────────────────────────────────────
;汎用連続行動フラグ
IF TFLAG:121
	TFLAG:216 = TFLAG:80
	TFLAG:211 = TFLAG:90
ENDIF
;エナジードレインリセット
SIF TFLAG:166 == 1000
	TFLAG:166 = 0
;イキそう、焦らしのリセット
IF NOWEX:11
	IF TFLAG:166
		TFLAG:170 = 2
	ELSE
		TFLAG:170 = 0
	ENDIF
	TFLAG:126 = 0
ENDIF
;女MASTER
IF !PENIS(MASTER) &&  NOWEX:0
	TFLAG:166 = 0
	TFLAG:126 = 0
ENDIF
SIF TFLAG:69
	TFLAG:126 = 0
IF TFLAG:126
	IF TFLAG:80 == 10
		TFLAG:216 = 10
	ELSE
		TFLAG:216 = 1
	ENDIF
	SIF TFLAG:90 == 10 || TFLAG:90 == 12 || TFLAG:90 == 16 || TFLAG:90 == 17 || TFLAG:90 == 18
		TFLAG:211 = TFLAG:90
	IF TEQUIP:71
		TFLAG:216 = 10
		TFLAG:211 = -1
	ENDIF
ENDIF

IF TFLAG:166
	IF TEQUIP:71
		TFLAG:216 = 10
	ELSE
		TFLAG:216 = 1
	ENDIF
ENDIF

;────────────────────────────────────
;データベースの登録、ターンのリセット
;────────────────────────────────────

CALL DATABASE_INPUT

FOR LOCAL,0,CHARANUM
	;体位の保存
	FOR LOCAL:1,1,11
		TEQUIP:LOCAL:(110 + LOCAL:1) = TEQUIP:LOCAL:(100 + LOCAL:1)
	NEXT
NEXT

;TEQUIPの保存
;性交
TFLAG:116 = TEQUIP:70
TFLAG:117 = TEQUIP:71
;顔面騎乗
TFLAG:118 = TEQUIP:44
TFLAG:119 = TEQUIP:45


;顔射フラグのリセット
TFLAG:22 = 0
TFLAG:23 = 0
;我慢フラグ
SIF TFLAG:168
	TFLAG:168 -= 1
SIF NOWEX:0 || NOWEX:1 || NOWEX:2 || NOWEX:3
	TFLAG:168 = 0
;射精後のターン数
SIF TFLAG:33
	TFLAG:33 += 1
;膣内射精後のターン数
SIF TFLAG:34
	TFLAG:34 += 1
;調教のターン数
TFLAG:50 ++
;失禁フラグ
TFLAG:107 = 0
;連続交合フラグ
SIF TFLAG:221 || TFLAG:222
	TFLAG:115 = 1
;今回のコマンドを「前回のコマンド」とする
PREVCOM = SELECTCOM
TFLAG:76 = TFLAG:70
TFLAG:79 = TFLAG:80
TFLAG:91 = TFLAG:90
TFLAG:171 = TFLAG:172
;射精フラグをリセット
TFLAG:18 = 0
;調教者行動済みのフラグをリセット
TFLAG:65 = 0
;実行可能反応の数をリセット
TFLAG:92 = 0
;行動パターンをリセット
TFLAG:93 = 0
;調教対象の反応による影響をリセット
TFLAG:94 = 0
;好感度/経験値をリセット
TFLAG:95 = 0
TFLAG:96 = 0
;仰向け状態の解除
SIF TEQUIP:37 || TEQUIP:44 || TEQUIP:45
	TFLAG:112 = 2
SIF TFLAG:112 > 0
	TFLAG:112 -= 1
;SP行動のリセット
TFLAG:120 = 0
;汎用連続行動フラグのリセット
TFLAG:121 = 0
;勃起計算のリセット
TFLAG:130 = 0
;印象のリセット
TFLAG:125 = 0
;助手プレイ時のTEQUIPをリセット
TFLAG:174 = 0
TFLAG:175 = 0
TFLAG:176 = 0
TFLAG:177 = 0
TFLAG:178 = 0
TFLAG:179 = 0
TFLAG:180 = 0
TFLAG:181 = 0
;追加行動フラグのリセット
TFLAG:229 = 0
TFLAG:230 = 0
TFLAG:231 = 0
TFLAG:232 = 0
TFLAG:233 = 0
;口上発生フラグ等のリセット
TFLAG:305 = 0
TFLAG:306 = 0
TFLAG:307 = 0
TFLAG:308 = 0
;状態異常
SIF TFLAG:371
	TFLAG:371 --
SIF TFLAG:372
	TFLAG:372 --
SIF TFLAG:372
	TIMES BASE:MASTER:5, 0.8
SIF TFLAG:373 && !CFLAG:MASTER:220
	TFLAG:373 --
;なすがまま
IF TFLAG:371
	TFLAG:370 = 1
ELSE
	TFLAG:370 = 0
ENDIF
;逃走COMABLE用フラグ
SIF TFLAG:404
	TFLAG:404 --
;絶頂による逃走判定ボーナス
SIF TFLAG:403
	TFLAG:403 --
;調教指令
TSTR:2 = %TSTR:1%
;絶頂の記録
TCVAR:MASTER:11 = NOWEX:11
TCVAR:11 = NOWEX:51
;────────────────────────────────────
;パラメータの上昇＆表示（DOWNもここで）
;────────────────────────────────────
SIF UP:0
	CFLAG:MASTER:30 += 10
SIF UP:1
	CFLAG:MASTER:31 += 10
SIF UP:2
	CFLAG:MASTER:32 += 10
SIF UP:3
	CFLAG:MASTER:33 += 10
SIF UP:40
	CFLAG:30 += 10
SIF UP:41
	CFLAG:31 += 10
SIF UP:42
	CFLAG:32 += 10
SIF UP:43
	CFLAG:33 += 10
	
FOR LOCAL , 0 , CHARANUM
	SIF CFLAG:LOCAL:30 > 50
		CFLAG:LOCAL:30 -= 1
	SIF CFLAG:LOCAL:31 > 50
		CFLAG:LOCAL:31 -= 1
	SIF CFLAG:LOCAL:32 > 50
		CFLAG:LOCAL:32 -= 1
	SIF CFLAG:LOCAL:33 > 50
		CFLAG:LOCAL:33 -= 1
NEXT

SIF UP:0 + UP:1 + UP:2 + UP:3 < 1
	TFLAG:99 |= 1
SIF UP:0 + UP:1 + UP:2 + UP:3 < 20
	TFLAG:99 |= 2
SIF UP:0 < 1
	TFLAG:99 |= 4
SIF UP:1 < 1
	TFLAG:99 |= 8
SIF UP:2 < 1
	TFLAG:99 |= 16
SIF UP:3 < 1
	TFLAG:99 |= 32
SIF UP:40 + UP:41 + UP:42 + UP:43 < 1
	TFLAG:99 |= 64
SIF UP:40 + UP:41 + UP:42 + UP:43 < 20
	TFLAG:99 |= 128
SIF UP:40 < 1
	TFLAG:99 |= 256
SIF UP:41 < 1
	TFLAG:99 |= 512
SIF UP:42 < 1
	TFLAG:99 |= 1024
SIF UP:43 < 1
	TFLAG:99 |= 2048
SIF UP:6 < 1
	TFLAG:99 |= 4096
SIF UP:7 < 1
	TFLAG:99 |= 8192
SIF UP:7 < 50
	TFLAG:99 |= 65536
SIF UP:8 < 1
	TFLAG:99 |= 16384
SIF UP:9 < 1
	TFLAG:99 |= 32768
SIF UP:10 < 1
	TFLAG:99 |= 131072
SIF UP:11 < 1
	TFLAG:99 |= 262144
SIF UP:12 < 1
	TFLAG:99 |= 524288
SIF UP:13 < 1
	TFLAG:99 |= 1048576

PRINTL 　
UPCHECK

REPEAT 50
	UP:COUNT = 0
	DOWN:COUNT = 0
	SOURCE:COUNT = 0
REND
PRINTW 　

;────────────────────────────────────
;パラメーターの自然変動
;────────────────────────────────────
;各感覚
IF TFLAG:99 & 1
	SIF TFLAG:170 == 0
		TIMES PALAM:0 , 0.95
	TIMES PALAM:1 , 0.95
	TIMES PALAM:2 , 0.95
	TIMES PALAM:3 , 0.95
ELSEIF TFLAG:99 & 2
	SIF TFLAG:170 == 0
		TIMES PALAM:0 , 0.99
	TIMES PALAM:1 , 0.99
	TIMES PALAM:2 , 0.99
	TIMES PALAM:3 , 0.99
ENDIF
IF TFLAG:99 & 4 
	PALAM:0 -= PALAM:0 / 25
	TFLAG:170 /= 2
ELSE
	TIMES TFLAG:170 , 1.20
ENDIF
IF TFLAG:170 < ABL:MASTER:3 * 2
	TFLAG:169 = 1
ELSE
	TFLAG:169 = 0
ENDIF
SIF TFLAG:99 & 8
	PALAM:1 -= PALAM:1 / 25
SIF TFLAG:99 & 16
	PALAM:2 -= PALAM:2 / 25
SIF TFLAG:99 & 32
	PALAM:3 -= PALAM:3 / 25

IF TFLAG:99 & 64
	TIMES PALAM:40 , 0.95
	TIMES PALAM:41 , 0.95
	TIMES PALAM:42 , 0.95
	TIMES PALAM:43 , 0.95
ELSEIF TFLAG:99 & 128
	TIMES PALAM:40 , 0.99
	TIMES PALAM:41 , 0.99
	TIMES PALAM:42 , 0.99
	TIMES PALAM:43 , 0.99
ENDIF
SIF TFLAG:99 & 256
	PALAM:40 -= PALAM:40 / 25
SIF TFLAG:99 & 512
	PALAM:41 -= PALAM:41 / 25
SIF TFLAG:99 & 1024
	PALAM:42 -= PALAM:42 / 25
SIF TFLAG:99 & 2048
	PALAM:43 -= PALAM:43 / 25

;潤滑
SIF TEQUIP:10 == 0
	PALAM:4 -= PALAM:4 / 20

;欲情
IF NOWEX:0 + NOWEX:1 + NOWEX:2 + NOWEX:3 > 0
	IF PALAM:5 > 8000
		TIMES PALAM:5 , 0.40
	ELSEIF PALAM:5 > 6000
		TIMES PALAM:5 , 0.50
	ELSEIF PALAM:5 > 5000
		TIMES PALAM:5 , 0.60
	ELSEIF PALAM:5 > 4000
		TIMES PALAM:5 , 0.65
	ELSE
		TIMES PALAM:5 , 0.70
	ENDIF
ELSEIF TFLAG:99 & 2
	IF PALAM:5 > 6000
		TIMES PALAM:5 , 0.90
	ELSEIF PALAM:5 > 4000
		TIMES PALAM:5 , 0.95
	ELSEIF PALAM:5 > 2000
		TIMES PALAM:5 , 0.99
	ENDIF
ENDIF

SIF TFLAG:99 & 4096
	PALAM:6 -= PALAM:6 / 20
SIF TFLAG:99 & 8192
	PALAM:7 -= PALAM:7 / 12
SIF TFLAG:99 & 65536
	PALAM:7 -= PALAM:7 / 5
SIF TFLAG:99 & 16384
	PALAM:8 -= PALAM:8 / 20
SIF TFLAG:99 & 32768
	PALAM:9 -= PALAM:9 / 18
SIF TFLAG:99 & 131072
	PALAM:10 -= PALAM:10 / 10
SIF TFLAG:99 & 262144
	PALAM:11 -= PALAM:11 / 10
SIF TFLAG:99 & 524288
	PALAM:12 -= PALAM:12 / 12
SIF TFLAG:99 & 1048576 && TFLAG:90 != 4
	PALAM:13 -= PALAM:13 / 20
SIF TFLAG:90 == 3 && TFLAG:94 < 4
	PALAM:13 -= PALAM:13 / 5

REPEAT 50
	IF PALAM:COUNT > 9999
		A = PALAM:COUNT - 9999
		PALAM:COUNT = 9999
		IF COUNT > 4 && A > EXP:MASTER:50 * 50
			EXP:MASTER:50 += 1
			PRINTL （突破参数极限）异常经验+1
		ENDIF
	ENDIF
	SIF PALAM:COUNT < 0
		PALAM:COUNT = 0
REND

TFLAG:99 = 0

;────────────────────────────────────
;調教終了の判定
;────────────────────────────────────
A = TFLAG:64 / 10
A += TFLAG:62 / 2
A += TFLAG:63 / 3

SIF TFLAG:64 < 20
	A -= 3
SIF TFLAG:64 < 30
	A -= 1
SIF TFLAG:64 > 30
	A += 1
SIF TFLAG:64 > 40
	A += 2
SIF TFLAG:64 > 50
	A += 2
SIF TFLAG:64 > 60
	A += 3
SIF TFLAG:64 > 70
	A += 3
SIF TFLAG:64 > 80
	A += 4
SIF TFLAG:64 > 90
	A += 4
SIF TFLAG:64 > 100
	A += 5 + TFLAG:64 / 10

IF BASE:6 < 100
	A += 5
ELSEIF BASE:6 < 250
	A += 3
ELSEIF BASE:6 < 400
	A += 1
ELSEIF BASE:6 > 800
	A -= 3
ELSEIF BASE:6 > 600
	A -= 1
ENDIF

SIF TFLAG:60 == 3
	A += 3
SIF TFLAG:60 == 4
	A += 1
SIF TFLAG:60 == 5
	A -= 2
SIF TFLAG:60 == 6
	A -= 5
SIF TFLAG:60 == 7
	A += 5

;今回の調教で大満足ボーナスを取得した
SIF TFLAG:67 > 0
	A += 5

A = MAX(A, 1)
B = TFLAG:77 == 3 ? 5 # 0

;※イベント戦闘/ダンジョン戦闘/一回休みに「調教終了口上」を入れようかと思ったけどやめた
;イベント戦闘
IF FLAG:1710 == 1
	IF BASE:MASTER:5 <= 0 && BASE:MASTER:1 <= 0 && BASE:MASTER:2 <= 0 && NO:TARGET != 48
		PRINTFORML %CALLNAME:MASTER%被%CALLNAME:TARGET%给予的快乐所俘虏…
		FLAG:1710 = 2
		BEGIN AFTERTRAIN
	ELSEIF TFLAG:63 >= 10
		PRINTFORML %CALLNAME:MASTER%失神了…
		FLAG:1710 = 3
		BEGIN AFTERTRAIN
	ENDIF
;ダンジョン戦闘
ELSEIF FLAG:1700
	IF BASE:MASTER:5 <= 0 && BASE:MASTER:2 <= 0
		IF BASE:MASTER:9
			BASE:MASTER:0 = MAX(MAXBASE:MASTER:0, BASE:MASTER:0 + MAXBASE:MASTER:0 * BASE:MASTER:9 / (2 * MAXBASE:MASTER:9))
			BASE:MASTER:1 += MAXBASE:MASTER:1 * BASE:MASTER:9 / (2 * MAXBASE:MASTER:9)
			BASE:MASTER:2 += MAXBASE:MASTER:2 * BASE:MASTER:9 / (2 * MAXBASE:MASTER:9)
			BASE:MASTER:5 += MAXBASE:MASTER:5 * BASE:MASTER:9 / (2 * MAXBASE:MASTER:9)
			BASE:MASTER:9 --
			TFLAG:63 = 0
			PRINTFORMW %CALLNAME:MASTER%咬紧牙关，使出了最后的力量…
		ELSE
			PRINTFORML %CALLNAME:MASTER%被%CALLNAME:TARGET%给予的快乐所俘虏…
			FLAG:1701 = 1
			BEGIN AFTERTRAIN
		ENDIF
	ELSEIF TFLAG:63 >= 10
		IF BASE:MASTER:9
			BASE:MASTER:0 = MAX(MAXBASE:MASTER:0, BASE:MASTER:0 + MAXBASE:MASTER:0 * BASE:MASTER:9 / (2 * MAXBASE:MASTER:9))
			BASE:MASTER:1 += MAXBASE:MASTER:1 * BASE:MASTER:9 / (2 * MAXBASE:MASTER:9)
			BASE:MASTER:2 += MAXBASE:MASTER:2 * BASE:MASTER:9 / (2 * MAXBASE:MASTER:9)
			BASE:MASTER:5 += MAXBASE:MASTER:5 * BASE:MASTER:9 / (2 * MAXBASE:MASTER:9)
			BASE:MASTER:9 --
			TFLAG:63 = 0
			PRINTFORMW %CALLNAME:MASTER%咬紧牙关，使出了最后的力量…
		ELSE
			PRINTFORML %CALLNAME:MASTER%失神了…
			FLAG:1701 = 1
			BEGIN AFTERTRAIN
		ENDIF
	ENDIF
;一回休み
ELSEIF TFLAG:101 == 1
	FLAG:13 = 3
	PRINTL 
	BEGIN AFTERTRAIN
;通常の調教(11カウント以降)
ELSEIF TFLAG:64 > 10
	IF RAND:A > 10 + (A:30 / 20) + 5
		;調教後フラグ
		FLAG:13 = 1
		;精力0
		SIF BASE:MASTER:2 <= 0
			FLAG:13 = 2
		CALL KOJO_EVENT(13)
		BEGIN AFTERTRAIN
	ELSEIF TFLAG:63 > 5 + TALENT:MASTER:110 - TALENT:MASTER:111 + TALENT:MASTER:125 - TALENT:MASTER:126 + CFLAG:MASTER:0 / 3 + RAND:5
		IF BASE:MASTER:0 + BASE:MASTER:1 > 600
			PRINTFORML 调教得太激烈了，%CALLNAME:MASTER%好像很累，%CALLNAME:TARGET%结束了调教
			CALL KOJO_EVENT(13,1)
			BEGIN AFTERTRAIN
		ELSE
			FLAG:13 = 4
			PRINTFORML 调教得太激烈了，%CALLNAME:MASTER%失神了
			IF !GETBIT(CFLAG:300, 56)
				PRINTL 【失神】屈服点+5
				CFLAG:301 += 5
				SETBIT CFLAG:300, 56
			ENDIF
			CALL KOJO_EVENT(13,2)
			BEGIN AFTERTRAIN
		ENDIF
	ENDIF
ENDIF

;────────────────────────────────────
;絶頂や時間経過等によるTEQUIPの解除
;────────────────────────────────────
;※BEGINの解決はその時点で実行できる全てのコードの処理が終わった後で初めて行われます
;※TEQUIP解除はこのタイミングでないと、調教終了口上から体位の参照等が出来ません
;※基本的には全部のTEQUIP解除はギリギリまで引っ張った方が便利かもしれませんが…
;※各所を整合するのも面倒なのでとりあえず必要と思われる分だけ。残りは近いうちに

;調教者射精による性交解除
TEQUIP:70 = NOWEX:51 ? 0 # TEQUIP:70
;調教対象射精(継続なし)による性交奉仕解除
TEQUIP:71 = NOWEX:11 && !TFLAG:106 ? 0 # TEQUIP:71
;調教対象射精(継続なし)によるC愛撫解除
IF TEQUIP:100 && NOWEX:11
	SELECTCASE TEQUIP:100
		CASE 1
			IF TFLAG:30 == 1
				TEQUIP:100 = 0
				TEQUIP:103 = 0
			ELSEIF RAND:2 || TFLAG:166 == 1000
				TEQUIP:100 = 0
				TEQUIP:103 = 0
				PRINTFORMW %CALLNAME:TARGET%津津有味地舔着粘在手上的%CALLNAME:MASTER%精液……
			ELSE
				PRINTFORML %CALLNAME:TARGET%在阴茎上涂满精液，
				PRINTFORMW 用缓慢的手淫催促着%CALLNAME:MASTER%下一波的精液……
			ENDIF
		CASE 2
			IF TFLAG:30 == 1
				TEQUIP:100 = 0
				TEQUIP:104 = 0
			ELSEIF RAND:2 || TFLAG:166 == 1000
				TEQUIP:100 = 0
				TEQUIP:104 = 0
				PRINTFORML %CALLNAME:TARGET%收回嘴唇慢慢地离开阴茎
				PRINTFORMW 向%CALLNAME:MASTER%展示了口中的精液后，大口吞咽了进去……
			ELSE
				PRINTFORML %CALLNAME:TARGET%把射入嘴中的精液吞下
				PRINTFORMW 亲吻龟头，吸吮尿道中的精液……
			ENDIF
	ENDSELECT
ENDIF
;調教対象絶頂による調教者自慰解除
SIF NOWEX:0 || NOWEX:1 || NOWEX:2 || NOWEX:3 || NOWEX:11
	CLEARBIT TEQUIP:69, 0
;調教者絶頂による調教者自慰解除
SIF NOWEX:40 || NOWEX:41 || NOWEX:42 || NOWEX:43
	CLEARBIT TEQUIP:69, 1

@KOJO_EVENT_13(ARG)
PRINTL 
DRAWLINE
CALL KOJO_EVENT(13, ARG)
SIF RESULT
	PRINTL
BEGIN AFTERTRAIN
DRAWLINE
