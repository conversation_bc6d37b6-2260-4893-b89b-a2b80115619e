﻿;梦魔の個性の設定
@PERSONALITY
;素質の変動
A = 0
B = 0
C = 0
D = 0
E = 0
F = 0
G = 0
H = 0
;S-M軸 100ならばどS
A = RAND:101
B = 100 - A
CFLAG:150 = A - 50
;年長-年少軸 姉
C = RAND:101
D = 100 - C
CFLAG:151 = C - 50
;ツン-デレ軸 ツン
E = RAND:101
F = 100 - E
CFLAG:152 = E - 50
;ビッチ軸
G = RAND:101
H = 100 - G
CFLAG:153 = G - 50

;暫定素質の初期化
REPEAT 200
	Z:COUNT = 0
REND
;性格の強弱にあわせてZ:に0～100の数値を代入
REPEAT 200
	SIF TALENT:COUNT
		Z:COUNT = 100
	IF A > B
		;S系素質
		IF COUNT == 11 || COUNT == 15 || COUNT == 53 || COUNT == 58 || COUNT == 83 || COUNT == 86 || COUNT == 90
			SIF A > Z:COUNT
				Z:COUNT = A
		ENDIF
	ELSE
		;M系素質
		IF COUNT == 17 || COUNT == 41 || COUNT == 65 || COUNT == 80
			SIF B > Z:COUNT
				Z:COUNT = B
		ENDIF
	ENDIF
	IF C > D
		;姉属性
		IF COUNT == 12 || COUNT == 14 || COUNT == 20 || COUNT == 41 || COUNT == 84 || COUNT == 87 
			SIF C > Z:COUNT
				Z:COUNT = C
		ENDIF
	ELSE
		;妹属性
		IF COUNT == 10 || COUNT == 16 || COUNT == 23 || COUNT == 40 || COUNT == 85 || COUNT == 88
			SIF D > Z:COUNT
				Z:COUNT = D
		ENDIF
	ENDIF
	IF E > F
		;ツン
		IF COUNT == 11 || COUNT == 15 || COUNT == 22 || COUNT == 28 || COUNT == 62 || COUNT == 71 || COUNT == 82 || COUNT == 86 || COUNT == 85 || COUNT == 93
			SIF E > Z:COUNT
				Z:COUNT = E
		ENDIF
	ELSE
		;デレ
		IF COUNT == 13 || COUNT == 21 || COUNT == 42 || COUNT == 31 || COUNT == 61 || COUNT == 63 || COUNT == 70 || COUNT == 87 || COUNT == 92
			SIF F > Z:COUNT
				Z:COUNT = F
		ENDIF
	ENDIF
	IF G > H
		;ビッチ
		IF COUNT == 21 || COUNT == 25 || COUNT == 29 || COUNT == 31 || COUNT == 32 || COUNT == 35 || COUNT == 36 || COUNT == 42 || COUNT == 52 || COUNT == 57 || COUNT == 59 || COUNT == 60 || COUNT == 61 || COUNT == 63 || COUNT == 70 || COUNT == 81 || COUNT == 92
			SIF G > Z:COUNT
				Z:COUNT = G
		ENDIF
	ELSE
		;貞淑
		IF COUNT == 13 || COUNT == 22 || COUNT == 28 || COUNT == 30 || COUNT == 33 || COUNT == 34 || COUNT == 37 || COUNT == 43 || COUNT == 62 || COUNT == 65 || COUNT == 71
			SIF H > Z:COUNT
				Z:COUNT = H
		ENDIF
	ENDIF
REND

;背反的な素質の処理、
REPEAT 100
IF (COUNT >= 10 && COUNT <=13) || (COUNT >= 15 && COUNT <= 21) || COUNT == 25 || COUNT == 35 || COUNT == 42 || COUNT == 43 || COUNT == 46 || COUNT == 92 || (COUNT >= 50 && COUNT <= 53) || COUNT == 56
	IF Z:(2 * COUNT) && Z:(2 * COUNT + 1)
		IF Z:(2 * COUNT) > Z:(2 * COUNT + 1)
			Z:(2 * COUNT) -= Z:(2 * COUNT + 1)
			Z:(2 * COUNT + 1) = 0
		ELSE
			Z:(2 * COUNT + 1) -= Z:(2 * COUNT)
			Z:(2 * COUNT) = 0
		ENDIF
	ENDIF
ELSE
	CONTINUE
ENDIF
REND

;臆病-気丈
IF Z:10 && Z:12
	IF Z:10 > Z:12
		Z:10 -= Z:12
		Z:12 = 0
	ELSE
		Z:12 -= Z:10
		Z:10 = 0
	ENDIF
ENDIF
;反抗的-素直
IF Z:11 && Z:13
	IF Z:11 > Z:13
		Z:11 -= Z:13
		Z:13 = 0
	ELSE
		Z:13 -= Z:11
		Z:11 = 0
	ENDIF
ENDIF
;おとなしい-生意気
IF Z:14 && Z:16
	IF Z:14 > Z:16
		Z:14 -= Z:16
		Z:16 = 0
	ELSE
		Z:16 -= Z:14
		Z:14 = 0
	ENDIF
ENDIF
;プライド高い-プライド低い
IF Z:15 && Z:17
	IF Z:15 > Z:17
		Z:15 -= Z:17
		Z:17 = 0
	ELSE
		Z:17 -= Z:15
		Z:15 = 0
	ENDIF
ENDIF
;汚臭鈍感-汚臭敏感
IF Z:61 && Z:62
	IF Z:61 > Z:62
		Z:61 -= Z:62
		Z:62 = 0
	ELSE
		Z:62 -= Z:61
		Z:61 = 0
	ENDIF
ENDIF
;献身的-受身
IF Z:63 && Z:65
	IF Z:63 > Z:65
		Z:63 -= Z:65
		Z:65 = 0
	ELSE
		Z:65 -= Z:63
		Z:63 = 0
	ENDIF
ENDIF
;快感に素直-否定
IF Z:70 && Z:71
	IF Z:70 > Z:71
		Z:70 -= Z:71
		Z:71 = 0
	ELSE
		Z:71 -= Z:70
		Z:70 = 0
	ENDIF
ENDIF
;両刀-男嫌い
IF Z:81 && Z:82
	IF Z:81 > Z:82
		Z:81 -= Z:82
		Z:82 = 0
	ELSE
		Z:82 -= Z:81
		Z:81 = 0
	ENDIF
ENDIF
;素質表では目立ちたがりは一線越えないの背反らしい。恥らいとの関係はどうしよう。
;素質の決定、Z:の値をもとにランダムで素質付与
REPEAT 200
	;ユニークは除外
	SIF TALENT:170
		CONTINUE
	;固定素質(性別、夢魔のランク、後天素質、種族特性は固定、また体型に関しては3サイズで設定)いらないな、これ。なにかの役に立つかもしれないので残しておこう。
	SIF COUNT == 111 || COUNT == 170 || COUNT >= 150 && COUNT <= 160 || COUNT == 171 || COUNT == 172 || COUNT >= 72 && COUNT <= 79 || COUNT == 89 || (COUNT >= 108 && COUNT <= 111) ||(COUNT >= 121 && COUNT <= 122) || COUNT == 0 || COUNT == 1
		CONTINUE
	IF TALENT:COUNT
		;対立する素質要素が強ければ、もともと持っていた素質が打ち消される
		SIF (RAND:(101 - Z:COUNT) > 50 && RAND:3 == 0) || Z:COUNT == 0
			TALENT:COUNT = 0
	ELSE
		IF Z:COUNT > 50
			IF 2 * (Z:COUNT - 50) > RAND:100 && RAND:3 == 0
				TALENT:COUNT = 1
			ELSE
				TALENT:COUNT = 0
			ENDIF
		ELSE
			TALENT:COUNT = 0
		ENDIF
	ENDIF
REND

A = (100 - 500/(CFLAG:0 + 5))
;5%でふたなり
IF RAND:20 == 0
	TALENT:121 = 1
	SIF A + 100 < RAND:200
		TALENT:1 = 1
ENDIF
;レベルに応じて処女の可能性
SIF A + 400 < RAND:500
	TALENT:0 = 1
SIF TALENT:0
	EXP:1 = 0
IF TALENT:0 && NO:TARGET == 8
	NO:TARGET = 9
	TALENT:37 = 1
ENDIF
;小柄、妖精、ラミア、マーメイド、ハービー、スライム以外は確率で美脚
SIF !TALENT:110 && !TALENT:150 && !TALENT:152 && !TALENT:153 && !TALENT:155 && !TALENT:159 && RAND:20 == 1
	TALENT:118 = 1

;陰毛
IF TALENT:110
	LOCAL = 3
ELSE
	LOCAL = 10
ENDIF
SELECTCASE RAND:LOCAL
	CASE 0
		TALENT:119 = 2
	CASE IS <= 5
		TALENT:119 = 1
	CASE 10
		TALENT:119 = -1
	CASEELSE
ENDSELECT
@EXP_REVISION
;高レベルの夢魔の経験を設定
A = 0
B = 0
;レベル補正
;徐々に鈍化しながら無限大まで大きくなる
A = SQRT((CFLAG:0 * CFLAG:0 * 10 + 5 ) * 20)
;上限が100
B = (100 - 500/(CFLAG:0 + 5))
;LV	A		B
;0	10		0
;1	17.32	16.67
;2	22.36	28.57
;3	26.46	37.5
;4	30		44.44
;5	33.17	50
;6	36.06	54.55
;7	38.73	58.33
;8	41.23	61.54
;9	43.59	64.29
;10	45.83	66.67
;後で1/10にする。
REPEAT 60
EXP:8 += EXP:COUNT
REND
EXP:8 /= 10
SIF EXP:8 < 10
	EXP:8 = 10
SIF B == 0
	RETURN 0
;V経験(eraSQではこれとA経験しかないので、この値を基本とする)
EXP:1 = EXP:1 * A * (80 + RAND:41) / 1000
;C経験(処女は補正する)
EXP:0 = (EXP:1 + (TALENT:0 * A / 2)) * ( 80 + RAND:41) / 100
;B経験
EXP:3 = (EXP:1 + (TALENT:0 * A / 2)) * ( 80 + RAND:41) / 100
;A経験
SIF EXP:2 || B > RAND:100
EXP:2 = (EXP:2 + EXP:0 / 4) * RAND:B * ( 2 + TALENT:104 - TALENT:105) * ( 80 + RAND:41) / 20000

;絶頂経験
EXP:4 = (EXP:4 + (EXP:0 + EXP:1 + EXP:2 + EXP:4) / 40) * B * (80 + RAND:41) / 1000
;射精経験
SIF TALENT:121
	EXP:5 = EXP:4 *  (80 + RAND:41) / 200
;性交経験
EXP:7 = (EXP:1 + EXP:2 / 2) * (80 + RAND:41) / 200
SIF TALENT:0 || TALENT:1
	EXP:7 = 0
;性知識
EXP:8 = (EXP:0 + EXP:1 + EXP:2 + EXP:4)  * ( 80 + RAND:41) / (400 * (TALENT:0 + TALENT:1 + 1))
;自慰経験
EXP:10 = (EXP:10 + (EXP:0 + EXP:1 + EXP:2 + EXP:4) / 40 ) * B * (1 + TALENT:60) * ( 80 + RAND:41) / 4000
;精液経験
IF EXP:20
	EXP:20 = EXP:20 * A * (80 + RAND:41) / 1000
ELSE
	EXP:20 = RAND:((5 + CFLAG:0 / 10) / 5) * ((CFLAG:153 + 50) / 4) * A * (80 + RAND:41) / 1000
ENDIF
;奉仕快楽経験
SIF EXP:21 || B > RAND:100
	EXP:21 = (EXP:21 + EXP:0 / 10) * B * (2 + TALENT:63 - TALENT:65) * (80 + RAND:41) / 10000
;口淫経験
EXP:22 = EXP:0 * (2 + TALENT:61 - TALENT:62) * (2 + TALENT:52) * (80 + RAND:41) / 800
;手淫経験
EXP:23 = EXP:0 * (2 + TALENT:57) * (80 + RAND:41) / 400
;道具使用経験
SIF B > RAND:100
	EXP:24 = EXP:0 * (2 + TALENT:59) * (80 + RAND:41) / 800
;キス経験(口淫よりも若干多めに)
EXP:25 = EXP:0 * (2 + TALENT:52) * (80 + RAND:41) / 300
;会話経験
EXP:26 = EXP:0 * (5 + TALENT:23 + TALENT:26 + TALENT:92) * (80 + RAND:41) / 5000
;苦痛快楽経験
SIF TALENT:80 || B > RAND:100
	EXP:30 = EXP:0 / 2 * (1 + TALENT:80) * RAND:B * (80 + RAND:41) / 20000
;緊縛経験
EXP:51 = (EXP:51 * A * (80 + RAND:41) / 100) + EXP:30 * (80 + RAND:41) / 200
;レズ経験
SIF EXP:40 || TALENT:121 || TALENT:81 || B > RAND:80
	EXP:40 = (EXP:40 + EXP:0 / 10 + TALENT:121 * 5 + TALENT:81 * 5) * RAND:B * ( 80 + RAND:41) / 1000
;異常経験
SIF EXP:50 || B > RAND:500
	EXP:50 = (EXP:0 / 300 + 1) * RAND:B * ( 80 + RAND:41) / 1000
;露出経験
SIF TALENT:29 || B > RAND:150 || EXP:62
EXP:62 = (EXP:0 + EXP:0 / 50) * (2 + TALENT:29) * (80 + RAND:41) / 400

EXP:63 = A * B * (80 + RAND:41) / 3000

@ABL_SET
REPEAT 30
L:COUNT = 0
REND
;欲望
LOCAL = 1
WHILE LOCAL
	IF L:1 >= 5 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > (200 + 50 * TALENT:71 - 50 * TALENT:70) * (2 + L:1 - 4) * (10 + L:1 - 5) / 20
		L:1 += 1
	ELSEIF L:1 < 5 && CFLAG:0 > 10 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > (200 + 50 * TALENT:71 - 50 * TALENT:70)
		L:1 = 5
	ELSEIF L:1 < 4 && CFLAG:0 > 7 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > ( 120 + 30 * TALENT:71 - 30 * TALENT:70)
		L:1 = 4
		LOCAL = 0
	ELSEIF L:1 < 3 && CFLAG:0 > 4 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > (60 + 15 * TALENT:71 - 15 * TALENT:70)
		L:1 = 3
		LOCAL = 0
	ELSEIF L:1 < 2 && CFLAG:0 > 2 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > (30 + 8 * TALENT:71 - 8 * TALENT:70)
		L:1 = 2
		LOCAL = 0
	ELSEIF L:1 < 1 && CFLAG:0 > 0 && EXP:4 + 3 * EXP:5 + 3 * EXP:6 > 0
		L:1 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;技巧
LOCAL = 1
WHILE LOCAL
	IF L:2 >= 5 && EXP:22 + EXP:23 + EXP:7 > (200 - TALENT:50 * 25 + TALENT:51 * 25) * (2 + L:2 - 4) * (10 + L:2 - 5) / 20 && EXP:63 > ( 150 - TALENT:50 * 30 + TALENT:51 * 30) * (2 + L:2 - 4) * (10 + L:2 - 5) / 20
		L:2 += 1
	ELSEIF L:2 < 5 && EXP:22 + EXP:23 + EXP:7 > 200 - TALENT:50 * 25 + TALENT:51 * 25 && EXP:63 > 100 - TALENT:50 * 20 + TALENT:51 * 20
		L:2 = 5
	ELSEIF L:2 < 4 && EXP:22 + EXP:23 + EXP:7 > 140 - TALENT:50 * 12 + TALENT:51 * 12 && EXP:63 > 60 - TALENT:50 * 15 + TALENT:51 * 15
		L:2 = 4
		LOCAL = 0
	ELSEIF L:2 < 3 && EXP:22 + EXP:23 + EXP:7 > 70 - TALENT:50 * 5 + TALENT:51 * 5 && EXP:63 > 40 - TALENT:50 * 10 + TALENT:51 * 10
		L:2 = 3
		LOCAL = 0
	ELSEIF L:2 < 2 && EXP:22 + EXP:23 + EXP:7 > 30 - TALENT:50 * 2 + TALENT:51 * 2 && EXP:63 > 20 - TALENT:50 * 5 + TALENT:51 * 5
		L:2 = 2
		LOCAL = 0
	ELSEIF L:2 < 1 && EXP:63
		L:2 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;Ｃ感覚
LOCAL = 1
WHILE LOCAL
	IF L:3 >= 5 && EXP:0 > (100 - TALENT:100 * 20 + TALENT:101 * 20)  * (2 + L:3 - 4) * (10 + L:3 - 5) / 20 && L:1 > L:3 - 2 - TALENT:100 + TALENT:101
		L:3 += 1
	ELSEIF L:3 < 5 && EXP:0 > 100 - TALENT:100 * 20 + TALENT:101 * 20 && L:1 > 3 - TALENT:100 + TALENT:101
		L:3 = 5
	ELSEIF L:3 < 4 && EXP:0 > 60 - TALENT:100 * 12 + TALENT:101 * 12 && EXP:4 > 10
		L:3 = 4
		LOCAL = 0
	ELSEIF L:3 < 3 && EXP:0 > 32 - TALENT:100 * 6 + TALENT:101 * 6 && EXP:4 > 0
		L:3 = 3
		LOCAL = 0
	ELSEIF L:3 < 2 && EXP:0 > 15 - TALENT:100 * 3 + TALENT:101 * 3
		L:3 = 2
		LOCAL = 0
	ELSEIF L:3 < 1 && EXP:0 > 3 - TALENT:100 + TALENT:101
		L:3 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;V感覚
LOCAL = 1
WHILE LOCAL
	IF L:4 >= 5 && EXP:1 > (150 - TALENT:102 * 20 + TALENT:103 * 20) * (2 + L:4 - 4) * (10 + L:4 - 5) / 20 && EXP:7 > (30 - TALENT:102 * 5 + TALENT:103 * 5 )  * (2 + L:4 - 4) * (10 + L:4 - 5) / 20
		L:4 += 1
	ELSEIF L:4 < 5 && EXP:1 > 150 - TALENT:102 * 20 + TALENT:103 * 20 && EXP:7 > 30 - TALENT:102 * 5 + TALENT:103 * 5 
		L:4 = 5
	ELSEIF L:4 < 4 && EXP:1 > 80 - TALENT:102 * 12 + TALENT:103 * 12 && EXP:7 > 15 - 3 * TALENT:102 + 3 * TALENT:103
		L:4 = 4
		LOCAL = 0
	ELSEIF L:4 < 3 && EXP:1 > 40 - TALENT:102 * 6 + TALENT:103 * 6 && EXP:7 > 5 - TALENT:102 + TALENT:103
		L:4 = 3
		LOCAL = 0
	ELSEIF L:4 < 2 && EXP:1 > 20 - TALENT:102 * 3 + TALENT:103 * 3
		L:4 = 2
		LOCAL = 0
	ELSEIF L:4 < 1 && EXP:1 > 5 - TALENT:102 + TALENT:103
		L:4 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;A感覚
LOCAL = 1
WHILE LOCAL
	IF L:5 >= 5 && EXP:2 + 10 * EXP:53 > (100 - TALENT:104 * 20 + TALENT:105 * 20) * (2 + L:5 - 4) * (10 + L:5 - 5) / 20
		L:5 += 1
	ELSEIF L:5 < 5 && EXP:2 > 100 - TALENT:104 * 20 + TALENT:105 * 20 && EXP:50 > 3
		L:5 = 5
	ELSEIF L:5 < 4 && EXP:2 > 60 - TALENT:104 * 12 + TALENT:105 * 12
		L:5 = 4
		LOCAL = 0
	ELSEIF L:5 < 3 && EXP:2 > 32 - TALENT:104 * 6 + TALENT:105 * 6 && EXP:50 > 0
		L:5 = 3
		LOCAL = 0
	ELSEIF L:5 < 2 && EXP:2 > 15 - TALENT:104 * 3 + TALENT:105 * 3
		L:5 = 2
		LOCAL = 0
	ELSEIF L:5 < 1 && EXP:2 > 3 - TALENT:104 + TALENT:105
		L:5 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;B感覚
LOCAL = 1
WHILE LOCAL
	IF L:6 >= 5 && EXP:3 > (100 - TALENT:106 * 20 + TALENT:107 * 20) * (2 + L:6 - 4) * (10 + L:6 - 5) / 20 && L:1 > L:6 - 2 - TALENT:106 + TALENT:107
		L:6 += 1
	ELSEIF L:6 < 5 && EXP:3 > 100 - TALENT:106 * 20 + TALENT:107 * 20 && L:1 > 3 - TALENT:106 + TALENT:107
		L:6 = 5
	ELSEIF L:6 < 4 && EXP:3 > 60 - TALENT:106 * 12 + TALENT:107 * 12 && L:1 > 2
		L:6 = 4
		LOCAL = 0
	ELSEIF L:6 < 3 && EXP:3 > 32 - TALENT:106 * 6 + TALENT:107 * 6 && L:1 > 1
		L:6 = 3
		LOCAL = 0
	ELSEIF L:6 < 2 && EXP:3 > 15 - TALENT:106 * 3 + TALENT:107 * 3
		L:6 = 2
		LOCAL = 0
	ELSEIF L:6 < 1 && EXP:3 > 3 - TALENT:106 + TALENT:107
		L:6 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;奉仕精神
LOCAL = 1
WHILE LOCAL
	IF L:7 >= 5 && CFLAG:0 > 10 + 3 * (L:7 - 4) && EXP:21 > (150 + 30 * TALENT:65 - 30 * TALENT:63) * (2 + L:7 - 4) * (10 + L:7 - 5) / 20
		L:7 += 1
	ELSEIF L:7 < 5 && CFLAG:0 > 10 && EXP:21 > 150 + 30 * TALENT:65 - 30 * TALENT:63
		L:7 = 5
	ELSEIF L:7 < 4 && CFLAG:0 > 7 && EXP:21 > 75 + 15 * TALENT:65 - 15 * TALENT:63
		L:7 = 4
		LOCAL = 0
	ELSEIF L:7 < 3 && CFLAG:0 > 4 && EXP:21 > 30 + 6 * TALENT:65 - 6 * TALENT:63
		L:7 = 3
		LOCAL = 0
	ELSEIF L:7 < 2 && CFLAG:0 > 2 && EXP:21 > 10 + 3 * TALENT:65 - 3 * TALENT:63
		L:7 = 2
		LOCAL = 0
	ELSEIF L:7 < 1 && CFLAG:0 > 0 && EXP:21
		L:7 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;レズっ気
LOCAL = 1
WHILE LOCAL
	IF L:9 >= 5 && EXP:40 > (600 - TALENT:81 * 120) * (2 + L:9 - 4) * (10 + L:9 - 5) / 20
		L:9 += 1
	ELSEIF L:9 < 5 && EXP:40 > 600 - TALENT:81 * 120
		L:9 = 5
	ELSEIF L:9 < 4 && EXP:40 > 350 - TALENT:81 * 66
		L:9 = 4
		LOCAL = 0
	ELSEIF L:9 < 3 && EXP:40 > 150 - TALENT:81 * 32
		L:9 = 3
		LOCAL = 0
	ELSEIF L:9 < 2 && EXP:40 > 60 - TALENT:81 * 15
		L:9 = 2
		LOCAL = 0
	ELSEIF L:9 < 1 &&  EXP:40 > 20 - TALENT:81 * 10
		L:9 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;精液中毒
LOCAL = 1
WHILE LOCAL
	IF L:13 >= 5 && L:1 > L:13 && EXP:20 > 240 * (2 + L:13 - 4) * (10 + L:13 - 5) / 20
		L:13 += 1
	ELSEIF L:13 < 5 && L:1 > 4 && EXP:20 > 240
		L:13 = 5
	ELSEIF L:13 < 4 && L:1 > 3 && EXP:20 > 120
		L:13 = 4
		LOCAL = 0
	ELSEIF L:13 < 3 && L:1 > 2 && EXP:20 > 60
		L:13 = 3
		LOCAL = 0
	ELSEIF L:13 < 2 && L:1 > 1 && EXP:20 > 30
		L:13 = 2
		LOCAL = 0
	ELSEIF L:13 < 1 && L:1 > 0 && EXP:20 > 15
		L:13 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;レズ中毒は好感度依存なので放置ということで
;会話
LOCAL = 1
WHILE LOCAL
	IF L:20 >= 5 && EXP:26 > 80 * (2 + L:20 - 4) * (10 + L:20 - 5) / 50
		L:20 += 1
	ELSEIF L:20 < 5 && EXP:26 > 40
		L:20 = 5
	ELSEIF L:20 < 4 && EXP:26 > 30
		L:20 = 4
		LOCAL = 0
	ELSEIF L:20 < 3 && EXP:26 > 20
		L:20 = 3
		LOCAL = 0
	ELSEIF L:20 < 2 && EXP:26 > 10
		L:20 = 2
		LOCAL = 0
	ELSEIF L:20 < 1 && EXP:26 > 5
		L:20 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;愛撫
LOCAL = 1
IF L:2 < 5
L:21 = RAND:(L:2 + 1)
ELSE
L:21 = RAND:(L:2 - 3 ) + 3
ENDIF
;道具
LOCAL = 1
WHILE LOCAL
	IF L:22 >= 5 && EXP:24 > (55 - TALENT:50 * 8 + TALENT:51 * 8) * (2 + L:22 - 4) * (10 + L:22 - 5) / 20
		L:22 += 1
	ELSEIF L:22 < 5 && EXP:24 > 55 - TALENT:50 * 8 + TALENT:51 * 8
		L:22 = 5
	ELSEIF L:22 < 4 && EXP:24 > 30 - TALENT:50 * 5 + TALENT:51 * 5
		L:22 = 4
		LOCAL = 0
	ELSEIF L:22 < 3 && EXP:24 > 15 - TALENT:50 * 2 + TALENT:51 * 2
		L:22 = 3
		LOCAL = 0
	ELSEIF L:22 < 2 && EXP:24 > 5
		L:22 = 2
		LOCAL = 0
	ELSEIF L:22 < 1 && EXP:24 > 2
		L:22 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;性交
LOCAL = 1
WHILE LOCAL
	IF L:23 >= 5 && EXP:7 > (80 - TALENT:50 * 6 + TALENT:51 * 6) * (2 + L:23 - 4) * (10 + L:23 - 5) / 20
		L:23 += 1
	ELSEIF L:23 < 5 && EXP:7 > 80 - TALENT:50 * 6 + TALENT:51 * 6
		L:23 = 5
	ELSEIF L:23 < 4 && EXP:7 > 50 - TALENT:50 * 4 + TALENT:51 * 4
		L:23 = 4
		LOCAL = 0
	ELSEIF L:23 < 3 && EXP:7 > 30 - TALENT:50 * 2 + TALENT:51 * 2
		L:23 = 3
		LOCAL = 0
	ELSEIF L:23 < 2 && EXP:7 > 15 - TALENT:50 + TALENT:51
		L:23 = 2
		LOCAL = 0
	ELSEIF L:23 < 1 && EXP:7 > 3
		L:23 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND
;羞恥
IF L:2 < 5
	L:24 = RAND:(L:2 + 1)
ELSE
	L:24 = RAND:(L:2 - 3 ) + 3
ENDIF
;奉仕
IF L:2 < 5
	L:25 = RAND:(L:2 + TALENT:65 + 1)
ELSE
	L:25 = (RAND:(L:2 - 3 ) + 3) * (3 + TALENT:65 - TALENT:63) / 3
ENDIF

;加虐
IF L:2 < 5
	L:26 = RAND:(L:2 + TALENT:83 + 1)
ELSE
	L:26 = (RAND:(L:2 - 3 ) + 3) * (3 + TALENT:83) / 3
ENDIF
;異常
LOCAL = 1
WHILE LOCAL
	IF L:27 >= 5 && EXP:50 > (25 - TALENT:50 * 4 + TALENT:51 * 4) *  (2 + L:27 - 4) * (10 + L:27 - 5) / 20
		L:27 += 1
	ELSEIF L:27 < 5 && EXP:50 > 25 - TALENT:50 * 4 + TALENT:51 * 4
		L:27 = 5
	ELSEIF L:27 < 4 && EXP:50 > 15 - TALENT:50 * 3 + TALENT:51 * 3
		L:27 = 4
		LOCAL = 0
	ELSEIF L:27 < 3 && EXP:50 > 8 - TALENT:50 * 2 + TALENT:51 * 2
		L:27 = 3
		LOCAL = 0
	ELSEIF L:27 < 2 && EXP:50 > 3 - TALENT:50 + TALENT:51
		L:27 = 2
		LOCAL = 0
	ELSEIF L:27 < 1 && EXP:50 > 1
		L:27 = 1
		LOCAL = 0
	ELSE
		LOCAL = 0
	ENDIF
WEND

REPEAT 30
SIF L:COUNT > 99
	L:COUNT = 99
SIF ABL:COUNT < L:COUNT
	ABL:COUNT = L:COUNT
REND

;スキル
;追加愛撫
SIF A > RAND:80
	TALENT:200 = 1
;エナジードレイン
SIF RAND:(A + 1) > RAND:100 && (NO:TARGET == 1 || NO:TARGET == 25)
	TALENT:201 = 2
;ディープスロート
SIF EXP:22 > RAND:500
	TALENT:202 = 1

;状態異常付与
LOCAL:1 = RAND:100 + 1
LOCAL:2 = RAND:100 + 1
LOCAL:3 = RAND:100 + 1
CFLAG:100 = 100 * LOCAL:1 / (LOCAL:1 + LOCAL:2 + LOCAL:3)
CFLAG:101 = 100 * LOCAL:2 / (LOCAL:1 + LOCAL:2 + LOCAL:3)
CFLAG:102 = 100 - TALENT:220 -TALENT:221

@SIZE_SET
;設定自体しない
SIF FLAG:2001 == 0 || TALENT:122
	RETURN 0
	
;既に設定されている
IF CFLAG:900
	RETURN 0
ENDIF
;ゴールデンカノン (B = T*0.54, W = T*0.38, H = T*0.54)を参考。
T = 0
B = 0
W = 0
H = 0
;妖精
IF TALENT:150
	T = 65 + RAND:11
	W = 26 * T * (95 + RAND:11) / (100 * 70)
	H = 35 * T * (97 + RAND:7) / (100 * 70)
;小柄体系 135～144cm
ELSEIF TALENT:110
	T = 135 + RAND:10
	W = 52 * T * (95 + RAND:11) / (100 * 140)
	H = 72 * T * (97 + RAND:7) / (100 * 140)
ELSE
	;長身 160～179cm
	;ユニークは素質変化しないように
	IF TALENT:111
		IF RAND:5 == 0 && TALENT:170 == 0
			T = 160 + RAND:20 - RAND:10
		ELSE
			T = 160 + 5 * TALENT:170 + RAND:(20 - 5 * TALENT:170)
		ENDIF
	;普通 145～165cm
	ELSE
		IF RAND:5 == 0
			T = 145 + RAND:11 + RAND:6 + RAND:6 + RAND:10
		ELSE
			T = 145 + RAND:11 + RAND:6 + RAND:6
		ENDIF
	ENDIF
	W = 58 * T * (95 + RAND:11) / (100 * 160)
	H = 86 * T * (97 + RAND:7) / (100 * 160)
ENDIF
;身長素質の再設定
TALENT:111 = 0
SIF T >= 165
	TALENT:111 = 1

;バストカップ計測
CALL B_FUNCTION
;
;ラミア
IF TALENT:152
	CFLAG:905 = T
	T += 350 + RAND:100
ENDIF

CFLAG:900 = T
CFLAG:901 = B
CFLAG:902 = W
CFLAG:903 = H
CFLAG:904 = C
@B_FUNCTION
;ゴールデンカノン (B = T*0.54, W = T*0.38, H = T*0.54)を参考。
;また、アンダーバストの値Uには T*0.432 (～20代の平均値)を用いる。ただし、W > U ならば U = Wとする。
;バストの値はアンダーバスト+バストカップで算出する。
;AAA（5cm）、AA（7.5cm)、A（10cm）、B（12.5cm）、C（15cm）、
;D（17.5cm）、E（20cm）、F（22.5cm）、G（25cm）、H（27.5cm)…… とだいたい2.5cm単位でカップが推移するものとする。
;ただし、妖精はそれぞれの値を半分とする。

;測りなおしの時には身長等をCFLAGから取得
SIF CFLAG:900
	T = CFLAG:900
SIF CFLAG:902
	W = CFLAG:902

U = T*432/1000

SIF W > U
	U = W
;巨乳の限界設定
IF FLAG:2001 == 1
	F = 0
ELSEIF FLAG:2001 == 2
	F = 2
ELSEIF FLAG:2001 == 3
	F = 5
ENDIF
;トップ-アンダー（境界部分は少ないということで）
;爆乳(H～：26～)
IF TALENT:109 == 2
	V = 24 + F + 2 * TALENT:170 + RAND:(12 - F - 2 * TALENT:170)
;巨乳（E～G：19～25）
ELSEIF TALENT:109 == 1
	V = 17 + 2 * TALENT:170 + RAND:4 + RAND:(F + 7 - 5 * TALENT:170)
;貧乳（～A：～10）
ELSEIF TALENT:108
	V = 4 + RAND:5 + RAND:(5 - 2 * TALENT:170)
;普通（B～D:11～18）
ELSE
	V = 9 + 2 * TALENT:170 + RAND:7 + RAND:(6 - 2 * TALENT:170)
ENDIF

;バストカップ決定
;非ユニークは確立で貧乳、標準、巨乳、爆乳の壁を越えます。
;AAA
IF V <= 5
	C = 1
;AA
ELSEIF V <= 8
	C = 2
;A
ELSEIF V <= 10
	C = 3
;B
ELSEIF V <= 13
	C = 4
;C
ELSEIF V <= 15
	C = 5
;D
ELSEIF V <= 18
	C = 6
;E
ELSEIF V <= 20
	C = 7
;F
ELSEIF V <= 23
	C = 8
;G
ELSEIF V <= 25
	C = 9
;H
ELSEIF V <= 28
	C = 10
;I
ELSEIF V <= 30
	C = 11
;J
ELSEIF V <= 33
	C = 12
;K
ELSEIF V <= 35
	C = 13
;L
ELSEIF V <= 38
	C = 14
;M
ELSEIF V <= 40
	C = 15
ELSE
	C = 16
ENDIF

;妖精なら値は半分
IF TALENT:150
	V /= 2
;小柄なら、バストカップの値自体を減らす
ELSEIF TALENT:100
	TIMES V , 0.90
ENDIF

;カップ数により、乳サイズの再設定
TALENT:109 = 0
TALENT:108 = 0
IF C >= 8 + FLAG:2001
	TALENT:109 = 2
ELSEIF C >= 7
	TALENT:109 = 1
ELSEIF C <= 3
	TALENT:108 = 1
ENDIF

;バスト決定
B = U + V

CFLAG:901 = B
CFLAG:904 = C

@GET_EXTALENT

;長身の設定…現在：ラミア、アプサラス、クィーンメイブ、アルケニー、ヴァンパイア、ストメロ、エスプラーナ、ジャバウォック
IF NO:TARGET == 10 || NO:TARGET == 14 || NO:TARGET == 17 || NO:TARGET == 18 || NO:TARGET == 25 || NO:TARGET == 34 || NO:TARGET == 35 || NO:TARGET == 38 || NO:TARGET == 41
	SIF TALENT:110 == 0
		TALENT:111 = 1
ENDIF
;上位夢魔
IF CFLAG:10 > 0
	TALENT:171 = 0
	TALENT:172 = 1
ENDIF
;爆乳の設定…爆乳は色々と便利なので、TALENT:109 == 2 で定義。現在：ラミア、ストゥーナ、メローナ、エスト
IF NO:TARGET == 10 || NO:TARGET == 34 || NO:TARGET == 35
	SIF TALENT:109
		TALENT:109 = 2
ENDIF
;トカゲ
SIF NO:TARGET == 10 || NO:TARGET == 12 || NO:TARGET == 13 || NO:TARGET == 34 || NO:TARGET == 35 || NO:TARGET == 37
	TALENT:177 = 1
;ケモノ
SIF NO:TARGET == 6 || NO:TARGET == 7 || NO:TARGET == 11 || NO:TARGET == 16 || NO:TARGET == 23 || NO:TARGET == 38 || NO:TARGET == 39 || NO:TARGET == 40
	TALENT:176 = 1
;悪魔
SIF NO:TARGET == 1 || NO:TARGET == 2 || NO:TARGET == 21 || (NO:TARGET == 13 && TALENT:172) || NO:TARGET == 25 || (NO:TARGET == 33 && TALENT:172)
	TALENT:175 = 1
;妖精
SIF NO:TARGET == 4 || NO:TARGET == 3 || NO:TARGET == 5 || NO:TARGET == 31 
	TALENT:174 = 1
;少女
SIF NO:TARGET == 8 || NO:TARGET == 9 || NO:TARGET == 24 || NO:TARGET == 28
	TALENT:173 = 1

;長い舌
SIF NO:TARGET == 10 || NO:TARGET == 12 || NO:TARGET == 34 || NO:TARGET == 35
	TALENT:161 = 1
;尻尾　ラミアは除外
SIF NO:TARGET == 1 || NO:TARGET == 2 || NO:TARGET == 6 || NO:TARGET == 7 || NO:TARGET == 16 || NO:TARGET == 23 
	TALENT:162 = 1
;爬虫類とジャバウォックはぶっとい尻尾
SIF NO:TARGET == 12 || NO:TARGET == 34 || NO:TARGET == 35 || NO:TARGET == 41
	TALENT:162 = 2
;獣耳
SIF NO:TARGET == 6 || NO:TARGET == 7 || NO:TARGET == 16 || NO:TARGET == 23
	TALENT:163 = 1
;エナジードレイン
SIF NO:TARGET == 32
	TALENT:201 = 2

;敵として登場したときにレベル等の設定を行います。
@ENEMY_SPECIES
FLAG:1702 += 1
FLAG:1703 = FLAG:1702

ADDCHARA Z
TARGET = CHARANUM-1
A:(FLAG:1702) = TARGET
SIF FLAG:1702 > 1
	ASSI:(FLAG:1702 - 1) = TARGET

IF FLAG:1702 == 1
	CFLAG:0 = FLAG:1705 * (50 + RAND:51) / 100
ELSE
	CFLAG:0 = CFLAG:(A:(FLAG:1702 - 1)):0 * (50 + RAND:51) / 100
ENDIF
CFLAG:10 = RAND:(20 + CFLAG:0) / 20
Z = NO:TARGET
SIF Z == 21 && CFLAG:10 == 0
	CFLAG:10 = 1

CALL CALL_NAME
CALL BASE_TRAINER_SETUP
CALL GET_EXTALENT
CALL SIZE_SET
SIF FLAG:2002
	CALL PERSONALITY
CALL EXP_REVISION
CALL ABL_SET
CALL BASE_TRAINER_SETUP

;所持アイテム
LOCAL:1 = RAND:3 + 1
FOR LOCAL:2,0,LOCAL:1
	LOCAL = RAND:50
	SIF STRLENS(ITEMNAME:LOCAL) && LOCAL != 9 && LOCAL != 40 && LOCAL != 42 && LOCAL != 33
		ITEM:LOCAL = 1
NEXT

IF TALENT:83
	ITEM:21 = 1
	ITEM:21 = 1
ENDIF
IF TALENT:58
	ITEM:20 = 1
	ITEM:22 = 1
ENDIF
