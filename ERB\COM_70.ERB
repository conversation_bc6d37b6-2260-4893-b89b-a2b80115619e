﻿;爱抚系リアクションコマンド群
;────────────────────────────────────
;070,イかせて！
;────────────────────────────────────
@COM70
TIMES SOURCE:0 , 1.20
TIMES SOURCE:1 , 1.20
TIMES SOURCE:2 , 1.20
TIMES SOURCE:3 , 1.20
TIMES SOURCE:22 , 0.70
TIMES SOURCE:24 , 0.80
SOURCE:30 += 50 + (MARK:MASTER:1 + 1) * (SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3) * CFLAG:MASTER:0 / 200
SOURCE:31 += 100 + (CFLAG:MASTER:0 + 5) * (SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3) / 100
SOURCE:32 += 100 + SOURCE:0 / 25 + SOURCE:1 / 25 + SOURCE:2 / 25 + SOURCE:3 / 25

TFLAG:94 = 2
TFLAG:125 = 1

A = 0
B = 0
A = (CFLAG:9 / 500) + TALENT:36 - TALENT:37 + TALENT:63 - TALENT:65
SIF A > 5
	A = 5
SIF A <= 0
	A = 1

SIF TALENT:83
	B += 1
SIF TALENT:86
	B += 1
IF ABL:26 >= ABL:21
	B += 1
ELSE
	B -= 1
ENDIF
SIF TALENT:87
	B -= 1
B += 5
;3<=B<=8

IF RAND:A > 0
	TFLAG:126 = 1
	IF RAND:B >= RAND:5
		C = ABL:2 / 5 + 2
		SIF C > 4
			C = 4
		TFLAG:126 += C
	ENDIF
ENDIF

IF TFLAG:126 == 1
	TFLAG:300 = 1
ELSEIF TFLAG:126 > 1
	TFLAG:300 = 2
ENDIF
TFLAG:167 = 1
RETURN 1
;────────────────────────────────────
;071,クンニ
;────────────────────────────────────
@COM71
IF PENIS(TARGET)
	STAIN:MASTER:0 |= STAIN:2
	STAIN:2 |= STAIN:MASTER:0
	CALL TOUCH_SET(101,4,TARGET)
ELSE
	STAIN:MASTER:0 |= STAIN:3
	STAIN:3 |= STAIN:MASTER:0
	CALL TOUCH_SET(102,4,TARGET)
ENDIF
CALL ABL_REVISION
LOCAL = 250 + S:41 * 2 * B:30 * (3 + TALENT:MASTER:52 + TALENT:MASTER:54 + TALENT:MASTER:161) / 900
SOURCE:40 += LOCAL * 2
SOURCE:10 += CFLAG:MASTER:0 * 10 + LOCAL / 10
SOURCE:11 += LOCAL / 5

SOURCE:31 += (CFLAG:MASTER:0 + 5) * (B:7 + 50) + (B:2 + 50) * 20 / 100
TFLAG:94 = 2
TFLAG:125 = 1
TFLAG:168 = 0
RETURN 1

;────────────────────────────────────
;072,アナル舐め
;────────────────────────────────────
@COM72
CALL TOUCH_SET(107,4,TARGET)
CALL ABL_REVISION
LOCAL = 250 + S:43 * 2 * B:30 * (3 + TALENT:MASTER:52 + TALENT:MASTER:54 + TALENT:MASTER:161) / 900
STAIN:MASTER:0 |= STAIN:4
STAIN:4 |= STAIN:MASTER:0
SOURCE:42 += LOCAL * 2
SOURCE:10 += CFLAG:MASTER:0 * 10 + LOCAL / 10
SOURCE:11 += LOCAL / 5

SOURCE:31 += (CFLAG:MASTER:0 + 5) * (B:7 + 50) + (B:2 + 50) * 20 / 100

TFLAG:94 = 2
TFLAG:125 = 1
TFLAG:168 = 0
RETURN 1

;────────────────────────────────────
;073,胸愛撫
;────────────────────────────────────
@COM73
CALL ABL_REVISION
LOCAL = 250 + S:43 * 2 * B:30 * (3 + TALENT:MASTER:52 + TALENT:MASTER:54 + TALENT:MASTER:161) / 900
STAIN:MASTER:0 |= STAIN:5
STAIN:5 |= STAIN:MASTER:0
SOURCE:43 += LOCAL * 2
SOURCE:10 += CFLAG:MASTER:0 * 10 + LOCAL / 10
SOURCE:11 += LOCAL / 5

SOURCE:31 += (CFLAG:MASTER:0 + 5) * (B:7 + 50) + (B:2 + 50) * 20 / 100

TFLAG:94 = 2
TFLAG:125 = 1
TFLAG:168 = 0
RETURN 1

;────────────────────────────────────
;074,キス
;────────────────────────────────────
@COM74
CALL ABL_REVISION
STAIN:MASTER:0 |= STAIN:0
STAIN:0 |= STAIN:MASTER:0
TIMES SOURCE:40, 1.2
TIMES SOURCE:41, 1.2
TIMES SOURCE:42, 1.2
TIMES SOURCE:43, 1.2
SOURCE:31 += (CFLAG:MASTER:0 + 5) * (B:7 + 50) + (B:2 + 50) * 20 / 100

TFLAG:94 = 2
TFLAG:125 = 1
TFLAG:168 = 0
RETURN 1

