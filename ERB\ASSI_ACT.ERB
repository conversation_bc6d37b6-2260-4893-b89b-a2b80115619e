﻿@ASSI_ACT
TFLAG:172 = 0
TFLAG:193 = 0

;薬品使用と強制脱衣の手伝うは優先
;脱衣
IF TFLAG:102 > 0 && ASSI == ASSI:1
	TFLAG:172 = 9
	TFLAG:190 = 70
;薬品
ELSEIF TFLAG:104 > 0 && ASSI == ASSI:1
	TFLAG:172 = 9
	TFLAG:190 = 71
;公衆プレイは強制待機
ELSEIF TFLAG:90 == 74
	TFLAG:172 = 9
	TFLAG:190 = 72
;休憩中は待機
ELSEIF TFLAG:70 == 1
	TFLAG:172 = 9
	TFLAG:190 = 72
ENDIF

SIF TFLAG:172 == 0
	CALL ASSIACT_SELECT
;TFLAG:190 == 70 脱衣  71 薬品使用   72 待機  80触手(未実装)

CALL SP_CHECK_A

;────────────────────────────────────
;014,待機する（ソースなし）
;────────────────────────────────────
IF TFLAG:172 == 72
	ALIGNMENT LEFT
	PRINTFORML %CALLNAME:TARGET%让%CALLNAME:ASSI%在旁边待命…
	ALIGNMENT RIGHT
;────────────────────────────────────
;015,触手使役（未実装）
;────────────────────────────────────
;ELSEIF TFLAG:172 == 80
	
ENDIF


@ASSIACT_SELECT
;TFLAG:172 = 1 ツープラトン/2 C責め/ 3 A責め /4 B責め /5 V責め/6 M責め/7 非接触責め/8 加虐/

;ツープラトン、発動率は暫定で50%に
IF RAND:2 == 0 && ASSI == ASSI:1
	;Wフェラなど
	IF TFLAG:175 == 1
		SIF (TFLAG:90 == 12 || TFLAG:90 == 16) && (TALENT:MASTER:121 || TALENT:MASTER:122)
			TFLAG:172 = 1
		SIF TFLAG:90 == 15 || (TFLAG:90 == 17 && !TALENT:ASSI:122)
			TFLAG:172 = 1
		SIF TALENT:ASSI:155 == 0 &&TFLAG:90 == 10  && (TALENT:MASTER:121 || TALENT:MASTER:122)
			TFLAG:172 = 1
		SIF TALENT:ASSI:152 == 0 && TALENT:ASSI:153 == 0 && TFLAG:90 == 18 
			TFLAG:172 = 1
	ENDIF
	
	;3P(TARGET挿入)
	IF ABL:MASTER:5 > 1
		SIF (TFLAG:90 == 32 || TFLAG:90 == 33) && (TALENT:ASSI:122 || TALENT:ASSI:121)
			TFLAG:172 = 1
	SIF (TALENT:MASTER:0 != 0 && TALENT:MASTER:122 != 0 && TFLAG:90 == 35) && (TALENT:ASSI:122 || TALENT:ASSI:121)
			TFLAG:172 = 1
	ENDIF
	
	;変則乱れ牡丹
		SIF TFLAG:90 == 34
			TFLAG:172 = 1
				
	;3P(MASTER挿入)
	SIF (TFLAG:90 == 36 || TFLAG:90 == 95) && !TALENT:ASSI:122
		TFLAG:172 = 1
			
	;ダブルフェラ
	SIF TFLAG:90 == 51 && (TALENT:121 || TALENT:122) && (TALENT:ASSI:121 || TALENT:ASSI:122)
		TFLAG:172 = 1
ENDIF
;行動予約
IF TFLAG:210
	TFLAG:172 = 1
	TFLAG:210 = 0
ENDIF
;挿入中は他の行動をとらないように
IF TFLAG:221 && TFLAG:114 == TFLAG:221
	TFLAG:115 = 1
	TFLAG:(211 + TFLAG:114) = 6
ENDIF

;助手の行動選択
IF TFLAG:172 != 1
	;C責め
	B = (10 + ABL:MASTER:3) * RAND:100
	;A責め
	C = (10 + ABL:MASTER:4) * RAND:100
	;B責め
	D = (10 + ABL:MASTER:6) * RAND:100
	;V責め
	E = (10 + ABL:MASTER:5) * RAND:100
	;M責め
	F = (10 + (TALENT:ASSI:54 + TALENT:ASSI:52) * 3) * RAND:100

	;MASTERの使用中部位
	SIF TFLAG:174  || TFLAG:175 || TEQUIP:71 || TEQUIP:31 || TFLAG:221
		B = 0
	SIF TFLAG:176 || TEQUIP:25 || TEQUIP:26 || TEQUIP:27 || TEQUIP:71 == 3 || TEQUIP:71 == 4 || TEQUIP:71 == 5 || TEQUIP:70 == 6
		C = 0
	SIF TFLAG:178 || TEQUIP:35 || TEQUIP:36
		D = 0
	SIF TFLAG:179 || TEQUIP:20 || (TEQUIP:70 && TEQUIP:70 < 6)
		E = 0
	SIF TFLAG:177 >= 2 || TEQUIP:42
		F = 0
	;着衣
	SIF TALENT:MASTER:122 && (TEQUIP:MASTER:3 || TEQUIP:MASTER:5 || TEQUIP:MASTER:6)
		D = 0
	IF TEQUIP:MASTER:4 > 1 && (TEQUIP:MASTER:8 & 4) == 0
		E = 0
	ENDIF

	;MASTERが処女
	SIF TALENT:MASTER:0
		E *= 0
	;MASTERがオトコ
	SIF TALENT:MASTER:122
		E *= 0
	SIF TFLAG:170
		B *= 2
	;同じ部位を連続して責めないように
	SIF TFLAG:109 == 2
		B /= 2
	SIF TFLAG:109 == 3
		C /= 2
	SIF TFLAG:109 == 4
		D /= 2
	SIF TFLAG:109 == 5
		E /= 2
	SIF TFLAG:109 == 6
		F /= 2
	;特殊な状況でなければ体の空いている部位を責めます
	IF B > C && B > D && B > E && B > F
		TFLAG:172 = 2
		CALL ASSI_ACTM2
	ELSEIF C > D && C > E && C > F
		TFLAG:172 = 3
		CALL ASSI_ACTM3
	ELSEIF D > E && D > F
		TFLAG:172 = 4
		CALL ASSI_ACTM4
	ELSEIF E > F
		TFLAG:172 = 5
		CALL ASSI_ACTM5
	ELSE
		TFLAG:172 = 6
		CALL ASSI_ACTM6
	ENDIF

	;空いている場所がない
	IF B == 0 && C == 0 && D == 0 && E == 0
		TFLAG:172 = 7
		CALL ASSI_ACTM7
	ENDIF
	;たまには言葉責めするように
	IF RAND:10 == 0
		IF TFLAG:80 == 1 || TFLAG:80 == 2 || TFLAG:80 == 3 || TFLAG:80 == 5 
			TFLAG:172 = 7
			CALL ASSI_ACTM7
		ENDIF
	ENDIF
	;MASTER自慰中
	IF TFLAG:80 == 4
		TFLAG:172 = 7
		CALL ASSI_ACTM7
	ENDIF

	;加虐
	IF TFLAG:80 == 6
		TFLAG:172 = 8
		CALL ASSI_ACTM8
	ENDIF
ENDIF


;以下は細かい設定
;C責め
@ASSI_ACTM2
;A 手で B 舌で C 素股 D パイズリ E 足コキ F ローター、オナホール G 性交
A = (10 + ABL:ASSI:21 + TALENT:ASSI:57 * 5) * RAND:100
B = (10 + ABL:ASSI:21 + (TALENT:ASSI:52 + TALENT:ASSI:54 - TALENT:ASSI:62) * 5) * RAND:100
C = (10 + ABL:ASSI:21) * RAND:100
D = (10 + ABL:ASSI:21 + (TALENT:ASSI:109 - TALENT:ASSI:108 + TALENT:ASSI:75 - TALENT:ASSI:62) * 5) * RAND:100
E = (10 + ABL:ASSI:21 + TALENT:ASSI:83 * 5) * RAND:100
F = (10 + ABL:ASSI:22 + TALENT:ASSI:59 * 5) * RAND:100
G = (10 + ABL:ASSI:23 + TALENT:ASSI:73 * 5) * RAND:100

;ASSIの種族
SIF TALENT:ASSI:155
	A *= 0
SIF TALENT:ASSI:152 || TALENT:ASSI:153
	E *= 0

;MASTERの体勢
IF TFLAG:112 == 0
	C *= 0
	G *= 0
ENDIF
SIF TFLAG:131 < 1000
	G *= 0
;衣装
IF TEQUIP:MASTER:4 > 1 && (TEQUIP:MASTER:8 & 4) == 0
	B = 0
	C = 0
	D = 0
	F = 0
	G = 0
ENDIF

;オトコ
IF TALENT:122
	D = 0
	C = 0
	G = 0
ENDIF

;道具の有無
IF TALENT:MASTER:122
	SIF ITEM:14 == 0
	F *= 0
ELSEIF TALENT:MASTER:121
	SIF ITEM:0 == 0 || ITEM:14 == 0
	F *= 0
ELSE
	E = 0
	G = 0
	D = 0
	SIF ITEM:0 == 0
	F *= 0
ENDIF

SIF ABL:ASSI:4 < 3 || ABL:ASSI:23 < 3
	G *= 0
	
IF A > B && A > C && A > D && A > E && A > F && A > G
	TFLAG:190 = 7
ELSEIF B > C && B > D && B > E && B > F && B > G
	TFLAG:190 = 1
ELSEIF C > D && C > E && C > F && C > G
	TFLAG:190 = 2
ELSEIF D > E && D > F && D > G
	TFLAG:190 = 3
ELSEIF E > F && E > G
	TFLAG:190 = 4
ELSEIF E > G
	TFLAG:190 = 5
ELSEIF G > 0
	TFLAG:190 = 6
ENDIF

;A責め
@ASSI_ACTM3
;A 手で B 舌で C ローター D アナルバイブ 
A = (10 + ABL:ASSI:21 + TALENT:ASSI:57 * 5) * RAND:100
B = (10 + ABL:ASSI:21 + (TALENT:ASSI:52 + TALENT:ASSI:54 - TALENT:ASSI:62) * 5) * RAND:100
C = (10 + ABL:ASSI:22 + TALENT:ASSI:59 * 5) * RAND:100
D = (10 + ABL:ASSI:22 + TALENT:ASSI:59 * 5) * RAND:100

;ASSIの種族
SIF TALENT:ASSI:155
	A *= 0
;仰向け
SIF TFLAG:112
	B = 0
;衣装
IF TEQUIP:MASTER:4 || TEQUIP:MASTER:2
	B = 0
	C = 0
	D = 0
ENDIF

;道具の有無
SIF ITEM:0 == 0
	C *= 0
SIF ITEM:11 == 0
	D *= 0
;MASTERのA感覚
SIF ABL:MASTER:5 < 2
	D *= 0
SIF TALENT:ASSI:121 == 0 && TALENT:ASSI:122 == 0
	E *= 0
	
IF A > B && A > C && A > D 
	TFLAG:190 = 10
ELSEIF B > C && B > D 
	TFLAG:190 = 11
ELSEIF C > D 
	TFLAG:190 = 12
ELSE
	TFLAG:190 = 13
ENDIF

@ASSI_ACTM4
;A 手で B 舌で C ローター
A = (10 + ABL:ASSI:21 + TALENT:ASSI:57 * 5) * RAND:100
B = (10 + ABL:ASSI:21 + (TALENT:ASSI:52 + TALENT:ASSI:54) * 5) * RAND:100
C = (10 + ABL:ASSI:22 + TALENT:ASSI:59 * 5) * RAND:100

;衣装
SIF TEQUIP:MASTER:3 || TEQUIP:MASTER:5 ||TEQUIP:MASTER:6 
	B = 0

;道具の有無
SIF ITEM:0 == 0
	C *= 0
	
IF A > B && A > C 
	TFLAG:190 = 20
ELSEIF B > C  
	TFLAG:190 = 21
ELSE
	TFLAG:190 = 22
ENDIF

@ASSI_ACTM5
;A 手で B バイブ C 性交
A = (10 + ABL:ASSI:21 + TALENT:ASSI:57 * 5) * RAND:100
B = (10 + ABL:ASSI:22 + TALENT:ASSI:59 * 5) * RAND:100
C = (10 + ABL:ASSI:23 + TALENT:ASSI:73 * 5) * RAND:100

;ASSIの種族
SIF TALENT:ASSI:155
	A *= 0

;道具の有無
SIF ITEM:1 == 0
	B *= 0
	
;性別
SIF !PENIS(ASSI)
	C *= 0

SIF A == 0 && B == 0 && C == 0
CALL ASSI_ACTM7

IF A > B && A > C 
	TFLAG:190 = 30
ELSEIF B > C  
	TFLAG:190 = 31
ELSE
	TFLAG:190 = 32
ENDIF

@ASSI_ACTM6
;A キス B 顔面騎乗 C 足を舐めさせる
A = (5 + ABL:ASSI:21 + (TALENT:ASSI:54 + TALENT:ASSI:52) * 5) * RAND:100
B = (5 + ABL:ASSI:7 + TALENT:ASSI:83 * 5) * RAND:100
C = (10 + ABL:ASSI:25 + TALENT:ASSI:83 * 5) * RAND:100

;ASSIの種族
SIF TALENT:ASSI:152 || TALENT:ASSI:153
	C *= 0

SIF TFLAG:112 == 0
	B *= 0

IF TFLAG:177
	B = 0
	C = 0
ENDIF

IF A >B && A > C 
	TFLAG:190 = 40
ELSEIF B > C
	TFLAG:190 = 41
ELSE
	TFLAG:190 = 42
ENDIF

@ASSI_ACTM7
;A 自慰見せつけ B 罵倒 C 写真を撮る D 調教者とキス E 痴態を見つめる
A = (10 + ABL:ASSI:12 + TALENT:ASSI:29 * 5) * RAND:100
B = (10 + ABL:ASSI:20 + TALENT:ASSI:83 * 5) * RAND:100
C = (10 + ABL:ASSI:24 ) * RAND:100
D = (10 + ABL:ASSI:9 + ABL:ASSI:12 ) * RAND:100
E = (10 + ABL:ASSI:24 + TALENT:ASSI:23 * 5) * RAND:100

IF A > B && A > C && A > D && A > E 
	TFLAG:190 = 50
ELSEIF B > C && B > D && B > E 
	TFLAG:190 = 51
ELSEIF C > D && C > E 
	TFLAG:190 = 52
ELSEIF D > E 
	TFLAG:190 = 53
ELSE
	TFLAG:190 = 54
ENDIF

@ASSI_ACTM8
;A 頭を踏みつける B 足を舐めさせる C 罵倒


A = (10 + ABL:ASSI:26 ) * RAND:100
B = (10 + ABL:ASSI:25 ) * RAND:100
C = (10 + ABL:ASSI:20) * RAND:100


;ASSIの種族
IF TALENT:ASSI:152 || TALENT:ASSI:153
	A *= 0
	B *= 0
ENDIF
	
SIF TFLAG:177
B *= 0

IF A > B && A > C 
	TFLAG:190 = 60
ELSEIF B > C  
	TFLAG:190 = 42
ELSE
	TFLAG:190 = 51
ENDIF

