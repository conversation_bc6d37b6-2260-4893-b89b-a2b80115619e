﻿;────────────────────────────────────
;道具 (ﾛｰﾀｰ20/バイ21/Aﾊﾞｲ22/Aﾋﾞｰ23/Ｃ愛24/ﾆﾌﾟﾙ25/搾乳26/浣腸68)
;────────────────────────────────────
@ACT_M2
;基準値
LOCAL:20 = 10
LOCAL:21 = 12 - TEQUIP:20 * 3
LOCAL:22 = 10 - TEQUIP:25 * 3
LOCAL:23 = 10 + TEQUIP:26 * 2
LOCAL:24 = 15 - TEQUIP:30 * 5 - TEQUIP:31 * 5
LOCAL:25 = 15 - TEQUIP:35 * 5
LOCAL:26 = 6
LOCAL:68 = 4 + TEQUIP:27 * 2

;本日の方針がＡ開発
IF TFLAG:77 == 1
	LOCAL:22 += 10
	LOCAL:23 += 10
ENDIF

;調教方針(1=休憩/2=ソフト/3=ノーマル/4=ハード/5=異常)
SELECTCASE TFLAG:70
	CASE 4
		LOCAL:68 += 6
	CASE 5
		LOCAL:26 += 4
		LOCAL:68 += 4
ENDSELECT

;────────────────────────────────────
;素質による変動
;────────────────────────────────────
;調教者が臆病/気丈
IF TALENT:10
	LOCAL:22 += TEQUIP:25 ? 0 # -3
	LOCAL:23 += TEQUIP:26 ? 0 # -2
	LOCAL:68 -= 5
ELSEIF TALENT:12
	LOCAL:22 += TEQUIP:25 ? 0 # 3 + RAND:3
	LOCAL:23 += TEQUIP:26 ? 0 # 2 + RAND:5
	LOCAL:68 += RAND:6
ENDIF

;調教者が抑圧/解放
IF TALENT:32
	LOCAL:21 += TEQUIP:20 ? 0 # -3
	LOCAL:22 += TEQUIP:25 ? 0 # -5
	LOCAL:23 += TEQUIP:26 ? 0 # -3
	LOCAL:26 += TEQUIP:36 ? 0 # -3
	LOCAL:68 += TEQUIP:27 ? 0 # -5
ELSEIF TALENT:33
	LOCAL:21 += TEQUIP:20 ? 0 # RAND:5
	LOCAL:22 += TEQUIP:25 ? 0 # RAND:8
	LOCAL:23 += TEQUIP:26 ? 0 # RAND:5
	LOCAL:26 += TEQUIP:36 ? 0 # RAND:5
	LOCAL:68 += TEQUIP:27 ? 0 # RAND:8
ENDIF

;調教者が汚臭敏感
SIF TALENT:62
	LOCAL:68 -= 8

;調教者が心根優しい
SIF TALENT:87
	LOCAL:68 += TEQUIP:27 ? 0 # -5

;調教者が幼稚
SIF TALENT:88
	LOCAL:26 += TEQUIP:36 ? 0 # 2 + RAND:6

;調教者が狂気
SIF TALENT:89
	LOCAL:68 += TEQUIP:27 ? 0 # RAND:9

;調教対象が淫核／淫茎
IF TALENT:MASTER:72
	LOCAL:20 += 3 + RAND:5
	LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # 4 + RAND:6
ENDIF

;調教対象が淫壷
SIF TALENT:MASTER:73
	LOCAL:21 += TEQUIP:20 ? 0 # 4 + RAND:6

;調教対象が淫尻
IF TALENT:MASTER:74
	LOCAL:22 += TEQUIP:25 ? 0 # 3 + RAND:8
	LOCAL:23 += 2 + RAND:4
	LOCAL:68 += TEQUIP:27 ? 0 # RAND:15
ENDIF

;調教対象が淫乳
IF TALENT:MASTER:75
	LOCAL:25 += TEQUIP:35 ? 0 # 4 + RAND:6
	LOCAL:26 += TEQUIP:36 ? 0 # RAND:12
ENDIF

;調教対象がＣ敏感/鈍感
IF TALENT:MASTER:100
	LOCAL:20 += 3 + RAND:3
	LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # 1 + RAND:7
ELSEIF TALENT:MASTER:101
	LOCAL:20 -= 3 + RAND:3
	LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # -1 - RAND:7
ENDIF

;調教対象がＶ敏感/鈍感
IF TALENT:MASTER:102
	LOCAL:21 += TEQUIP:20 ? 0 # 4 + RAND:3
ELSEIF TALENT:MASTER:103
	LOCAL:21 += TEQUIP:20 ? 0 # -4 - RAND:3
ENDIF

;調教対象がＡ敏感/鈍感
IF TALENT:MASTER:104
	LOCAL:22 += TEQUIP:25 ? 0 # 3 + RAND:3
	LOCAL:23 += 2 + RAND:5
ELSEIF TALENT:MASTER:105
	LOCAL:22 += TEQUIP:25 ? 0 # -3 - RAND:3
	LOCAL:23 -= 2 + RAND:5
ENDIF

;調教対象がＢ敏感/鈍感
IF TALENT:MASTER:106
	LOCAL:25 += TEQUIP:35 ? 0 # 3 + RAND:3
	LOCAL:26 += TEQUIP:36 ? 0 # 1 + RAND:7
ELSEIF TALENT:MASTER:107
	LOCAL:25 += TEQUIP:35 ? 0 # -3 - RAND:3
	LOCAL:26 += TEQUIP:36 ? 0 # -1 - RAND:7
ENDIF

;調教対象がふたなり
SIF TALENT:MASTER:121
	LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # RAND:3 + TALENT:23 * 3 - TALENT:22

;調教対象が処女
SIF TALENT:MASTER:0
	LOCAL:21 += CFLAG:3 ? -5 + RAND:6 # -10

;調教対象が母乳体質
SIF TALENT:MASTER:114
	LOCAL:26 += TEQUIP:36 ? 0 # 5 + RAND:5

;────────────────────────────────────
;能力、パラメーターによる変動
;────────────────────────────────────
;調教者の加虐
SELECTCASE ABL:26
	CASE IS > 4
		LOCAL:68 += 3 + RAND:3
	CASE 4
		LOCAL:68 += 2 + RAND:3
	CASE 3
		LOCAL:68 += 2
	CASE 2
		LOCAL:68 += 1 + RAND:2
	CASE 1
		LOCAL:68 += 1
ENDSELECT

;調教対象のＣ感覚
SELECTCASE ABL:MASTER:3
	CASE IS > 4
		LOCAL:20 += RAND:9
		LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # 6
	CASE 4
		LOCAL:20 += RAND:9
		LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # 5
	CASE 3
		LOCAL:20 += RAND:7
		LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # 4
	CASE 2
		LOCAL:20 += RAND:5
		LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # 3
	CASE 1
		LOCAL:20 += RAND:3
		LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # 2
ENDSELECT

;調教対象のＶ感覚
SELECTCASE ABL:MASTER:4
	CASE IS > 4
		LOCAL:21 += TEQUIP:20 ? 0 # 3 + RAND:6
	CASE 4
		LOCAL:21 += TEQUIP:20 ? 0 # 2 + RAND:5
	CASE 3
		LOCAL:21 += TEQUIP:20 ? 0 # 2 + RAND:4
	CASE 2
		LOCAL:21 += TEQUIP:20 ? 0 # 1 + RAND:3
	CASE 1
		LOCAL:21 += TEQUIP:20 ? 0 # 1 + RAND:2
ENDSELECT

;調教対象のＡ感覚
SELECTCASE ABL:MASTER:5
	CASE IS > 4
		LOCAL:22 += TEQUIP:25 ? 0 # 3 + RAND:5
		LOCAL:23 += 3 + RAND:5
	CASE 4
		LOCAL:22 += TEQUIP:25 ? 0 # 2 + RAND:6
		LOCAL:23 += 2 + RAND:6
	CASE 3
		LOCAL:22 += TEQUIP:25 ? 0 # 2 + RAND:4
		LOCAL:23 += 2 + RAND:4
	CASE 2
		LOCAL:22 += TEQUIP:25 ? 0 # 1 + RAND:4
		LOCAL:23 += 1 + RAND:4
	CASE 1
		LOCAL:22 += TEQUIP:25 ? 0 # 1 + RAND:2
		LOCAL:23 += 1 + RAND:2
ENDSELECT

;調教対象のＢ感覚
SELECTCASE ABL:MASTER:6
	CASE IS > 4
		LOCAL:25 += TEQUIP:35 ? 0 # 3 + RAND:6
		LOCAL:26 += TEQUIP:36 ? 0 # 2 + RAND:9
	CASE 4
		LOCAL:25 += TEQUIP:35 ? 0 # 2 + RAND:6
		LOCAL:26 += TEQUIP:36 ? 0 # 1 + RAND:9
	CASE 3
		LOCAL:25 += TEQUIP:35 ? 0 # 2 + RAND:4
		LOCAL:26 += TEQUIP:36 ? 0 # 1 + RAND:6
	CASE 2
		LOCAL:25 += TEQUIP:35 ? 0 # 1 + RAND:4
		LOCAL:26 += TEQUIP:36 ? 0 # RAND:6
	CASE 1
		LOCAL:25 += TEQUIP:35 ? 0 # 1 + RAND:2
		LOCAL:26 += TEQUIP:36 ? 0 # RAND:3
ENDSELECT

;調教者に調合知識が無い場合、潤滑のチェック
IF !TALENT:55
	SELECTCASE PALAM:4
		CASE IS < 100
			LOCAL:21 -= 5
			LOCAL:22 -= 5
			LOCAL:23 -= 5
		CASE IS < 250
			LOCAL:21 -= 3
			LOCAL:22 -= 3
			LOCAL:23 -= 1
		CASE IS < 500
			LOCAL:21 -= 1
			LOCAL:22 -= 1
	ENDSELECT
ENDIF

;罪悪感
SELECTCASE CFLAG:5
	CASE IS > 100
		LOCAL:22 -= 2
		LOCAL:23 -= 1
		LOCAL:26 -= 2
		LOCAL:68 -= 5
	CASE IS > 80
		LOCAL:22 -= 1
		LOCAL:26 -= 1
		LOCAL:68 -= 4
	CASE IS > 60
		LOCAL:68 -= 3
	CASE IS > 40
		LOCAL:68 -= 2
	CASE IS > 20
		LOCAL:68 -= RAND:2
ENDSELECT

;アライメント
SELECTCASE CFLAG:6
	CASE IS > 49
		LOCAL:20 += 3
		LOCAL:68 -= 3
	CASE IS > 39
		LOCAL:20 += 2 + RAND:2
		LOCAL:68 -= 2 + RAND:2
	CASE IS > 29
		LOCAL:20 += 2
		LOCAL:68 -= 2
	CASE IS > 19
		LOCAL:20 += 1 + RAND:2
		LOCAL:68 -= 1 + RAND:2
	CASE IS > 9
		LOCAL:20 += 1
		LOCAL:68 -= 1
	CASE IS > -1
		LOCAL:20 += RAND:2
		LOCAL:68 -= RAND:2
	CASE IS > -11
		LOCAL:20 -= RAND:2
		LOCAL:68 += RAND:2
	CASE IS > -21
		LOCAL:20 -= 1
		LOCAL:68 += 1
	CASE IS > -31
		LOCAL:20 -= 1 + RAND:2
		LOCAL:68 += 1 + RAND:2
	CASE IS > -41
		LOCAL:20 -= 2
		LOCAL:68 += 2
	CASE IS > -51
		LOCAL:20 -= 2 + RAND:2
		LOCAL:68 += 2 + RAND:2
	CASEELSE
		LOCAL:20 -= 3
		LOCAL:68 += 3
ENDSELECT

;────────────────────────────────────
;ゲージや状態による変動
;────────────────────────────────────
;調教対象の体力
SELECTCASE BASE:MASTER:0
	CASE IS < 50
		LOCAL:20 -= 3
		;持続に責める道具を止める
		LOCAL:21 += TEQUIP:20 ? 5 # -5
		LOCAL:22 += TEQUIP:25 ? 5 # -5
		LOCAL:23 += TEQUIP:26 ? RAND:9 # -4
		LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 3 # -3
		LOCAL:25 += TEQUIP:35 ? 3 # -3
		LOCAL:26 += TEQUIP:36 ? 6 # -6
		LOCAL:68 += TEQUIP:27 ? RAND:15 # -7
	CASE IS < 250
		LOCAL:20 -= 1
		;持続に責める道具を止める
		LOCAL:21 += TEQUIP:20 ? 3 # -3
		LOCAL:22 += TEQUIP:25 ? 3 # -3
		LOCAL:23 += TEQUIP:26 ? RAND:5 # -2
		LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 1 # -1
		LOCAL:25 += TEQUIP:35 ? 1 # -1
		LOCAL:26 += TEQUIP:36 ? 4 # -4
		LOCAL:68 += TEQUIP:27 ? RAND:11 # -5
	CASE IS < 500
		LOCAL:21 += TEQUIP:20 ? 1 # -1
		LOCAL:22 += TEQUIP:25 ? 1 # -1
		LOCAL:26 += TEQUIP:36 ? 2 # -2
		LOCAL:68 += TEQUIP:27 ? RAND:6 # -2
ENDSELECT

;調教者の状態(0=通常/1=疲弊/2=衰弱/3=無気力/4=朦朧/5=情欲/6=怒り/7=退屈/8=狂乱)
SELECTCASE TFLAG:60
	CASE 7
		LOCAL:21 += TEQUIP:20 ? 5 # 0
		LOCAL:22 += TEQUIP:25 ? 5 # 0
		LOCAL:23 += TEQUIP:26 ? 5 + RAND:5 # 0
		LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 5 # 0
		LOCAL:25 += TEQUIP:35 ? 5 # 0
		LOCAL:26 += TEQUIP:36 ? 5 # 0
		LOCAL:68 += TEQUIP:27 ? 5 + RAND:5 # 0
	CASE 8
		LOCAL:68 += RAND:10
ENDSELECT

;────────────────────────────────────
;前回の行動や状況による変動
;────────────────────────────────────
;調教対象自慰中
IF TEQUIP:69 & 1
	LOCAL:21 += TEQUIP:20 ? 0 # 3 + RAND:7
	LOCAL:22 += TEQUIP:25 ? 0 # 1 + RAND:9
	LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 0 # 4 + RAND:6
	LOCAL:25 += TEQUIP:35 ? 0 # 4 + RAND:6
	LOCAL:26 += TEQUIP:36 ? 0 # -5
ENDIF

;調教者自慰中
IF TEQUIP:69 & 2
	LOCAL:26 -= 3
	LOCAL:68 -= 3
ENDIF

;もっとハードにお願いした
IF SELECTCOM == 6
	LOCAL:26 += TEQUIP:36 ? 0 # 2 + RAND:3
	LOCAL:68 += TEQUIP:27 ? 0 # 2 + RAND:3
ENDIF

;許しを乞ったかつ、調教者の状態が狂乱以外
IF SELECTCOM == 7 && TFLAG:60 < 8
	LOCAL:21 += TEQUIP:20 ? 3 # 0
	LOCAL:22 += TEQUIP:25 ? 3 # 0
	LOCAL:23 += TEQUIP:26 ? 5 # 0
	LOCAL:24 += (TEQUIP:30 || TEQUIP:31) ? 2 # 0
	LOCAL:25 += TEQUIP:35 ? 2 # 0
	LOCAL:26 += TEQUIP:36 > 1 ? 5 # 0
	LOCAL:68 += TEQUIP:27 > 1 ? 5 # 0
ENDIF

;────────────────────────────────────
;同じ行動連続実行の確率をダウンします
;────────────────────────────────────
SELECTCASE TFLAG:91
	CASE 20 TO 26, 68
		LOCAL:(TFLAG:91) -= 3 + RAND:5
ENDSELECT

;────────────────────────────────────
;TEQUIP処理
;────────────────────────────────────
;調教対象がスカート以外の下半身上着または全身上着を着ている
IF TEQUIP:MASTER:4 > 1 || TEQUIP:MASTER:6
	LOCAL:20 -= 3
	LOCAL:24 -= 5
;調教対象が下半身上着にスカートを着ている
ELSEIF TEQUIP:MASTER:4
	LOCAL:20 -= 1
	LOCAL:21 -= 3
	LOCAL:22 -= 3
	LOCAL:23 -= 2
	LOCAL:24 -= 1
	LOCAL:68 -= 2
ENDIF

;調教対象が上半身上着または全身上着を着ている
SIF TEQUIP:MASTER:5 || TEQUIP:MASTER:6
	LOCAL:25 -= 3

;調教対象が下半身下着を着ている
IF TEQUIP:MASTER:2
	LOCAL:20 -= 1
	LOCAL:21 -= 3
	LOCAL:22 -= 3
	LOCAL:23 -= 2
	LOCAL:24 -= 1
ENDIF

;調教対象が上半身下着を着ている
SIF TEQUIP:MASTER:3
	LOCAL:25 -= 3

;────────────────────────────────────
;その他変動や実行不可能の判定
;────────────────────────────────────
;お仕置きモード
SIF TFLAG:69
	LOCAL:68 += 3 + RAND:4

;不可能判定とカウンタ値の下限チェック
FOR LOCAL:900, 20, 27
	CALLFORM ACT_ABLE{LOCAL:900}
	SIF !RESULT || LOCAL:(LOCAL:900) < -99
		LOCAL:(LOCAL:900) = -99
NEXT
CALL ACT_ABLE68
SIF !RESULT || LOCAL:68 < -99
	LOCAL:68 = -99

;────────────────────────────────────
;最終判定
;────────────────────────────────────
SELECTCASE MAX(LOCAL:20, LOCAL:21, LOCAL:22, LOCAL:23, LOCAL:24, LOCAL:25, LOCAL:26, LOCAL:68)
;ここには来ないはず
;	CASE -99
;		PRINTL (道具カウンタ異常)
;		TFLAG:90 = 20
	CASE LOCAL:20
		TFLAG:90 = 20
	CASE LOCAL:21
		TFLAG:90 = 21
	CASE LOCAL:23
		TFLAG:90 = 23
	CASE LOCAL:22
		TFLAG:90 = 22
	CASE LOCAL:24
		TFLAG:90 = 24
	CASE LOCAL:25
		TFLAG:90 = 25
	CASE LOCAL:26
		TFLAG:90 = 26
	CASE LOCAL:68
		TFLAG:90 = 68
ENDSELECT

;デバッグ＆調整用カウンタ
IF FLAG:4
	PRINTFORML 　　道具：ﾛｰﾀｰ[{LOCAL:20,3}]/バイ[{LOCAL:21,3}]/Aﾊﾞｲ[{LOCAL:22,3}]/Aﾋﾞｰ[{LOCAL:23,3}]
	PRINTFORML 　　　　　Ｃ愛[{LOCAL:24,3}]/ﾆﾌﾟﾙ[{LOCAL:25,3}]/搾乳[{LOCAL:26,3}]/灌肠[{LOCAL:68,3}]
ENDIF


;-----------------------------------------------------------
;道具 の実行判定
;-----------------------------------------------------------
@ACTM_ABLE2
FOR LOCAL, 20, 26
	CALLFORM ACT_ABLE{LOCAL}
	SIF RESULT
		RETURN 1
NEXT
;ローター～ニプルキャップまでが全て使えない状態なら、道具は不可とする
;CALL ACT_ABLE26
;SIF RESULT
;	RETURN 1
;CALL ACT_ABLE68
;SIF RESULT
;	RETURN 1
RETURN 0
