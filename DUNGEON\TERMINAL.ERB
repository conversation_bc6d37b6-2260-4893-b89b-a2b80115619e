﻿;イベントパネル、階段パネルを踏むとここを中継点として、各ダンジョンのイベントに飛びます。
;階層処理のターミナル
@STAIRS
;IF FLAG:52 == 1 && !RAND:1
;	CALL DUNGEON_ROOM_STAIRS
;	RETURN 0
;ENDIF
SIF !STRLENS(LOCALS)
	SPLIT "0/CAVE/FOREST/SEA/LIBRARY/DESERT/TOWN", "/", LOCALS
CALLFORM DUNGEON_%LOCALS:(FLAG:1700)%_STAIRS


;イベント処理のターミナル
@EVENT
SIF !STRLENS(LOCALS)
	SPLIT "0/RYUDUF/ELTUM/ASHOS/LIBRARY/GOZBURN/TOWN", "/", LOCALS
CALLFORM DUNGEON_EVENT_%LOCALS:(FLAG:1700)%


@ESCAPE_DUNGEON
IF FLAG:1700 == 6
	SIF ASSI:4 > -1 && TALENT:(ASSI:4):181
		CALL KOJO_EVENT_SUPPORT(2, 1)
	PRINTFORMW %NAME:MASTER%は拠点に帰ることにした
ELSE
	SIF ASSI:4 > -1 && TALENT:(ASSI:4):181
		CALL KOJO_EVENT_SUPPORT(2)
	PRINTFORMW %NAME:MASTER%は%STR:(800 + FLAG:1700)%から脱出した
ENDIF
;状態異常フラグの解除
FOR LOCAL, 0, 10
	CFLAG:MASTER:(220 + LOCAL) = 0
NEXT
;現状復帰処理
CFLAG:MASTER:12 = FLAG:1706
FLAG:13 = 5
FLAG:1700 = 0
FLAG:1709 = 0
TARGET = TARGET:10
ASSI = ASSI:10
ASSI:1 = ASSI:11
ASSI:2 = ASSI:12
;ASSI:4 = -1
FOR LOCAL, 0, 50
	ITEM:LOCAL = FLAG:(1650 + LOCAL)
NEXT
BEGIN TURNEND
