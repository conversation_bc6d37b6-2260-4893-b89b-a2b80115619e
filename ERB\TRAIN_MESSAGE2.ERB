﻿;-------------------------------------------------
;関数名:PENIS_DESCRIPTION
;概　要:ペニス描写関数
;引　数:なし
;戻り値:ペニスの状態を表す文字列
;備　考:文中関数
;なんとなくコードが煩雑になりそうな予感がしたので関数可してみた
;使用例
;PRINTFORML %CALLNAME:MASTER%の%PENIS_DESCRIPTION()%ペニスが(以下略
;-------------------------------------------------
@PENIS_DESCRIPTION
#FUNCTIONS

SELECTCASE TFLAG:90
	;愛撫
	CASE 10 TO 18
		SELECTCASE TFLAG:131
			;萎え
			CASE IS < 1000
				;射精直後
				IF TFLAG:33 == 2
					IF RAND:3 == 0
						RETURNF "刚射完的"
					ELSEIF RAND:2 == 0
						RETURNF "刚射完还很敏感的"
					ELSE
						RETURNF "先前释放了很多精液的"
					ENDIF
				ENDIF
				RETURNF ""
			;勃起
			CASEELSE
				IF TFLAG:33 == 2
					IF RAND:3 == 0
						RETURNF "终于射精的"
					ELSEIF RAND:2 == 0
						RETURNF "不知衰退的"
					ELSE
						RETURNF "不停震动的"
					ENDIF
				ENDIF
				IF TFLAG:131 == 1500
					IF RAND:3 == 0
						RETURNF "剧烈怒张的"
					ELSEIF RAND:2 == 0
						RETURNF "翘起的"
					ELSE
						RETURNF "勃起到极限的"
					ENDIF
				ENDIF
					IF RAND:3 == 0
						RETURNF "怒张的"
					ELSEIF RAND:2 == 0
						RETURNF "屹立的"
					ELSE
						RETURNF "昂然挺立的"
					ENDIF
				RETURNF ""
		ENDSELECT
ENDSELECT
@PENIS_DESCRIPTION_T
#FUNCTIONS
SELECTCASE TCVAR:131
	CASE 0
		RETURNF "还很柔软的"
	CASE 1
		RETURNF "慢慢的增加了硬度的"
	CASE 2
		RETURNF "勃起的"
	CASEELSE
		RETURNF "弯曲的"
ENDSELECT
;-------------------------------------------------
;関数名:PENIS_DESCRIPTION2
;概　要:ペニス描写関数
;引　数:なし
;戻り値:ペニスの状態を表す文字列
;備　考:文中関数
;-------------------------------------------------
@PENIS_DESCRIPTION2
#FUNCTIONS
IF TFLAG:166
	IF BASE:MASTER:2
		SELECTCASE RAND:3
			CASE 0
				RETURNF "一下子就结束了的"
			CASE 1
				RETURNF "一直持续射精的"
			CASE 2
				RETURNF "从里面溢了精液的"
		ENDSELECT
	ELSE
		SELECTCASE RAND:2
			CASE 0
				RETURNF "持续空射的"
			CASE 1
				RETURNF "一下子就结束了的"
		ENDSELECT
	ENDIF
ENDIF
;-------------------------------------------------
;関数名:EQUIPACTION_DESCRIPTION
;概　要:TEQUIP関係の調教者の行動描写
;引　数:なし
;戻り値:TEQUIPの状態を表す文字列
;備　考:文中関数
;使用例:%EQUIPACTION_DESCRIPTION()%
;-------------------------------------------------
@EQUIPACTION_DESCRIPTION
#FUNCTIONS
IF TEQUIP:44 || TEQUIP:45
	RETURNF @"一边跨坐在%CALLNAME:MASTER%的脸上，一边"
ELSEIF TEQUIP:69
	SELECTCASE TEQUIP:69
		CASE 1
			RETURNF @"一边观赏着%CALLNAME:MASTER%自慰的样子，一边"
		CASE 2
			RETURNF @"一边看着%CALLNAME:MASTER%自慰的样子，一边"
		CASE 3
			RETURNF @"一边观赏着%CALLNAME:MASTER%自慰的样子，一边"
	ENDSELECT
ELSEIF TEQUIP:70
	RETURNF @"一边挺动腰部、向上突入着%CALLNAME:MASTER%，一边"
ELSEIF TEQUIP:71
	IF TEQUIP:71 == 6
		RETURNF @"一边将插入后穴的阴茎紧紧缠住，一边"
	ELSE
		RETURNF @"一边贪食着腔内的阴茎，一边"
	ENDIF
ENDIF
;-------------------------------------------------
;関数名:OPPAI_DESCRIPTION
;概　要:言葉でなく心で理解しろ
;引　数:ARG…キャラ登録番号
;戻り値:夢
;備　考:文中関数
;使用例:%OPPAI_DESCRIPTION(TARGET)%
;-------------------------------------------------
@OPPAI_DESCRIPTION(ARG)
#FUNCTIONS
;爆乳
IF TALENT:ARG:109 == 2
	SELECTCASE RAND:3
		CASE 0
			RETURNF "饱满的胸"
		CASE 1
			RETURNF "气势磅礴的胸"
		CASE 2
			RETURNF "丰盈有质感的胸"
	ENDSELECT
;巨乳
ELSEIF TALENT:ARG:109
	SELECTCASE RAND:3
		CASE 0
			RETURNF "丰满的胸"
		CASE 1
			RETURNF "自满的胸"
		CASE 2
			RETURNF "有魅力的的巨乳"
	ENDSELECT
;貧乳
ELSEIF TALENT:ARG:108
	SELECTCASE RAND:3
		CASE 0
			RETURNF "含蓄的胸"
		CASE 1
			RETURNF "可爱的胸"
		CASE 2
			RETURNF "可爱的请求勃起的乳头"
	ENDSELECT
;並乳
ELSE
	SELECTCASE RAND:3
		CASE 0
			RETURNF "柔软的胸"
		CASE 1
			RETURNF "均称的胸"
		CASE 2
			RETURNF "胸"
	ENDSELECT
ENDIF

;-------------------------------------------------
;関数名:VAGINA_DESCRIPTION
;引　数:ARG…キャラ登録番号
;戻り値:夢
;備　考:文中関数
;使用例:%OPPAI_DESCRIPTION(TARGET)%
;-------------------------------------------------
@VAGINA_DESCRIPTION(ARG)
#FUNCTIONS
;小柄体型
IF TALENT:ARG:110
	;パイパン
	IF TALENT:119 == 2
	SELECTCASE RAND:3
		CASE 0
			RETURNF "稚嫩的细缝"
		CASE 1
			RETURNF "稚嫩的性器"
		CASE 2
			RETURNF "光滑的窄缝"
	ENDSELECT
	ELSE
	SELECTCASE RAND:3
		CASE 0
			RETURNF "稚嫩的细缝"
		CASE 1
			RETURNF "稚嫩的性器"
		CASE 2
			RETURNF "未开发的窄缝"
	ENDSELECT
	ENDIF
;巨乳
ELSE
	SELECTCASE RAND:3
		CASE 0
			RETURNF "阴唇"
		CASE 1
			RETURNF "女性器"
		CASE 2
			RETURNF "秘唇"
	ENDSELECT
ENDIF

