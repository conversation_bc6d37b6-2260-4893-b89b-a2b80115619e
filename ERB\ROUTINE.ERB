﻿;────────────────────────────────────
;コマンド選択後のTFLAGリセット
;────────────────────────────────────
@EVENTCOM
#PRI
DRAWLINE
FOR LOCAL, 0, 40
	TFLAG:LOCAL = 0
NEXT
;アクション内処女童貞喪失フラグのリセット
TFLAG:301 = 0
TFLAG:302 = 0


;────────────────────────────────────
;ターン終了の回復
;────────────────────────────────────
@BASE_RECOVERY
SIF FLAG:1700
	RETURN 0
CALL ABL_REVISION
REPEAT CHARANUM
	;体力回復
	BASE:COUNT:0 += (400 + TIME * 100 + MAXBASE:COUNT:0 / 5) * (4 + TIME + TALENT:COUNT:112 - TALENT:COUNT:113 + TALENT:COUNT:115 * 2) * 3 / (10 + CFLAG:COUNT:11)
	IF COUNT == 0
		SIF BASE:MASTER:0 > (MAXBASE:MASTER:0) * (100 + FLAG:3188) / 100
			BASE:MASTER:0 = (MAXBASE:MASTER:0) * (100 + FLAG:3188) / 100
	ELSE
		SIF BASE:COUNT:0 > MAXBASE:COUNT:0
			BASE:COUNT:0 = MAXBASE:COUNT:0
	ENDIF
	
	;精液/噴乳ゲージ回復
	SIF MAXBASE:COUNT:2 > 0
		BASE:COUNT:2 += MAXBASE:COUNT:2 * (50 + TIME * 20 + TALENT:COUNT:125 * 10 - TALENT:COUNT:126 * 10) / (100 + CFLAG:COUNT:11 * 10)
	IF COUNT == 0
		SIF BASE:MASTER:2 > (MAXBASE:MASTER:2) * (200 + FLAG:3190) / 200
			BASE:MASTER:2 = (MAXBASE:MASTER:2) * (200 + FLAG:3190) / 200
	ELSE
		SIF BASE:COUNT:2 > MAXBASE:COUNT:2
			BASE:COUNT:2 = MAXBASE:COUNT:2
	ENDIF
	
	SIF MAXBASE:COUNT:3 > 0
		BASE:COUNT:3 += (1200 + TIME * 400 + CFLAG:COUNT:0 * 80) * (3 + TIME + TALENT:COUNT:112 - TALENT:COUNT:113) * (3 + ABL:COUNT:0) / (7 + CFLAG:COUNT:11)
	SIF BASE:COUNT:3 > MAXBASE:COUNT:3
		BASE:COUNT:3 = MAXBASE:COUNT:3
	
	;理性回復
	BASE:COUNT:5 = MAXBASE:COUNT:5
	
	;興味/苛立ちの回復、満足のリセット
	BASE:COUNT:6 = MAXBASE:COUNT:6
	BASE:COUNT:7 = 0
	BASE:COUNT:8 = 0
	
	;罪悪感回復
	IF CFLAG:COUNT:5 > 0
		CFLAG:COUNT:5 -= 3
		CFLAG:COUNT:5 = CFLAG:COUNT:5 * (200 + CFLAG:COUNT:6) / 500
		SIF CFLAG:COUNT:6 < 75
			CFLAG:COUNT:5 -= 2
		SIF CFLAG:COUNT:6 < 50
			CFLAG:COUNT:5 -= 2
		SIF CFLAG:COUNT:6 < 25
			CFLAG:COUNT:5 -= 2
		SIF CFLAG:COUNT:6 < 0
			CFLAG:COUNT:5 -= 2
		SIF CFLAG:COUNT:6 < -25
			CFLAG:COUNT:5 -= 3
		SIF CFLAG:COUNT:6 < -50
			CFLAG:COUNT:5 -= 3
		SIF CFLAG:COUNT:6 < -75
			CFLAG:COUNT:5 = 0
		SIF CFLAG:COUNT:5 < 0
			CFLAG:COUNT:5 = 0
	ENDIF
	
	;気力回復
	BASE:COUNT:1 += (240 + TIME * 60 + MAXBASE:COUNT:1 / 5) * (4 + TIME * 2 + TALENT:COUNT:112 - TALENT:COUNT:113 + TALENT:COUNT:115) * (3 + TALENT:COUNT:125 - TALENT:COUNT:126) / (10 + CFLAG:COUNT:11)
	IF COUNT == 0
		SIF BASE:MASTER:1 > (MAXBASE:MASTER:1) * (100 + FLAG:3189) / 100
			BASE:MASTER:1 = (MAXBASE:MASTER:1) * (100 + FLAG:3189) / 100
	ELSE
		SIF BASE:COUNT:1 > MAXBASE:COUNT:1
			BASE:COUNT:1 = MAXBASE:COUNT:1
	ENDIF
	;疲弊フラグの解消
	SIF CFLAG:COUNT:11 > 20
		CFLAG:COUNT:11 -= 5
	CFLAG:COUNT:11 -= 1 + CFLAG:COUNT:11 / 3 + TIME
	SIF BASE:COUNT:0 > MAXBASE:COUNT:0 * 4 / 5
		CFLAG:COUNT:11 -= 1
	SIF BASE:COUNT:1 > MAXBASE:COUNT:1 * 4 / 5
		CFLAG:COUNT:11 -= 1
	SIF CFLAG:COUNT:11 < 0
		CFLAG:COUNT:11 = 0
REND
IF TARGET > 0 && TALENT:117
	TIMES BASE:MASTER:0, 1.2
	TIMES BASE:MASTER:1, 1.2
	SIF BASE:MASTER:0 > (MAXBASE:MASTER:0) * (100 + FLAG:3188) / 100
		BASE:MASTER:0 = (MAXBASE:MASTER:0) * (100 + FLAG:3188) / 100
	SIF BASE:MASTER:1 > (MAXBASE:MASTER:1) * (100 + FLAG:3189) / 100
		BASE:MASTER:1 = (MAXBASE:MASTER:1) * (100 + FLAG:3189) / 100
ENDIF
;ストック
BASE:MASTER:9 = MAXBASE:MASTER:9

;日時の更新
;午前なら午後に、午後なら次の日にする
;日時更新処理が２箇所あったのが気になったので関数にまとめた
;それにしても酷い関数名だぜ…英語とかわかんないよね
@NEXTTIME
IF TIME++
	;日付変更時のイベント呼び出し
	;CALL EVENT_NEXTDAY_T
	;CALL EVENT_NEXTDAY
	
	;総日数/日/年/季節/昼夜の更新
	DAY++
	FLAG:31 = DAY % 120
	FLAG:32 = DAY / 120
	FLAG:33 = FLAG:31 / 30
	TIME = 0
	PRINTW 一天结束了・・・

	;100日目かつ通常モードならエンディングへ(シナリオモードができたらシナリオ時に変更するかも)
	IF DAY == 100 && FLAG:5 == 0
		CALL ENDING
	ENDIF
	
ENDIF


;────────────────────────────────────
;NEWDAYを呼び出し
;────────────────────────────────────
@EVENTSHOP
#PRI
;SIF TIME == 0 && TARGET > 0
;	CALL EVENT_NEWDAY
