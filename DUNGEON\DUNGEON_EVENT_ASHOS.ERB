﻿;初期設定
@DUNGEON_SEA
;ダンジョンタイプ
FLAG:1709 = 3
;ダンジョン難易度補正
FLAG:1705 = FLAG:1704
IF FLAG:1704 == 1
CALL DUNGEON_MAP_PRODUCE_SEA
ELSE
CALL DUNGEON_MAP_PRODUCE_ELEVATOR
ENDIF

;階層構造
@DUNGEON_SEA_STAIRS
FLAG:1704 += 1
FLAG:1705 += 1

IF FLAG:1704 % 5 == 0
	CALL DUNGEON_MAP_PRODUCE_ELEVATOR
ELSE
	CALL DUNGEON_MAP_PRODUCE_SEA
ENDIF


@DUNGEON_EVENT_ASHOS
IF FLAG:1704 % 5 == 0
	IF FLAG:1723 < FLAG:1704 / 5
		PRINTW 転送機を稼動させた。
		FLAG:1723 = FLAG:1704 / 5
	ELSE
		PRINTL 転送機はすでに稼動している。
		PRINTL 拠点に帰還しますか？
		PRINTL [1] - はい
		PRINTL [2] - いいえ
		DRAWLINE
		INPUT
		IF RESULT == 1
			CALL ESCAPE_DUNGEON
			RETURN 0
		ELSE
			;移動メニューに移ります
			FLAG:1711 = 1
		ENDIF
	ENDIF
ELSE
	CALL DUNGEON_RANDOM_EVENT
	FLAG:M -= 128
ENDIF



