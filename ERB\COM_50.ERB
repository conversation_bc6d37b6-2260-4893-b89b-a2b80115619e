﻿;────────────────────────────────────
;050,自慰し始める（自慰、悦楽、反抗、お仕置きポイント）
;────────────────────────────────────
@COM50
CALL ONANIE_APPLY
CALL ABL_REVISION
SOURCE:32 += (SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3) / 1000
SOURCE:30 += 250 + (SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3) * (B:1 + 10) / 300

A = (SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3) * (3 + TALENT:86) / 50
SIF A > 100
	A = 100
;調教者が満足してるならお仕置きにならないかもしれません
IF BASE:8 / 100 + CFLAG:MASTER:0 / 2 < BASE:7 / 100 + RAND:5
	TFLAG:68 += A
	TFLAG:300 = 1
ENDIF
TFLAG:125 = 3
TFLAG:168 = 0
RETURN 1

;────────────────────────────────────
;051,道具を外す（快Ａ、達成逆、反抗、お仕置きポイント）
;────────────────────────────────────
@COM51
CALL ABL_REVISION
SOURCE:31 -= 100 + (CFLAG:2 - 500) * (SOURCE:0 + SOURCE:1 + SOURCE:2) / 1000
SOURCE:33 += 250 + SOURCE:1 / 2 + SOURCE:2 / 2 + (CFLAG:MASTER:0 + 1) * (B:2 + 10) * CFLAG:2 / 100

;道具を外すの優先順判定。一度は一つまで。最初はランダム１～３外す予定だったが何だか面倒で
IF TEQUIP:41
	TEQUIP:41 = 0
ELSEIF TEQUIP:36
	TEQUIP:36 = 0
ELSEIF TEQUIP:27
	SOURCE:2 = 500 * TEQUIP:27 * (100 + RAND:11 - RAND:11) / 100
	TEQUIP:27 = 0
ELSEIF TEQUIP:25
	TEQUIP:25 = 0
ELSEIF TEQUIP:20
	TEQUIP:20 = 0
ELSEIF TEQUIP:31
	TEQUIP:31 = 0
ELSEIF TEQUIP:30
	TEQUIP:30 = 0
ELSEIF TEQUIP:42
	TEQUIP:42 = 0
ELSEIF TEQUIP:35
	TEQUIP:35 = 0
ELSE
	SOURCE:2 = 50 * TEQUIP:26 + 50 * TEQUIP:26 + (TALENT:MASTER:59 + 1) * B:2 * CFLAG:MASTER:0 * (TEQUIP:26 - 1) / 10
	TEQUIP:26 = 0
ENDIF

;自慰してるならそれもやめる
SIF TEQUIP:69 == 1
	TEQUIP:69 = 0
SIF TEQUIP:69 == 3
	TEQUIP:69 = 2

A = (BASE:7 / 100 + PALAM:9 / 1000) * (A:30 + 5) + 5 + RAND:10
SIF A > 100
	A = 100
TFLAG:68 += A

;暴れたように設定
TFLAG:94 = 4
TFLAG:125 = 4
RETURN 1


;────────────────────────────────────
;052,ぼーっとする（効果なし。実行できる反応が一つもない時の救済、必要かどうかはわかりませんが）
;────────────────────────────────────
@COM52
RETURN 1
