﻿;_Rename.csv
;eramakerでは内部でハードコーディングされている表示置換用の設定ファイル

;単位名
;お金の単位 , (単位)
;お金の単位 , 圓

;単位の位置
;単位の位置 , (位置)
;位置は
;前：数字の前に表示
;後：数字の後に表示
;単位の位置 , 後

;起動時のレポートなし時の簡略表示の設定
;起動時簡略表示 , (表示したい文章)
;起動時簡略表示 , 少女祈祷中...
起動時簡略表示 , 本丸开启中...

;販売アイテム最大数
;基本100固定なSHOP販売アイテム最大数の設定
;販売アイテム数 , (数)
販売项目数 , 0

;DRAWLINEの表示文字
;DRAWLINEで表示する文字
;DRAWLINE文字 , (半角文字)
;DRAWLINE文字 , +

;BARの表示文字1
;BARで表示する文字(*側)
;BAR文字1 , (半角文字)
;全角不可
;BAR文字1 , |

;BARの表示文字2
;BARで表示する文字(.側)
;BAR文字2 , (半角文字)
;全角不可
;BAR文字2 , .

;タイトルでのシステムメニュー表示1
;起動画面での「[0] 最初からはじめる」の文字列部分
;システムメニュー0 , (文字列)
システムメニュー0 , 从最初开始

;タイトルでのシステムメニュー表示2
;起動画面での「[1] ロードしてはじめる」の文字列部分
;システムメニュー1 , (文字列)
システムメニュー1 , 继续游戏

;1.721追加。COM_ABLEが見つからない時の初期値
;COM_ABLE初期値 , 1

;1.731j追加。汚れを初期化する命令RESET_STAINで用いる汚れの初期値
;汚れの初期値, (初期値の値の羅列を'/'で区切った物)
汚れの初期值, 0/0/2/1/8/0/0
