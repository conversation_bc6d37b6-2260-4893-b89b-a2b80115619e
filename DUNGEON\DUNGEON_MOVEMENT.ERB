﻿;────────────────────────────────────
;ダンジョン内での視界、敵の動きなどの制御
;────────────────────────────────────
@DUNGEON_MAP
CALL VISION
CALL DUNGEON_MOVE
IF FLAG:M & 256
	X = 2
;暗いところでは視界を狭く
ELSEIF FLAG:1709 == 1 || FLAG:1709 == 2
	X = 3
;砂漠での視界は広く
ELSEIF FLAG:1709 == 5 || FLAG:1709 == 6
	X = 5
ELSE
	X = 4
ENDIF
;遠見
X += CFLAG:MASTER:15 == 1 ? 1 # 0
X += FLAG:3126
X += FLAG:3141 * 10
X += FLAG:3142 * 20
X = X * X

A = 0
B = 0

PRINTL *未完成*
REPEAT 100
A = 1800 + COUNT
CALL DISTANCE_CALC
	;視界
	IF D > X
		IF B == 9
			PRINTL ＃
		ELSE
			PRINT ＃
		ENDIF
	ELSEIF FLAG:A & 2 && D > 2
		IF B == 9
			PRINTL ＃
		ELSE
			PRINT ＃
		ENDIF
	ELSEIF FLAG:A & 1
		IF B == 9
			PRINTL ■
		ELSE
			PRINT ■
		ENDIF
	;MASTER現在位置
	ELSEIF FLAG:A & 4
		IF FLAG:A & 8
			IF B == 9
				PRINTL ●
			ELSE
				PRINT ●
			ENDIF
		ELSE
			IF B == 9
				PRINTL ＠
			ELSE
				PRINT ＠
			ENDIF
		ENDIF
	ELSEIF FLAG:A & 256
		SETCOLOR 150, 150, 150
		SIF (FLAG:A & 16 ||  FLAG:A & 8 ||  FLAG:A & 64 ||  FLAG:A & 128 ||  FLAG:A & 1024) && D <= 2
			SETCOLOR 0 , 255, 0
		IF B == 9
			PRINTL ％
		ELSE
			PRINT ％
		ENDIF
		RESETCOLOR
	;敵位置
	ELSEIF FLAG:A & 4096
		SIF FLAG:A & 64
			SETCOLOR 255, 0,255
		SIF  FLAG:A & 16
			SETCOLOR 255, 255,0
		SIF  FLAG:A & 1024
			SETCOLOR 255, 0, 255
		IF B == 9
			PRINTL ∞
		ELSE
			PRINT ∞
		ENDIF
		RESETCOLOR
	;敵位置
	ELSEIF FLAG:A & 8
		SETCOLOR 255, 0,0
		SIF FLAG:A & 64
			SETCOLOR 255, 0,255
		SIF  FLAG:A & 16
			SETCOLOR 255, 255,0
		SIF  FLAG:A & 1024
			SETCOLOR 255, 0, 255
		IF B == 9
			PRINTL ＆
		ELSE
			PRINT ＆
		ENDIF
		RESETCOLOR
	;アイテムボックス
	ELSEIF FLAG:A & 16
		SETCOLOR 0 , 255, 0
		SIF FLAG:A & 2048 && FLAG:3139
			SETCOLOR 255, 0,0
		IF B == 9
			PRINTL ］
		ELSE
			PRINT ］
		ENDIF
		RESETCOLOR
	;アイテム
	ELSEIF FLAG:A & 1024
		SETCOLOR 0 , 255, 0
		IF B == 9
			PRINTL ）
		ELSE
			PRINT ）
		ENDIF
		RESETCOLOR
	;階段
	ELSEIF FLAG:A & 64
		IF B == 9
			PRINTL ＞
		ELSE
			PRINT ＞
		ENDIF
	ELSEIF FLAG:A & 32
		IF B == 9
			PRINTL ＜
		ELSE
			PRINT ＜
		ENDIF
	;イベント
	ELSEIF FLAG:A & 128
		IF B == 9
			PRINTL ！
		ELSE
			PRINT ！
		ENDIF
	;水
	ELSEIF FLAG:A & 512
		SETCOLOR 102, 255, 255
		IF B == 9
			PRINTL ○
		ELSE
			PRINT ○
		ENDIF
		RESETCOLOR
	ELSE
		IF B == 9
			PRINTL □
		ELSE
			PRINT □
		ENDIF
	ENDIF
	
	
	IF B == 9
		B = 0
	ELSE
		B += 1
	ENDIF
REND

;マスター等の位置情報を取得します
@DUNGEON_MOVE
M = 0
B = 1
REPEAT 5
E:COUNT = 0
REND
FLAG:1707 = 0
REPEAT 100
	A = 1800 + COUNT
	;MASTERの位置をMに代入
	SIF FLAG:A & 4
		M = A 
	;敵の位置をE:1～E:5に代入
	IF FLAG:A & 8
		E:B = A
		FLAG:1707 += 1
		B += 1
	ENDIF
REND

;距離計算
@DISTANCE_CALC
M:1 = M % 10
M:2 = M / 10
A:1 = A % 10
A:2 = A / 10
D = ( M:1 - A:1 ) * ( M:1 - A:1 ) + ( M:2 - A:2 ) * ( M:2 - A:2 )

;敵の動き
@ENEMY_MOVE
CALL DUNGEON_MOVE
REPEAT 5
C:1 = COUNT
C = C:1 + 1

A = E:C

	IF E:C > 0
		SIF FLAG:(E:C ) & 8
			CALL ENEMY_MOVE_SUBREPEAT
	ELSE
		CONTINUE
	ENDIF

;多重リピート
COUNT = C:1
REND

@ENEMY_MOVE_SUBREPEAT
LOCAL = 1
WHILE LOCAL
;8方向＋足踏みをランダムで選択
B = RAND:9 + 1
A = E:C

IF FLAG:1709 == 1 || FLAG:1709 == 2
	X = 3
ELSEIF FLAG:1709 == 5 || FLAG:1709 == 6
	X = 5
ELSE
	X = 4
ENDIF
X -= FLAG:3125

X = X * X
SIF X <= 1
X += 2

SIF FLAG:(E:C) & 4096
	GOTO MOVE
CALL DISTANCE_CALC

;追跡モード
IF D < X && CFLAG:MASTER:222 == 0 && FLAG:3144 == 0
	IF M:1 == A:1 && M:2 > A:2
		IF LOCAL == 1
			B = 2
		ELSE
			B = 5
		ENDIF
	ELSEIF M:1 == A:1 && M:2 < A:2
		IF LOCAL == 1
			B = 8
		ELSE
			B = 5
		ENDIF
	ELSEIF M:2 == A:2 && M:1 > A:1
		IF LOCAL == 1
			B = 6
		ELSE
			B = 5
		ENDIF
	ELSEIF M:2 == A:2 && M:1 < A:1
		IF LOCAL == 1
			B = 4
		ELSE
			B = 5
		ENDIF
	ELSEIF M:1 > A:1 && M:2 > A:2
		IF LOCAL == 1
			B = 3
		ELSEIF LOCAL == 2
			B = 2
		ELSEIF LOCAL == 3
			B = 6
		ELSE
			B = 5
		ENDIF
	ELSEIF M:1 > A:1 && M:2 < A:2
		IF LOCAL == 1
			B = 9
		ELSEIF LOCAL == 2
			B = 8
		ELSEIF LOCAL == 3
			B = 6
		ELSE
			B = 5
		ENDIF
	ELSEIF M:1 < A:1 && M:2 > A:2
		IF LOCAL == 1
			B = 1
		ELSEIF LOCAL == 2
			B = 2
		ELSEIF LOCAL == 3
			B = 4
		ELSE
			B = 5
		ENDIF
	ELSEIF M:1 < A:1 && M:2 < A:2
		IF LOCAL == 1
			B = 7
		ELSEIF LOCAL == 2
			B = 8
		ELSEIF LOCAL == 3
			B = 4
		ELSE
			B = 5
		ENDIF
	ENDIF
ENDIF
SIF M:1 == A:1 && M:2 == A:2
	B = 5
$MOVE
IF B == 1
	;進行方向に壁なんかがあるとやり直し
	IF (FLAG:(E:C - 1) & 1 || FLAG:(E:C + 9) & 1 || FLAG:(E:C + 10) & 1) || (E:C >= 1890 || E:C % 10 == 0) || FLAG:(E:C + 9) & 8
		LOCAL += 1
		CONTINUE
	ELSE
		FLAG:(E:C) -= 8
		FLAG:(E:C + 9) |= 8
		IF FLAG:(E:C) & 4096
			FLAG:(E:C) -= 4096
			FLAG:(E:C + 9) |= 4096
		ENDIF
		LOCAL = 0
	ENDIF
ELSEIF B == 2
	IF ( FLAG:(E:C + 10) & 1) || (E:C >= 1890 ) || FLAG:(E:C + 10) & 9
		LOCAL += 1
		CONTINUE
	ELSE
		FLAG:(E:C) -= 8
		FLAG:(E:C + 10) |= 8
		IF FLAG:(E:C) & 4096
			FLAG:(E:C) -= 4096
			FLAG:(E:C + 10) |= 4096
		ENDIF
		LOCAL = 0
	ENDIF
ELSEIF B == 3
	IF (FLAG:(E:C + 1) & 1 || FLAG:(E:C + 11) & 1 || FLAG:(E:C + 10) & 1) || (E:C >= 1890 || E:C % 10 == 9) || FLAG:(E:C + 11) & 8
		LOCAL += 1
		CONTINUE
	ELSE
		FLAG:(E:C) -= 8
		FLAG:(E:C + 11) |= 8
		IF FLAG:(E:C) & 4096
			FLAG:(E:C) -= 4096
			FLAG:(E:C + 11) |= 4096
		ENDIF
		LOCAL = 0
	ENDIF
ELSEIF B == 4
	IF (FLAG:(E:C - 1) & 1 ) || (E:C % 10 == 0) || FLAG:(E:C - 1) & 8
		LOCAL += 1
		CONTINUE
	ELSE
		FLAG:(E:C) -= 8
		FLAG:(E:C - 1) |= 8
		IF FLAG:(E:C) & 4096
			FLAG:(E:C) -= 4096
			FLAG:(E:C - 1) |= 4096
		ENDIF
		LOCAL = 0
	ENDIF
ELSEIF B == 5
		LOCAL = 0
ELSEIF B == 6
	IF (FLAG:(E:C + 1) & 1 ) || ( E:C % 10 == 9) || FLAG:(E:C + 1) & 8
		LOCAL += 1
		CONTINUE
	ELSE
		FLAG:(E:C) -= 8
		FLAG:(E:C + 1) |= 8
		IF FLAG:(E:C) & 4096
			FLAG:(E:C) -= 4096
			FLAG:(E:C + 1) |= 4096
		ENDIF
		LOCAL = 0
	ENDIF
ELSEIF B == 7
	IF (FLAG:(E:C - 1) & 1 || FLAG:(E:C - 11) & 1 || FLAG:(E:C - 10) & 1) || (E:C < 1810 || E:C % 10 == 0) || FLAG:(E:C - 11) & 8
		LOCAL += 1
		CONTINUE
	ELSE
		FLAG:(E:C) -= 8
		FLAG:(E:C - 11) |= 8
		IF FLAG:(E:C) & 4096
			FLAG:(E:C) -= 4096
			FLAG:(E:C - 11) |= 4096
		ENDIF
		LOCAL = 0
	ENDIF
ELSEIF B == 8
	IF ( FLAG:(E:C - 10) & 1) || (E:C < 1810 ) || FLAG:(E:C - 10) & 9
		LOCAL += 1
		CONTINUE
	ELSE
		FLAG:(E:C) -= 8
		FLAG:(E:C - 10) |= 8
		IF FLAG:(E:C) & 4096
			FLAG:(E:C) -= 4096
			FLAG:(E:C - 10) |= 4096
		ENDIF
		LOCAL = 0
	ENDIF
ELSEIF B == 9
	IF (FLAG:(E:C + 1) & 1 || FLAG:(E:C - 9) & 1 || FLAG:(E:C - 10) & 1) || (E:C < 1810 || E:C % 10 == 9) || FLAG:(E:C - 9) & 8
		LOCAL += 1
		CONTINUE
	ELSE
		FLAG:(E:C) -= 8
		FLAG:(E:C - 9) |= 8
		IF FLAG:(E:C) & 4096
			FLAG:(E:C) -= 4096
			FLAG:(E:C - 9) |= 4096
		ENDIF
		LOCAL = 0
	ENDIF
ENDIF

WEND



;視界（壁の先は見えないように）
@VISION
;視界の初期化
FOR LOCAL, 0, 100
	A = 1800 + LOCAL
	SIF FLAG:A & 2
		FLAG:A -= 2
NEXT
SIF FLAG:3140 || FLAG:3142
	RETURN 0
CALL DUNGEON_MOVE

A = 0
B = 0
REPEAT 100
	C = COUNT
	A = 1800 + COUNT

	;障害物がさえぎる視界を決めます。
	IF FLAG:A & 1 || FLAG:A & 256
		;MASTERと壁のX,Y座標
		M:1 = 2 * (M % 10)
		M:2 = 2 * (M / 10)
		A:1 = 2 * (A % 10)
		A:2 = 2 * (A / 10)
		;MASTER(パネルの中央にいる)と壁パネルの四隅を結ぶ直線の傾き
		C:1 = 100 * (M:2 - A:2 + 1 ) / (M:1 - A:1 + 1 )
		C:2 = 100 * (M:2 - A:2 + 1 ) / (M:1 - A:1 - 1 )
		C:3 = 100 * (M:2 - A:2 - 1 ) / (M:1 - A:1 + 1 )
		C:4 = 100 * (M:2 - A:2 - 1 ) / (M:1 - A:1 - 1 )
		;C:1～C:4を大きい順に並び替え EMUERAの関数がよくわからないのでもっさり
		C:5 = MAX (C:1 , C:2 , C:3 , C:4)
		IF C:5 == C:1
			C:6 = MAX (C:2 , C:3 , C:4)
		ELSEIF C:5 == C:2
			C:6 = MAX (C:1 , C:3 , C:4)
		ELSEIF C:5 == C:3
			C:6 = MAX (C:1 , C:2 , C:4)
		ELSE
			C:6 = MAX (C:1 , C:2 , C:3)
		ENDIF
		C:8 = MIN (C:1 , C:2 , C:3 , C:4)
		IF C:8 == C:1
			C:7 = MIN (C:2 , C:3 , C:4)
		ELSEIF C:8 == C:2
			C:7 = MIN (C:1 , C:3 , C:4)
		ELSEIF C:8 == C:3
			C:7 = MIN (C:1 , C:2 , C:4)
		ELSE
			C:7 = MIN (C:1 , C:2 , C:3)
		ENDIF
		CALL VISION_SUBREPEAT
	ELSE
		CONTINUE
	ENDIF
	;多重リピート
	COUNT = C
REND


@VISION_SUBREPEAT
;パネル一つ一つについて、↑で見た障害物が視界をさえぎっているかどうか、検定
FOR LOCAL, 0, 100
B = 1800 + LOCAL
;対象とするパネルのX,Y座標
B:1 = 2 * (B % 10)
B:2 = 2 * (B / 10)
;MASTERと障害物、MASTERと対象パネルとの距離を算出（壁よりも近い場所は視界がさえぎられない）
D:1 = ( M:1 - A:1 ) * ( M:1 - A:1 ) + ( M:2 - A:2 ) * ( M:2 - A:2 )
D:2 = ( M:1 - B:1 ) * ( M:1 - B:1 ) + ( M:2 - B:2 ) * ( M:2 - B:2 )
SIF D:1 >= D:2
	CONTINUE
;障害物と対象パネルがMASTERからみて同じ方向になければCONTINUE
SIF (M:1 - B:1) * ( M:1 - A:1 ) < 0 || (M:2 - B:2) * ( M:2 - A:2 )  < 0
CONTINUE

;MASTERと障害物パネルの四方を結ぶ直線が、対象パネルを囲んでいれば、不可視ビットを立てます。
;そのためにMASTERと対象パネルを結ぶ直線の傾きを算出します。
;障害物がMASTERの真北とかにあると、傾きが無限大になったり、0をはさんだりするので別処理
;arctanとか使えたらもっとすっきりするかもしれない。

;対象パネルがMASTERの南北方向にある場合
IF M:1 == A:1
	;障害物の裏側
	IF M:1 == B:1
		FLAG:B |= 2
	ELSE
		;MASTERと対象パネルを結ぶ直線の傾き
		C:9 = 100 * (M:2 - B:2 ) / (M:1 - B:1 )
		SIF C:9 > C:6 || C:9 < C:7
			FLAG:B |= 2
	ENDIF
;対象パネルがMASTERの東西方向にある場合
ELSEIF M:2 == A:2
	IF M:2 == B:2
		FLAG:B |= 2
	ELSEIF M:1 != B:1
		C:9 = 100 * (M:2 - B:2 ) / (M:1 - B:1 )
		SIF C:9 < C:5 && C:9 > C:8
			FLAG:B |= 2
	ENDIF
;その他
ELSEIF M:1 != B:1
	;MASTERと対象パネルを結ぶ直線の傾き
	C:9 = 100 * (M:2 - B:2 ) / (M:1 - B:1 )
	SIF C:9 < C:5 && C:9 > C:8
		FLAG:B |= 2
ENDIF
NEXT
