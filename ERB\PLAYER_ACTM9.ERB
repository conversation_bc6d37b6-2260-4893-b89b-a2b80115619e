﻿;────────────────────────────────────
;休憩 (休み90/服薬91/治療92)
;────────────────────────────────────
@ACT_M9
;基準値
LOCAL:90 = 5
LOCAL:91 = 8
LOCAL:92 = 10

;────────────────────────────────────
;ゲージや状態による変動
;────────────────────────────────────
;調教者の体力
SELECTCASE BASE:0
	CASE IS < 300
		LOCAL:90 += 3
	CASE IS < 600
		LOCAL:90 += 2
	CASE IS < 900
		LOCAL:90 += 1
ENDSELECT

;調教者の気力
SELECTCASE BASE:1
	CASE IS < 150
		LOCAL:92 -= RAND:9
	CASE IS < 300
		LOCAL:92 -= RAND:6
	CASE IS < 450
		LOCAL:92 -= RAND:3
ENDSELECT

;調教者の状態(0=通常/1=疲弊/2=衰弱/3=無気力/4=朦朧/5=情欲/6=怒り/7=退屈/8=狂乱)
SELECTCASE TFLAG:60
	CASE 3
		LOCAL:92 -= 3
	CASE 4
		LOCAL:92 -= RAND:5
ENDSELECT

;────────────────────────────────────
;同じ行動連続実行の確率をダウンします
;────────────────────────────────────
;前のターンの調教指令
SELECTCASE TFLAG:91
	CASE 90 TO 92
		LOCAL:(TFLAG:91) -= 3 + RAND:5
ENDSELECT

;────────────────────────────────────
;ヒート値を差し引きます
;────────────────────────────────────
;栄養剤のヒート値
SIF FLAG:23 > 0
	LOCAL:91 -= FLAG:23
;治療のヒート値
SIF FLAG:24 > 0
	LOCAL:92 -= FLAG:24

;────────────────────────────────────
;実行不可能の判定
;────────────────────────────────────
;調合知識がない
SIF !TALENT:55
	LOCAL:92 -= 5

;不可能判定とカウンタ値の下限チェック
FOR LOCAL:900, 90, 93
	CALLFORM ACT_ABLE{LOCAL:900}
	SIF !RESULT || LOCAL:(LOCAL:900) < -99
		LOCAL:(LOCAL:900) = -99
NEXT

;────────────────────────────────────
;最終判定
;────────────────────────────────────
SELECTCASE MAX(LOCAL:90, LOCAL:91, LOCAL:92)
;ここには来ないはず
;	CASE -99
;		PRINTL (休憩カウンタ異常)
;		TFLAG:90 = 90
	CASE LOCAL:90
		TFLAG:90 = 90
	CASE LOCAL:91
		TFLAG:90 = 91
	CASE LOCAL:92
		TFLAG:90 = 92
ENDSELECT

;デバッグ＆調整用カウンタ
SIF FLAG:4
	PRINTFORML 　　休息：休息[{LOCAL:90,3}]/服药[{LOCAL:91,3}]/治疗[{LOCAL:92,3}]


;-----------------------------------------------------------
;休憩 の実行判定
;-----------------------------------------------------------
@ACTM_ABLE9
FOR LOCAL, 90, 93
	CALLFORM ACT_ABLE{LOCAL}
	SIF RESULT
		RETURN 1
NEXT
RETURN 0
