﻿;────────────────────────────────────
;データベース記録機能
;────────────────────────────────────
@DATABASE_INPUT
;今回の調教方針/メニュー/行動を記録
FLAG:(1239 + TFLAG:70) += TFLAG:70 >= 0
FLAG:(1250 + TFLAG:80) += TFLAG:80 >= 0
FLAG:(1000 + TFLAG:90) += TFLAG:90 >= 0

;同じ行動連続実行を記録
SIF TFLAG:90 == TFLAG:91
	FLAG:1210++
;射精量のレコード
SIF FLAG:1213 < LOSEBASE:92
	FLAG:1213 = LOSEBASE:92

;今回の選択できる反応の数を記録
FLAG:(1300 + TFLAG:92)++

;今回の調教対象による影響を記録
FLAG:(1200 + TFLAG:94)++

;パラメーターの最大上昇値を記録
FOR LOCAL, 0, 50
	SIF UP:LOCAL - DOWN:LOCAL > FLAG:(1400 + LOCAL)
		FLAG:(1400 + LOCAL) = UP:LOCAL - DOWN:LOCAL
NEXT
;絵本の執筆
IF CFLAG:TARGET:(2000 + TFLAG:90) == 0
	FLAG:(4000 + NO:TARGET) += CFLAG:MASTER:15 == 2 ? 25 # 20
ELSE
	FLAG:(4000 + NO:TARGET) += CFLAG:MASTER:15 == 2 ? 2 # 1
ENDIF
CFLAG:TARGET:(2000 + TFLAG:90) += TFLAG:90 >= 0
CFLAG:MASTER:(2000 + TFLAG:90) += TFLAG:90 >= 0

;────────────────────────────────────
;データベース閲覧機能
;────────────────────────────────────
@DATABASE_OUTPUT
WHILE 1
	PRINTL 请选择要查看的项目
	PRINTL 
	PRINTL [0] 返回
	PRINTL [1] 查看各调教者行动的执行次数
	PRINTL [2] 查看各参数的上升记录
	PRINTL [3] 查阅调教方针和菜单的记录
	PRINTL [4] 查看其他记录
	$INPUT_LOOP
	INPUT
	SELECTCASE RESULT
		CASE 0
			BREAK
		CASE 1
			DRAWLINE
			PRINTL 每个调教者行动的执行次数：
			FOR LOCAL, 0, 200
				SIF FLAG:(1000 + LOCAL)
					PRINTFORML 　%TOSTR(LOCAL,"000")%.%STR:(100 + LOCAL),20,LEFT% [{FLAG:(1000 + LOCAL),4}]回
			NEXT
			PRINTL [0] - 返回  [1] - 查看调教者的其他数据
			INPUT
			IF RESULT == 1
				CALL DATABASE_TRAINER
				RESTART
			ELSE
				RESTART
			ENDIF
		CASE 2
			DRAWLINE
			PRINTL 各参数的上升记录：
			FOR LOCAL, 0, 50
				SIF FLAG:(1400 + LOCAL)
					PRINTFORML 　%TOSTR(LOCAL,"00")%.%PALAMNAME:LOCAL% [{FLAG:(1400 + LOCAL),5}]
			NEXT
		CASE 3
			DRAWLINE
			PRINTL 調教方针：
			PRINTFORML 　01.休息 [{FLAG:1240,4}]回
			PRINTFORML 　02.温和 [{FLAG:1241,4}]回
			PRINTFORML 　03.正常 [{FLAG:1242,4}]回
			PRINTFORML 　04.严厉 [{FLAG:1243,4}]回
			PRINTFORML 　05.异常 [{FLAG:1244,4}]回
			PRINTFORML 　06.ＳＭ [{FLAG:1245,4}]回
			DRAWLINE
			PRINTL 調教菜单：
			PRINTFORML 　00.聊天 [{FLAG:1250,4}]回
			PRINTFORML 　01.爱抚 [{FLAG:1251,4}]回
			PRINTFORML 　02.道具 [{FLAG:1252,4}]回
			PRINTFORML 　03.性交 [{FLAG:1253,4}]回
			PRINTFORML 　04.羞恥 [{FLAG:1254,4}]回
			PRINTFORML 　05.侍奉 [{FLAG:1255,4}]回
			PRINTFORML 　06.加虐 [{FLAG:1256,4}]回
			PRINTFORML 　07.异常 [{FLAG:1257,4}]回
			PRINTFORML 　08.使役 [{FLAG:1258,4}]回
			PRINTFORML 　09.休息 [{FLAG:1259,4}]回
			PRINTFORML 　10.性奉 [{FLAG:1260,4}]回
		CASE 4
			DRAWLINE
			PRINTL 其他的记录：
			PRINTFORML 　调教者做出与上次相同行动的次数 [{FLAG:1210,4}]回
			PRINTFORML 　最长调教时间记录 [{FLAG:1211,2}]
			PRINTFORML 　最短调教时间记录 [{FLAG:1212,2}]
			PRINTFORML 　射精量记录 [{FLAG:1213}]
			DRAWLINE
			PRINTL 调教对象的反应记录：
			PRINTFORML 　0.不影响调教者的行动　       [{FLAG:1200,4}]回
			PRINTFORML 　1.消极的服从　　　　　　　　 [{FLAG:1201,4}]回
			PRINTFORML 　2.积极的服从　　　　　　　　 [{FLAG:1202,4}]回
			PRINTFORML 　3.哀求  　　　　　　　　　　 [{FLAG:1203,4}]回
			PRINTFORML 　4.闹腾（除去被压制的次数）   [{FLAG:1204,4}]回
			PRINTFORML 　5.拒绝　　　　　　　　　　   [{FLAG:1205,4}]回
			PRINTFORML 　6.逃走／挣扎　　　　　　　   [{FLAG:1206,4}]回
			DRAWLINE
			PRINTL 调教对象可选择的反应的记录：
			FOR LOCAL, 0, 100
				SIF FLAG:(1300 + LOCAL)
					PRINTFORML 　{LOCAL,2}个 [{FLAG:(1300 + LOCAL),4}]回
			NEXT
		CASEELSE
			CLEARLINE 1
			GOTO INPUT_LOOP
	ENDSELECT
	DRAWLINE
	WAIT
WEND

@DATABASE_TRAINER
WHILE 1
	PRINTL [0] 返回
	FOR LOCAL,1,CHARANUM
	PRINTFORML [{LOCAL}] - %CALLNAME:LOCAL%
	NEXT
	$INPUT_LOOP
	INPUT
	IF RESULT == 0
		BREAK
	ELSEIF (RESULT < 0 && RESULT > CHARANUM - 1) || CFLAG:RESULT:92
		GOTO INPUT_LOOP
	ELSE
		DRAWLINE
		PRINTFORML %CALLNAME:RESULT%的记录：
		FOR LOCAL, 0, 100
			SIF CFLAG:RESULT:(2000 + LOCAL)
				PRINTFORML 　%TOSTR(LOCAL,"000")%.%STR:(100 + LOCAL),20,LEFT% [{CFLAG:RESULT:(2000 + LOCAL),4}]回
		NEXT
	ENDIF
WEND
