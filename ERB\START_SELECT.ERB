﻿;────────────────────────────────────
;最初の調教者の選択
;自制
;────────────────────────────────────
@START_CHARA_SELECT_T
TFLAG:100 = 0
C = 0

;【此处插入动态角色扫描】
; ==== 动的に最大ItemIDを検索（角色总数动态扫描）====
A = 100
B = 0
REPEAT 200
	IF ITEMNAME:A != ""
		B = A
	ENDIF
	A += 1
REND
FLAG:8 = B - 100 + 1
;【插入结束】

CALL START_CHARA_SHOW_T

$INPUT_LOOPA
INPUT
IF RESULT == 1
	IF TFLAG:100 > 0
		TFLAG:100 -= 1
		CALL START_CHARA_SHOW_T
	ENDIF

	GOTO INPUT_LOOPA
ELSEIF RESULT == 9
	;最大ページ数と比較、ページ数変動の際は
	IF TFLAG:100 < FLAG:8 / 20
		TFLAG:100 += 1
		CALL START_CHARA_SHOW_T
	ENDIF

	GOTO INPUT_LOOPA
ELSEIF RESULT == 0 && FLAG:5 == 9 && NO:MASTER != 0
	ADDCHARA C
	TARGET = CHARANUM-1
	PRINTW 调教者设定为「你」了
ELSE
	C = RESULT - 99

	; 安全检查：角色模板是否存在
	IF ITEMNAME:(C + 100) == ""
		PRINTL 角色模板不存在，请勿选择未定义的角色
		GOTO INPUT_LOOPA
	ENDIF

	ADDCHARA C
	TARGET = CHARANUM-1
	PRINTFORMW 调教者设定为「%NAME:TARGET%」了
	ENDIF

;────────────────────────────────────
;最初の調教者の選択表示
;────────────────────────────────────
@START_CHARA_SHOW_T
DRAWLINE
PRINTL ★★请选择调教者★★
SIF FLAG:5 == 9 && NO:MASTER != 0
	PRINTL 　[0]选择「你」
T = 0
S = 0
REPEAT 20
	S = 100 + TFLAG:100 * 20 + T
	IF FLAG:S > 0
		PRINT [
		PRINTV S
		PRINT ]
		PRINTFORML %ITEMNAME:S%
	ENDIF
	T += 1
REND

DRAWLINE
PRINT 　page
PRINTV TFLAG:100
PRINTC 　[1]上一页
PRINTC [9]下一页
PRINTL 

;────────────────────────────────────
;エキストラモードの主人公の選択
;────────────────────────────────────
@START_CHARA_SELECT
TFLAG:100 = 0
C = 0

;【此处插入动态角色扫描】
; ==== 动的に最大ItemIDを検索（角色总数动态扫描）====
A = 100
B = 0
REPEAT 200
	IF ITEMNAME:A != ""
		B = A
	ENDIF
	A += 1
REND
FLAG:8 = B - 100 + 1
;【插入结束】

CALL START_CHARA_SHOW

$INPUT_LOOPB
INPUT
IF RESULT == 1
	IF TFLAG:100 > 0
		TFLAG:100 -= 1
		CALL START_CHARA_SHOW
	ENDIF

	GOTO INPUT_LOOPB
ELSEIF RESULT == 9
	;最大ページ数と比較、ページ数変動の際は
	IF TFLAG:100 < FLAG:8 / 20
		TFLAG:100 += 1
		CALL START_CHARA_SHOW
	ENDIF

	GOTO INPUT_LOOPB
ELSEIF RESULT == 0 && FLAG:5 == 9
	ADDCHARA C
	MASTER = CHARANUM-1
	PRINTW 主人公设定为「你」了
	CALL MASTER_CUSTOM
ELSE
	C = RESULT - 99
	;キャラがいない＝フラグ値が0なら再入力
	IF FLAG:RESULT == 0
		PRINTL 没有那个角色
		GOTO INPUT_LOOPB
	ELSE
		ADDCHARA C
		MASTER = CHARANUM-1
		FLAG:RESULT = 0
		PRINTFORMW 主人公设定为「%NAME:MASTER%」了
	ENDIF
ENDIF

;────────────────────────────────────
;エキストラモードの主人公の選択
;────────────────────────────────────
@START_CHARA_SHOW
DRAWLINE
PRINTL ★★选择要被调教的角色★★
SIF FLAG:5 == 9
	PRINTL 　[0]选择「你」

T = 0
S = 0
REPEAT 20
	S = 100 + TFLAG:100 * 20 + T
	IF FLAG:S > 0
		PRINT [
		PRINTV S
		PRINT ]
		PRINTFORML %ITEMNAME:S%
	ENDIF
	T += 1
REND

DRAWLINE
PRINT 　page
PRINTV TFLAG:100
PRINTC 　[1]上一页
PRINTC [9]下一页
PRINTL 
