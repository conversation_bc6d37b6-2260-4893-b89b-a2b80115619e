﻿;────────────────────────────────────
;000,沈黙する（お仕置きポイント）
;────────────────────────────────────
@COM0
;質問されて答えない場合はお仕置きポイントに加算
IF TFLAG:90 == 0 || TFLAG:90 == 1
	TFLAG:68 += 1 + RAND:3 + MARK:3 / 2 + BASE:7 / 100
	TFLAG:300 = 1
ENDIF
TFLAG:125 = 3
RETURN 1


;────────────────────────────────────
;001,気弱く応答する（トラウマ、屈従、恐怖、消極的従う、哀願）
;────────────────────────────────────
@COM1
TIMES SOURCE:24 , 1.20
SOURCE:30 += 100 + MARK:2 * 20
SIF TFLAG:90 == 4 || TFLAG:90 == 7 || TFLAG:90 == 8 || TFLAG:90 == 9 || TFLAG:90 == 66
	SOURCE:34 += 200 + MARK:MASTER:4 * 50

;大体の状況は消極的従うになるが、特定の状況で哀願になります
A = PALAM:6 / 1000 + PALAM:8 / 1000 + PALAM:17 / 1500 + PALAM:7 / 1000 + PALAM:10 / 1500 + PALAM:12 / 2000
A -= PALAM:9 / 200 - MARK:3 * 2
PRINTL 
IF (TFLAG:90 == 4 || TFLAG:90 == 7 || TFLAG:90 == 8 || TFLAG:90 == 27) && A > 0
	TFLAG:94 = 3
	TFLAG:300 = 1
ELSE
	TFLAG:94 = 1
ENDIF
PRINTL 
TFLAG:125 = 2
RETURN 1


;────────────────────────────────────
;002,無愛想に応答する（反抗、お仕置きポイント）
;────────────────────────────────────
@COM2
SOURCE:33 += 200
TFLAG:125 = 3
TFLAG:68 += RAND:5 + MARK:3 / 2 + BASE:7 / 100 - BASE:5 / 100
RETURN 1

;────────────────────────────────────
;003,愛想よく応答する（達成、悦楽、積極的従う）
;────────────────────────────────────
@COM3
SOURCE:31 += 200
SOURCE:32 += 50 + MARK:MASTER:1 * 20 + SOURCE:0 / 8 + SOURCE:1 / 8 + SOURCE:2 / 8 + SOURCE:3 / 8
TFLAG:94 = 2
TFLAG:125 = 1
RETURN 1


;────────────────────────────────────
;004,強気に応答する（反抗、お仕置きポイント）
;────────────────────────────────────
@COM4
SOURCE:33 += 500 + MARK:3 * 10
TFLAG:68 += 5 + MARK:3 / 2 + BASE:7 / 100 - BASE:6 / 100
SIF TFLAG:90 == 27
	TFLAG:300 = 1
TFLAG:125 = 3
RETURN 1


;────────────────────────────────────
;005,皮肉を言う（屈従、反抗、消極的従う、お仕置きポイント）
;────────────────────────────────────
@COM5
SOURCE:30 += 250 - MARK:3 * 20
SOURCE:33 += 250 - MARK:2 * 20
TFLAG:68 += 3 + RAND:5 + MARK:3 / 5
IF TFLAG:90 != 0 && TFLAG:90 != 1
	TFLAG:94 = 1
	PRINTL 
	PRINTFORML %CALLNAME:MASTER%虽然发出讽刺，但是没有做反抗的事情…
ENDIF
TFLAG:125 = 3
RETURN 1


;────────────────────────────────────
;006,もっとハードにして（屈従、お仕置きポイント）
;────────────────────────────────────
@COM6
CALL ABL_REVISION
SOURCE:30 += 50 + MARK:MASTER:0 * 20 + B:11 * CFLAG:MASTER:0 * 2
;この恥知らずのメス豚め！とか言いながらお仕置きになります
A = BASE:7 / 100 + TALENT:83 * 3 + TALENT:86 * 5 - TALENT:87 * 5 - MARK:2 - CFLAG:6 / 10 + RAND:5
IF A > 0
	TFLAG:68 += A
	TFLAG:300 = 1
ENDIF
TFLAG:125 = 5
RETURN 1

;────────────────────────────────────
;007,許しを乞う（屈従、恐怖、哀願、お仕置きポイント）
;────────────────────────────────────
@COM7
SOURCE:30 += 200 + MARK:2 * MARK:MASTER:4 * 10
SOURCE:34 += SOURCE:13 / 3 + SOURCE:14 / 5 + SOURCE:22 / 4 + SOURCE:24 / 3

;意見を出すなんていい度胸だね！とか言いながらお仕置きになります
A = BASE:7 / 80 - BASE:5 / 100 - CFLAG:6 / 10 + PALAM:9 / 1000 + TALENT:83 * 5 + TALENT:86 * 5 - TALENT:87 * 5 - MARK:MASTER:4 / 2
SIF TFLAG:70 == 5
	A += RAND:7
IF A > 0
	;こんなことさせてなおお仕置きするのは変かな？
	IF TFLAG:90 != 74
		TFLAG:68 += A
		TFLAG:300 = 1
	ENDIF
ELSE
	TFLAG:94 = 3
ENDIF
TFLAG:125 = 6
RETURN 1

;────────────────────────────────────
;008,気持ち良くして（悦楽、積極的従う、お仕置きポイント）
;────────────────────────────────────
@COM8
CALL ABL_REVISION
SOURCE:32 += PALAM:5 / 5 + CFLAG:MASTER:0 * B:1 * MARK:MASTER:1 / 10
;この恥知らずのメス豚め！とか言いながらお仕置きになります
A = BASE:7 / 100 - CFLAG:6 / 10 - CFLAG:MASTER:0 + TALENT:83 * 3 + TALENT:86 * 5 + MARK:MASTER:1 / 2 + RAND:5
IF A > 0
	TFLAG:68 += A
	TFLAG:300 = 1
ENDIF
TFLAG:94 = 2
TFLAG:125 = 5
RETURN 1
