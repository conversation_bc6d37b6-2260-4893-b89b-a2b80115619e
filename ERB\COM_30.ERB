﻿;────────────────────────────────────
;030,痛みを我慢する（痛み、拘束、反抗、恐怖）
;────────────────────────────────────
@COM30
TIMES SOURCE:13 , 0.85
TIMES SOURCE:14 , 0.95

SOURCE:33 += 10 + SOURCE:13 * (MARK:3 + 1) / 20 - SOURCE:14
SIF SOURCE:33 < 0
	SOURCE:33 = 0
SOURCE:34 += SOURCE:13 * (MARK:MASTER:4 + 10) * (11 - MARK:MASTER:0) * (11 - MARK:3) / 1000
TFLAG:94 = 1
TFLAG:125 = 2
RETURN 1

;────────────────────────────────────
;031,悲鳴を上げる（屈従、達成、恐怖）
;────────────────────────────────────
@COM31
CALL ABL_REVISION
SOURCE:30 += 200 + SOURCE:13 * (CFLAG:MASTER:0 + 1) * (MARK:2 + 10) / 100
SOURCE:31 += (SOURCE:13 / 3 + SOURCE:30 / 2) * (3 + TALENT:83 + TALENT:86 - TALENT:87) / 5
SOURCE:34 += 200 + SOURCE:13 * (200 - B:11) * (MARK:MASTER:4 + 10) / 2000
TFLAG:94 = 1
TFLAG:125 = 5
IF SOURCE:13 > 500 || SOURCE:13 > PALAM:7 * 2

ELSE
	TFLAG:300 = 1
ENDIF
RETURN 1

;────────────────────────────────────
;032,暴れる（痛み、拘束、達成逆、反抗、恐怖、暴れる、お仕置きポイント）
;────────────────────────────────────
@COM32
CALL ABL_REVISION
IF (TEQUIP:40 || TEQUIP:46 || TEQUIP:47) && TFLAG:90 != 63
	SOURCE:13 += (TEQUIP:40 + TEQUIP:46 + TEQUIP:47 + 1) * 200
	TIMES SOURCE:14 , 1.50
ENDIF

SOURCE:33 += 250 + (13 + MARK:3 * 2 - MARK:2 + TALENT:MASTER:11 * 5 - TALENT:MASTER:14 * 3) * 20
SOURCE:31 -= SOURCE:33 / 2

;暴れ成功の判定
A = CFLAG:MASTER:7 * 2 + BASE:MASTER:0 / 200 + BASE:MASTER:1 / 100 + MARK:3 + PALAM:9 / 1000 - MARK:MASTER:4 - B:30 / 10 + TALENT:MASTER:11 * 5 - TALENT:MASTER:13 * 5 + TALENT:MASTER:111 * 5 - TALENT:MASTER:110 * 5
B = BASE:0 / 250 + BASE:1 / 150 + A:30 + TALENT:111 * 5 - TALENT:110 * 5 + PALAM:10 / 1500 - RAND:5
SIF TFLAG:90 != 63
	B += (TEQUIP:40 + TEQUIP:46 + TEQUIP:47) * 5

IF TFLAG:90 == 12
	C = 2
ELSEIF TFLAG:90 == 15
	C = 5
ELSEIF TFLAG:90 == 17
	C = 3
ELSEIF TFLAG:90 == 18
	C = 0
ELSEIF TFLAG:90 == 22 || TFLAG:90 == 23
	C = 12
ELSEIF TFLAG:90 == 30 || TEQUIP:70 == 1
	C = 12
ELSEIF TFLAG:90 == 31 || TEQUIP:70 == 2
	C = 18
ELSEIF TFLAG:90 == 32 || TEQUIP:70 == 3
	C = 7
ELSEIF TFLAG:90 == 33 || TEQUIP:70 == 4
	C = 15
ELSEIF TFLAG:90 == 34 || TEQUIP:70 == 5
	C = 16
ELSEIF TFLAG:90 == 35 || TEQUIP:70 == 6
	C = 20
ELSEIF TFLAG:90 == 36 || TEQUIP:70 == 7
	C = 12
ELSEIF TFLAG:90 == 56
	C = 14
ELSEIF TFLAG:90 == 60
	C = 9
ELSEIF TFLAG:90 == 61
	C = 12
ELSEIF TFLAG:90 == 62
	C = 7
ELSEIF TFLAG:90 == 63
	C = 8
ELSEIF TFLAG:90 == 64
	C = 9
ELSEIF TFLAG:90 == 92
	C = -10
ELSE
	C = 10
ENDIF
SIF ASSI > 0
	C += C:30 / 2 + 3
;ＳＰ行動
SIF TFLAG:120 > 199
	C += 10

IF A > B + C && TEQUIP:43 == 0
	TFLAG:94 = 4
	TIMES SOURCE:33 , 2.50
	;道具装着失敗
	SIF TFLAG:90 == 21
		TEQUIP:20 = 0
	SIF TFLAG:90 == 22
		TEQUIP:25 = 0
	SIF TFLAG:90 == 23
		TEQUIP:26 = 0
	IF TFLAG:90 == 24
		TEQUIP:30 = 0
		TEQUIP:31 = 0
	ENDIF
	SIF TFLAG:90 == 25
		TEQUIP:35 = 0
	SIF TFLAG:90 == 26
		TEQUIP:36 = 0
	SIF TFLAG:90 == 63
		TEQUIP:40 = 0
	SIF TFLAG:232
		TEQUIP:46 = 0
	SIF TFLAG:233
		TEQUIP:47 = 0
	SIF TFLAG:90 == 64
		TEQUIP:41 = 0
	SIF TFLAG:90 == 65
		TEQUIP:42 = 0
	LOSEBASE:0 += 15 * (6 + MARK:3 + TALENT:MASTER:111 * 5 - TALENT:MASTER:110 * 5)
	LOSEBASE:1 += 20 * (8 + CFLAG:MASTER:7 + TALENT:MASTER:11 * 3 + TALENT:MASTER:16 * 3 - TALENT:MASTER:13 * 3 - TALENT:MASTER:14 * 3)
ELSE
	TFLAG:94 = 1
	TIMES SOURCE:33 , 0.10
	IF TEQUIP:40 && TFLAG:90 != 63
		TFLAG:300 = 1
	ELSEIF TFLAG:232
		TFLAG:300 = 4
	ELSEIF TFLAG:233
		TFLAG:300 = 5
	ELSE
		TFLAG:300 = 2
		LOSEBASE:0 += 10 * (6 + MARK:3 + TALENT:MASTER:111 * 5 - TALENT:MASTER:110 * 5)
		LOSEBASE:1 += 15 * (8 + CFLAG:MASTER:7 + TALENT:MASTER:11 * 3 + TALENT:MASTER:16 * 3 - TALENT:MASTER:13 * 3 - TALENT:MASTER:14 * 3)
	ENDIF
ENDIF
TFLAG:125 = 4
TFLAG:68 += TFLAG:94 * 2 + BASE:6 / 200 + BASE:7 / 150 + RAND:5
RETURN 1

;────────────────────────────────────
;033,怯える（屈従、恐怖、哀願、お仕置きポイント）
;────────────────────────────────────
@COM33
SOURCE:30 += 200 + MARK:2 * MARK:MASTER:4 * 10
SOURCE:34 += SOURCE:13 / 3 + SOURCE:14 / 5 + SOURCE:22 / 4 + SOURCE:24 / 3

;意見を出すなんていい度胸だね！とか言いながらお仕置きになります
A = BASE:7 / 80 - BASE:5 / 100 - CFLAG:6 / 10 + PALAM:9 / 1000 + TALENT:83 * 5 + TALENT:86 * 5 - TALENT:87 * 5 - MARK:MASTER:4 / 2
SIF TFLAG:70 == 5
	A += RAND:7
IF A > 0
	;こんなことさせてなおお仕置きするのは変かな？
	IF TFLAG:90 != 74
		TFLAG:68 += A
		TFLAG:300 = 1
	ENDIF
ELSE
	TFLAG:94 = 3
ENDIF
TFLAG:125 = 6
RETURN 1


;────────────────────────────────────
;各行動の反抗難易度、現在未使用
;────────────────────────────────────
@COUNTER_CHECK
C = 0
SIF TFLAG:90 == 10 || TFLAG:90 == 12
	C += 2
SIF TFLAG:90 == 13 || TFLAG:90 == 14
	C += 3
SIF TFLAG:90 == 15
	C += 2
SIF TFLAG:90 == 16 || TFLAG:90 == 18
	C -= 1
SIF TFLAG:90 == 22 || TFLAG:90 == 23
	C += 2
SIF TEQUIP:70 == 1 || TEQUIP:70 == 4
	C += 2
SIF TEQUIP:70 == 2 || TEQUIP:70 == 6
	C += 5
SIF TEQUIP:70 == 3 || TEQUIP:70 == 5
	C -= 3
SIF TEQUIP:70 == 7
	C += 1
SIF TFLAG:80 == 6 && TFLAG:90 != 66 && TFLAG:90 != 68 && TFLAG:90 != 56
	C += 5
SIF TFLAG:90 == 56
	C += 4
SIF TFLAG:70 == 5
	C += 1
