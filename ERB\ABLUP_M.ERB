﻿;────────────────────────────────────
;主人公の能力上昇
;────────────────────────────────────
;従順、屈服ルートと恐怖ルート、臆病/気丈、プライド高い/低いを見る
@ABLUP_0M
LOCAL = 0
SELECTCASE ABL:MASTER:0
	CASE 0
		LOCAL = (UP:6 + UP:8 > 100 - TALENT:MASTER:17 * 20 + TALENT:MASTER:15 * 20 && EXP:MASTER:60 > 5) || (UP:7 > 60 - TALENT:MASTER:10 * 15 + TALENT:MASTER:12 * 15 && EXP:MASTER:61 > 5)
	CASE 1
		LOCAL = (UP:6 + UP:8 > 700 - TALENT:MASTER:17 * 100 + TALENT:MASTER:15 * 100 && EXP:MASTER:60 > 12 - TALENT:MASTER:17 + TALENT:MASTER:15 && MARK:2 > 1) || (UP:7 > 250 - TALENT:MASTER:10 * 50 + TALENT:MASTER:12 * 50 && EXP:MASTER:61 > 12 && MARK:MASTER:4 > 1)
	CASE 2
		LOCAL = (UP:6 + UP:8 > 1600 - TALENT:MASTER:17 * 350 + TALENT:MASTER:15 * 350 && EXP:MASTER:60 > 30 - TALENT:MASTER:17 * 3 + TALENT:MASTER:15 * 3 && MARK:2 > 3) || (UP:7 > 800 - TALENT:MASTER:10 * 200 + TALENT:MASTER:12 * 200 && EXP:MASTER:61 > 30 - TALENT:MASTER:10 * 2 + TALENT:MASTER:12 * 2 && MARK:MASTER:4 > 3)
	CASE 3
		LOCAL = (UP:6 + UP:8 > 2600 - TALENT:MASTER:17 * 600 + TALENT:MASTER:15 * 600 && EXP:MASTER:60 > 50 - TALENT:MASTER:17 * 7 + TALENT:MASTER:15 * 7 && MARK:2 > 5) || (UP:7 > 1500 - TALENT:MASTER:10 * 400 + TALENT:MASTER:12 * 400 && EXP:MASTER:61 > 50 - TALENT:MASTER:10 * 5 + TALENT:MASTER:12 * 5 && MARK:MASTER:4 > 5 - TALENT:MASTER:10 + TALENT:MASTER:12)
	CASE 4
		LOCAL = (UP:6 + UP:8 > 4000 - TALENT:MASTER:17 * 1050 + TALENT:MASTER:15 * 1050 && EXP:MASTER:60 > 100 - TALENT:MASTER:17 * 15 + TALENT:MASTER:15 * 15 && MARK:2 > 8 - TALENT:MASTER:17 + TALENT:MASTER:15) || (UP:7 > 2500 - TALENT:MASTER:10 * 750 + TALENT:MASTER:12 * 750 && EXP:MASTER:61 > 100 - TALENT:MASTER:10 * 10 + TALENT:MASTER:12 * 10 && MARK:MASTER:4 > 8 - TALENT:MASTER:10 + TALENT:MASTER:12)
	CASE IS < 99
		LOCAL = UP:6 + UP:8 > (4000 - TALENT:MASTER:17 * 1050 + TALENT:MASTER:15 * 1050) * (5 + (ABL:MASTER:0 - 4)) / 5 && EXP:MASTER:60 > (100 - TALENT:MASTER:17 * 15 + TALENT:MASTER:15 * 15 ) * (2 + ABL:MASTER:0 - 4) * (10 + ABL:MASTER:0 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(0)


;欲望、いろんな経験を条件に
@ABLUP_1M
LOCAL = 0
SELECTCASE ABL:MASTER:1
	CASE 0
		LOCAL = UP:5 > 50 && EXP:MASTER:0 + EXP:MASTER:1 + EXP:MASTER:2 + EXP:MASTER:3 > 6
	CASE 1
		LOCAL = UP:5 > 350 && EXP:MASTER:4 > 1 && MARK:MASTER:1 > 0
	CASE 2
		LOCAL = UP:5 > 900 && EXP:MASTER:8 + EXP:MASTER:10 > 20 && MARK:MASTER:1 > 3
	CASE 3
		LOCAL = UP:5 > 1800 && EXP:MASTER:50 > 5 && MARK:MASTER:1 > 6
	CASE 4
		LOCAL = UP:5 > 3000 && (EXP:MASTER:0 + EXP:MASTER:1 + EXP:MASTER:2 + EXP:MASTER:3) / 2 + EXP:MASTER:10 * 2 + EXP:MASTER:50 * 3 + EXP:MASTER:52 * 10 + EXP:MASTER:53 * 10 > 200 && MARK:MASTER:1 > 9
	CASE IS < 99
		LOCAL = UP:5 > 3000 * (5 + (ABL:MASTER:1 - 4)) / 5 && (EXP:MASTER:0 / 2 + EXP:MASTER:1 + EXP:MASTER:2 + EXP:MASTER:3) / 2 + EXP:MASTER:10 * 2 + EXP:MASTER:50 * 3 + EXP:MASTER:52 * 10 + EXP:MASTER:53 * 10 > 300 * (2 + ABL:MASTER:1 - 4) * (10 + ABL:MASTER:1 - 5) / 20 && MARK:MASTER:1 > 9
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(1)


;技巧、奉仕で調教者を気持ちよくするとあがる
@ABLUP_2M
LOCAL = 0
SELECTCASE ABL:MASTER:2
	CASE 0
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 100 - TALENT:MASTER:50 * 20 + TALENT:MASTER:51 * 20
	CASE 1
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 500 - TALENT:MASTER:50 * 90 + TALENT:MASTER:51 * 90 && EXP:MASTER:22 + EXP:MASTER:23 + EXP:MASTER:7 > 30 - TALENT:MASTER:50 * 2 + TALENT:MASTER:51 * 2
	CASE 2
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 800 - TALENT:MASTER:50 * 200 + TALENT:MASTER:51 * 200 && EXP:MASTER:22 + EXP:MASTER:23 + EXP:MASTER:7 > 70 - TALENT:MASTER:50 * 5 + TALENT:MASTER:51 * 5
	CASE 3
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 1100 - TALENT:MASTER:50 * 400 + TALENT:MASTER:51 * 400 && EXP:MASTER:22 + EXP:MASTER:23 + EXP:MASTER:7 > 140 - TALENT:MASTER:50 * 12 + TALENT:MASTER:51 * 12 && EXP:MASTER:8 > 10
	CASE 4
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 1500 - TALENT:MASTER:50 * 750 + TALENT:MASTER:51 * 750 && EXP:MASTER:22 + EXP:MASTER:23 + EXP:MASTER:7 > 200 - TALENT:MASTER:50 * 25 + TALENT:MASTER:51 * 25 && CFLAG:MASTER:0 > 8
	CASE IS < 99
		LOCAL = UP:40 + UP:41 + UP:42 + UP:43 > 2000 - TALENT:MASTER:50 * 750 + TALENT:MASTER:51 * 750 && EXP:MASTER:22 + EXP:MASTER:23 + EXP:MASTER:7 > (200 - TALENT:MASTER:50 * 25 + TALENT:MASTER:51 * 25) * (2 + ABL:MASTER:2 - 4) * (10 + ABL:MASTER:2 - 5) / 20 && EXP:MASTER:63 > ( 100 - TALENT:MASTER:50 * 30 + TALENT:MASTER:51 * 30) * (2 + ABL:MASTER:2 - 4) * (10 + ABL:MASTER:2 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(2)


;Ｃ感覚、Ｃ敏感、Ｃ経験、絶頂経験、欲望を見る
@ABLUP_3M
LOCAL = 0
SELECTCASE ABL:MASTER:3
	CASE 0
		LOCAL = UP:0 > 50 - TALENT:MASTER:100 * 20 + TALENT:MASTER:101 * 20 && EXP:MASTER:0 > 3 - TALENT:MASTER:100 + TALENT:MASTER:101
	CASE 1
		LOCAL = UP:0 > 250 - TALENT:MASTER:100 * 60 + TALENT:MASTER:101 * 60 && EXP:MASTER:0 > 40 - TALENT:MASTER:100 * 5 + TALENT:MASTER:101 * 5
	CASE 2
		LOCAL = UP:0 > 800 - TALENT:MASTER:100 * 150 + TALENT:MASTER:101 * 150 && EXP:MASTER:0 > 150 - TALENT:MASTER:100 * 12 + TALENT:MASTER:101 * 12 && EXP:MASTER:4 > 0
	CASE 3
		LOCAL = UP:0 > 1600 - TALENT:MASTER:100 * 400 + TALENT:MASTER:101 * 400 && EXP:MASTER:0 > 250 - TALENT:MASTER:100 * 30 + TALENT:MASTER:101 * 30
	CASE 4
		LOCAL = UP:0 > 2800 - TALENT:MASTER:100 * 750 + TALENT:MASTER:101 * 750 && EXP:MASTER:0 > 500 - TALENT:MASTER:100 * 50 + TALENT:MASTER:101 * 50 && ABL:MASTER:1 > 3 - TALENT:MASTER:100 + TALENT:MASTER:101
	CASE IS < 99
		LOCAL = UP:0 > 2800 - TALENT:MASTER:100 * 750 + TALENT:MASTER:101 * 750 && EXP:MASTER:0 > (300 - TALENT:MASTER:100 * 50 + TALENT:MASTER:101 * 50) * (2 + ABL:MASTER:3 - 4) * (10 + ABL:MASTER:3 - 5) / 10
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(3)


;Ｖ感覚、Ｖ敏感、Ｖ経験、性交経験、Ｖ拡張経験を見る
@ABLUP_4M
LOCAL = 0
SELECTCASE ABL:MASTER:4
	CASE 0
		LOCAL = UP:1 > 50 - TALENT:MASTER:102 * 20 + TALENT:MASTER:103 * 20 && EXP:MASTER:1 > 3 - TALENT:MASTER:102 + TALENT:MASTER:103
	CASE 1
		LOCAL = UP:1 > 250 - TALENT:MASTER:102 * 60 + TALENT:MASTER:103 * 60 && EXP:MASTER:1 > 40 - TALENT:MASTER:102 * 5 + TALENT:MASTER:103 * 5
	CASE 2
		LOCAL = UP:1 > 800 - TALENT:MASTER:102 * 150 + TALENT:MASTER:103 * 150 && EXP:MASTER:1 > 92 - TALENT:MASTER:102 * 12 + TALENT:MASTER:103 * 12 && EXP:MASTER:7 > 5 - TALENT:MASTER:102 + TALENT:MASTER:103
	CASE 3
		LOCAL = UP:1 > 1600 - TALENT:MASTER:102 * 400 + TALENT:MASTER:103 * 400 && EXP:MASTER:1 > 170 - TALENT:MASTER:102 * 30 + TALENT:MASTER:103 * 30
	CASE 4
		LOCAL = UP:1 > 2800 - TALENT:MASTER:102 * 750 + TALENT:MASTER:103 * 750 && EXP:MASTER:4 + 3 * EXP:MASTER:7 + 10 * EXP:MASTER:52 > 300 - TALENT:MASTER:102 * 50 + TALENT:MASTER:103 * 50
	CASE IS < 99
		LOCAL = UP:1 > 2800 - TALENT:MASTER:102 * 750 + TALENT:MASTER:103 * 750 && EXP:MASTER:4 + 3 * EXP:MASTER:7 + 10 * EXP:MASTER:52 > (300 - TALENT:MASTER:102 * 50 + TALENT:MASTER:103 * 50) * (2 + ABL:MASTER:4 - 4) * (10 + ABL:MASTER:4 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(4)


;Ａ感覚、Ａ敏感、Ａ経験、異常経験、Ａ拡張経験を見る
@ABLUP_5M
LOCAL = 0
SELECTCASE ABL:MASTER:5
	CASE 0
		LOCAL = UP:2 > 50 - TALENT:MASTER:104 * 20 + TALENT:MASTER:105 * 20 && EXP:MASTER:2 > 3 - TALENT:MASTER:104 + TALENT:MASTER:105
	CASE 1
		LOCAL = UP:2 > 250 - TALENT:MASTER:104 * 60 + TALENT:MASTER:105 * 60 && EXP:MASTER:2 > 40 - TALENT:MASTER:104 * 5 + TALENT:MASTER:105 * 5
	CASE 2
		LOCAL = UP:2 > 800 - TALENT:MASTER:104 * 150 + TALENT:MASTER:105 * 150 && EXP:MASTER:2 > 92 - TALENT:MASTER:104 * 12 + TALENT:MASTER:105 * 12 && EXP:MASTER:50 > 0
	CASE 3
		LOCAL = UP:2 > 1600 - TALENT:MASTER:104 * 400 + TALENT:MASTER:105 * 400 && EXP:MASTER:2 > 170 - TALENT:MASTER:104 * 30 + TALENT:MASTER:105 * 30
	CASE 4
		LOCAL = UP:2 > 2800 - TALENT:MASTER:104 * 750 + TALENT:MASTER:105 * 750 && EXP:MASTER:2 + 10 * EXP:MASTER:53 > 300 - TALENT:MASTER:104 * 50 + TALENT:MASTER:105 * 50 && EXP:MASTER:50 > 3
	CASE IS < 99
		LOCAL = UP:2 > 2800 - TALENT:MASTER:104 * 750 + TALENT:MASTER:105 * 750 && EXP:MASTER:2 + 10 * EXP:MASTER:53 > (300 - TALENT:MASTER:104 * 50 + TALENT:MASTER:105 * 50) * (2 + ABL:MASTER:5 - 4) * (10 + ABL:MASTER:5 - 5) / 20 
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(5)


;Ｂ感覚、Ｂ敏感、Ｂ経験、そして快楽刻印を見る
@ABLUP_6M
LOCAL = 0
SELECTCASE ABL:MASTER:6
	CASE 0
		LOCAL = UP:3 > 50 - TALENT:MASTER:106 * 20 + TALENT:MASTER:107 * 20 && EXP:MASTER:3 > 3 - TALENT:MASTER:106 + TALENT:MASTER:107
	CASE 1
		LOCAL = UP:3 > 250 - TALENT:MASTER:106 * 60 + TALENT:MASTER:107 * 60 && EXP:MASTER:3 > 40 - TALENT:MASTER:106 * 5 + TALENT:MASTER:107 * 5
	CASE 2
		LOCAL = UP:3 > 800 - TALENT:MASTER:106 * 150 + TALENT:MASTER:107 * 150 && EXP:MASTER:3 > 92 - TALENT:MASTER:106 * 12 + TALENT:MASTER:107 * 12 && MARK:MASTER:1 > 2
	CASE 3
		LOCAL = UP:3 > 1600 - TALENT:MASTER:106 * 400 + TALENT:MASTER:107 * 400 && EXP:MASTER:3 > 170 - TALENT:MASTER:106 * 30 + TALENT:MASTER:107 * 30
	CASE 4
		LOCAL = UP:3 > 2800 - TALENT:MASTER:106 * 750 + TALENT:MASTER:107 * 750 && EXP:MASTER:3 + 10 * EXP:MASTER:6 > 300 - TALENT:MASTER:106 * 50 + TALENT:MASTER:107 * 50
	CASE IS < 99
		LOCAL = UP:3 > 2800 - TALENT:MASTER:106 * 750 + TALENT:MASTER:107 * 750 && EXP:MASTER:3 + 10 * EXP:MASTER:6 > (300 - TALENT:MASTER:106 * 50 + TALENT:MASTER:107 * 50) * (2 + ABL:MASTER:6 - 4) * (10 + ABL:MASTER:6 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(6)


;奉仕精神、献身的/受け身、奉仕関連の経験、そして奉仕快楽経験を見る
@ABLUP_7M
LOCAL = 0
SELECTCASE ABL:MASTER:7
	CASE 0
		LOCAL = UP:8 > 80 - TALENT:MASTER:63 * 40 + TALENT:MASTER:65 * 40
	CASE 1
		LOCAL = UP:8 > 300 - TALENT:MASTER:63 * 75 + TALENT:MASTER:65 * 75 && EXP:MASTER:22 + EXP:MASTER:23 > 6 - TALENT:MASTER:63 * 2 + TALENT:MASTER:65 * 2
	CASE 2
		LOCAL = UP:8 > 750 - TALENT:MASTER:63 * 180 + TALENT:MASTER:65 * 180 && EXP:MASTER:22 + EXP:MASTER:23 > 20 - TALENT:MASTER:63 * 5 + TALENT:MASTER:65 * 5 && EXP:MASTER:21 > 0
	CASE 3
		LOCAL = UP:8 > 1400 - TALENT:MASTER:63 * 350 + TALENT:MASTER:65 * 350 && EXP:MASTER:22 + EXP:MASTER:23 > 50 - TALENT:MASTER:63 * 9 + TALENT:MASTER:65 * 9 && EXP:MASTER:21 > 50 - TALENT:MASTER:63 + TALENT:MASTER:65
	CASE 4
		LOCAL = UP:8 > 2500 - TALENT:MASTER:63 * 750 + TALENT:MASTER:65 * 750 && EXP:MASTER:22 + EXP:MASTER:23 > 100 - TALENT:MASTER:63 * 15 + TALENT:MASTER:65 * 15 && EXP:MASTER:21 > 200 - TALENT:MASTER:63 * 4 + TALENT:MASTER:65 * 4
	CASE IS < 99
		LOCAL = UP:8 > (2500 - TALENT:MASTER:63 * 750 + TALENT:MASTER:65 * 750) * (5 + (ABL:MASTER:7 - 4)) / 5 && EXP:MASTER:22 + EXP:MASTER:23 > (100 - TALENT:MASTER:63 * 15 + TALENT:MASTER:65 * 15) * (2 + ABL:MASTER:7 - 4) * (10 + ABL:MASTER:7 - 5) / 20 && EXP:MASTER:21 > (200 - TALENT:MASTER:63 * 4 + TALENT:MASTER:65 * 4) * (2 + ABL:MASTER:7 - 4) * (10 + ABL:MASTER:7 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(7)


;露出癖、露出経験、異常経験、抑圧/解放を見る。レベルにアップする条件はちょっと厳しいかもしれません。
@ABLUP_8M
LOCAL = 0
SELECTCASE ABL:MASTER:8
	CASE 0
		LOCAL = UP:11 > 75 - TALENT:MASTER:33 * 15 && EXP:MASTER:62 > 2 - TALENT:MASTER:33 + TALENT:MASTER:32
	CASE 1
		LOCAL = UP:11 > 300 + TALENT:MASTER:32 * 100 - TALENT:MASTER:33 * 100 && EXP:MASTER:62 > 8 + TALENT:MASTER:32 * 3 - TALENT:MASTER:33 * 3 && EXP:MASTER:4 > 0
	CASE 2
		LOCAL = UP:11 > 750 + TALENT:MASTER:32 * 200 - TALENT:MASTER:33 * 200 && EXP:MASTER:62 > 25 + TALENT:MASTER:32 * 6 - TALENT:MASTER:33 * 6
	CASE 3
		LOCAL = UP:11 > 1500 + TALENT:MASTER:32 * 300 - TALENT:MASTER:33 * 300 && EXP:MASTER:62 > 60 + TALENT:MASTER:32 * 12 - TALENT:MASTER:33 * 12 && EXP:MASTER:50 > 1
	CASE 4
		LOCAL = UP:11 > 2650 + TALENT:MASTER:32 * 500 - TALENT:MASTER:33 * 500 && EXP:MASTER:62 > 100 + TALENT:MASTER:32 * 12 - TALENT:MASTER:33 * 12
	CASE IS < 99
		LOCAL = UP:11 > 2650 + TALENT:MASTER:32 * 500 - TALENT:MASTER:33 * 500 && EXP:MASTER:62 > (100 + TALENT:MASTER:32 * 12 - TALENT:MASTER:33 * 12) * (2 + ABL:MASTER:8 - 4) * (10 + ABL:MASTER:8 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(8)


;レズっ気、情愛と悦楽のソース、レズ経験、両刀を見る
@ABLUP_9M
LOCAL = 0
SELECTCASE ABL:MASTER:9
	CASE 0
		LOCAL = SOURCE:11 + SOURCE:32 > 100 - TALENT:MASTER:81 * 20 && EXP:MASTER:40 > 20 - TALENT:MASTER:81 * 10
	CASE 1
		LOCAL = SOURCE:11 + SOURCE:32 > 400 - TALENT:MASTER:81 * 65 && EXP:MASTER:40 > 60 - TALENT:MASTER:81 * 15
	CASE 2
		LOCAL = SOURCE:11 + SOURCE:32 > 900 - TALENT:MASTER:81 * 150 && EXP:MASTER:40 > 150 - TALENT:MASTER:81 * 32
	CASE 3
		LOCAL = SOURCE:11 + SOURCE:32 > 1750 - TALENT:MASTER:81 * 350 && EXP:MASTER:40 > 350 - TALENT:MASTER:81 * 66
	CASE 4
		LOCAL = SOURCE:11 + SOURCE:32 > 3000 - TALENT:MASTER:81 * 600 && EXP:MASTER:40 > 600 - TALENT:MASTER:81 * 120
	CASE IS < 99
		LOCAL = SOURCE:11 + SOURCE:32 > 3000 - TALENT:MASTER:81 * 600 && EXP:MASTER:40 > (600 - TALENT:MASTER:81 * 120) * (2 + ABL:MASTER:9 - 4) * (10 + ABL:MASTER:9 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(9)


;ＢＬっ気、情愛と悦楽のソース、ＢＬ経験、両刀を見る
@ABLUP_10M
LOCAL = 0
SELECTCASE ABL:MASTER:10
	CASE 0
		LOCAL = SOURCE:11 + SOURCE:32 > 100 - TALENT:MASTER:81 * 20 && EXP:MASTER:41 > 20 - TALENT:MASTER:81 * 10
	CASE 1
		LOCAL = SOURCE:11 + SOURCE:32 > 400 - TALENT:MASTER:81 * 65 && EXP:MASTER:41 > 60 - TALENT:MASTER:81 * 15
	CASE 2
		LOCAL = SOURCE:11 + SOURCE:32 > 900 - TALENT:MASTER:81 * 150 && EXP:MASTER:41 > 150 - TALENT:MASTER:81 * 32
	CASE 3
		LOCAL = SOURCE:11 + SOURCE:32 > 1750 - TALENT:MASTER:81 * 350 && EXP:MASTER:41 > 350 - TALENT:MASTER:81 * 66
	CASE 4
		LOCAL = SOURCE:11 + SOURCE:32 > 3000 - TALENT:MASTER:81 * 600 && EXP:MASTER:41 > 600 - TALENT:MASTER:81 * 120
	CASE IS < 99
		LOCAL = SOURCE:11 + SOURCE:32 > 3000 - TALENT:MASTER:81 * 600 && EXP:MASTER:41 > (600 - TALENT:MASTER:81 * 120) * (2 + ABL:MASTER:10 - 4) * (10 + ABL:MASTER:10 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(10)


;マゾっ気、苦痛快楽/緊縛経験、倒錯的を見る
@ABLUP_11M
LOCAL = 0
SELECTCASE ABL:MASTER:11
	CASE 0
		LOCAL = UP:10 + UP:12 - UP:13 > 100 - TALENT:MASTER:80 * 30 && EXP:MASTER:30 > 1 - TALENT:MASTER:80
	CASE 1
		LOCAL = UP:10 + UP:12 - UP:13 > 200 - TALENT:MASTER:80 * 50 && EXP:MASTER:30 + EXP:MASTER:51 > 11 - TALENT:MASTER:80 * 2
	CASE 2
		LOCAL = UP:10 + UP:12 - UP:13 > 500 - TALENT:MASTER:80 * 120 && EXP:MASTER:30 + EXP:MASTER:51 > 34 - TALENT:MASTER:80 * 4
	CASE 3
		LOCAL = UP:10 + UP:12 - UP:13 > 1000 - TALENT:MASTER:80 * 250 && EXP:MASTER:30 + EXP:MASTER:51 > 70 - TALENT:MASTER:80 * 10
	CASE 4
		LOCAL = UP:10 + UP:12 - UP:13 > 1500 - TALENT:MASTER:80 * 450 && EXP:MASTER:30 + EXP:MASTER:51 > 119 - TALENT:MASTER:80 * 19 && EXP:MASTER:50 > 0
	CASE IS < 99
		LOCAL = UP:10 + UP:12 - UP:13 > 1500 - TALENT:MASTER:80 * 450 && EXP:MASTER:30 + EXP:MASTER:51 > (119 - TALENT:MASTER:80 * 19) * (2 + ABL:MASTER:11 - 4) * (10 + ABL:MASTER:11 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(11)


;自慰中毒、自慰で得られた快感、欲望と露出癖、自慰と調教自慰経験、自慰しやすいを見る
@ABLUP_12M
LOCAL = 0
SELECTCASE ABL:MASTER:12
	CASE 0
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 250 && ABL:MASTER:1 + ABL:MASTER:8 > 3 - TALENT:MASTER:60 && EXP:MASTER:10 + EXP:MASTER:11 * 2 > 45 - TALENT:MASTER:60 * 15
	CASE 1
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 600 && ABL:MASTER:1 + ABL:MASTER:8 > 4 - TALENT:MASTER:60 && EXP:MASTER:10 + EXP:MASTER:11 * 2 > 70 - TALENT:MASTER:60 * 25
	CASE 2
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 1000 && ABL:MASTER:1 + ABL:MASTER:8 > 6 - TALENT:MASTER:60 * 2 && EXP:MASTER:10 + EXP:MASTER:11 * 2 > 110 - TALENT:MASTER:60 * 35
	CASE 3
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 1500 - TALENT:MASTER:60 * 200 && ABL:MASTER:1 + ABL:MASTER:8 > 8 - TALENT:MASTER:60 * 2 && EXP:MASTER:10 + EXP:MASTER:11 * 2 > 165 - TALENT:MASTER:60 * 45
	CASE 4
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 2000 - TALENT:MASTER:60 * 400 && ABL:MASTER:1 + ABL:MASTER:8 > 11 - TALENT:MASTER:76 * 2 - TALENT:MASTER:60 && EXP:MASTER:10 + EXP:MASTER:11 * 2 > 250 - TALENT:MASTER:60 * 70
	CASE IS < 99
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 2000 - TALENT:MASTER:60 * 400 && ABL:MASTER:1 + ABL:MASTER:8 > (11 - TALENT:MASTER:76 * 2 - TALENT:MASTER:60) * (5 + (ABL:MASTER:12 - 4)) / 5 && EXP:MASTER:10 + EXP:MASTER:11 * 2 > (250 - TALENT:MASTER:60 * 70) * (2 + ABL:MASTER:12 - 4) * (10 + ABL:MASTER:12 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(12)


;精液中毒、奉仕精神、精液経験を見る
@ABLUP_13M
LOCAL = 0
SELECTCASE ABL:MASTER:13
	CASE 0
		LOCAL = ABL:MASTER:7 > 0 && EXP:MASTER:20 > 25
	CASE 1
		LOCAL = ABL:MASTER:7 > 1 && EXP:MASTER:20 > 40
	CASE 2
		LOCAL = ABL:MASTER:7 > 2 && EXP:MASTER:20 > 52
	CASE 3
		LOCAL = ABL:MASTER:7 > 3 && EXP:MASTER:20 > 64
	CASE 4
		LOCAL = ABL:MASTER:7 > 4 && EXP:MASTER:20 > 75
	CASE IS < 99
		LOCAL = ABL:MASTER:7 > ABL:MASTER:13 - 1 && EXP:MASTER:20 > 75 * (2 + ABL:MASTER:13 - 4) * (10 + ABL:MASTER:13 - 5) / 20
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(13)


;レズ中毒、好感度、レズ経験、両刀、レズっ気を見る
@ABLUP_14M
LOCAL = 0
SELECTCASE ABL:MASTER:14
	CASE 0
		LOCAL = CFLAG:2 > 499 && EXP:MASTER:40 > 100 - TALENT:MASTER:81 * 20 && ABL:MASTER:9 > 0
	CASE 1
		LOCAL = CFLAG:2 > 999 && EXP:MASTER:40 > 200 - TALENT:MASTER:81 * 40 && ABL:MASTER:9 > 1
	CASE 2
		LOCAL = CFLAG:2 > 1499 && EXP:MASTER:40 > 300 - TALENT:MASTER:81 * 60 && ABL:MASTER:9 > 2
	CASE 3
		LOCAL = CFLAG:2 > 1999 && EXP:MASTER:40 > 400 - TALENT:MASTER:81 * 80 && ABL:MASTER:9 > 3
	CASE 4
		LOCAL = CFLAG:2 > 2499 && EXP:MASTER:40 > 500 - TALENT:MASTER:81 * 100 && ABL:MASTER:9 > 4
	CASE IS < 99
		LOCAL = CFLAG:2 > 2499 + (ABL:MASTER:14 - 5) * 500 && EXP:MASTER:40 > (500 - TALENT:MASTER:81 * 100) * (2 + ABL:MASTER:14 - 4) * (10 + ABL:MASTER:14 - 5) / 20 && ABL:MASTER:9 > ABL:MASTER:14
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(14)


;苦痛中毒、苦痛、苦痛快楽経験、異常経験、どＭ、マゾっ気、欲望を見る
@ABLUP_15M
LOCAL = 0
SELECTCASE ABL:MASTER:15
	CASE 0
		LOCAL = UP:10 > 250 && EXP:MASTER:30 > 20 - TALENT:MASTER:77 * 5 && ABL:MASTER:1 > 1 && EXP:MASTER:50 > 0 && ABL:MASTER:11 > 0
	CASE 1
		LOCAL = UP:10 > 650 && EXP:MASTER:30 > 42 - TALENT:MASTER:77 * 9 && ABL:MASTER:1 > 2 && ABL:MASTER:11 > 1
	CASE 2
		LOCAL = UP:10 > 1250 && EXP:MASTER:30 > 65 - TALENT:MASTER:77 * 13 && ABL:MASTER:1 > 3 && ABL:MASTER:11 > 2
	CASE 3
		LOCAL = UP:10 > 2000 && EXP:MASTER:30 > 90 - TALENT:MASTER:77 * 20 && ABL:MASTER:1 > 4 && ABL:MASTER:11 > 3
	CASE 4
		LOCAL = UP:10 > 3000 && EXP:MASTER:30 > 120 - TALENT:MASTER:77 * 25 && EXP:MASTER:50 > 9 && ABL:MASTER:11 > 4
	CASE IS < 99
		LOCAL = UP:10 > 3000 && EXP:MASTER:30 > (120 - TALENT:MASTER:77 * 25) * (2 + ABL:MASTER:15 - 4) * (10 + ABL:MASTER:15 - 5) / 20 && EXP:MASTER:50 > (ABL:MASTER:15 - 1) * (ABL:MASTER:15 - 1) && ABL:MASTER:11 > ABL:MASTER:15
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(15)


;拘束中毒、不快、緊縛経験、異常経験、どＭ、マゾっ気、従順を見る
@ABLUP_16M
LOCAL = 0
SELECTCASE ABL:MASTER:16
	CASE 0
		LOCAL = UP:12 > 250 && EXP:MASTER:51 > 20 - TALENT:MASTER:77 * 5 && ABL:MASTER:0 > 1 && EXP:MASTER:50 > 0 && ABL:MASTER:11 > 0
	CASE 1
		LOCAL = UP:12 > 650 && EXP:MASTER:51 > 42 - TALENT:MASTER:77 * 9 && ABL:MASTER:0 > 2 && ABL:MASTER:11 > 1
	CASE 2
		LOCAL = UP:12 > 1250 && EXP:MASTER:51 > 65 - TALENT:MASTER:77 * 13 && ABL:MASTER:0 > 3 && ABL:MASTER:11 > 2
	CASE 3
		LOCAL = UP:12 > 2000 && EXP:MASTER:51 > 90 - TALENT:MASTER:77 * 20 && ABL:MASTER:0 > 4 && ABL:MASTER:11 > 3
	CASE 4
		LOCAL = UP:12 > 3000 && EXP:MASTER:51 > 120 - TALENT:MASTER:77 * 25 && EXP:MASTER:50 > 9 && ABL:MASTER:11 > 4
	CASE IS < 99
		LOCAL = UP:12 > 3000 && EXP:MASTER:51 > (120 - TALENT:MASTER:77 * 25) * (2 + ABL:MASTER:16 - 4) * (10 + ABL:MASTER:16 - 5) / 20 && EXP:MASTER:50 > 9 && ABL:MASTER:11 > ABL:MASTER:16
ENDSELECT
SIF LOCAL
	CALL ABLUP_MASTER(16)


@ABLUP_MASTER(ARG)
ABL:MASTER:ARG++
CALL KOJO_EVENT(23, ARG)
PRINTFORMW %CALLNAME:MASTER%的%ABLNAME:ARG%变成了\@ ABL:MASTER:ARG > 9 ? {ABL:MASTER:ARG} # %TOFULL(TOSTR(ABL:MASTER:ARG))% \@
