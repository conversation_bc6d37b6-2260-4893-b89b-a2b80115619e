﻿;────────────────────────────────────
;新しい調教者の追加
;魔改版 完全安定自動分页版
;────────────────────────────────────
@NEW_TRAINER
TFLAG:100 = 0

$INPUT_LOOP

CALL NEW_TRAINER_SHOW

INPUT

IF RESULT == 1
	IF TFLAG:100 > 0
		TFLAG:100 -= 1
	ENDIF

ELSEIF RESULT == 9
	; ==== 每次动态重新计算最大角色ID ====
	A = 100
	B = 0
	REPEAT 200
		IF ITEMNAME:A != ""
			B = A
		ENDIF
		A += 1
	REND
	
	IF TFLAG:100 < (B - 100 + 19) / 20
		TFLAG:100 += 1
	ENDIF

ELSEIF RESULT == 0
	RETURN 0

ELSEIF RESULT > 99 && RESULT < (101 + FLAG:8)
	C = RESULT - 99
	IF FLAG:RESULT == 0
		PRINTL 角色が見つかりません
		GOTO INPUT_LOOP
	ENDIF
	CFLAG:201 -= 1000
	A = TARGET
	ADDCHARA C
	TARGET = CHARANUM-1
	SIF TALENT:170
		FLAG:RESULT = 0
	FLAG:11 += 1
	RESULT = 0
	CALL KOJO_EVENT(103, 0, 1)
	SIF RESULT
		PRINTL 
	PRINTFORMW 调教者设定为「%NAME:TARGET%」了
	SIF RESULT != 12
		CALL KOJO_EVENT(103, 1)
	CALL BASE_TRAINER_SETUP
ENDIF

GOTO INPUT_LOOP


@NEW_TRAINER_SHOW
DRAWLINE
PRINTV DAY+1
PRINT 日
IF TIME == 0
	PRINTL  昼
ELSE
	PRINTL  夜
ENDIF
SIF TARGET >= 0
	PRINTFORM 被%CALLNAME:TARGET%调教中
SIF ASSI >= 0
	PRINTFORM （助手：%CALLNAME:ASSI%）

PRINTFORML (剩余MP　{CFLAG:201})

; ==== 每次动态扫描最大角色ID ====
A = 100
B = 0
REPEAT 200
	IF ITEMNAME:A != ""
		B = A
	ENDIF
	A += 1
REND

T = 0
S = 0
REPEAT 20
	S = 100 + TFLAG:100 * 20 + T
	IF FLAG:S > 0
		PRINT [
		PRINTV S
		PRINT ]
		PRINTFORML %ITEMNAME:S%
	ENDIF
	T += 1
REND

DRAWLINE
PRINT 　page
PRINTV TFLAG:100
PRINTC 　[1]上一页
PRINTC [0]不购买
PRINTC [9]下一页
PRINTL 

;────────────────────────────────────
;調教者のゲージセットアップ
;────────────────────────────────────
@BASE_TRAINER_SETUP
;調教者の射精/母乳/尿意ゲージをセットアップ
IF TALENT:122 || TALENT:121
	MAXBASE:2 = 10000
	SIF TALENT:123
		MAXBASE:2 = 5000
	SIF TALENT:124
		MAXBASE:2 = 20000
ELSE
	MAXBASE:2 = 0
ENDIF
MAXBASE:3 = 0
MAXBASE:4 = 0
SIF TALENT:114
	MAXBASE:3 = 10000
SIF TALENT:116
	MAXBASE:4 = 10000

;調教者の理性/興味/苛立ち/満足ゲージをセットアップ
MAXBASE:5 = 1000
MAXBASE:6 = 1000
MAXBASE:7 = 1000
MAXBASE:8 = 1000

;臆病/気丈
IF TALENT:10
	MAXBASE:5 -= 50
ELSEIF TALENT:12
	MAXBASE:5 += 50
ENDIF
;反抗的/素直
IF TALENT:11
	MAXBASE:7 -= 50
ELSEIF TALENT:13
	MAXBASE:7 += 50
ENDIF
;大人しい/生意気
IF TALENT:14
	MAXBASE:7 -= 50
ELSEIF TALENT:16
	MAXBASE:7 += 50
ENDIF
;プライド高い/プライド低い
IF TALENT:15
	MAXBASE:5 += 50
	MAXBASE:7 -= 25
	MAXBASE:8 += 25
ELSEIF TALENT:17
	MAXBASE:5 -= 50
	MAXBASE:7 += 25
	MAXBASE:8 -= 25
ENDIF
;感情乏しい/感情豊富な
IF TALENT:24
	MAXBASE:5 += 50
ELSEIF TALENT:25
	MAXBASE:5 -= 50
ENDIF
;自制的/衝動的
IF TALENT:MASTER:20
	MAXBASE:5 += 50
	MAXBASE:7 += 50
ELSEIF TALENT:21
	MAXBASE:5 -= 50
	MAXBASE:7 -= 50
ENDIF
;無関心/好奇心
IF TALENT:MASTER:22
	MAXBASE:6 -= 50
	MAXBASE:8 -= 50
ELSEIF TALENT:23
	MAXBASE:6 += 50
	MAXBASE:8 += 50
ENDIF
;目立ちたがり
IF TALENT:29
	MAXBASE:7 -= 50
	MAXBASE:8 += 50
ENDIF
;抑圧/解放
IF TALENT:MASTER:32
	MAXBASE:6 -= 50
	MAXBASE:8 -= 50
ELSEIF TALENT:33
	MAXBASE:6 += 50
	MAXBASE:8 += 50
ENDIF
;自慰しやすい
SIF TALENT:60
	MAXBASE:5 -= 50
;倒錯的
SIF TALENT:80
	MAXBASE:5 -= 50
;男嫌い
SIF TALENT:82 && TALENT:MASTER:122
	MAXBASE:6 -= 50
;幼稚
IF TALENT:88
	MAXBASE:5 -= 50
	MAXBASE:6 += 25
	MAXBASE:7 -= 50
ENDIF
;狂気
IF TALENT:89
	MAXBASE:5 -= 200
	MAXBASE:7 -= 100
	MAXBASE:8 += 200
ENDIF
;禁断の知識
SIF TALENT:130
	MAXBASE:5 += 100

;極端な数値をギャップ
SIF MAXBASE:5 < 500
	MAXBASE:5 = 500
SIF MAXBASE:5 > 1500
	MAXBASE:5 = 1500
SIF MAXBASE:6 < 500
	MAXBASE:6 = 500
SIF MAXBASE:6 > 1500
	MAXBASE:6 = 1500
SIF MAXBASE:7 < 500
	MAXBASE:7 = 500
SIF MAXBASE:7 > 1500
	MAXBASE:7 = 1500
SIF MAXBASE:8 < 500
	MAXBASE:7 = 500
SIF MAXBASE:8 > 1500
	MAXBASE:7 = 1500
B = 0
REPEAT CFLAG:0 + 1
	B += 100 + COUNT * COUNT * 10
REND

CFLAG:4 = B

;ゲージを初期化する
BASE:0 = MAXBASE:0
BASE:1 = MAXBASE:1
BASE:2 = MAXBASE:2
BASE:3 = MAXBASE:3
BASE:4 = 0
BASE:5 = MAXBASE:5
BASE:6 = MAXBASE:6
BASE:7 = 0
BASE:8 = 0

;ついでに衣装の初期化を呼び出します
CALL CLOTHES_SETUP(TARGET)
