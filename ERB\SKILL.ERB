﻿;绝顶時スキル閃き
@SKILL_NOWEX
RETURN 0
IF !TALENT:201 && NOWEX:11 && (TEQUIP:71 || (TFLAG:80 == 1 && TFLAG:90 != 27)) && !GETBIT(FLAG:40, 1) && !RESULT
	LOCAL = 1
	SIF NO:TARGET == 1 || NO:TARGET == 25
		LOCAL = 10
	IF RAND:(MIN(1 + ((CFLAG:0 * CFLAG:MASTER:0 * LOCAL) / 10),500)) > RAND:2000
		TALENT:201 = 1
		CALL PIKON
		PRINTFORMW %CALLNAME:TARGET%闪过了[能量蛋白]！
		RETURN 1
	ENDIF
ENDIF
;追加行動スキルの閃き
@SKILL_ACTION
RETURN 0
SIF TALENT:200 || GETBIT(FLAG:40, 0)
	RETURN 0
LOCAL = (20 + CFLAG:2010) / 20
IF MIN(CFLAG:0 * CFLAG:MASTER:0 * (2 + TALENT:50 + TALENT:57) * (10 + CFLAG:2010) / 20 , 1000) > RAND:1000
	TALENT:200 = 1
	CALL PIKON
	PRINTFORMW %CALLNAME:TARGET%闪过了[追加爱抚]！
	RETURN 1
ENDIF
;SPアクションの閃き
@SKILL_SPACTION(ARG)
RETURN 0
SELECTCASE ARG
	;ディープスロート
	CASE 0
		IF ABL:2 * EXP:22 / 10 > RAND:1000
			CALL PIKON
			TALENT:202 = 1
			PRINTFORMW %CALLNAME:TARGET%闪过了[深喉]！
		ENDIF
ENDSELECT
;極意
@SKILL_SECRET(ARG)
RETURN 0
;ARG=0 エナジードレイン
;ARG=1 追加愛撫
;ARG=2 ディープスロート
;ARG=3 ラブトラップ
SELECTCASE ARG
	CASE 0
	 	SIF GETBIT(CFLAG:MASTER:319,2)
	 		RETURN 1
		IF !GETBIT(FLAG:42,2)
			IF CFLAG:MASTER:2900 + CFLAG:MASTER:2901 > 40 - 10 * (TALENT:MASTER:50 - TALENT:MASTER:51)
				SETBIT FLAG:42,2
				PRINTFORMW %CALLNAME:MASTER%看破了[能量蛋白]！
				RETURN 1
			ENDIF
		ENDIF
		;拠点じゃないとダメ
		SIF FLAG:1700 || FLAG:1701
			RETURN 0
		IF TFLAG:166 && TFLAG:166 != 1000 && !GETBIT(FLAG:40, 1)
			IF CFLAG:2900 + CFLAG:2901 > 20 - 5 * (TALENT:50 - TALENT:51)
				CALL PIKON
				SETBIT FLAG:40,1
				PRINTFORMW %CALLNAME:TARGET%学会了[能量蛋白]的极意
			ENDIF
		ENDIF
		RETURN 0
	CASE 1
		IF !GETBIT(FLAG:42, 1)
			IF CFLAG:MASTER:2902 > 60 - 10 * (TALENT:MASTER:50 - TALENT:MASTER:51)
				SETBIT FLAG:42,1
				CALL PIKON
				PRINTFORMW %CALLNAME:MASTER%看破了[追加爱抚]!
			ENDIF
		ENDIF
		SIF FLAG:1700 || FLAG:1701
			RETURN 0
		IF !GETBIT(FLAG:40, 0)
			IF CFLAG:2902 > 30 - 5 * (TALENT:50 - TALENT:51)
				CALL PIKON
				SETBIT FLAG:40,0
				PRINTFORMW %CALLNAME:TARGET%学会了[追加爱抚]的极意
			ENDIF
		ENDIF
	CASE 2
	 	SIF GETBIT(CFLAG:MASTER:319,6)
	 		RETURN 1
		IF !GETBIT(FLAG:42, 6)
			IF CFLAG:MASTER:2903 > 20 - 5 * (TALENT:MASTER:50 - TALENT:MASTER:51)
				CALL PIKON
				SETBIT FLAG:42,6
				PRINTFORMW %CALLNAME:MASTER%看破了[深喉]！
				RETURN 1
			ENDIF
		ENDIF
		;拠点じゃないとダメ
		SIF FLAG:1700 || FLAG:1701
			RETURN 0
		IF !GETBIT(FLAG:40, 2)
			IF CFLAG:2903 > 10 - 3 * (TALENT:50 - TALENT:51)
				CALL PIKON
				SETBIT FLAG:40,2
				PRINTFORMW %CALLNAME:TARGET%学会了[深喉]的极意
			ENDIF
		ENDIF
		RETURN 0
	;ラブトラップ
	CASE 3
	 	SIF GETBIT(CFLAG:MASTER:319,7)
	 		RETURN 1
		IF !GETBIT(FLAG:42, 7)
			IF CFLAG:MASTER:2904 > 20 - 5 * (TALENT:MASTER:50 - TALENT:MASTER:51)
				CALL PIKON
				SETBIT FLAG:42,7
				PRINTFORMW %CALLNAME:MASTER%看破了[恋爱陷阱]！
				RETURN 1
			ENDIF
		ENDIF
		RETURN 0
ENDSELECT

;状態異常への見切り
@SKILL_BADSTATUS(ARG)
RETURN 0
SIF GETBIT(CFLAG:MASTER:319,ARG + 3)
	RETURN 1
IF !GETBIT(FLAG:42, ARG + 3)
	IF (CFLAG:MASTER:0 + 10) * (CFLAG:0 + 10) / 10 > RAND:1500
		SETBIT FLAG:42,ARG + 3
		CALL PIKON
		PRINTFORMW %CALLNAME:MASTER%看破了[%STR:(ARG + 3 + 600)%]!
		RETURN 1
	ENDIF
ENDIF

@PIKON
RETURN 0
PRINTL ＼　　__　　／
PRINTL ＿　（ｍ）　＿
PRINTL  　　|ミ|
PRINTL ／ 　｀´ 　＼
