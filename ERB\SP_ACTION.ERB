﻿;特殊能力行動の試作案です。はっきり言えばかなり手抜きですが、このやり方は调教者思考ルーチンへの負担が一番少ないです
;────────────────────────────────────
;調教者の特殊能力のチェック。特定の調教者と行動の組み合わせによって特殊能力行動に変化します
;────────────────────────────────────
@SP_CHECK
IF NO:TARGET == 1
	;針＝＞パスウェイジョンニードル
	IF TFLAG:90 == 62 && CFLAG:0 > 0
		TFLAG:120 = 200
	;縄で緊縛＝＞八方鬼縛陣
	ELSEIF TFLAG:90 == 63 && ABL:2 > 2
		TFLAG:120 = 201
	ENDIF
ELSEIF NO:TARGET == 28
	;不気味な微笑み＝＞狂気の瞳
	IF TFLAG:90 == 9 && ABL:27 > 1
		TFLAG:120 = 202
	;このキャラクターの新しい特殊能力を作りたい時はこのように拡張してください
	;ELSEIF TFLAG:90 == XX && CFLAG:0 > X && ABL:X > X && ...
	;	TFLAG:120 = 2XX
	ENDIF
;新しいキャラクターの特殊能力を作りたい時はこちらから拡張してください
;ELSEIF NO:TARGET == XX
ENDIF


;────────────────────────────────────
;調教者の特殊能力行動の実行。（）の中にベースとなる調教指令からの変更点を説明
;────────────────────────────────────
@SP_ACTION
;────────────────────────────────────
;パスウェイジョンニードル（痛み補正、調教時間短縮）
;────────────────────────────────────
IF TFLAG:120 == 200
	SOURCE:13 = 320 * (TALENT:83 + 2) + (ABL:2 + 1) * (ABL:26 + 2) * (2 + TALENT:53 * 3) + (ABL:2 * 2 + CFLAG:0) * (TALENT:83 + TALENT:53 * 2 + 1) * (12 + TALENT:83)
	SIF TALENT:87
		TIMES SOURCE:13 , 0.80
	SOURCE:22 = 200 + SOURCE:13 / 2 - CFLAG:MASTER:0 * 30 - ABL:MASTER:11 * 60 - ABL:MASTER:15 * 200
	SIF SOURCE:22 < 0
		SOURCE:22 = 0
	SOURCE:23 = ABL:MASTER:11 * (ABL:MASTER:15 - 1) * (ABL:MASTER:15 + 1) * SOURCE:13 / 100
	SIF SOURCE:23 < 0
		SOURCE:23 = 0
	SOURCE:24 = SOURCE:13 / 2 - SOURCE:23
	SIF SOURCE:24 < 0
		SOURCE:24 = 0
	SIF CFLAG:MASTER:8 == 62
		TIMES SOURCE:24 , 1.50
	;出血
	IF TALENT:MASTER:122 || TALENT:MASTER:121
		STAIN:MASTER:2 |= 32
	ELSE
		STAIN:MASTER:3 |= 32
	ENDIF
	STAIN:MASTER:5 |= 32
	SIF TEQUIP:69
		TEQUIP:69 = 0
	SIF TEQUIP:70
		TEQUIP:70 = 0
	TFLAG:64 += 1
;────────────────────────────────────
;八方鬼縛陣（拘束補正、調教時間短縮）
;────────────────────────────────────
ELSEIF TFLAG:120 == 201
	IF TEQUIP:40 == 0
		SOURCE:14 = 120 + (MARK:MASTER:3 + CFLAG:0 * 2 + 1) * (ABL:2 * 2 + 1) * 5 * (TALENT:58 * 3 + 2)
		SOURCE:23 = ABL:MASTER:11 * (ABL:MASTER:16 - 1) * (ABL:MASTER:16 + 1) * SOURCE:14 / 100
		;装備番号は変わらないが、INFO.ERBとUSERCOM.ERBで表示するメッセージを設定できます
		TEQUIP:40 = 1 + SOURCE:14 / 500
	ELSE
		TEQUIP:40 = 0
	ENDIF
	SIF TEQUIP:69
		TEQUIP:69 = 0
	SIF TEQUIP:70
		TEQUIP:70 = 0
	TFLAG:64 += 1
;────────────────────────────────────
;狂気の瞳（ランダムパラメータ上昇、逸脱に補正）
;────────────────────────────────────
ELSEIF TFLAG:120 == 202
	SIF RAND:6 > 2
		TFLAG:102 = 6
	SIF RAND:6 > 2
		TFLAG:103 = 6
	A = RAND:14
	B = RAND:14
	SIF B == A
		B += 1 + RAND:3
	SIF B > 13
		B -= 13
	PALAM:A += 500 + RAND:500
	PALAM:B += 300 + RAND:300
	SOURCE:22 = (3 + RAND:5) * (CFLAG:0 + 3) * 50
	SOURCE:24 = 250 + RAND:5 * 100
	SIF CFLAG:MASTER:8 == 9
		TIMES SOURCE:24 , 1.50
	SIF TEQUIP:69 == 2
		TEQUIP:69 = 0
	SIF TEQUIP:69 == 3
		TEQUIP:69 = 1
	SIF TEQUIP:70
		TEQUIP:70 = 0
	TFLAG:64 += 1
	TFLAG:100 |= 32
ENDIF


;────────────────────────────────────
;助手の特殊能力のチェック。特定の助手と行動の組み合わせによって特殊能力行動に変化します
;────────────────────────────────────
@SP_CHECK_A
IF NO:ASSI == 32
	;写真を撮る＝＞新聞の記事にする
	IF TFLAG:110 == 11
		TFLAG:113 = 100
	ENDIF
;新しいキャラクターの特殊能力を作りたい時はこちらから拡張してください。番号は100からでお願いします
;ELSEIF NO:ASSI == XX
	;ＸＸＸ＝＞ＸＸＸＸＸ
	;IF TFLAG:110 == XX
	;	TFLAG:113 = 1XX
	;ENDIF
ENDIF


;────────────────────────────────────
;助手の特殊能力行動の実行
;────────────────────────────────────
@SP_ACTION_A
;────────────────────────────────────
;新聞の記事にする（写真の価値を高める、露出に補正）
;────────────────────────────────────
IF TFLAG:113 == 100
	SOURCE:20 += 25 * (2 + ABL:ASSI:2 * 2 + ABL:ASSI:27) * (2 + ITEM:9)
	SOURCE:22 += 20 * SOURCE:20 / (3 + ABL:MASTER:0 + ABL:MASTER:1 + ABL:MASTER:8 * 3 + ABL:MASTER:11 * 2 + CFLAG:MASTER:0 + EXP:MASTER:13 + EXP:MASTER:50 / 2)
	SOURCE:24 += (2 + TALENT:MASTER:34 - TALENT:MASTER:35) * (SOURCE:22 + 1000) / (1000 + MARK:MASTER:3 * 100 - MARK:MASTER:4 * 50 + EXP:MASTER:50 * 10 + ABL:MASTER:8 * 250 + EXP:MASTER:13 * 100)
	SOURCE:23 += SOURCE:20 * (CFLAG:MASTER:0 + ABL:MASTER:8 + 1) * (ABL:MASTER:8 * 2 + 5) / (1000 + SOURCE:24)
	IF TEQUIP:69 == 1 || TEQUIP:69 == 3
		SOURCE:20 += 200 + SOURCE:20 / 5
		TIMES SOURCE:22 , 1.35
		TIMES SOURCE:23 , 1.50
		TIMES SOURCE:31 , 1.50
	ENDIF
	PRINTFORML %CALLNAME:ASSI%用照相机拍下了%CALLNAME:MASTER%的样子，
	PRINTFORML 以此为素材开始写报告……
	;今回の調教で撮影した回数をカウント
	TFLAG:51 += 1
;────────────────────────────────────
;新しい特殊能力行動を追加する場合ここから拡張して行動の内容を記入してください
;────────────────────────────────────
;ELSEIF TFLAG:113 == 1XX
ENDIF

@SP_TRAIN_MESSAGE
;八方鬼縛陣
IF TFLAG:120 == 201
	;解除
	IF TFLAG:93 == 1
		PRINTFORML 由于%CALLNAME:TARGET%不可思议的力量，
		PRINTFORML 拘束着%CALLNAME:MASTER%御札一瞬间回到了%CALLNAME:TARGET%的手上...
	;通常
	ELSE
		PRINTFORMSL STR:M
		PRINTFORMSL STR:N
	ENDIF
ELSEIF TFLAG:120 == 202
	;PRINTL ＜シックスナイン＞
	IF TSTR:2 == "シックスナイン"
		PRINTFORML %CALLNAME:TARGET%跨坐上%CALLNAME:MASTER%的脸
		PRINTFORML 含弄着阴茎不肯离开…
	ELSE
		PRINTFORML %CALLNAME:TARGET%推倒%CALLNAME:MASTER%后跨坐上%CALLNAME:MASTER%的脸，
		PRINTFORML 在%CALLNAME:MASTER%毫无防备的股间开始用舌头舔弄了起来……
	ENDIF
ENDIF

