﻿;≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
;口上向け関数ファイル２
;ぱんくしょんRevではない何か…誰かさんが好き勝手やるファイルです
;※Emuera専用のためサブフォルダに配置すること※
;≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡

;-------------------------------------------------
;関数名:ASSIID
;概　要:助手ID取得関数
;引　数:なし
;戻り値:ASSIとして扱われている助手のID(1～3)
;備  考:式中関数
;ASSIに入っているキャラ(＝口上主)が助手１か助手２か助手３かを判別する
;助手口上で使うかも
;-------------------------------------------------
@ASSIID
#FUNCTION
SIF ASSI < 0
	RETURNF 0
FOR LOCAL, 1, 4
	SIF ASSI == ASSI:LOCAL
		RETURNF LOCAL
NEXT


;-------------------------------------------------
;関数名:PLEASURE_CHECK
;概　要:快感チェック関数
;引　数:ARG…真ならMASTERの技量
;            偽ならTARGETの技量(省略時はTARGET)
;戻り値:真なら愛撫で全然感じなかったことを表す
;備  考:式中関数
;愛撫の巧拙を判定する関数のつもり
;COM12を参考にしているが、独自の改造を施したので
;何かおかしいかもしれない。参考程度に…
;-------------------------------------------------
@PLEASURE_CHECK(ARG)
#FUNCTION
;MASTERがTARGETに与える快感をチェック
IF ARG
	;TARGETの抵抗度合い
	LOCAL:0 = 20 + TALENT:15 * 5 + TALENT:12 * 5 - (CFLAG:9 / 200)
	;TARGETの受けた快感
	LOCAL:1 = SOURCE:40 * (1 + TALENT:100) + SOURCE:41 * (1 + TALENT:102) + SOURCE:42 * (1 + TALENT:104) + SOURCE:43 * (1 + TALENT:106)
	LOCAL:0 -= LOCAL:1 / (500 - TALENT:70 * 100 + TALENT:71 * 50)
;TARGETがMASTERに与える快感をチェック
ELSE
	;MASTERの抵抗度合い
	LOCAL:0 = 20 + TALENT:MASTER:15 * 5 + TALENT:MASTER:12 * 5 + MARK:3 + PALAM:9 / 500 - GET_ABL(MASTER, 0) * 3 / 10 - CFLAG:MASTER:0
	;MASTERの受けた快感
	LOCAL:1 = SOURCE:0 * (1 + TALENT:MASTER:100) + SOURCE:1 * (1 + TALENT:MASTER:102) + SOURCE:2 * (1 + TALENT:MASTER:104) + SOURCE:3 * (1 + TALENT:MASTER:106)
	LOCAL:0 -= LOCAL:1 / (500 - TALENT:MASTER:70 * 100 + TALENT:MASTER:71 * 50)
ENDIF
RETURNF LOCAL:0 > 0


;-------------------------------------------------
;関数名:BIND_CHECK
;概　要:拘束チェック関数
;引　数:ARG…部位(0.四肢/1.目/2.口/??.鼻/??.耳/??.手/??.足)
;戻り値:該当部位が拘束されて使用不可になっているかどうか
;備  考:式中関数
;拘束をチェックする関数。ちょっと試作品
;拘束手段を後から追加する場合に、その都度口上側を更新するのが嫌なので作成
;テンプレで使うかどうかはしばらく様子見
;-------------------------------------------------
@BIND_CHECK(ARG)
#FUNCTION
SELECTCASE ARG
	;四肢
	CASE 0
		;縄/蔦。ラミアは胴体しか拘束していない…はず、なので別扱い
		RETURNF TEQUIP:40 || TEQUIP:46
	;視覚
	CASE 1
		;アイマスク
		RETURNF TEQUIP:41
	;口
	CASE 2
		;ボールギャグ
		RETURNF TEQUIP:42
ENDSELECT
RETURNF 0


;-------------------------------------------------
;関数名:LINT64
;概　要:64ビット整数配列関数(LOCALを利用)
;引　数:ARG:0…配列番号を指定(0～999)
;       ARG:1…偽の場合は値を変更せずに状態を返す
;              真の場合は値を変更してから状態を返す
;       ARG:2…代入する値(ARG:1が真の場合のみ有効)
;戻り値:指定配列の値
;備  考:式中関数
;       試験的に、EVENT_KおよびKOJO_FUNCTIONでのみ利用可
;LOCALを配列に見立てて、一時的な変数置き場に利用
;当面は関数間の配列受け渡しとかで使うかもしれない
;実験的要素が強い。やはりCALLFが(以下略
;LUINT32とかLINT8とかの需要はあるのだろうか
;-------------------------------------------------
@LINT64(ARG:0, ARG:1, ARG:2)
#FUNCTION
SIF ARG:1
	LOCAL:(ARG:0) = ARG:2
RETURNF LOCAL:(ARG:0)


;-------------------------------------------------
;関数名:LBIT
;概　要:ビット列関数(LOCALを利用)
;引　数:ARG:0…ビット番号を指定(0～63999)
;       ARG:1…偽の場合はビット状態を変更せずに状態を返す
;              真の場合はビット状態を変更してから状態を返す
;戻り値:指定ビットの状態(0.OFF/1.ON)
;備  考:式中関数
;       試験的に、EVENT_KおよびKOJO_FUNCTIONでのみ利用可
;LOCAL配列をbit列に見立てて、一時的なスイッチに利用
;風当たりが強くなってきたLOCAL@を排除する計画の一環
;ただ、CALLFが使えないと微妙かも
;-------------------------------------------------
@LBIT(ARG:0, ARG:1)
#FUNCTION
SIF ARG:1
	SETBIT LOCAL:(ARG:0 >> 6), ARG:0 & 63
RETURNF GETBIT(LOCAL:(ARG:0 >> 6), ARG:0 & 63)


;-------------------------------------------------
;関数名:MC_PLAYER
;概　要:Ｃ愛撫者/愛撫物判定関数
;引　数:ARG…真なら愛撫している物が何であるかを返す
;            偽なら愛撫している人が誰なのかを返す。複数存在する場合は調教者優先
;戻り値:物の場合→0.なし/1.口/2.手/3.ペニス/4.ヴァギナ/5.アナル/6.胸/7.足
;                 /90.触手/91.スキュラ触手
;                 /100.ローター/112.クリキャップ/114.オナホール
;       人の場合→0.なし/1.調教対象自身/2.調教者/4.助手1/8.助手2/16.助手３
;                 /64.道具/128.便所利用者
;備  考:式中関数
;仕様を整理して作り直した、新しい愛撫者/愛撫物判定関数
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@MC_PLAYER(ARG)
#FUNCTION
;TEQUIPによる判定1(優先度高)
;アナルセックスさせる
SIF TEQUIP:71 == 6
	RETURNF ARG ? 5 # 2
;性交奉仕中(アナルセックス以外)
SIF TEQUIP:71
	RETURNF ARG ? 4 # 2
;クリキャップ
SIF TEQUIP:30
	RETURNF ARG ? 112 # 64
;オナホール
SIF TEQUIP:31
	RETURNF ARG ? 114 # 64

;調教者アクションによる判定1(優先度高)
SELECTCASE TFLAG:90
	;手で愛撫/対面座位/背面座位/秘貝開帳
	CASE 10, 33, 34, 41
		RETURNF ARG ? 2 # 2
	;口で愛撫
	CASE 12
		RETURNF ARG ? 1 # 2
	;性器を擦り合う
	CASE 16
		RETURNF ARG ? 4 # 2
	;パイズリする
	CASE 17
		RETURNF ARG ? 6 # 2
	;足コキする
	CASE 18
		RETURNF ARG ? 7 # 2
	;ローター
	CASE 20
		RETURNF ARG ? 100 # 2
	;素股
	CASE 53
		;命令に従った場合のみ
		SIF TFLAG:94 != 5
			RETURNF ARG ? 3 # 2
ENDSELECT

;調教者追加アクションによる判定
;アルケニー愛撫Ｃ
SIF TFLAG:230 == 1
	RETURNF ARG ? 2 # 2
;スキュラ触手Ｃ
SIF TEQUIP:91
	RETURNF ARG ? 91 # 2
;継続手淫
SIF TEQUIP:100 == 1
	RETURNF ARG ? 2 # 2
;継続フェラ
SIF TEQUIP:100 == 2
	RETURNF ARG ? 1 # 2
;助手アクションによる判定
FOR LOCAL, 1, 4
	;助手がいない場合は判定しない
	SIF ASSI:LOCAL < 0
		CONTINUE
	SELECTCASE CFLAG:(ASSI:LOCAL):190
		;口で愛撫
		CASE 1
			RETURNF ARG ? 1 # (2 << LOCAL)
		;性器を擦り合う
		CASE 2
			RETURNF ARG ? 4 # (2 << LOCAL)
		;パイズリする
		CASE 3
			RETURNF ARG ? 6 # (2 << LOCAL)
		;足コキする
		CASE 4
			RETURNF ARG ? 7 # (2 << LOCAL)
		;道具で愛撫
		CASE 5
			;愛撫者判定
			SIF !ARG
				RETURNF (2 << LOCAL)
			;愛撫物判定
			RETURNF PENIS(MASTER) ? 114 # 100
		;逆レイプ
		CASE 6
			RETURNF ARG ? 3 # (2 << LOCAL)
		;手で愛撫
		CASE 7
			RETURNF ARG ? 2 # (2 << LOCAL)
	ENDSELECT
NEXT

;調教者アクションによる判定2(優先度低)
SELECTCASE TFLAG:90
	;Ｃ愛撫道具
	CASE 24
		;愛撫者判定
		SIF !ARG
			RETURNF 2
		;愛撫物判定
		RETURNF PENIS(MASTER) ? 114 # 112
	;公衆肉便器プレイ
	CASE 74
		;便器として使用された場合
		SIF TFLAG:93 > 1
			RETURNF ARG ? 4 # 128
	;性交奉仕(アナルセックス以外)(抵抗された)
	CASE 36, 95 TO 98
		;快Ｃを得ている
		SIF SOURCE:0 > 0
			RETURNF ARG ? 4 # 2
	;アナルセックスさせる(抵抗された)
	CASE 99
		;快Ｃを得ている
		SIF SOURCE:0 > 0
			RETURNF ARG ? 5 # 2
ENDSELECT

;TEQUIPによる判定2(優先度低)
;調教対象自慰中(快Ｃを得ている)
SIF (TEQUIP:69 & 1) && SOURCE:0 > 0
	RETURNF ARG ? 2 # 1


;-------------------------------------------------
;関数名:MV_PLAYER
;概　要:Ｖ愛撫者/愛撫物判定関数
;引　数:ARG…真なら愛撫している物が何であるかを返す
;            偽なら愛撫している人が誰なのかを返す。複数存在する場合は調教者優先
;戻り値:物の場合→0.なし/1.口/2.手/3.ペニス/4.ヴァギナ/5.アナル/6.胸/7.足
;                 /10.腕/90.触手/91.スキュラ触手
;                 /100.ローター/101.バイブ/103.ペニスバンド/133.三角木馬
;       人の場合→0.なし/1.調教対象自身/2.調教者/4.助手1/8.助手2/16.助手３
;                 /64.道具/128.便所利用者
;備  考:式中関数
;仕様を整理して作り直した、新しい愛撫者/愛撫物判定関数
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@MV_PLAYER(ARG)
#FUNCTION
;オトコは除く
SIF TALENT:MASTER:122
	RETURNF 0

;TEQUIPによる判定1(優先度高)
;性交中(アナルセックス除く)
IF TEQUIP:70 && TEQUIP:70 != 6
	;愛撫者判定
	SIF !ARG
		RETURNF 2
	;愛撫物判定
	RETURNF PENIS(TARGET) ? 3 # 103
ENDIF
;バイブ
SIF TEQUIP:20
	RETURNF ARG ? 101 # 64
;三角木馬
SIF TEQUIP:43
	RETURNF ARG ? 133 # 64

;調教者アクションによる判定1(優先度高)
;フィストファック/両穴フィスト
SIF TFLAG:90 == 70 || TFLAG:90 == 72
	RETURNF ARG ? 10 # 2

;調教者追加アクションによる判定
;アルケニー愛撫Ｖ
SIF TFLAG:230 == 2
	RETURNF ARG ? 2 # 2
;スキュラ触手Ｖ
SIF TEQUIP:94
	RETURNF ARG ? 91 # 2

;助手アクションによる判定
FOR LOCAL, 1, 4
	;助手がいない場合は判定しない
	SIF ASSI:LOCAL < 0
		CONTINUE
	SELECTCASE CFLAG:(ASSI:LOCAL):190
		;指挿入
		CASE 30
			RETURNF ARG ? 2 # (2 << LOCAL)
		;バイブ
		CASE 31
			RETURNF ARG ? 101 # (2 << LOCAL)
		;性交
		CASE 30
			;愛撫者判定
			SIF !ARG
				RETURNF (2 << LOCAL)
			;愛撫物判定
			RETURNF PENIS(ASSI:LOCAL) ? 3 # 103
	ENDSELECT
NEXT

;調教者アクションによる判定2(優先度低)
SELECTCASE TFLAG:90
	;バイブ
	CASE 21
		RETURNF ARG ? 101 # 2
	;性交(アナルセックス以外)(抵抗された)
	CASE 30 TO 34
		;愛撫者判定
		SIF !ARG
			RETURNF 2
		;愛撫物判定
		RETURNF PENIS(TARGET) ? 3 # 103
	;三角木馬
	CASE 67
		RETURNF ARG ? 133 # 2
	;公衆肉便器プレイ
	CASE 74
		;便器として使用された場合
		SIF TFLAG:93 > 1
			RETURNF ARG ? 3 # 128
ENDSELECT

;TEQUIPによる判定2(優先度低)
;調教対象自慰中(快Ｖを得ている)
SIF (TEQUIP:69 & 1) && SOURCE:1 > 0
	RETURNF ARG ? 101 # 1


;-------------------------------------------------
;関数名:MA_PLAYER
;概　要:Ａ愛撫者/愛撫物判定関数
;引　数:ARG…真なら愛撫している物が何であるかを返す
;            偽なら愛撫している人が誰なのかを返す。複数存在する場合は調教者優先
;戻り値:物の場合→0.なし/1.口/2.手/3.ペニス/4.ヴァギナ/5.アナル/6.胸/7.足
;                 /10.腕/90.触手/91.スキュラ触手
;                 /100.ローター/102.アナルビーズ/103.ペニスバンド/111.アナルバイブ/132.浣腸器＋プラグ
;       人の場合→0.なし/1.調教対象自身/2.調教者/4.助手1/8.助手2/16.助手３
;                 /64.道具/128.便所利用者
;備  考:式中関数
;仕様を整理して作り直した、新しい愛撫者/愛撫物判定関数
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@MA_PLAYER(ARG)
#FUNCTION
;TEQUIPによる判定1(優先度高)
;アナルセックス中
IF TEQUIP:70 == 6
	;愛撫者判定
	SIF !ARG
		RETURNF 2
	;愛撫物判定
	RETURNF PENIS(TARGET) ? 3 # 103
ENDIF
;アナルバイブ
SIF TEQUIP:25
	RETURNF ARG ? 111 # 64
;アナルビーズ
SIF TEQUIP:26
	RETURNF ARG ? 102 # 64
;浣腸器＋プラグ
SIF TEQUIP:27
	RETURNF ARG ? 132 # 64

;調教者アクションによる判定1(優先度高)
SELECTCASE TFLAG:90
	;アナル愛撫
	CASE 13
		RETURNF ARG ? 2 # 2
	;アナル舐め
	CASE 14
		RETURNF ARG ? 1 # 2
	;アナルビーズ
	CASE 23
		;ここでは解除時の処理のみ
		SIF TFLAG:93 == 1
			RETURNF ARG ? 102 # 2
	;浣腸器＋プラグ
	CASE 68
		;ここでは解除時の処理のみ
		SIF TFLAG:93 == 1
			RETURNF ARG ? 132 # 2
	;アナルフィスト/両穴フィスト
	CASE 71, 72
		RETURNF ARG ? 10 # 2
ENDSELECT

;調教者追加アクションによる判定
;アルケニー愛撫Ａ
SIF TFLAG:230 == 3
	RETURNF ARG ? 2 # 2
;スキュラ触手Ａ
SIF TEQUIP:92
	RETURNF ARG ? 91 # 2

;助手アクションによる判定
FOR LOCAL, 1, 4
	;助手がいない場合は判定しない
	SIF ASSI:LOCAL < 0
		CONTINUE
	SELECTCASE CFLAG:(ASSI:LOCAL):190
		;アナル愛撫
		CASE 10
			RETURNF ARG ? 2 # (2 << LOCAL)
		;アナル舐め
		CASE 11
			RETURNF ARG ? 1 # (2 << LOCAL)
		;Ａローター
		CASE 12
			RETURNF ARG ? 100 # (2 << LOCAL)
		;アナルバイブ
		CASE 13
			RETURNF ARG ? 111 # (2 << LOCAL)
	ENDSELECT
NEXT

;調教者アクションによる判定2(優先度低)
SELECTCASE TFLAG:90
	;アナルバイブ
	CASE 22
		RETURNF ARG ? 111 # 2
	;アナルビーズ
	CASE 23
		RETURNF ARG ? 102 # 2
	;アナルセックス(抵抗された)
	CASE 35
		;愛撫者判定
		SIF !ARG
			RETURNF 2
		;愛撫物判定
		RETURNF PENIS(TARGET) ? 3 # 103
	;浣腸器＋プラグ
	CASE 68
		RETURNF ARG ? 132 # 2
	;公衆肉便器プレイ
	CASE 74
		;便器として使用された場合
		SIF TFLAG:93 > 1
			RETURNF ARG ? 3 # 128
ENDSELECT

;TEQUIPによる判定2(優先度低)
;調教対象自慰中(快Ａを得ている)
SIF (TEQUIP:69 & 1) && SOURCE:2 > 0
	RETURNF ARG ? 111 # 1


;-------------------------------------------------
;関数名:MB_PLAYER
;概　要:Ｂ愛撫者/愛撫物判定関数
;引　数:ARG…真なら愛撫している物が何であるかを返す
;            偽なら愛撫している人が誰なのかを返す。複数存在する場合は調教者優先
;戻り値:物の場合→0.なし/1.口/2.手/3.ペニス/4.ヴァギナ/5.アナル/6.胸/7.足
;                 /90.触手/91.スキュラ触手
;                 /100.ローター/113.ニプルキャップ/141.搾乳器
;       人の場合→0.なし/1.調教対象自身/2.調教者/4.助手1/8.助手2/16.助手３
;                 /64.道具/128.便所利用者
;備  考:式中関数
;仕様を整理して作り直した、新しい愛撫者/愛撫物判定関数
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@MB_PLAYER(ARG)
#FUNCTION
;TEQUIPによる判定1(優先度高)
;ニプルキャップ
SIF TEQUIP:35
	RETURNF ARG ? 113 # 64
;搾乳器
SIF TEQUIP:36
	RETURNF ARG ? 141 # 64

;調教者アクションによる判定1(優先度高)
SELECTCASE TFLAG:90
	;手で愛撫
	CASE 10
		;愛撫
		SIF !TFLAG:93
			RETURNF ARG ? 2 # 2
		RETURNF 
	;胸愛撫
	CASE 11
		;愛撫者判定
		SIF !ARG
			RETURNF 2
		;愛撫物判定(乳首吸いの場合は口)
		RETURNF TFLAG:93 == 1 ? 1 # 2
	;背面座位
	CASE 34
		RETURNF ARG ? 2 # 2
	;パイズリ
	CASE 52
		;命令に従った場合のみ
		SIF TFLAG:94 != 5
			RETURNF ARG ? 3 # 2
ENDSELECT

;調教者追加アクションによる判定
;アルケニー愛撫Ｂ
SIF TFLAG:230 == 4
	RETURNF ARG ? 2 # 2
;スキュラ触手Ｂ
SIF TEQUIP:93
	RETURNF ARG ? 91 # 2

;助手アクションによる判定
FOR LOCAL, 1, 4
	;助手がいない場合は判定しない
	SIF ASSI:LOCAL < 0
		CONTINUE
	SELECTCASE CFLAG:(ASSI:LOCAL):190
		;胸愛撫
		CASE 20
			RETURNF ARG ? 2 # (2 << LOCAL)
		;乳首舐め
		CASE 21
			RETURNF ARG ? 1 # (2 << LOCAL)
		;乳首ローター
		CASE 22
			RETURNF ARG ? 100 # (2 << LOCAL)
	ENDSELECT
NEXT

;調教者アクションによる判定2(優先度低)
SELECTCASE TFLAG:90
	;ニプルキャップ
	CASE 25
		RETURNF ARG ? 113 # 2
	;搾乳器
	CASE 26
		RETURNF ARG ? 141 # 2
	;公衆肉便器プレイ
	CASE 74
		;便器として使用された(オトコは除く)
		SIF TFLAG:93 > 1 &&  !TALENT:MASTER:122
			RETURNF ARG ? 3 # 128
ENDSELECT

;TEQUIPによる判定2(優先度低)
;調教対象自慰中(オトコ以外、快Ｂを得ている)
SIF (TEQUIP:69 & 1) && !TALENT:MASTER:122 && SOURCE:3 > 0
	RETURNF ARG ? 2 # 1


;-------------------------------------------------
;関数名:MASTER_EX
;概　要:調教対象絶頂判定関数
;引　数:ARG:0…愛撫者→1.調教対象自身/2.調教者/4.助手１/8.助手２/16.助手３
;                      /64.道具/128.便所利用者
;              ビット和での指定が可能
;       ARG:1…(省略可)絶頂の種別→1.Ｃ絶頂/2.Ｖ絶頂/3.Ａ絶頂/4.Ｂ絶頂
;                                  /11.噴乳/12.射精/13.放尿
;戻り値:今回のコマンドで、指定した愛撫者が調教者を指定の絶頂に導いたかどうか
;       ARG:1を省略した場合は全ての絶頂についてビットで返す
;       (1.Ｃ絶頂/2.Ｖ絶頂/4.Ａ絶頂/8.Ｂ絶頂/1024.噴乳/2048.射精/4096.放尿)
;備  考:式中関数
;仕様を整理して作り直した、新しい絶頂判定関数
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@MASTER_EX(ARG:0, ARG:1)
#FUNCTION
LOCAL:0 = 0
LOCAL:1 = MC_PLAYER(0)
LOCAL:2 = MV_PLAYER(0)
LOCAL:3 = MA_PLAYER(0)
LOCAL:4 = MB_PLAYER(0)

LOCAL:0 |= NOWEX:0 && (LOCAL:1 & ARG:0) ? 1 # 0
LOCAL:0 |= NOWEX:1 && (LOCAL:2 & ARG:0) ? 2 # 0
LOCAL:0 |= NOWEX:2 && (LOCAL:3 & ARG:0) ? 4 # 0
LOCAL:0 |= NOWEX:3 && (LOCAL:4 & ARG:0) ? 8 # 0

;噴乳するにはＢ絶頂→Ｂ愛撫者
LOCAL:0 |= NOWEX:10 && (LOCAL:4 & ARG:0) ? 1024 # 0

;射精するには
;・射精/大量射精/空射精	：Ｃ絶頂→Ｃ愛撫者
;・早漏					：快Ｃ　→Ｃ愛撫者
;・Ａ責射精				：Ａ絶頂→Ａ愛撫者
;※Ａ責射精でない射精はＣ愛撫者、Ａ責射精はＡ愛撫者
LOCAL:0 |= (NOWEX:11 && NOWEX:11 != 5 && (LOCAL:1 & ARG:0)) || (NOWEX:11 == 5 && (LOCAL:3 & ARG:0)) ? 2048 # 0

;放尿するにはＣ絶頂or射精(空以外)or快Ｃが必要
;(注意：媚薬や栄養剤でもゲージ増加するが、快ＣＶＡＢのいずれかが無いと失禁判定自体されない)
;・Ｃ絶頂		：Ｃ愛撫者
;・射精(空以外)	：射精させた者
;・快Ｃ			：Ｃ愛撫者
;※Ａ責射精でＡ愛撫者の方が優先ならＡ愛撫者、それ以外はＣ愛撫者。優先順位は愛撫者の数字の低い順
;※Ｃ愛撫者を見るべきときにＣ愛撫者がいない場合：媚薬や栄養剤のせいなので、ＶＡＢを優先順位順で見る
IF NOWEX:11 == 5 && (!LOCAL:1 || LOCAL:1 > LOCAL:3)
	LOCAL:0 |= NOWEX:12 && (LOCAL:3 & ARG:0) ? 4096 # 0
ELSEIF LOCAL:1
	LOCAL:0 |= NOWEX:12 && (LOCAL:1 & ARG:0) ? 4096 # 0
ELSEIF LOCAL:2 && (!LOCAL:3 || LOCAL:2 < LOCAL:3) && (!LOCAL:4 || LOCAL:2 < LOCAL:4)
	LOCAL:0 |= NOWEX:12 && (LOCAL:2 & ARG:0) ? 4096 # 0
ELSEIF LOCAL:3 && (!LOCAL:4 || LOCAL:3 < LOCAL:4)
	LOCAL:0 |= NOWEX:12 && (LOCAL:3 & ARG:0) ? 4096 # 0
ELSE
	LOCAL:0 |= NOWEX:12 && (LOCAL:4 & ARG:0) ? 4096 # 0
ENDIF

RETURNF ARG:1 ? GETBIT(LOCAL:0, ARG:1 - 1) # LOCAL:0


;-------------------------------------------------
;関数名:TC_PLAYER
;概　要:ｃ愛撫者/愛撫物判定関数
;引　数:ARG…真なら愛撫している物が何であるかを返す
;            偽なら愛撫している人が誰なのかを返す。複数存在する場合は調教対象優先
;戻り値:物の場合→0.なし/1.口/2.手/3.ペニス/4.ヴァギナ/5.アナル/6.胸/7.足
;       人の場合→0.なし/1.調教対象/2.調教者自身/4.助手1/8.助手2/16.助手３
;備  考:式中関数
;仕様を整理して作り直した、新しい愛撫者/愛撫物判定関数
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@TC_PLAYER(ARG)
#FUNCTION
;TEQUIPによる判定1(優先度高)
;アナルセックス(調教者にペニスがある場合のみ)
SIF PENIS(TARGET) && TEQUIP:70 == 6
	RETURNF ARG ? 5 # 1
;性交中(アナルセックス以外)(調教者にペニスがある場合のみ)
SIF PENIS(TARGET) && TEQUIP:70
	RETURNF ARG ? 4 # 1
;顔面騎乗
SIF TEQUIP:44
	RETURNF ARG ? 1 # 1

;調教者アクションによる判定1(優先度高)
SELECTCASE TFLAG:90
	;性器を擦り合う
	CASE 16
		;愛撫者判定
		SIF !ARG
			RETURNF 1
		;愛撫物判定
		RETURNF PENIS(MASTER) ? 3 # 4
	;手で愛撫を強制
	CASE 50
		;奉仕した場合のみ
		SIF TFLAG:94 != 5
			RETURNF ARG ? 2 # 1
	;口で愛撫を強制
	CASE 51
		;顔面騎乗/顔面騎乗アナル以外、かつ奉仕した場合のみ
		SIF TFLAG:93 != 2 && TFLAG:93 != 3 && TFLAG:94 != 5
			RETURNF ARG ? 1 # 1
	;パイズリ
	CASE 52
		;奉仕した場合のみ
		SIF TFLAG:94 != 5
			RETURNF ARG ? 6 # 1
	;素股
	CASE 53
		;奉仕した場合のみ
		SIF TFLAG:94 != 5
			RETURNF ARG ? 4 # 1
	;足コキ
	CASE 54
		;奉仕した場合のみ
		SIF TFLAG:94 != 5
			RETURNF ARG ? 7 # 1
	;イラマチオ
	CASE 56
		RETURNF ARG ? 1 # 1
	;対面座位させる/背面座位させる
	CASE 97, 98
		RETURNF ARG ? 2 # 1
ENDSELECT

;調教者追加アクションによる判定
;助手アクションによる判定

;調教者アクションによる判定2(優先度低)
SELECTCASE TFLAG:90
	;性交(アナルセックス以外)(抵抗された)
	CASE 30 TO 34
		RETURNF ARG ? 4 # 1
	;アナルセックス(抵抗された)
	CASE 35
		RETURNF ARG ? 5 # 1
	;口で愛撫を強制
	CASE 51
		;顔面騎乗(抵抗された)
		SIF TFLAG:93 == 2
			RETURNF ARG ? 1 # 1
ENDSELECT

;TEQUIPによる判定2(優先度低)
;調教対象自慰中(快ｃを得ている)
SIF (TEQUIP:69 & 2) && SOURCE:40 > 0
	RETURNF ARG ? 2 # 2


;-------------------------------------------------
;関数名:TV_PLAYER
;概　要:ｖ愛撫者/愛撫物判定関数
;引　数:ARG…真なら愛撫している物が何であるかを返す
;            偽なら愛撫している人が誰なのかを返す。複数存在する場合は調教対象優先
;戻り値:物の場合→0.なし/1.口/2.手/3.ペニス/4.ヴァギナ/5.アナル/6.胸/7.足
;                 /101.バイブ
;       人の場合→0.なし/1.調教対象/2.調教者自身/4.助手1/8.助手2/16.助手３
;備  考:式中関数
;仕様を整理して作り直した、新しい愛撫者/愛撫物判定関数
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@TV_PLAYER(ARG)
#FUNCTION
;オトコは除く
SIF TALENT:TARGET:122
	RETURNF 0

;TEQUIPによる判定1(優先度高)
;性交奉仕中(アナルセックスさせる以外)
SIF TEQUIP:71 && TEQUIP:71 != 6
	RETURNF ARG ? 3 # 1

;調教者アクションによる判定1(優先度高)
;調教者追加アクションによる判定
;助手アクションによる判定

;調教者アクションによる判定2(優先度低)
SELECTCASE TFLAG:90
	;性交奉仕(アナルセックスさせる以外)(抵抗された)
	CASE 36, 95 TO 98
		RETURNF ARG ? 3 # 1
ENDSELECT

;TEQUIPによる判定2(優先度低)
;調教対象自慰中(快ｖを得ている)
SIF (TEQUIP:69 & 2) && SOURCE:41 > 0
	RETURNF ARG ? 101 # 2


;-------------------------------------------------
;関数名:TA_PLAYER
;概　要:ａ愛撫者/愛撫物判定関数
;引　数:ARG…真なら愛撫している物が何であるかを返す
;            偽なら愛撫している人が誰なのかを返す。複数存在する場合は調教対象優先
;戻り値:物の場合→0.なし/1.口/2.手/3.ペニス/4.ヴァギナ/5.アナル/6.胸/7.足
;       人の場合→0.なし/1.調教対象/2.調教者自身/4.助手1/8.助手2/16.助手３
;備  考:式中関数
;仕様を整理して作り直した、新しい愛撫者/愛撫物判定関数
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@TA_PLAYER(ARG)
#FUNCTION
;TEQUIPによる判定1(優先度高)
;顔面騎乗アナル
SIF TEQUIP:45
	RETURNF ARG ? 1 # 1
;アナルセックスさせる
SIF TEQUIP:71 == 6
	RETURNF ARG ? 3 # 1

;調教者アクションによる判定1(優先度高)
;調教者追加アクションによる判定
;助手アクションによる判定

;調教者アクションによる判定2(優先度低)
SELECTCASE TFLAG:90
	;口で愛撫を強制
	CASE 51
		;顔面騎乗アナル(抵抗された)
		SIF TFLAG:93 == 3
			RETURNF ARG ? 1 # 1
	;アナルセックスさせる(抵抗された)
	CASE 99
		RETURNF ARG ? 3 # 1
ENDSELECT

;TEQUIPによる判定2(優先度低)


;-------------------------------------------------
;関数名:TB_PLAYER
;概　要:ｂ愛撫者/愛撫物判定関数
;引　数:ARG…真なら愛撫している物が何であるかを返す
;            偽なら愛撫している人が誰なのかを返す。複数存在する場合は調教対象優先
;戻り値:物の場合→0.なし/1.口/2.手/3.ペニス/4.ヴァギナ/5.アナル/6.胸/7.足
;       人の場合→0.なし/1.調教対象/2.調教者自身/4.助手１/8.助手２/16.助手３
;備  考:式中関数
;仕様を整理して作り直した、新しい愛撫者/愛撫物判定関数
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@TB_PLAYER(ARG)
#FUNCTION
;TEQUIPによる判定1(優先度高)

;調教者アクションによる判定1(優先度高)
SELECTCASE TFLAG:90
	;胸愛撫
	CASE 11
		;揉み合いで、消極的従う/積極的従うの場合
		SIF TFLAG:93 == 2 && (TFLAG:94 == 1 || TFLAG:94 == 2)
			RETURNF ARG ? 2 # 1
	;パイズリする
	CASE 17
		RETURNF ARG ? 3 # 1
	;胸愛撫強制
		;奉仕した場合
		IF TFLAG:94 != 5
			;愛撫者判定
			SIF !ARG
				RETURNF 1
			;愛撫物判定(ぱふぱふ/口愛撫は口)
			RETURNF TFLAG:93 ? 1 # 2
		ENDIF
	;背面座位させる
	CASE 98
		RETURNF ARG ? 2 # 1
ENDSELECT

;調教者追加アクションによる判定
;助手アクションによる判定
;調教者アクションによる判定2(優先度低)

;TEQUIPによる判定2(優先度低)
;調教対象自慰中(オトコ以外、快ｂを得ている)
SIF (TEQUIP:69 & 2) && !TALENT:TARGET:122 && SOURCE:41 > 0
	RETURNF ARG ? 2 # 2


;-------------------------------------------------
;関数名:TARGET_EX
;概　要:調教者絶頂判定関数
;引　数:ARG:0…愛撫者→1.調教対象/2.調教者自身/4.助手１/8.助手２/16.助手３
;              ビット和での指定が可能
;       ARG:1…(省略可)絶頂の種別→1.ｃ絶頂/2.ｖ絶頂/3.ａ絶頂/4.ｂ絶頂
;                                  /11.噴乳/12.射精
;戻り値:今回のコマンドで、指定した愛撫者が調教者を指定の絶頂に導いたかどうか
;       ARG:1を省略した場合は全ての絶頂についてビットで返す
;       (1.ｃ絶頂/2.ｖ絶頂/4.ａ絶頂/8.ｂ絶頂/1024.噴乳/2048.射精)
;備  考:式中関数
;仕様を整理して作り直した、新しい絶頂判定関数
;実は現状で有効に使える場面があまり無いかもしれない
;リアクション口上や絶頂口上での使用を想定している
;-------------------------------------------------
@TARGET_EX(ARG:0, ARG:1)
#FUNCTION
LOCAL:0 = 0
LOCAL:1 = TC_PLAYER(0)
LOCAL:2 = TV_PLAYER(0)
LOCAL:3 = TA_PLAYER(0)
LOCAL:4 = TB_PLAYER(0)

LOCAL:0 |= NOWEX:40 && (LOCAL:1 & ARG) ? 1 # 0
LOCAL:0 |= NOWEX:41 && (LOCAL:2 & ARG) ? 2 # 0
LOCAL:0 |= NOWEX:42 && (LOCAL:3 & ARG) ? 4 # 0
LOCAL:0 |= NOWEX:43 && (LOCAL:4 & ARG) ? 8 # 0

;噴乳するにはｂ絶頂→ｂ愛撫者
LOCAL:0 |= NOWEX:50 && (LOCAL:4 & ARG) ? 1024 # 0
;射精するにはｃ絶頂→ｃ愛撫者
LOCAL:0 |= NOWEX:51 && (LOCAL:1 & ARG) ? 2048 # 0

RETURNF ARG:1 ? GETBIT(LOCAL:0, ARG:1 - 1) # LOCAL:0
