﻿@BOOKSHELF
DRAWLINE
LOCAL:1 = MAXARRAY(FLAG, 4000, 4100) > 500
PRINTL [0] - 返回
PRINTL [1] - 管理书架
PRINTL [2] - 梦魔数据
SIF LOCAL:1
	PRINTL [3] - 执笔绘本
SIF FLAG:42 || (FLAG:40 && CAPACITY() > 1)
	PRINTL [4] - 技能书
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
		RETURN 0
	CASE 1
		CALL BOOK_SETTING
	CASE 2
		CALL BOOK_DATABASE
	CASE 3
		SIF !LOCAL:1
			GOTO INPUT_ERROR
		CALL BOOK_SALE
	CASE 4
		SIF FLAG:40 && CAPACITY() > 1
			PRINTL [0] - 极意
		SIF FLAG:42
			PRINTL [1] - 见切
		INPUT
		IF RESULT == 0 && FLAG:40 && CAPACITY() > 1
			CALL BOOK_SKILL
		ELSEIF RESULT == 1 && FLAG:42
			CALL BOOK_SKILL2
		ELSE
			GOTO INPUT_ERROR
		ENDIF
	CASEELSE
		$INPUT_ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
RESTART


@BOOK_SETTING
DRAWLINE
PRINTL [0] - 返回
PRINTL [1] - 洞窟地图
PRINTL [2] - 森林地图
PRINTL [3] - 海图
PRINTL [4] - 图书馆示意图
PRINTL [5] - 沙漠地图
$INPUT_LOOP1
INPUT
SELECTCASE RESULT
	CASE 0
	CASE 1 TO 5
		LOCAL:1 = RESULT
		LOCAL:2 = GETCOLOR()
		DRAWLINE
		PRINTFORML 进行%STR:(800 + RESULT)%的设定
		PRINTL 请选择要追加的书。
		WHILE 1
			FOR LOCAL, 0, 15
				PRINTFORM [{LOCAL, 3}] - 
				SETCOLOR GETBIT(FLAG:(1750 + LOCAL:1), LOCAL) ? 0x00FF00 # LOCAL:2
				PRINTSL ITEMNAME:(600 + LOCAL)
				SETCOLOR LOCAL:2
			NEXT
			PRINTL [100] - 返回
			PRINTL [101] - 复位
			$INPUT_LOOP2
			INPUT
			SELECTCASE RESULT
				CASE 0 TO 14
					INVERTBIT FLAG:(1750 + LOCAL:1), RESULT
					CONTINUE
				CASE 100
					RESTART
				CASE 101
					FLAG:(1750 + LOCAL:1) = 0
					CONTINUE
				CASEELSE
					CLEARLINE 1
					GOTO INPUT_LOOP2
			ENDSELECT
		WEND
	CASEELSE
		CLEARLINE 1
		GOTO INPUT_LOOP1
ENDSELECT


@BOOK_DATABASE
DRAWLINE
FOR LOCAL, 0, 100
	SIF !FLAG:(4100 + LOCAL)
		CONTINUE
	PRINTFORMLC [{LOCAL, 3}] - %CSVNAME(LOCAL, 0)%
NEXT
PRINTL 
PRINTL [100] - 返回
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0 TO 99
		SIF !FLAG:(4100 + RESULT)
			GOTO INPUT_ERROR
		CALL BOOK_DATABASE_DETAIL(RESULT)
		RESTART
	CASE 100
	CASEELSE
		$INPUT_ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT

@BOOK_DATABASE_DETAIL(ARG)
PRINTL ※未完成※
PRINTSW CSVNAME(ARG, 0)


@BOOK_SALE
DRAWLINE
PRINTL 执笔哪个梦魔的绘本？
PRINTL                                收入
FOR LOCAL, 0, 100
	SIF FLAG:(4000 + LOCAL) <= 500
		CONTINUE
	LOCAL:(100 + LOCAL) = MIN(POWER(FLAG:(4000 + LOCAL) - FLAG:(4100 + LOCAL), 2) / 10, 50000)
	PRINTFORML [{LOCAL, 3}] - %CSVNAME(LOCAL, 0), 22, LEFT%{LOCAL:(100 + LOCAL), 5}$
NEXT
PRINTL [100] - 返回
$INPUT_LOOP1
INPUT
SELECTCASE RESULT
	CASE 0 TO 99
		SIF FLAG:(4000 + RESULT) <= 500
			GOTO INPUT_ERROR
		LOCAL = RESULT
		PRINTFORML 预计会有{LOCAL:(100 + LOCAL)}$的收入。
		PRINTL 要出版吗？
		PRINTL [0] - 是
		PRINTL [1] - 否
		$INPUT_LOOP2
		INPUT
		SELECTCASE RESULT
			CASE 0
				PRINTFORMW 作为版税得到了{LOCAL:(100 + LOCAL)}$的收入
				MONEY += LOCAL:(100 + LOCAL)
				FLAG:(4100 + LOCAL) = FLAG:(4000 + LOCAL)
			CASE 1
			CASEELSE
				CLEARLINE 1
				GOTO INPUT_LOOP2
		ENDSELECT
		RESTART
	CASE 100
	CASEELSE
		$INPUT_ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP1
ENDSELECT


@BOOK_SKILL
DRAWLINE
PRINTL 可以让梦魔记住技能
PRINTL [0] - 返回
SIF GETBIT(FLAG:40, 0)
	PRINTL [1] - 追加爱抚
SIF GETBIT(FLAG:40, 1)
	PRINTL [2] - 能量蛋白
SIF GETBIT(FLAG:40, 2)
	PRINTL [3] - 深喉
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
	CASE 1 TO 64
		SIF !GETBIT(FLAG:40, RESULT - 1)
			GOTO INPUT_ERROR
		CALL CHARALIST_SKILL(RESULT - 1)
		RESTART
	CASEELSE
		$INPUT_ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
@BOOK_SKILL2
DRAWLINE
PRINTL [0] - 返回
FOR LOCAL,0,3
	PRINTFORM [{LOCAL + 1}] - 
		IF CFLAG:MASTER:(310 + LOCAL)
			PRINTFORML %STR:(CFLAG:MASTER:(310 + LOCAL) + 600)%
		ELSE
			PRINTL 可以追加见切
		ENDIF
NEXT
PRINTL 
FOR LOCAL,1,50
	SIF GETBIT(FLAG:42,LOCAL)
		PRINTFORML [{100 + LOCAL}] - %STR:(600 + LOCAL)%
NEXT

INPUT
IF RESULT == 0
	RETURN 0
ELSEIF RESULT > 0 && RESULT < 4
	CFLAG:MASTER:(309 + RESULT) = 0
ELSEIF RESULT > 100 && RESULT < 150 && GETBIT(FLAG:42,RESULT - 100)
	IF !CFLAG:MASTER:310
		CFLAG:MASTER:310 = RESULT - 100
	ELSEIF !CFLAG:MASTER:311
		CFLAG:MASTER:311 = RESULT - 100
	ELSEIF !CFLAG:MASTER:312
		CFLAG:MASTER:312 = RESULT - 100
	ELSE
		PRINTW 没有框架
	ENDIF
ENDIF
CFLAG:MASTER:319 = 0
FOR LOCAL,0,3
	SETBIT CFLAG:MASTER:319,CFLAG:MASTER:(310  + LOCAL)
NEXT
RESTART

@CHARALIST_SKILL(ARG)
SIF !STRLENS(LOCALS)
	SPLIT "　/○/◎", "/", LOCALS
PRINTSL TALENTNAME:(200 + ARG)
PRINTL ○…学习完毕　◎…固有技能
PRINTL [ 0] - 返回
FOR LOCAL, 0, CHARANUM
	;MASTERまたは一時退避中は除く
	;MASTERが0番以外になったり、0番が調教可能キャラになったりしないという前提で
	SIF LOCAL == MASTER || CFLAG:LOCAL:92
		CONTINUE
	PRINTFORML [{LOCAL, 2}] - %CALLNAME:LOCAL, 20, LEFT% %LOCALS:(LIMIT(TALENT:LOCAL:(200 + ARG), 0, 2))%
NEXT
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
	CASE 0 TO CHARANUM - 1
		SIF CFLAG:RESULT:92
			GOTO INPUT_ERROR
		IF TALENT:RESULT:(200 + ARG) >= 2
			CLEARLINE 1
			REUSELASTLINE 固有技能不能排除
			GOTO INPUT_LOOP
		ENDIF
		TALENT:RESULT:(200 + ARG) = !TALENT:RESULT:(200 + ARG)
		RESTART
	CASEELSE
		$INPUT_ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
