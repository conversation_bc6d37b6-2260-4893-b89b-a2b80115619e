﻿;────────────────────────────────────
;010,恥ずかしがる（露出逆、屈従、反抗、消極的従う、お仕置きポイント）
;────────────────────────────────────
@COM10
A = 200 + MARK:3 * 10 - CFLAG:MASTER:0 * 15
SOURCE:20 -= A
IF SOURCE:20 < 0
	SOURCE:20 = 0
	A = 0
ENDIF
SOURCE:30 += 200 - A / 2
SOURCE:33 += A / 10

TFLAG:94 = 1

SIF A > 0 && BASE:7 / 100 + RAND:5 > BASE:8 / 100 + MARK:2 + ABL:MASTER:0 + CFLAG:6 / 10
	TFLAG:68 += 1 + RAND:3

SIF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 10
	TFLAG:300 = 1
RETURN 1

;────────────────────────────────────
;011,嫌がる（トラウマ、反抗、暴れる、お仕置きポイント）
;────────────────────────────────────
@COM11
CALL ABL_REVISION
TIMES SOURCE:24 , 1.20
A = 200 + SOURCE:24 / 3 - MARK:2 * 20 - B:30 * 5 + MARK:3 * 20
SIF A > 1000
	A = 1000
SIF A < 0
	A = 0
SOURCE:33 += A

;COM32の暴れるより判定が弱い
B = MARK:3 + BASE:MASTER:0 / 150 + BASE:MASTER:1 / 100 - TALENT:110 * 5 + TALENT:111 * 5 + PALAM:9 / 1000 - (TEQUIP:40 + TEQUIP:46 + TEQUIP:47) * 3 - TEQUIP:41 * 4 - TEQUIP:42
D = BASE:0 / 120 + BASE:1 / 80 - TALENT:110 * 5 + TALENT:111 * 5 + A:30 * 2

IF TFLAG:90 == 12
	C = 2
ELSEIF TFLAG:90 == 15
	C = 5
ELSEIF TFLAG:90 == 17
	C = 3
ELSEIF TFLAG:90 == 18
	C = 0
ELSEIF TFLAG:90 == 22 || TFLAG:90 == 23
	C = 12
ELSEIF TFLAG:90 == 30 || TEQUIP:70 == 1
	C = 12
ELSEIF TFLAG:90 == 31 || TEQUIP:70 == 2
	C = 18
ELSEIF TFLAG:90 == 32 || TEQUIP:70 == 3
	C = 7
ELSEIF TFLAG:90 == 33 || TEQUIP:70 == 4
	C = 15
ELSEIF TFLAG:90 == 34 || TEQUIP:70 == 5
	C = 16
ELSEIF TFLAG:90 == 35 || TEQUIP:70 == 6
	C = 20
ELSEIF TFLAG:90 == 36 || TEQUIP:70 == 7
	C = 12
ELSEIF TFLAG:90 == 56
	C = 14
ELSEIF TFLAG:90 == 60
	C = 12
ELSEIF TFLAG:90 == 61
	C = 15
ELSEIF TFLAG:90 == 62
	C = 14
ELSEIF TFLAG:90 == 63
	C = 12
ELSEIF TFLAG:90 == 64
	C = 12
ELSEIF TFLAG:90 == 92
	C = -10
ELSE
	C = 10
ENDIF

SIF ASSI > 0
	C += C:30 / 2 + 3
;ＳＰ行動
SIF TFLAG:120 > 199
	C += 10

IF B > C + D && TEQUIP:43 == 0
	TFLAG:68 += (B - D) / 2 + 3
	TFLAG:94 = 4
ELSE
	TFLAG:68 += B / 5 + 2
ENDIF
IF TFLAG:94 != 4
	TFLAG:94 = 1
	TFLAG:125 = 3
ELSE
	LOSEBASE:0 += 5 * (4 + MARK:3 + TALENT:MASTER:111 * 2 - TALENT:MASTER:110 * 2)
	LOSEBASE:1 += 8 * (7 + CFLAG:MASTER:7 + TALENT:MASTER:11 * 3 + TALENT:MASTER:16 * 3 - TALENT:MASTER:13 * 3 - TALENT:MASTER:14 * 3)
	TFLAG:125 = 4
	TFLAG:300 = 1
ENDIF
RETURN 1

;────────────────────────────────────
;012,下手だと罵る（達成逆、反抗、お仕置きポイント）
;────────────────────────────────────
@COM12
CALL ABL_REVISION
A = 20 + TALENT:MASTER:15 * 5 + TALENT:MASTER:12 * 5 + MARK:3 + PALAM:9 / 500 - B:30 * 3 / 10 - CFLAG:MASTER:0
B = SOURCE:0 * (1 + TALENT:MASTER:100) + SOURCE:1 * (1 + TALENT:MASTER:102) + SOURCE:2 * (1 + TALENT:MASTER:104) + SOURCE:3 * (1 + TALENT:MASTER:106)
A -= B / (500 - TALENT:70 * 100 + TALENT:71 * 50)
SIF A < 0
	A = 0
SOURCE:31 -= A * 50
SOURCE:33 += 200 + A * 30
TFLAG:94 = 1
TFLAG:68 += A / 4 + 5

IF A > 0
	TFLAG:125 = 4
ELSE
	TFLAG:125 = 3
	TFLAG:300 = 1
ENDIF
RETURN 1


;────────────────────────────────────
;013,不敵に笑う（トラウマ逆、反抗、消極的従う、お仕置きポイント）
;────────────────────────────────────
@COM13
TIMES SOURCE:24 , 0.80
SOURCE:33 += 500 - SOURCE:0 / 50 * (1 + TALENT:MASTER:100) + SOURCE:1 / 50 * (1 + TALENT:MASTER:102) + SOURCE:2 / 50 * (1 + TALENT:MASTER:104) + SOURCE:3 / 50 * (1 + TALENT:MASTER:106)
SIF SOURCE:33 < 0
	SOURCE:33 = 0
TFLAG:94 = 1
TFLAG:68 += BASE:7 / 80 - BASE:6 / 150 + 1 + RAND:3
TFLAG:125 = 3
RETURN 1


;────────────────────────────────────
;014,快感を我慢する（快ＣＶＡＢ補正、不快追加、達成、悦楽、反抗、哀願、お仕置きポイント）
;────────────────────────────────────
@COM14
TIMES SOURCE:0 , 0.80
TIMES SOURCE:1 , 0.80
TIMES SOURCE:2 , 0.80
TIMES SOURCE:3 , 0.80
UP:12 += SOURCE:0 / 25 + SOURCE:1 / 25 + SOURCE:2 / 25 + SOURCE:3 / 25
SOURCE:31 += 100 + SOURCE:0 / 50 + SOURCE:1 / 50 + SOURCE:2 / 50 + SOURCE:3 / 50
SOURCE:32 += 100 + SOURCE:0 / 50 + SOURCE:1 / 50 + SOURCE:2 / 50 + SOURCE:3 / 50
A = (MARK:3 + 1) * (100 + SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3) / 500
SIF A > 500
	A = 500
SOURCE:33 += A

;我慢フラグ
TFLAG:168 = 3

;哀願成立の判定
SIF SOURCE:33 < 500 && (TALENT:87 * 5 - TALENT:86 * 5 - BASE:7 / 100 + BASE:8 / 100 > 0 + CFLAG:6 / 10) && TEQUIP:42 == 0
	TFLAG:94 = 3
IF TFLAG:94 == 3
	TFLAG:125 = 6
	TFLAG:300 = 1
ELSE
	TFLAG:94 = 1
ENDIF
RETURN 1

;────────────────────────────────────
;015,快感を受け入れる（快ＣＶＡＢ補正、屈従、達成、悦楽、積極的従う）
;────────────────────────────────────
@COM15
TIMES SOURCE:0 , 1.20
TIMES SOURCE:1 , 1.20
TIMES SOURCE:2 , 1.20
TIMES SOURCE:3 , 1.20
TIMES SOURCE:22 , 0.70
TIMES SOURCE:24 , 0.80
SOURCE:30 += 50 + (MARK:MASTER:1 + 1) * (SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3) * CFLAG:MASTER:0 / 200
SOURCE:31 += 100 + (CFLAG:MASTER:0 + 5) * (SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3) / 100
SOURCE:32 += 100 + SOURCE:0 / 25 + SOURCE:1 / 25 + SOURCE:2 / 25 + SOURCE:3 / 25
TFLAG:125 = 1
TFLAG:94 = 2
TFLAG:168 = 0
RETURN 1
