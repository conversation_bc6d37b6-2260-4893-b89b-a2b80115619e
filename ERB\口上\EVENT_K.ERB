﻿;≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
;口上呼び出し用ターミナルファイル
;※Emuera専用のためサブフォルダに配置すること※
;≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡

;※OP追加
;-------------------------------------------------
;オープニング口上@KOJO_OPENING
;ゲーム開始時、ご主人様決定直後に出力します
;RESULT値が0だと口上を表示しなかったことを表します
;-------------------------------------------------
@KOJO_OPENING
RESULT = 0
RESULTS =
TRYCALLFORM KOJO_K{NO:TARGET}
IF !FLAG:7
	RETURN 0
ELSEIF !RESULT
	TRYCALLFORM KOJO_OPENING_K00
	RETURN 0
ENDIF
CALLFORM KOJO%RESULTS%_COLOR_K{NO:TARGET}
TRYCALLFORM KOJO%RESULTS%_OPENING_K{NO:TARGET}
RESETCOLOR
RETURN RESULT
;※OP追加

;-------------------------------------------------
;イベント口上@KOJO_EVENT
;イベント時の口上、イベント開始時に出力します
;引数0はイベント番号、引数1はイベントの派生番号（派生がないものは省略してよい）
;引数2はキャラの登録番号（省略する=0だとTARGET、そのため現状MASTERは呼び出せないがたぶん困らない）
;RESULT値が0だと口上を表示しなかったことを表します
;-------------------------------------------------
@KOJO_EVENT(ARG:0, ARG:1, ARG:2)
ARG:2 = ARG:2 ? ARG:2 # TARGET
SIF ARG:2 < 0
	RETURN 0
RESULT = 0
RESULTS =
TRYCALLFORM KOJO_K{NO:(ARG:2)}
IF !FLAG:7
	RETURN 0
ELSEIF !RESULT
	TRYCALLFORM KOJO_EVENT_K00_{ARG:0}(ARG:1)
	RETURN 0
ENDIF
CALLFORM KOJO%RESULTS%_COLOR_K{NO:(ARG:2)}
TRYCALLFORM KOJO%RESULTS%_EVENT_K{NO:(ARG:2)}_{ARG:0}(ARG:1)
RESETCOLOR
RETURN RESULT
;-------------------------------------------------
;助手イベント口上@KOJO_EVENT_ASSI
;助手関連イベント時の口上、イベント開始時に出力します。常に右揃えになる
;引数0はイベント番号、引数1はイベントの派生番号（派生がないものは省略してよい）
;RESULT値が0だと口上を表示しなかったことを表します
;また、RESULT値が1だとDRAWLINE、2だと一行改行、3だと両方します
;-------------------------------------------------
@KOJO_EVENT_ASSI(ARG:0, ARG:1)
SIF ASSI < 0
	RETURN 0
RESULT = 0
RESULTS =
TRYCALLFORM KOJO_K{NO:ASSI}
IF !FLAG:7
	RETURN 0
ELSEIF !RESULT
	CALL KOJO_EVENT_ASSI_K00
	RETURN 0
ENDIF
CALLFORM KOJO%RESULTS%_COLOR_K{NO:ASSI}
ALIGNMENT RIGHT
TRYCALLFORM KOJO%RESULTS%_ASSI_K{NO:ASSI}_{ARG:0}(ARG:1)
RESETCOLOR
ALIGNMENT LEFT

RETURN RESULT

;-------------------------------------------------
;刻印取得口上@KOJO_MARK
;刻印取得時に出力します
;引数はそれぞれが刻印番号に対応、そのコマンドで取得した刻印の数値が入ります
;RESULT値が0だと口上を表示しなかったことを表します
;-------------------------------------------------
@KOJO_MARK(ARG:0, ARG:1, ARG:2, ARG:3, ARG:4)
SIF TARGET < 0
	RETURN 0
RESULT = 0
RESULTS =
TRYCALLFORM KOJO_K{NO:TARGET}
IF !FLAG:7
	RETURN 0
ELSEIF !RESULT
	CALL KOJO_MARK_K00
	RETURN 0
ENDIF
CALLFORM KOJO%RESULTS%_COLOR_K{NO:TARGET}
TRYCALLFORM KOJO%RESULTS%_MARK_K{NO:TARGET}(ARG,ARG:1,ARG:2,ARG:3,ARG:4)
RESETCOLOR
RETURN RESULT

;-------------------------------------------------
;脱衣アクション口上@KOJO_DATUI
;調教者アクションの前、強制脱衣か自主脱衣が発生したときに出力します
;RESULT値が0だと口上を表示しなかったことを表します
;-------------------------------------------------
@KOJO_DATUI
;調教者が脱がせた
IF TFLAG:102 && TFLAG:190 != 70
	RESULT = 0
	RESULTS =
	TRYCALLFORM KOJO_K{NO:TARGET}
	IF !FLAG:7
		RETURN 0
	ELSEIF !RESULT
		CALL KOJO_DATUI_K00
		RETURN 0
	ENDIF
	CALLFORM KOJO%RESULTS%_COLOR_K{NO:TARGET}
	TRYCALLFORM KOJO%RESULTS%_DATUI_K{NO:TARGET}
	RESETCOLOR

	RETURN RESULT
;助手が脱がせた
ELSEIF TFLAG:102 && TFLAG:190 == 70
	ALIGNMENT RIGHT
	RESULT = 0
	RESULTS =
	TRYCALLFORM KOJO_K{NO:ASSI}
	IF !FLAG:7
		RETURN 0
	ELSEIF !RESULT
		CALL KOJO_DATUI_ASSI_K00
		RETURN 0
	ENDIF
	CALLFORM KOJO%RESULTS%_COLOR_K{NO:ASSI}
	TRYCALLFORM KOJO%RESULTS%_DATUI_ASSI_K{NO:ASSI}
	RESETCOLOR
ENDIF
;調教者が脱いだ
IF TFLAG:103
	RESULT = 0
	RESULTS =
	TRYCALLFORM KOJO_K{NO:TARGET}
	IF !FLAG:7
		RETURN 0
	ELSEIF !RESULT
		CALL KOJO_DATUI_K00
		RETURN 0
	ENDIF
	CALLFORM KOJO%RESULTS%_COLOR_K{NO:TARGET}
	TRYCALLFORM KOJO%RESULTS%_DATUI_K{NO:TARGET}
	RESETCOLOR
ENDIF
RETURN RESULT

;-------------------------------------------------
;アクション口上@KOJO_ACT
;調教者アクションが発生したときに出力します
;RESULT値が0だと口上を表示しなかったことを表します
;RESULT値が2だと次の「媚薬等使用口上@KOJO_USE」を呼びません
;アクション口上内で媚薬等使用口上を兼ねて書いた場合に指定してください
;-------------------------------------------------
@KOJO_ACT
RESULT = 0
RESULTS =
TRYCALLFORM KOJO_K{NO:TARGET}
IF !FLAG:7
	CALL TRAIN_MESSAGE
	RETURN 0
ELSEIF !RESULT
	CALL KOJO_TRAINMASSAGE_K00
	RETURN 0
ENDIF
;地の文スキップ
RESULT = 0
TRYCALLFORM KOJO%RESULTS%_TRAIN_MESSAGE_SKIP_K{NO:TARGET}
FLAG:12 = RESULT
IF FLAG:12
	TRYCALLFORM KOJO%RESULTS%_TRAINMESSAGE_K{NO:TARGET}
ELSE
	CALL TRAIN_MESSAGE
ENDIF
CALLFORM KOJO%RESULTS%_COLOR_K{NO:TARGET}
;助手が媚薬等使用している場合、使用フラグを逃がす
LOCAL = TFLAG:104
SIF TFLAG:190 == 71
	TFLAG:104 = 0
TRYCALLFORM KOJO%RESULTS%_ACT_K{NO:TARGET}_{TFLAG:90}
RESETCOLOR
TFLAG:104 = LOCAL
TFLAG:307 = RESULT == 2 ? 0 # TFLAG:307
RETURN RESULT


;-------------------------------------------------
;追加アクション口上@KOJO_ACT_EXTRA
;追加アクションが発生したときに出力します
;RESULT値が0だと口上を表示しなかったことを表します
;-------------------------------------------------
@KOJO_ACT_EXTRA(ARG)
RESULT = 0
RESULTS =
TRYCALLFORM KOJO_K{NO:TARGET}
SIF !RESULT || !FLAG:7
	RETURN 0

SIF !GETBIT(TFLAG:305, ARG)
	RETURN 0

CALLFORM KOJO%RESULTS%_COLOR_K{NO:TARGET}
TRYCALLFORM KOJO%RESULTS%_ACT_EXTRA_K{NO:TARGET}_{ARG * 10 + TFLAG:(229 + ARG)}
RESETCOLOR
LOCAL = RESULT
SIF FLAG:7 || LOCAL
	CLEARBIT TFLAG:305, ARG
RETURN RESULT


;-------------------------------------------------
;助手アクション口上@KOJO_ACT_ASSI
;助手アクションが発生したときに出力します
;RESULT値が0だと口上を表示しなかったことを表します
;-------------------------------------------------
@KOJO_ACT_ASSI
SIF ASSI < 1
	RETURN 0
LOCAL = TARGET
TARGET = ASSI
RESULT = 0
RESULTS =
TRYCALLFORM KOJO_K{NO:ASSI}
IF !FLAG:7
	TARGET = LOCAL
	RETURN 0
ELSEIF !RESULT
	CALL KOJO_ACT_ASSI_K00
	TARGET = LOCAL
	RETURN 0
ENDIF
CALLFORM KOJO%RESULTS%_COLOR_K{NO:ASSI}
TRYCALLFORM KOJO%RESULTS%_ACT_ASSI_K{NO:ASSI}_{TFLAG:190}
RESETCOLOR
TARGET = LOCAL
RETURN RESULT


;-------------------------------------------------
;媚薬等使用口上@KOJO_USE
;媚薬かローションが使われたときに出力します
;RESULT値が0だと口上を表示しなかったことを表します
;助手が行った場合や「アクション口上@KOJO_ACT」のRESULT値が2だと呼ばれません
;アクション口上内で媚薬使用口上を兼ねて書いた場合に指定してください
;-------------------------------------------------
@KOJO_USE
RESULT = 0
RESULTS =
TRYCALLFORM KOJO_K{NO:TARGET}
IF !FLAG:7
	RETURN 0
ELSEIF !RESULT
	CALL KOJO_USE_K00
	RETURN 0
ENDIF
CALLFORM KOJO%RESULTS%_COLOR_K{NO:TARGET}
TRYCALLFORM KOJO%RESULTS%_USE_K{NO:TARGET}
RESETCOLOR
TFLAG:307 = RESULT ? 0 # TFLAG:307
RETURN RESULT

;-------------------------------------------------
;リアクション口上@KOJO_REACT
;調教対象がコマンドを実行した後に出力します
;最初に調教指示ごとに個別の口上を参照し、そのRESULT値が0だと口上を表示しなかったことを表し、
;表示しなかった（RESULT=0）の場合のみコマンドごとに汎用の口上を参照します
;RESULT値が0だといずれの口上も表示しなかったことを表します
;-------------------------------------------------
@KOJO_REACT
RESULT = 0
RESULTS =
TRYCALLFORM KOJO_K{NO:TARGET}
IF !FLAG:7
	RETURN 0
ELSEIF !RESULT
	CALL KOJO_REACT_K00
	RETURN 0
ENDIF
CALLFORM KOJO%RESULTS%_COLOR_K{NO:TARGET}
RESULT = 0
TRYCALLFORM KOJO%RESULTS%_REACT_K{NO:TARGET}_{TFLAG:90}
SIF !RESULT
	TRYCALLFORM KOJO%RESULTS%_COM_K{NO:TARGET}_{SELECTCOM}
RESETCOLOR
RETURN RESULT

