﻿@ENDING
RETURN 0

;使用するフラグ
;5000が執筆した夢魔の数、5001が執筆したユニーク夢魔の数、5002が屈服刻Lv10の数(SOURCE.ERBでカウント)
;バランスは全然とれていませんがそのうちなんとかします･･･

;夢魔データの数を数える
FOR LOCAL, 0, 100
	SIF !FLAG:(4100 + LOCAL)
		CONTINUE
	;ユニーク夢魔の数を数える(ユニークのCSVの番号が32～40)
	IF LOCAL >= 32 && LOCAL <= 40
		FLAG:5001 += 1
	;ユニークでなければ
	ELSE
		FLAG:5000 += 1
	ENDIF
NEXT

DRAWLINE
PRINTFORMW ・・・
PRINTFORMW ・・・・・・
PRINTFORMW ・・・・・・・・・
PRINTL 
PRINTFORMW 今天终于到了和梦魔约定好的第100天了……
PRINTL 

;屈服刻印Lv10の数が三つ以上または称号夢魔の生贄持ちの場合バッドエンド
IF FLAG:5002 >= 3 || CFLAG:MASTER:303 > 2000
	PRINTFORML 但是，那种事已经无所谓了。
	PRINTFORML 不断地被梦魔玩弄身心……
	PRINTFORML 这个世界上还有比这更幸福的吗?
	PRINTFORML 现在%CALLNAME:MASTER%除了主人给予的快乐的事以外，脑子里已经没有其他的了…

;監禁中だとバッドエンド
ELSEIF FLAG:1701
	PRINTFORMW 但是%CALLNAME:MASTER%被%CALLNAME:TARGET%监禁着无法把书拿出来･･･
	PRINTL 
	PRINTFORML ···今后自己会变成什么样呢?
	PRINTFORML 已经不能回去了吗?
	PRINTFORML 被监禁的%CALLNAME:MASTER%能做的只有安静地等待驯化……

;監禁中でない場合
ELSE
	PRINTFORMW 「哎呀，没有好好被监禁着，一直在等我呢%UNICODE(0x2661)%」
	PRINTL 
	PRINTFORML 不知从哪里传来耳熟的声音、像有谁在看%CALLNAME:MASTER%准备的书一样、
	PRINTFORMW 书页自己动了起来、被翻得乱七八糟的。
	PRINTL 

	;調べた量が足りない
	IF FLAG:5000 < 9
		PRINTFORML 「哼恩・・・・・・・・・
		;ユニークについて書いてある
			SIF !FLAG:5001 == 0
			PRINTFORML 　确实有些东西很稀有、
		PRINTFORML 　给了100天的时间，却只有这个啊？
		PRINTFORML 　・・・说不定比起写书，和我们一起玩会更开心？
		PRINTFORML 　那样的话，这个房子就给你吧。
		PRINTFORML 　也不用回去了吧？
		PRINTFORMW 　那么，祝你好梦%UNICODE(0x2661)%」

	;量は足りているがユニークがない
	ELSEIF FLAG:5000 >= 9 && FLAG:5001 == 0
		PRINTFORML 「哎呀，你真努力啊%UNICODE(0x2661)%
		PRINTFORML 　但是这本书里关于稀有的梦魔什么都没写呢。
		PRINTFORML 　这样太无聊了。
		PRINTFORML 　呵呵，很遗憾不能让你回去。
		PRINTFORML 　但是我会把房子给努力的%CALLNAME:MASTER%哦%UNICODE(0x2661)%
		PRINTFORMW 　那么，祝你好梦%UNICODE(0x2661)%」

	;量もユニークも足りている
	ELSE
		PRINTFORML 「好厉害！居然总结到这里了呢%UNICODE(0x2661)%
		PRINTFORML 　没有可以抱怨的，那么我也要遵守约定了。
		PRINTFORMW 　但是，现在的%CALLNAME:MASTER%ss真的想回去吗？嘿嘿･･･%UNICODE(0x2661)%」
		PRINTL 
		
		;夢から醒めるか醒めないかの選択
		PRINTL [0]梦醒吧
		PRINTL [1]梦不要醒来
		$ENDINGINPUT_LOOP
		INPUT
			IF RESULT == 0
				CALL NORMAL_END
			ELSEIF RESULT == 1
				PRINTFORML 「呵呵，看来你是喜欢我们了%UNICODE(0x2661)%
				PRINTFORML 　作为奖励、就把房子送给努力的%CALLNAME:MASTER%了。
				PRINTFORML 　今后也要尽情享受这个梦哦%UNICODE(0x2661)%
				PRINTFORMW 　那么，祝你好梦%UNICODE(0x2661)%」
			ELSE
				GOTO ENDINGINPUT_LOOP
			ENDIF
	ENDIF
	PRINTL 
	PRINTFORMW パタンと本が静かに閉じると、静まり返った部屋には%CALLNAME:MASTER%だけが残された。
ENDIF

PRINTL 
PRINTFORML 啪嗒一声书静静地合上了，寂静的房间里只剩下了%CALLNAME:MASTER%。
PRINTFORMW 从今以后也要作为梦魔的奴隶被玩弄、被榨取的日子还会继续下去。・・・・・・
PRINTFORMW ・・・・・・・・・
PRINTFORMW ・・・・・・
PRINTFORMW ・・・


;文章を出してロード画面へ
@NORMAL_END
PRINTFORML 「啊啦、真遗憾･･･
PRINTFORML 　但是你写了这么好的东西，我会好好遵守约定的。。
PRINTFORMW 　什么时候有机会再玩儿吧%UNICODE(0x2661)%」
PRINTL 
PRINTFORMW ・・・・・・・・・
PRINTFORMW ・・・・・・
PRINTFORMW ・・・
PRINTL 
PRINTFORML 回过神来，%CALLNAME:MASTER%已经趴在自己房间的桌子上了
PRINTFORML 是在调查梦魔的过程中睡着了吗?
PRINTFORML 好像做了一个很长的梦･･････
PRINTFORML 眼前是一张几乎空白的纸。
PRINTFORML 但是，是因为睡得足了吗？
PRINTFORML 不可思议的是，现在关于梦魔的书好像能写出很多。
PRINTFORML %CALLNAME:MASTER%打了个大呵欠，拿起笔继续写书。
PRINTFORMW 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　Fin
PRINTL 
DRAWLINE
LOADGAME

