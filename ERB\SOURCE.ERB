﻿@SOURCE_CHECK
;────────────────────────────────────
;遅延された調教者行動の実行
;────────────────────────────────────
RESULT = 0
CALL ACTION_APPLY2
IF RESULT
	PRINTW
ELSE
	CALL REACTION_MESSAGE
	PRINTW
ENDIF
;────────────────────────────────────
;服薬などによる尿意ゲージ発生
;────────────────────────────────────
SIF BASE:MASTER:4 > MAXBASE:MASTER:4
	MAXBASE:MASTER:4 = 10000 - TALENT:MASTER:123 * 5000 + TALENT:MASTER:124 * 5000

;────────────────────────────────────
;お仕置きモードの処理
;────────────────────────────────────
IF TFLAG:69 > 0
	TIMES SOURCE:13 , 1.25
	TIMES SOURCE:14 , 1.25
	TIMES SOURCE:24 , 1.15
ENDIF

;────────────────────────────────────
;気力０による上下の処理（前半）
;────────────────────────────────────
IF BASE:MASTER:1 <= 0
	TIMES SOURCE:0 , 1.25
	TIMES SOURCE:1 , 1.25
	TIMES SOURCE:2 , 1.25
	TIMES SOURCE:3 , 1.25
ENDIF

IF BASE:1 <= 0
	TIMES SOURCE:40 , 1.25
	TIMES SOURCE:41 , 1.25
	TIMES SOURCE:42 , 1.25
	TIMES SOURCE:43 , 1.25
ENDIF

;────────────────────────────────────
;快感のソース
;────────────────────────────────────
;連続ボーナス
IF TFLAG:90 == TFLAG:91
	TFLAG:450 *= 2
ELSEIF SELECTCOM == 61
	TFLAG:450 *= 2
ELSE 
	TFLAG:450 = 1
ENDIF

SIF SOURCE:0 + SOURCE:1 + SOURCE:2 + SOURCE:3 > 0
	CALL CACL_SOURCE00

SIF SOURCE:40 + SOURCE:41 + SOURCE:42 + SOURCE:43 > 0
	CALL CACL_SOURCE40

;────────────────────────────────────
;行為・精神・反応のソース
;────────────────────────────────────
FOR LOCAL, 10, 40
	SIF SOURCE:LOCAL > 0
		CALLFORM CACL_SOURCE{LOCAL}
NEXT

;────────────────────────────────────
;助手補正という名のサボり
;────────────────────────────────────
IF TFLAG:172 == 1
	FOR LOCAL, 0, 14
		TIMES UP:LOCAL, 1.50
	NEXT
ENDIF

;────────────────────────────────────
;調教対象の反応による変動（最初はこれがなかったがより正しく調教対象の行為を反応するためやむ得なく追加しました）
;────────────────────────────────────
IF SELECTCOM == 15 || SELECTCOM == 21
	TIMES UP:8 , 1.10
	TIMES UP:12 , 0.50
	TIMES UP:13 , 0.25
ELSEIF SELECTCOM == 3 || SELECTCOM == 42
	TIMES UP:9 , 0.80
	TIMES UP:12 , 0.60
	TIMES UP:13 , 0.20
ELSEIF SELECTCOM == 43 || SELECTCOM == 6 || SELECTCOM == 8
	TIMES UP:7 , 0.50
	TIMES UP:8 , 1.25
	TIMES UP:12 , 0.20
	TIMES UP:13 , 0.05
	TFLAG:95 += 1
ENDIF

;────────────────────────────────────
;相性による上下の処理(未実装)
;────────────────────────────────────
;R = NO:PLAYER
;IF RELATION:R != 0
;	PRINTFORML ＜相性{RELATION:R/100}.{RELATION:R%100}倍＞
;ENDIF

;────────────────────────────────────
;調教者の体力/気力追加消費（最初はソースだけでこれを解決したかったがうまく表現できませんでした…）
;────────────────────────────────────
IF TFLAG:80 == 0 || TFLAG:90 == 66
	LOSEBASE:1 += 25
ELSEIF TFLAG:80 == 1
	LOSEBASE:0 += 30
	LOSEBASE:1 += 50
ELSEIF TFLAG:80 == 2
	LOSEBASE:0 += 40
	LOSEBASE:1 += 35
ELSEIF TFLAG:80 == 3
	LOSEBASE:0 += 60
	LOSEBASE:1 += 50
ELSEIF TFLAG:80 == 4
	SIF TFLAG:90 == 42
		LOSEBASE:1 += 40
	SIF TFLAG:90 == 43 || TFLAG:90 == 44
		LOSEBASE:0 += 40
	LOSEBASE:0 += 15
	LOSEBASE:1 += 40
ELSEIF TFLAG:80 == 5
	SIF TFLAG:90 == 56
		LOSEBASE:1 += 40
	LOSEBASE:0 += 5
	LOSEBASE:1 += 30
ELSEIF TFLAG:80 == 6 && TFLAG:90 != 66
	LOSEBASE:0 += 50
	LOSEBASE:1 += 15
ELSEIF TFLAG:80 == 7 && TFLAG:90 != 73
	LOSEBASE:0 += 35
	LOSEBASE:1 += 60
ENDIF
IF TFLAG:230 || TFLAG:231 || TFLAG:232
	TIMES LOSEBASE:0, 1.5
	TIMES LOSEBASE:1, 1.5
ENDIF

;────────────────────────────────────
;気力０による上下の処理（後半）
;────────────────────────────────────
IF BASE:MASTER:1 <= 0
	TIMES UP:5 , 1.40
	TIMES UP:7 , 1.20
	TIMES UP:9 , 0.60
	TIMES UP:12 , 0.80
	LOSEBASE:90 += 10 + LOSEBASE:91 / 3
	LOSEBASE:95 += 10 + LOSEBASE:91 / 2
ENDIF

IF BASE:1 <= 0
	LOSEBASE:0 += 10 + LOSEBASE:1 / 3
	LOSEBASE:5 += 10 + LOSEBASE:1 / 2
ENDIF

;────────────────────────────────────
;調教対象のゲージの変動
;────────────────────────────────────
BASE:MASTER:0 -= LOSEBASE:90
BASE:MASTER:1 -= LOSEBASE:91
BASE:MASTER:2 -= LOSEBASE:92
BASE:MASTER:3 -= LOSEBASE:93
BASE:MASTER:4 -= LOSEBASE:94
SIF TALENT:MASTER:20
	TIMES LOSEBASE:95 , 0.75
SIF TALENT:MASTER:21
	TIMES LOSEBASE:95 , 1.25
BASE:MASTER:5 -= LOSEBASE:95

;────────────────────────────────────
;調教者のゲージの変動
;────────────────────────────────────
;興味の処理、調教対象の反応や変化を見せないとつまらない
V = SOURCE:30 + SOURCE:31 + SOURCE:32
X = UP:0 + UP:1 + UP:2 + UP:3 + UP:5
Y = UP:6 + UP:7 + UP:8 + UP:10 + UP:11
Z = UP:9 * 2 + UP:12 + UP:13
IF V > 1500 || X > 1500 || Y > 1500
	BASE:6 += 50 - TALENT:22 * 20 + TALENT:23 * 20
ELSEIF V > 1200 || X > 1200 || Y > 1200
	BASE:6 += 40 - TALENT:22 * 15 + TALENT:23 * 15
ELSEIF V > 900 || X > 900 || Y > 900
	BASE:6 += 30 - TALENT:22 * 10 + TALENT:23 * 10
ELSEIF V > 600 || X > 600 || Y > 600
	BASE:6 += 20 - TALENT:22 * 5 + TALENT:23 * 5
ELSE
	BASE:6 -= 10 - TALENT:22 * 5 + TALENT:23 * 5
ENDIF

;短気の調教者は負の反応にうんざり
IF TALENT:85
	IF Z > 1500
		BASE:6 -= 150
	ELSEIF Z > 1050
		BASE:6 -= 80
	ELSEIF Z > 600
		BASE:6 -= 30
	ELSEIF Z > 200
		BASE:6 -= 10
	ENDIF
ENDIF

;自然消耗
IF BASE:6 > MAXBASE:6 / 3
	BASE:6 -= MAXBASE:6 * (20 + A:30) / 200
ELSE
	BASE:6 -= MAXBASE:6 * (20 + A:30) / 300
ENDIF
SIF BASE:6 > MAXBASE:6
	BASE:6 = MAXBASE:6
SIF BASE:6 < 0
	BASE:6 = 0

;興味低下による気力消耗
SIF BASE:6 < 200
	LOSEBASE:1 += (300 - BASE:6) / (10 + TALENT:22 * 3 - TALENT:23 * 3 + TALENT:24 * 2 - TALENT:25 * 2)

;苛立ちの処理
IF Z > 1500
	BASE:7 += 100 + TALENT:85 * 25 - TALENT:84 * 25 + TALENT:16 * 10 - TALENT:14 * 10
ELSEIF Z > 1200
	BASE:7 += 80 + TALENT:85 * 20 - TALENT:84 * 20 + TALENT:16 * 8 - TALENT:14 * 8
ELSEIF Z > 900
	BASE:7 += 60 + TALENT:85 * 16 - TALENT:84 * 16 + TALENT:16 * 6 - TALENT:14 * 6
ELSEIF Z > 600
	BASE:7 += 40 + TALENT:85 * 13 - TALENT:84 * 13 + TALENT:16 * 4 - TALENT:14 * 4
ELSEIF Z > 300
	BASE:7 += 20 + TALENT:85 * 10 - TALENT:84 * 10 + TALENT:16 * 2 - TALENT:14 * 2
ENDIF

;哀願/暴れる/要求を拒否
IF TFLAG:94 == 3
	BASE:7 += 25 - TALENT:87 * 25 + TALENT:86 * 10 + TALENT:83 * 25 - TALENT:MASTER:78 * 5 - TALENT:MASTER:79 * 10 + PALAM:9 / 250
ELSEIF TFLAG:94 == 4
	BASE:7 += 50 + PALAM:9 / 200 - TALENT:84 * 15 + TALENT:85 * 20 + TALENT:15 * 20 + MARK:3 * 2
ELSEIF TFLAG:94 == 5
	BASE:7 += 25 + PALAM:6 / 200 - TALENT:84 * 25 + TALENT:85 * 15 + TALENT:87 * 5 + TALENT:15 * 10 + MARK:2
ENDIF

;狂気でランダム変動、この設定に賛同できない人はかなりいるかもしれません…
SIF TALENT:89
	BASE:7 += (10 - RAND:20) * (1 + RAND:10)

SIF BASE:7 > MAXBASE:7 / 3 && UP:9 < 10
	BASE:7 -= BASE:8 / 20
SIF BASE:7 > MAXBASE:7
	BASE:7 = MAXBASE:7
SIF BASE:7 < 0
	BASE:7 = 0

;いらいらによる理性消耗
SIF BASE:7 > 700
	LOSEBASE:5 += (BASE:7 - 500) / (5 + TALENT:89 * RAND:3)

BASE:0 -= LOSEBASE:0
BASE:1 -= LOSEBASE:1
BASE:2 -= LOSEBASE:2
BASE:3 -= LOSEBASE:3
BASE:4 -= LOSEBASE:4
SIF TALENT:20
	TIMES LOSEBASE:5 , 0.75
SIF TALENT:21
	TIMES LOSEBASE:5 , 1.25
BASE:5 -= LOSEBASE:5

;────────────────────────────────────
;コマンド実行時の口上
;────────────────────────────────────
CALL KOJO_REACT
	DRAWLINE

;────────────────────────────────────
;絶頂時の口上
;────────────────────────────────────
PRINTL 
RESULT = 0
SIF MASTER_EX(79) || TARGET_EX(15)
	CALL KOJO_EVENT(20)
IF RESULT
	PRINTL 
	DRAWLINE
ENDIF

;RESULT = 0
;SIF MASTER_EX(4) || TARGET_EX(4)
;	CALL KOJO_EVENT_ASSI(20)
;
;RESULT = 0
;IF MASTER_EX(8) || TARGET_EX(8)
;	ASSI = ASSI:2
;	CALL KOJO_EVENT_ASSI(20)
;	ASSI = ASSI:1
;ENDIF
;
;RESULT = 0
;IF MASTER_EX(16) || TARGET_EX(16)
;	ASSI = ASSI:3
;	CALL KOJO_EVENT_ASSI(20)
;	ASSI = ASSI:1 < 0 ? ASSI:3 # ASSI:1
;ENDIF

;────────────────────────────────────
;絶頂関連の表示
;────────────────────────────────────
CALL PRINT_SOURCE00
CALL PRINT_SOURCE40

;────────────────────────────────────
;メッセージ表示（未実装）
;────────────────────────────────────
;CALL TRAIN_MESSAGE
;SIF FLAG:7 > 0
;	CALL KOJO_MESSAGE_PALAMCNG

;────────────────────────────────────
;刻印取得のチェック、そして刻印による素質変動（条件設定はかなり適当…調整が必要と思うなら連絡してください）
;────────────────────────────────────
;刻印は一回に一段階まであがる仕様です
VARSET LOCAL

;刻印:苦痛刻印
SELECTCASE MARK:MASTER:0
	CASE 0
		LOCAL = UP:10 > 300
	CASE 1
		LOCAL = UP:10 > 600
	CASE 2
		LOCAL = UP:10 > 1000
	CASE 3
		LOCAL = UP:10 > 1400
	CASE 4
		LOCAL = UP:10 > 1800
	CASE 5
		LOCAL = UP:10 > 2200
	CASE 6
		LOCAL = UP:10 > 2600
	CASE 7
		LOCAL = UP:10 > 3000
	CASE 8
		LOCAL = UP:10 > 3500
	CASE 9
		LOCAL = UP:10 > 4000
ENDSELECT
SIF LOCAL
	LOCAL:10 = ++MARK:MASTER:0

;刻印:快楽刻印 今の仕様では快楽刻印がもりもり上がるので調整　要検討
LOCAL = 0
SELECTCASE MARK:MASTER:1
	CASE 0
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 600
	CASE 1
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 1200 && MARK:2 > 0
	CASE 2
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 2000 && MARK:2 > 1
	CASE 3
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 2800 && MARK:2 > 2
	CASE 4
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 3700 && MARK:2 > 3
	CASE 5
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 4400 && MARK:2 > 4
	CASE 6
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 5200 && MARK:2 > 5
	CASE 7
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 6000 && MARK:2 > 6
	CASE 8
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 6900 && MARK:2 > 7
	CASE 9
		LOCAL = UP:0 + UP:1 + UP:2 + UP:3 > 8000 && MARK:2 > 8
ENDSELECT
SIF LOCAL
	LOCAL:11 = ++MARK:MASTER:1

;刻印:屈服刻印 イベント制です。特定の条件でターンエンドすると屈服ポイントが貯まりだんだんと篭絡されていきます。
;好意的反応
IF TFLAG:94 == 2 && !GETBIT(CFLAG:300, 0)
	PRINTL 【原谅的心】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 0
ENDIF
;裸に剥いた
IF !(TEQUIP:MASTER:9 || TEQUIP:MASTER:2 || TEQUIP:MASTER:3 || TEQUIP:MASTER:4 || TEQUIP:MASTER:5 || TEQUIP:MASTER:6) && !GETBIT(CFLAG:300, 1)
	PRINTL 【被脱光衣服】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 1
ENDIF
;勃起させた
IF TFLAG:131 >= 1000 && !GETBIT(CFLAG:300, 2)
	PRINTL 【勃起】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 2
ENDIF
;快Ｃを1000以上与えた
IF PALAM:0 >= 1000 && !GETBIT(CFLAG:300, 3)
	PRINTL 【快Ｃ1000以上】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 3
ENDIF
;快Ｖを1000以上与えた
IF PALAM:1 >= 1000 && !GETBIT(CFLAG:300, 4)
	PRINTL 【快Ｖ1000以上】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 4
ENDIF
;快Ａを1000以上与えた
IF PALAM:2 >= 1000 && !GETBIT(CFLAG:300, 5)
	PRINTL 【快Ａ1000以上】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 5
ENDIF
;快Ｂを1000以上与えた
IF PALAM:3 >= 1000 && !GETBIT(CFLAG:300, 6)
	PRINTL 【快Ｂ1000以上】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 6
ENDIF
;苦痛、恐怖
IF (UP:7 || UP:10) && !GETBIT(CFLAG:300, 7)
	PRINTL 【威圧】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 7
ENDIF
;ムチで叩いた
IF TFLAG:90 == 61 && !GETBIT(CFLAG:300, 8)
	PRINTL 【鞭子】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 8
ENDIF
;アナル調教
IF UP:2 && !GETBIT(CFLAG:300, 9)
	PRINTL 【肛门调教】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 9
ENDIF
;拘束した
IF (TEQUIP:40 || TEQUIP:46 || TEQUIP:47) && !GETBIT(CFLAG:300, 10)
	PRINTL 【拘束】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 10
ENDIF
;キス
IF TFLAG:90 == 15 && !GETBIT(CFLAG:300, 11)
	PRINTL 【亲吻】屈服点+1
	CFLAG:301 += 1
	SETBIT CFLAG:300, 11
ENDIF
;Ｃ絶頂
IF NOWEX:0 && !GETBIT(CFLAG:300, 12)
	PRINTL 【Ｃ绝顶】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 12
ENDIF
;Ｖ絶頂
IF NOWEX:1 && !GETBIT(CFLAG:300, 13)
	PRINTL 【Ｖ绝顶】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 13
ENDIF
;Ａ絶頂
IF NOWEX:2 && !GETBIT(CFLAG:300, 14)
	PRINTL 【Ａ绝顶】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 14
ENDIF
;Ｂ絶頂
IF NOWEX:3 && !GETBIT(CFLAG:300, 15)
	PRINTL 【Ｂ绝顶】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 15
ENDIF
;野外プレイ
IF TEQUIP:52 && !GETBIT(CFLAG:300, 16)
	PRINTL 【野外PLAY】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 16
ENDIF
;羞恥プレイ
IF TEQUIP:56 && !GETBIT(CFLAG:300, 17)
	PRINTL 【羞耻play】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 17
ENDIF
;放尿させた
IF TFLAG:90 == 45 && (TFLAG:94 == 1 || TFLAG:94 == 2) && !GETBIT(CFLAG:300, 18)
	PRINTL 【放尿】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 18
ENDIF
;射精
IF NOWEX:11 && !GETBIT(CFLAG:300, 19)
	PRINTL 【射精】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 19
ENDIF
;大量射精
IF NOWEX:11 == 3 && !GETBIT(CFLAG:300, 20)
	PRINTL 【大量射精】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 20
ENDIF
;射精(早漏)
IF NOWEX:11 == 4 && !GETBIT(CFLAG:300, 21)
	PRINTL 【射精(早漏)】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 21
ENDIF
;膣内射精された
IF TFLAG:105 == 1 && !GETBIT(CFLAG:300, 22)
	PRINTL 【腔内射精された】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 22
ENDIF
;バイブ
IF TEQUIP:20 && !GETBIT(CFLAG:300, 23)
	PRINTL 【振动棒】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 23
ENDIF
;アナルバイブ、ビーズ
IF (TEQUIP:25 || TEQUIP:26) && !GETBIT(CFLAG:300, 24)
	PRINTL 【肛门异物插入】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 24
ENDIF
;膣内射精
IF TFLAG:105 == 2 && !GETBIT(CFLAG:300, 25)
	PRINTL 【腔内射精】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 25
ENDIF
;自慰絶頂
IF (NOWEX:0 || NOWEX:1 || NOWEX:2 || NOWEX:3) && (TEQUIP:69 & 1) && !GETBIT(CFLAG:300, 26)
	PRINTL 【自慰绝顶】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 26
ENDIF
;口内射精された
IF (TFLAG:90 == 51 || TFLAG:90 == 56) && NOWEX:51 && !GETBIT(CFLAG:300, 27)
	PRINTL 【被口内射精】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 27
ENDIF
;口内射精
IF NOWEX:11 && TFLAG:90 == 12 && !GETBIT(CFLAG:300, 28)
	PRINTL 【口内射精】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 28
ENDIF
;積極奉仕
IF (TFLAG:80 == 5 || TFLAG:90 == 55 || TFLAG:90 == 56) && TFLAG:94 == 2 && !GETBIT(CFLAG:300, 29)
	PRINTL 【积极侍奉】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 29
ENDIF
;性交
IF (TEQUIP:70 || TEQUIP:71) && !GETBIT(CFLAG:300, 30)
	PRINTL 【性交】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 30
ENDIF
;腰をふる
IF (TEQUIP:70 || TEQUIP:71) && TFLAG:94 == 2 && !GETBIT(CFLAG:300, 31)
	PRINTL 【摇腰】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 31
ENDIF
;情欲
IF TFLAG:61 == 5 && !GETBIT(CFLAG:300, 32)
	PRINTL 【情欲】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 32
ENDIF
;焦らしプレイ
IF TFLAG:126 > 1 && !GETBIT(CFLAG:300, 33)
	PRINTL 【戏弄play】屈服点+2
	CFLAG:301 += 2
	SETBIT CFLAG:300, 33
ENDIF
;拘束絶頂
IF (NOWEX:0 || NOWEX:1 || NOWEX:2 || NOWEX:3) && (TEQUIP:40 || TEQUIP:46 || TEQUIP:47) && !GETBIT(CFLAG:300, 34)
	PRINTL 【拘束绝顶】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 34
ENDIF
;浣腸
IF TEQUIP:27 && !GETBIT(CFLAG:300, 35)
	PRINTL 【灌肠】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 35
ENDIF
;失禁
IF TFLAG:107 && !GETBIT(CFLAG:300, 36)
	PRINTL 【失禁】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 36
ENDIF
;多重絶頂
IF ((NOWEX:0 != 0) + (NOWEX:1 != 0) + (NOWEX:2 != 0) + (NOWEX:3 != 0)) > 1 && !GETBIT(CFLAG:300, 37)
	PRINTL 【多重绝顶】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 37
ENDIF
;同時絶頂
IF (NOWEX:0 || NOWEX:1 || NOWEX:2 || NOWEX:3) && (NOWEX:40 || NOWEX:41 || NOWEX:42 || NOWEX:43) && !GETBIT(CFLAG:300, 38)
	PRINTL 【同时绝顶】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 38
ENDIF
;同時絶頂(性交)
IF ((TEQUIP:70 && (NOWEX:1 || NOWEX:2) && NOWEX:51) || (TEQUIP:71 && NOWEX:11 && (NOWEX:41 || NOWEX:42))) && !GETBIT(CFLAG:300, 39)
	PRINTL 【同时绝顶(性交)】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 39
ENDIF
;膣内射精絶頂
IF TFLAG:105 == 1 && NOWEX:1 && !GETBIT(CFLAG:300, 39)
	PRINTL 【腔内射精绝顶】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 39
ENDIF
;噴乳
IF NOWEX:10 && !GETBIT(CFLAG:300, 40)
	PRINTL 【喷乳】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 40
ENDIF
;アナルへ射精
IF (TFLAG:105 == 6 || TFLAG:105 == 8) && !GETBIT(CFLAG:300, 41)
	PRINTL 【肛门内射精】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 41
ENDIF
;アナルに射精
IF TFLAG:105 == 5 && !GETBIT(CFLAG:300, 42)
	PRINTL 【被肛门内射精】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 42
ENDIF
;抜かずの二発
IF TFLAG:106 && NOWEX:11 && !GETBIT(CFLAG:300, 43)
	PRINTL 【不拔出的二发】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 43
ENDIF
;空射精
IF NOWEX:11 == 1 && !GETBIT(CFLAG:300, 44)
	PRINTL 【空射精】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 44
ENDIF
;Ａ責射精
IF NOWEX:11 == 5 && !GETBIT(CFLAG:300, 45)
	PRINTL 【Ａ点玩弄射精】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 45
ENDIF
;アナルセックス(受け)
IF TEQUIP:70 == 6 && !GETBIT(CFLAG:300, 46)
	PRINTL 【肛门性交(受)】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 46
ENDIF
;アナルセックス(攻め)
IF TEQUIP:71 == 6 && !GETBIT(CFLAG:300, 47)
	PRINTL 【肛门性交(攻)】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 47
ENDIF
;お仕置き
IF TFLAG:69 && (UP:7 || UP:10) && !GETBIT(CFLAG:300, 48)
	PRINTL 【惩罚】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 48
ENDIF
;イかせて！
IF SELECTCOM == 70 && !GETBIT(CFLAG:300, 49)
	PRINTL 【让我去！】屈服点+3
	CFLAG:301 += 3
	SETBIT CFLAG:300, 49
ENDIF
;排泄
IF TFLAG:90 == 68 && TFLAG:93 == 1 && !GETBIT(CFLAG:300, 50)
	PRINTL 【排泄】屈服点+5
	CFLAG:301 += 5
	SETBIT CFLAG:300, 50
ENDIF
;公衆便所
IF TFLAG:90 == 74 && (TFLAG:93 == 2 || TFLAG:93 == 3) && !GETBIT(CFLAG:300, 51)
	PRINTL 【公共厕所】屈服点+5
	CFLAG:301 += 5
	SETBIT CFLAG:300, 51
ENDIF
;三角木馬
IF TEQUIP:43 && !GETBIT(CFLAG:300, 52)
	PRINTL 【三角木馬】屈服点+5
	CFLAG:301 += 5
	SETBIT CFLAG:300, 52
ENDIF
;Ｖ拡張
IF (TFLAG:90 == 70 || TFLAG:90 == 72) && !GETBIT(CFLAG:300, 53)
	PRINTL 【Ｖ扩张】屈服点+5
	CFLAG:301 += 5
	SETBIT CFLAG:300, 53
ENDIF
;Ａ拡張
IF  (TFLAG:90 == 71 || TFLAG:90 == 72) && !GETBIT(CFLAG:300, 54)
	PRINTL 【Ａ扩张】屈服点+5
	CFLAG:301 += 5
	SETBIT CFLAG:300, 54
ENDIF
;抜かずの三発
IF TFLAG:106 > 1 && NOWEX:11 && !GETBIT(CFLAG:300, 55)
	PRINTL 【不拔出的三发】屈服点+5
	CFLAG:301 += 5
	SETBIT CFLAG:300, 55
ENDIF
;56失神はEVENTCOMENDで
;57,58処女、童貞喪失はEXPCHECで
;監禁中
IF FLAG:1701 && !GETBIT(CFLAG:300, 59)
	PRINTL 【监禁中】屈服点+10
	CFLAG:301 += 10
	SETBIT CFLAG:300, 59
ENDIF

CFLAG:MASTER:300 |= CFLAG:300
LOCAL:1 = CFLAG:301
SIF TALENT:MASTER:男性
	TIMES LOCAL:1, 1.10
SIF TALENT:MASTER:扶她 !| TALENT:MASTER:男性
	TIMES LOCAL:1, 1.20
SIF TALENT:MASTER:胆怯
	TIMES LOCAL:1, 1.05
SIF TALENT:MASTER:刚强
	TIMES LOCAL:1, 0.95
SIF TALENT:MASTER:反抗
	TIMES LOCAL:1, 0.95
SIF TALENT:MASTER:高傲
	TIMES LOCAL:1, 1.05
SIF TALENT:MASTER:自卑
	TIMES LOCAL:1, 0.95
SIF TALENT:威圧感
	TIMES LOCAL:1, 1.50

LOCAL = 0
SELECTCASE MARK:2
	CASE 0
		LOCAL = LOCAL:1
	CASE 1
		LOCAL = LOCAL:1 > 5
	CASE 2
		LOCAL = LOCAL:1 > 10
	CASE 3
		LOCAL = LOCAL:1 > 18
	CASE 4
		LOCAL = LOCAL:1 > 28
	CASE 5
		LOCAL = LOCAL:1 > 40
	CASE 6
		LOCAL = LOCAL:1 > 55
	CASE 7
		LOCAL = LOCAL:1 > 71
	CASE 8
		LOCAL = LOCAL:1 > 90
	CASE 9
		LOCAL = LOCAL:1 > 100
ENDSELECT
SIF LOCAL
	LOCAL:12 = ++MARK:2

;刻印:反抗刻印
IF TFLAG:94 != 1 && TFLAG:94 != 2
	LOCAL = 0
	SELECTCASE MARK:3
		CASE 0
			LOCAL = UP:9 > 300
		CASE 1
			LOCAL = UP:9 > 600
		CASE 2
			LOCAL = UP:9 > 1000
		CASE 3
			LOCAL = UP:9 > 1400
		CASE 4
			LOCAL = UP:9 > 1800
		CASE 5
			LOCAL = UP:9 > 2200
		CASE 6
			LOCAL = UP:9 > 2600
		CASE 7
			LOCAL = UP:9 > 3000
		CASE 8
			LOCAL = UP:9 > 3500
		CASE 9
			LOCAL = UP:9 > 4000
	ENDSELECT
	SIF MARK:3 > 10 - MARK:5
		LOCAL = 0
	SIF LOCAL
		LOCAL:13 = ++MARK:3
ENDIF

;刻印:トラウマ
LOCAL = 0
SELECTCASE MARK:MASTER:4
	CASE 0
		LOCAL = UP:7 > 300
	CASE 1
		LOCAL = UP:7 > 600
	CASE 2
		LOCAL = UP:7 > 1000
	CASE 3
		LOCAL = UP:7 > 1400
	CASE 4
		LOCAL = UP:7 > 1800
	CASE 5
		LOCAL = UP:7 > 2200
	CASE 6
		LOCAL = UP:7 > 2600
	CASE 7
		LOCAL = UP:7 > 3000
	CASE 8
		LOCAL = UP:7 > 3500
	CASE 9
		LOCAL = UP:7 > 4000
ENDSELECT
IF LOCAL
	LOCAL:14 = ++MARK:MASTER:4
	CFLAG:MASTER:8 = TFLAG:90
ENDIF

;────────────────────────────────────
;刻印取得口上と表示
;────────────────────────────────────
SIF LOCAL:10 || LOCAL:11 || LOCAL:12 || LOCAL:13 || LOCAL:14
	PRINTL 
RESULT = 0
CALL KOJO_MARK(LOCAL:10, LOCAL:11, LOCAL:12, LOCAL:13, LOCAL:14)

FOR LOCAL, 10, 15
	IF LOCAL:LOCAL
		PRINTFORML %MARKNAME:(LOCAL - 10)%は{LOCAL:LOCAL}になった
		SIF LOCAL - 10 == 2
			CFLAG:MASTER:303 += POWER(LOCAL:LOCAL,2) - POWER(LOCAL:LOCAL-1,2)
		IF LOCAL == 10 && LOCAL:LOCAL == 9 && TALENT:MASTER:12
			TALENT:MASTER:12 = 0
			PRINTFORMW 然后，%CALLNAME:MASTER%失去了[刚强]
		ELSEIF LOCAL == 11 && LOCAL:LOCAL == 5 && TALENT:MASTER:71
			TALENT:MASTER:71 = 0
			PRINTFORMW 然后，%CALLNAME:MASTER%失去了[否定快感]
		ELSEIF LOCAL == 11 && LOCAL:LOCAL == 7 && TALENT:MASTER:32
			TALENT:MASTER:32 = 0
			PRINTFORMW 然后，%CALLNAME:MASTER%失去了[性压抑]
		ELSEIF LOCAL == 11 && LOCAL:LOCAL == 10 && !TALENT:MASTER:70
			TALENT:MASTER:70 = 1
			PRINTFORMW 然后，%CALLNAME:MASTER%获得了[接受快感]
		ELSEIF LOCAL == 12 && LOCAL:LOCAL == 7 && TALENT:MASTER:11
			TALENT:MASTER:11 = 0
			PRINTFORMW 然后，%CALLNAME:MASTER%失去了[反抗]
		ELSEIF LOCAL == 12 && LOCAL:LOCAL == 10
			;エンディングで使用する屈服刻印Lv10のカウント
			FLAG:5002 += 1
			IF TALENT:MASTER:15
				TALENT:MASTER:15 = 0
				PRINTFORMW 然后，%CALLNAME:MASTER%失去了[高傲]
			ENDIF
		ELSEIF LOCAL == 14 && LOCAL:LOCAL == 10
			IF !TALENT:MASTER:10
				TALENT:MASTER:10 = 1
				PRINTFORMW 然后，%CALLNAME:MASTER%获得了[胆怯]
			ELSEIF !TALENT:MASTER:89 && (TALENT:MASTER:88 || !RAND:3)
				TALENT:MASTER:89 = 1
				PRINTFORMW 然后，%CALLNAME:MASTER%获得了[疯癫]
			ELSEIF !TALENT:MASTER:88
				TALENT:MASTER:88 = 1
				PRINTFORMW 然后，%CALLNAME:MASTER%获得了幼儿退行
			ENDIF
		ENDIF
	ENDIF
NEXT

;────────────────────────────────────
;経験のチェック、調教ソースの表示
;────────────────────────────────────
CALL EXP_CHECK
FOR LOCAL, 0, 50
	PRINTFORM \@ SOURCE:LOCAL > 0 ? %SOURCENAME:LOCAL%({SOURCE:LOCAL}) # \@
NEXT
PRINTL 

;────────────────────────────────────
;数値変動の表示
;────────────────────────────────────
;相性
;SIF RELATION:(NO:PLAYER) && RELATION:(NO:PLAYER) != 100
;	PRINTFORML ＜相性%TOSTR(RELATION:(NO:PLAYER), "0\\.00")%倍＞

;前回の調教者が助手か主人か
;TFLAG:50 = ASSIPLAY != 0

;体力・気力の減少
PRINTFORML 体力-{LOSEBASE:90} 气力-{LOSEBASE:91} 理性-{LOSEBASE:95}
PRINTFORML （%CALLNAME:TARGET%）体力-{LOSEBASE:0} 气力-{LOSEBASE:1}

;体力・気力０、疲弊のメッセージもあるのでこれは必要ないと思います
;SIF BASE:MASTER:0 <= 0
;	PRINTL ★体力０★
;SIF BASE:MASTER:1 <= 0
;	PRINTL ★気力０★
;SIF BASE:0 <= 0
;	PRINTL ★体力０★（調教者）
;SIF BASE:1 <= 0
;	PRINTL ★気力０★（調教者）

;────────────────────────────────────
;能力上昇の呼び出し
;────────────────────────────────────
SIF (UP:6 + UP:8 > 0 || UP:7 > 0) && TFLAG:94 < 4
	CALL ABLUP_0M
SIF UP:5 > 0
	CALL ABLUP_1M
SIF TFLAG:80 == 5 && UP:40 + UP:41 + UP:42 + UP:43 > 0
	CALL ABLUP_2M

FOR LOCAL, 0, 4
	SIF UP:LOCAL > 0
		CALLFORM ABLUP_{3 + LOCAL}M
NEXT

SIF ((TFLAG:80 == 5 || TFLAG:80 == 10  || TEQUIP:70 == 3 ) && TFLAG:94 == 2) && UP:0 + UP:1 + UP:2 + UP:3 + UP:5 > 1000
	CALL ABLUP_7M
SIF UP:11 > 0
	CALL ABLUP_8M

SIF TALENT:MASTER:122 == 0 && TALENT:122 == 0
	CALL ABLUP_9M
SIF TALENT:MASTER:122 && TALENT:122
	CALL ABLUP_10M
SIF UP:10 + UP:12 > UP:13
	CALL ABLUP_11M

SIF TEQUIP:69 == 1 || TEQUIP:69 == 3
	CALL ABLUP_12M
SIF NOWEX:51 || NOWEX:11
	CALL ABLUP_13M
SIF TALENT:MASTER:122 == 0 && TALENT:122 == 0
	CALL ABLUP_14M
SIF UP:10 > UP:13
	CALL ABLUP_15M
SIF TEQUIP:40 || TEQUIP:41 || TEQUIP:42 || TEQUIP:43 || TEQUIP:46 || TEQUIP:47
	CALL ABLUP_16M

SIF UP:40 + UP:41 + UP:42 + UP:43 > 0
	CALL ABLUP_1T
SIF NOWEX:0 + NOWEX:1 + NOWEX:2 + NOWEX:3 > 0
	CALL ABLUP_2T

FOR LOCAL, 0, 4
	SIF UP:(40 + LOCAL) > 0
		CALLFORM ABLUP_{3 + LOCAL}T
NEXT

SIF TFLAG:80 == 1 || TFLAG:80 == 2
	CALL ABLUP_7T
;SIF ???
	;CALL ABLUP_8T

SIF TALENT:MASTER:122 == 0 && TALENT:122 == 0
	CALL ABLUP_9T
SIF TALENT:MASTER:122 && TALENT:122
	CALL ABLUP_10T
SIF NOWEX:51 || NOWEX:11
	CALL ABLUP_13T
SIF TALENT:MASTER:122 == 0 && TALENT:122 == 0
	CALL ABLUP_14T

SIF 0 <= TFLAG:80 && TFLAG:80 <= 8
	CALLFORM ABLUP_{20 + TFLAG:80}T
SIF TFLAG:80 == 10
	CALL ABLUP_23T
