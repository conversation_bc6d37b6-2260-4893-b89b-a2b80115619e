﻿;────────────────────────────────────
;会話 (気分0/好意1/知識2/慰め3/脅す4/衣装5/合意6/仕置き7/怒鳴る8/嗤う9/罵倒66)
;────────────────────────────────────
@ACT_M0
;基準値
LOCAL:0 = 10
LOCAL:1 = 10
LOCAL:2 = 10
LOCAL:3 = 10
LOCAL:4 = 10
LOCAL:5 = 0
LOCAL:6 = 10
LOCAL:7 = 10
LOCAL:8 = 10
LOCAL:9 = 10
LOCAL:66 = 0

SIF TFLAG:80 == 4
	LOCAL:66 += 10

;調教方針(1=休憩/2=ソフト/3=ノーマル/4=ハード/5=異常)
SELECTCASE TFLAG:70
	CASE 2
		LOCAL:3 += TFLAG:69 ? 0 # 3
	CASE 3
		LOCAL:0 += 2
		LOCAL:1 += TFLAG:69 ? 0 # 1
	CASE 4
		LOCAL:4 += TFLAG:69 ? 0 # 3
		LOCAL:66 += 10
ENDSELECT

;────────────────────────────────────
;素質による変動
;────────────────────────────────────
;調教者が大人しい/生意気
IF TALENT:14
	LOCAL:3 += RAND:2
	LOCAL:8 -= 4 + RAND:3
ELSEIF TALENT:16
	LOCAL:3 -= RAND:3
	LOCAL:8 += 1 + RAND:2
ENDIF

;調教者がプライド高い/低い
IF TALENT:15
	LOCAL:1 -= RAND:3
	LOCAL:3 -= RAND:5
	LOCAL:5 += 3
	LOCAL:66 += RAND:3
ELSEIF TALENT:17
	LOCAL:1 += RAND:3
	LOCAL:3 += RAND:5
	LOCAL:9 -= 5
ENDIF

;調教者が自制的/衝動的
IF TALENT:20
	LOCAL:8 -= 5
ELSEIF TALENT:21
	LOCAL:8 += 4 + RAND:3
ENDIF

;調教者が無関心/好奇心
IF TALENT:22
	LOCAL:0 -= 5
	LOCAL:1 -= 5
	LOCAL:2 -= 5
ELSEIF TALENT:23
	LOCAL:0 += 5
	LOCAL:1 += 5
	LOCAL:2 += 5
ENDIF

;調教者が感情乏しい/情緒豊か
IF TALENT:24
	LOCAL:3 -= 3 + RAND:5
	LOCAL:4 -= 4 + RAND:3
	LOCAL:8 -= 6
ELSEIF TALENT:25
	LOCAL:3 += 4
	LOCAL:4 += 3 + RAND:3
	LOCAL:8 += 3 + RAND:5
	LOCAL:9 += RAND:5
ENDIF

;調教者が楽観的/悲観的
IF TALENT:26
	LOCAL:1 += 3
	LOCAL:5 += RAND:3
	LOCAL:6 += 5
ELSEIF TALENT:7
	LOCAL:1 -= 2 + RAND:3
	LOCAL:5 -= RAND:3
	LOCAL:6 -= RAND:7
ENDIF

;調教者が一線越えない
IF TALENT:28
	LOCAL:1 -= 3
	LOCAL:6 -= 5
	LOCAL:9 -= 5
ENDIF

;調教者が目立ちたがり
IF TALENT:29
	LOCAL:1 += RAND:8
	LOCAL:2 += 1 + RAND:6
	LOCAL:4 += RAND:7
	LOCAL:7 += 3
	LOCAL:8 += RAND:5
	LOCAL:9 += RAND:5
ENDIF

;調教者が貞操観念/貞操無頓着
IF TALENT:30
	LOCAL:6 += 7
ELSEIF TALENT:31
	LOCAL:6 -= 7
ENDIF

;調教者が抑圧/解放
IF TALENT:32
	LOCAL:2 -= 10
	LOCAL:6 -= 5
ELSEIF TALENT:33
	LOCAL:2 += RAND:10
	LOCAL:6 += RAND:5
ENDIF

;調教者が恥じらい/恥薄い
IF TALENT:34
	LOCAL:5 -= 5
	LOCAL:6 -= 5
ELSEIF TALENT:35
	LOCAL:5 += 5
	LOCAL:6 += 5
ENDIF

;調教者が習得早い/習得遅い
IF TALENT:50
	LOCAL:2 += 5
ELSEIF TALENT:51
	LOCAL:2 -= 5
ENDIF

;調教者が献身的
SIF TALENT:63
	LOCAL:3 += 5

;調教者が倒錯的
IF TALENT:80
	LOCAL:4 += RAND:5
	LOCAL:7 += 5
	LOCAL:66 += RAND:5
ENDIF

;調教者が両刀
IF TALENT:81
	LOCAL:1 += (TALENT:122 ^^ TALENT:MASTER:122) ? 0 # RAND:3
ELSE
	LOCAL:1 += (TALENT:122 ^^ TALENT:MASTER:122) ? 0 # -6
ENDIF

;調教者が男嫌いかつ、調教対象がオトコ
IF TALENT:82 && TALENT:MASTER:122
	LOCAL:1 -= 5
	LOCAL:3 -= 3
	LOCAL:6 -= 5
ENDIF

;調教者がサド
IF TALENT:83
	LOCAL:3 -= 3
	LOCAL:4 += 5
	LOCAL:7 += 7
	LOCAL:66 += 7
ENDIF

;調教者が慎重/短気
IF TALENT:84
	LOCAL:6 -= RAND:5
	LOCAL:7 -= 3
	LOCAL:8 -= 5
ELSEIF TALENT:85
	LOCAL:6 += RAND:5
	LOCAL:8 += 5
ENDIF

;調教者が意地悪/心根優しい
IF TALENT:86
	LOCAL:3 += 5
	LOCAL:4 -= 5
	LOCAL:7 -= 5
ELSEIF TALENT:87
	LOCAL:3 -= 5
	LOCAL:4 += RAND:7
	LOCAL:7 += 5
	LOCAL:9 += 5
	LOCAL:66 += 5
ENDIF

;調教者が幼稚
IF TALENT:88
	LOCAL:2 -= 5
	LOCAL:6 -= 3
	LOCAL:8 += RAND:3
	LOCAL:9 += RAND:5
ENDIF

;調教者が狂気
IF TALENT:89
	LOCAL:8 += RAND:7
	LOCAL:9 += 1 + RAND:9
ENDIF

;調教者が威圧感
SIF TALENT:90
	LOCAL:4 += RAND:6

;調教者が魅惑
SIF TALENT:91
	LOCAL:1 += RAND:6

;調教者が親しみやすい/近寄り難い
IF TALENT:92
	LOCAL:0 += 3
	LOCAL:1 += 3
	LOCAL:3 += 3
ELSEIF TALENT:93
	LOCAL:0 -= 3
	LOCAL:1 -= 3
	LOCAL:3 -= 3
ENDIF

;調教者が空気を読む程度の能力
SIF TALENT:132
	LOCAL:0 -= 5

;調教者が思考を読む程度の能力…って、どう見てもさとり様です。本当に(ry
;ところで、心を読む夢魔っていましたっけ？
;SIF NO:TARGET == 61
;	LOCAL:0 -= 10

;調教対象が臆病
SIF TALENT:MASTER:10
	LOCAL:3 += RAND:6

;調教対象が気丈
SIF TALENT:MASTER:12
	LOCAL:4 += RAND:6

;調教対象が感情乏しい/情緒豊か
IF TALENT:MASTER:24
	LOCAL:0 += RAND:5
	LOCAL:1 += RAND:3
ELSEIF TALENT:MASTER:25
	LOCAL:0 -= RAND:5
	LOCAL:1 -= RAND:3
ENDIF

;調教対象が楽観的/悲観的
IF TALENT:MASTER:26
	LOCAL:3 -= RAND:5
	LOCAL:4 += RAND:3
ELSEIF TALENT:MASTER:27
	LOCAL:3 += RAND:3
	LOCAL:4 -= RAND:5
ENDIF

;────────────────────────────────────
;能力、パラメーターによる変動
;────────────────────────────────────
;調教対象の従順
SELECTCASE ABL:MASTER:0
	CASE IS > 4
		LOCAL:4 -= 5
	CASE 4
		LOCAL:4 -= 4
	CASE 3
		LOCAL:4 -= 3
	CASE 2
		LOCAL:4 -= 2
	CASE 1
		LOCAL:4 -= 1
ENDSELECT

;調教対象の欲望
SELECTCASE ABL:MASTER:1
	CASE IS > 4
		LOCAL:1 += RAND:8
	CASE 4
		LOCAL:1 += RAND:6
	CASE 3
		LOCAL:1 += RAND:4
	CASE 2
		LOCAL:1 += RAND:3
	CASE 1
		LOCAL:1 += RAND:2
ENDSELECT

;調教対象の露出癖
SELECTCASE ABL:MASTER:8
	CASE IS > 4
		LOCAL:5 += RAND:6
	CASE 4
		LOCAL:5 += RAND:5
	CASE 3
		LOCAL:5 += RAND:4
	CASE 2
		LOCAL:5 += RAND:3
	CASE 1
		LOCAL:5 += RAND:2
ENDSELECT

;快感と好意。快ＣＶＡＢが高いときに自分が好きか聞く…そういう手口か
SELECTCASE (PALAM:0 + PALAM:1 + PALAM:2 + PALAM:3) * 2
	CASE IS > 60000
		LOCAL:1 += 7
	CASE IS > 48000
		LOCAL:1 += 6
	CASE IS > 36000
		LOCAL:1 += 5
	CASE IS > 24000
		LOCAL:1 += 4
	CASE IS > 12000
		LOCAL:1 += 3
	CASE IS > 6000
		LOCAL:1 += 2
	CASE IS > 3000
		LOCAL:1 += 1
ENDSELECT

;反抗
SELECTCASE PALAM:9
	CASE IS > 7500
		LOCAL:4 += 7
		LOCAL:7 += 7
		LOCAL:8 += RAND:9
	CASE IS > 6000
		LOCAL:4 += 5
		LOCAL:7 += 5
		LOCAL:8 += RAND:7
	CASE IS > 4500
		LOCAL:4 += 3
		LOCAL:7 += 3
		LOCAL:8 += RAND:5
	CASE IS > 3000
		LOCAL:4 += 1
		LOCAL:7 += 1
		LOCAL:8 += RAND:3
ENDSELECT

;恥情
SELECTCASE PALAM:11
	CASE IS > 7500
		LOCAL:0 += RAND:12
	CASE IS > 6000
		LOCAL:0 += RAND:9
	CASE IS > 5000
		LOCAL:0 += RAND:7
	CASE IS > 4000
		LOCAL:0 += RAND:5
	CASE IS > 3000
		LOCAL:0 += RAND:3
ENDSELECT

;恐怖
SELECTCASE PALAM:7
	CASE IS > 7500
		LOCAL:3 += 3 + TALENT:87 * 2
	CASE IS > 5000
		LOCAL:3 += 2 + TALENT:87 * 2
	CASE IS > 2500
		LOCAL:3 += 1 + TALENT:87
ENDSELECT

;抑鬱
SELECTCASE PALAM:13
	CASE IS > 7500
		LOCAL:3 += 3 + TALENT:87 * 2
	CASE IS > 5000
		LOCAL:3 += 2 + TALENT:87 * 2
	CASE IS > 2500
		LOCAL:3 += 1 + TALENT:87
ENDSELECT

;調教対象の反発刻印
SELECTCASE MARK:3
	CASE IS > 9
		LOCAL:4 += RAND:10 + 4
	CASE 9
		LOCAL:4 += RAND:9 + 3
	CASE 8
		LOCAL:4 += RAND:8 + 3
	CASE 7
		LOCAL:4 += RAND:7 + 2
	CASE 6
		LOCAL:4 += RAND:6 + 2
	CASE 5
		LOCAL:4 += RAND:5 + 1
	CASE 4
		LOCAL:4 += RAND:4 + 1
	CASE 3
		LOCAL:4 += RAND:3 + 1
	CASE 2
		LOCAL:4 += RAND:3
	CASE 1
		LOCAL:4 += RAND:2
ENDSELECT

;調教対象の性知識
SELECTCASE EXP:MASTER:8
	CASE IS < 10
		LOCAL:2 += 2 + RAND:5
	CASE IS < 15
		LOCAL:2 += 1 + RAND:2
	CASE IS < 20
		LOCAL:2 += RAND:2
	CASE IS < 25
		LOCAL:2 -= 2
	CASE IS < 30
		LOCAL:2 -= 5
	CASEELSE
		LOCAL:2 -= 10
ENDSELECT

;罪悪感
SELECTCASE CFLAG:5
	CASE IS > 100
		LOCAL:3 += 3
		LOCAL:4 -= 5
		LOCAL:7 -= 5
	CASE IS > 80
		LOCAL:3 += 2
		LOCAL:4 -= 4
		LOCAL:7 -= 4
	CASE IS > 60
		LOCAL:3 += 2
		LOCAL:4 -= 3
		LOCAL:7 -= 3
	CASE IS > 40
		LOCAL:3 += 1
		LOCAL:4 -= 2
		LOCAL:7 -= 2
	CASE IS > 20
		LOCAL:4 -= 1
		LOCAL:7 -= 1
ENDSELECT

;アライメント
SELECTCASE CFLAG:6
	CASE IS > 49
		LOCAL:3 += 3 + RAND:5
		LOCAL:4 -= 3 + RAND:5
	CASE IS > 39
		LOCAL:3 += 2 + RAND:4
		LOCAL:4 -= 2 + RAND:4
	CASE IS > 29
		LOCAL:3 += 2 + RAND:3
		LOCAL:4 -= 2 + RAND:3
	CASE IS > 19
		LOCAL:0 += 1
		LOCAL:3 += 1 + RAND:2
		LOCAL:4 -= 1 + RAND:2
	CASE IS > 9
		LOCAL:0 += 2
		LOCAL:1 += 1
		LOCAL:3 += RAND:2
		LOCAL:4 -= RAND:2
	CASE IS > -1
		LOCAL:0 += 3
		LOCAL:1 += 2
	CASE IS > -11
		LOCAL:0 += 2
		LOCAL:1 += 1
		LOCAL:3 -= RAND:2
		LOCAL:4 += RAND:2
	CASE IS > -21
		LOCAL:0 += 1
		LOCAL:3 -= 1 + RAND:2
		LOCAL:4 += 1 + RAND:2
	CASE IS > -31
		LOCAL:3 -= 2 + RAND:3
		LOCAL:4 += 2 + RAND:3
	CASE IS > -41
		LOCAL:3 -= 2 + RAND:4
		LOCAL:4 += 2 + RAND:4
	CASE IS > -51
		LOCAL:3 -= 3 + RAND:5
		LOCAL:4 += 3 + RAND:5
	CASEELSE
		LOCAL:3 -= 3 + RAND:6
		LOCAL:4 += 3 + RAND:6
ENDSELECT

;────────────────────────────────────
;ゲージや状態による変動
;────────────────────────────────────
;調教者の理性
SELECTCASE BASE:5
	CASE IS < 250
		LOCAL:8 += 3 + RAND:6
		LOCAL:9 += 1 + RAND:7
	CASE IS < 500
		LOCAL:8 += 2 + RAND:3
		LOCAL:9 += RAND:5
	CASE IS < 750
		LOCAL:8 -= RAND:2
	CASE IS < 1000
		LOCAL:8 -= 2
		LOCAL:9 -= 1 + RAND:2
	CASEELSE
		LOCAL:8 -= 5
		LOCAL:9 -= 3 + RAND:4
ENDSELECT

;調教者の興味
SELECTCASE BASE:6
	CASE IS < 200
		LOCAL:0 -= 5
		LOCAL:1 -= 5
	CASE IS < 300
		LOCAL:0 -= 4
		LOCAL:1 -= 4
	CASE IS < 400
		LOCAL:0 -= 3
		LOCAL:1 -= 3
	CASE IS < 500
		LOCAL:0 -= 2
		LOCAL:1 -= 2
	CASE IS < 600
		LOCAL:0 -= 1
		LOCAL:1 -= 1
ENDSELECT

;調教者の苛立ち
SELECTCASE BASE:7
	CASE IS > 1000
		LOCAL:7 += 6
		LOCAL:8 += 5 + RAND:5
	CASE IS > 800
		LOCAL:7 += 5
		LOCAL:8 += 4 + RAND:4
	CASE IS > 600
		LOCAL:7 += 4
		LOCAL:8 += 3 + RAND:4
	CASE IS > 400
		LOCAL:7 += 3
		LOCAL:8 += 2 + RAND:3
	CASE IS > 200
		LOCAL:7 += 2
		LOCAL:8 += 1 + RAND:3
ENDSELECT

;調教者の満足
SELECTCASE BASE:8
	CASE IS > 1000
		LOCAL:4 -= 5
		LOCAL:7 -= 3
		LOCAL:8 -= 5
		LOCAL:66 -= 3
	CASE IS > 750
		LOCAL:4 -= 3
		LOCAL:7 -= 2
		LOCAL:8 -= 3
		LOCAL:66 -= 2
	CASE IS > 500
		LOCAL:4 -= 2
		LOCAL:7 -= 1
		LOCAL:8 -= 2
		LOCAL:66 -= 1
ENDSELECT

;調教者の状態(0=通常/1=疲弊/2=衰弱/3=無気力/4=朦朧/5=情欲/6=怒り/7=退屈/8=狂乱)
SELECTCASE TFLAG:60
	CASE 3
		LOCAL:8 -= 5
	CASE 4
		LOCAL:3 -= RAND:5
		LOCAL:4 -= RAND:5
		LOCAL:8 -= RAND:5
	CASE 6
		LOCAL:1 -= 3
		LOCAL:2 -= 3
		LOCAL:3 -= 5
		LOCAL:4 += RAND:3
		LOCAL:7 += 3
		LOCAL:8 += 5
		LOCAL:66 += 3
	CASE 7
		LOCAL:0 -= 5
		LOCAL:1 -= 5
		LOCAL:2 -= 5
		LOCAL:8 += 5
	CASE 8
		LOCAL:8 += RAND:5
		LOCAL:9 += 6
		LOCAL:66 += RAND:5
ENDSELECT

;────────────────────────────────────
;前回の行動や状況による変動
;────────────────────────────────────
;装着道具類(バイブ,アナルバイブ,アナルビーズ,浣腸＋アナルプラグ,クリキャップ,オナホール,ニプルキャップ,搾乳器)
SELECTCASE TEQUIP:20 + TEQUIP:25 + TEQUIP:26 + (TEQUIP:27 * 3) + TEQUIP:30 + TEQUIP:31 + TEQUIP:35 + (TEQUIP:36 * 2)
	CASE IS > 10
		LOCAL:0 += 6
	CASE IS > 8
		LOCAL:0 += 5
	CASE IS > 6
		LOCAL:0 += 4
	CASE IS > 4
		LOCAL:0 += 3
	CASE IS > 2
		LOCAL:0 += 2
	CASE IS > 0
		LOCAL:0 += 1
ENDSELECT

;媚薬
IF TEQUIP:11
	LOCAL:0 += TEQUIP:11 / 2
	LOCAL:1 += TEQUIP:11
ENDIF

;ＳＭ系道具(縄,アイマスク,ボールギャグ,三角木馬)
IF TEQUIP:40 || TEQUIP:46 || TEQUIP:47
	LOCAL:0 += 1
	LOCAL:4 += 2
ENDIF
IF TEQUIP:41
	LOCAL:0 += 3
	LOCAL:4 += 2
ENDIF
IF TEQUIP:42
	LOCAL:0 -= 5
	LOCAL:1 -= 10
	LOCAL:6 -= 10
ENDIF
IF TEQUIP:43
	LOCAL:3 -= 5
	LOCAL:4 += 3
	LOCAL:9 += 5
ENDIF

;野外プレイ中
IF TEQUIP:52
	LOCAL:0 += 3
	LOCAL:4 += RAND:5
ENDIF

;調教対象が自慰中
SIF TEQUIP:69 & 1
	LOCAL:5 -= 5

;調教者が自慰中
IF TEQUIP:69 & 2
	LOCAL:3 -= 3
	LOCAL:4 -= 3
	LOCAL:7 -= 5
	LOCAL:8 -= 5
	LOCAL:9 -= 5
ENDIF

;調教者挿入中
IF TEQUIP:70
	LOCAL:7 -= 5
	LOCAL:9 -= 5
ENDIF

;衣装関連、まだまだ調整する必要があります…
;上着
;調教対象が上下両方の上着、または全身上着を着ている
IF (TEQUIP:MASTER:4 && TEQUIP:MASTER:5) || TEQUIP:MASTER:6
	LOCAL:5 += 12
	;調教者が抑圧/解放
	IF TALENT:32
		LOCAL:5 -= 4
	ELSEIF TALENT:33
		LOCAL:5 += 4
	ENDIF
;調教対象が上下どちらかの上着を着ている
ELSEIF TEQUIP:MASTER:4 || TEQUIP:MASTER:5
	LOCAL:5 += 10
	;調教者が抑圧/解放
	IF TALENT:32
		LOCAL:5 -= 3
	ELSEIF TALENT:33
		LOCAL:5 += 3
	ENDIF
ENDIF

;下着
;調教対象が上下両方の下着を着ている
IF TEQUIP:MASTER:2 && TEQUIP:MASTER:3
	LOCAL:5 += 8
	;調教者が抑圧/解放
	IF TALENT:32
		LOCAL:5 -= 4
	ELSEIF TALENT:33
		LOCAL:5 += 4
	ENDIF
;調教対象が上下どちらかの下着を着ている
ELSEIF TEQUIP:MASTER:2 || TEQUIP:MASTER:3
	LOCAL:5 += 6
	;調教者が抑圧/解放
	IF TALENT:32
		LOCAL:5 -= 3
	ELSEIF TALENT:33
		LOCAL:5 += 3
	ENDIF
ENDIF

;調教対象が上着/下着類を着ている場合。着衣プレイが好み？Yes!Yes!Yes…
IF TEQUIP:MASTER:2 || TEQUIP:MASTER:3 || TEQUIP:MASTER:4 || TEQUIP:MASTER:5 || TEQUIP:MASTER:6
	LOCAL:5 += 2 + RAND:5
	;調教者が一線越えない
	SIF TALENT:28
		LOCAL:5 -= 5
	;調教者が倒錯的
	SIF TALENT:80
		LOCAL:5 -= 3 + RAND:5
	;調教者が両刀でなく、かつ調教対象が同性
	SIF !TALENT:81 && !(TALENT:122 ^^ TALENT:MASTER:122)
		LOCAL:5 -= 3
	;調教者が男嫌いかつ、調教対象がオトコ
	SIF TALENT:82 && TALENT:MASTER:122
		LOCAL:5 -= 5
ENDIF

;調教対象がコスプレ中。飽きるまで衣装変更しないように
IF TEQUIP:MASTER:9
	;調教者の興味
	SELECTCASE BASE:6
		CASE IS > 750
			LOCAL:5 = 0
		CASE IS > 500
			LOCAL:5 -= 10
		CASE IS > 250
			LOCAL:5 -= 5
		CASEELSE
			LOCAL:5 += 5
	ENDSELECT
ENDIF

;許しを乞ったかつ、調教者の状態が狂乱以外
IF SELECTCOM == 7 && TFLAG:60 < 8
	LOCAL:4 -= 5
	LOCAL:8 -= 5
	LOCAL:66 -= 5
ENDIF

;そんな話はもう終わったフラグ(1=好意,2=知識,4=慰め,8=脅す,16=合意,32=嗤う,64=罵倒)
SIF TFLAG:100 & 1
	LOCAL:1 -= 5
SIF TFLAG:100 & 2
	LOCAL:2 -= 5
SIF TFLAG:100 & 4
	LOCAL:3 -= 5
SIF TFLAG:100 & 8
	LOCAL:4 -= 5
SIF TFLAG:100 & 16
	LOCAL:6 -= 5
SIF TFLAG:100 & 32
	LOCAL:9 -= 5
SIF TFLAG:100 & 64
	LOCAL:66 -= 5

;────────────────────────────────────
;同じ行動連続実行の確率をダウンします
;────────────────────────────────────
SELECTCASE TFLAG:91
	CASE 0 TO 9, 66
		LOCAL:(TFLAG:91) -= 3 + RAND:5
ENDSELECT

;────────────────────────────────────
;実行不可能の判定、お仕置きモードのチェック
;────────────────────────────────────
;お仕置きモード
IF TFLAG:69
	LOCAL:7 += 8 + RAND:6
	LOCAL:9 += RAND:8
	LOCAL:66 += 6 + RAND:8
ENDIF

;調教者自身に性知識が無い
SIF !EXP:8
	LOCAL:2 = 0

;不可能判定とカウンタ値の下限チェック
FOR LOCAL:900, 0, 10
	CALLFORM ACT_ABLE{LOCAL:900}
	SIF !RESULT || LOCAL:(LOCAL:900) < -99
		LOCAL:(LOCAL:900) = -99
NEXT
CALL ACT_ABLE66
SIF !RESULT || LOCAL:66 < -99
	LOCAL:66 = -99

;────────────────────────────────────
;最終判定
;────────────────────────────────────
SELECTCASE MAX(LOCAL:0, LOCAL:1, LOCAL:2, LOCAL:3, LOCAL:4, LOCAL:5, LOCAL:6, LOCAL:7, LOCAL:8, LOCAL:9, LOCAL:66)
;ここには来ないはず
;	CASE -99
;		PRINTL (会話カウンタ異常)
;		TFLAG:90 = 0
	CASE LOCAL:0
		TFLAG:90 = 0
	CASE LOCAL:1
		TFLAG:90 = 1
	CASE LOCAL:7
		TFLAG:90 = 7
	CASE LOCAL:5
		TFLAG:90 = 5
	CASE LOCAL:6
		TFLAG:90 = 6
	CASE LOCAL:2
		TFLAG:90 = 2
	CASE LOCAL:3
		TFLAG:90 = 3
	CASE LOCAL:4
		TFLAG:90 = 4
	CASE LOCAL:8
		TFLAG:90 = 8
	CASE LOCAL:66
		TFLAG:90 = 66
	CASE LOCAL:9
		TFLAG:90 = 9
ENDSELECT

;デバッグ＆調整用カウンタ
IF FLAG:4
	PRINTFORML 　　聊天：气氛[{LOCAL:0,3}]/好意[{LOCAL:1,3}]/知识[{LOCAL:2,3}]/安慰[{LOCAL:3,3}]/威胁[{LOCAL:4,3}]
	PRINTFORML 　　　　　衣装[{LOCAL:5,3}]/合意[{LOCAL:6,3}]/放置[{LOCAL:7,3}]/怒鸣[{LOCAL:8,3}]/嘲笑[{LOCAL:9,3}]
	PRINTFORML 　　　　　辱骂[{LOCAL:66,3}]
ENDIF


;-----------------------------------------------------------
;会話 の実行判定
;-----------------------------------------------------------
@ACTM_ABLE0
FOR LOCAL, 0, 10
	CALLFORM ACT_ABLE{LOCAL}
	SIF RESULT
		RETURN 1
NEXT
CALL ACT_ABLE66
SIF RESULT
	RETURN 1
RETURN 0
