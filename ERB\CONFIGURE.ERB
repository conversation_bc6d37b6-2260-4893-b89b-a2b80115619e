﻿@CONFIGURE
FLAG:2000 = 1
[SKIPSTART]
PRINTFORM [1] 　新调教者的追加方法

IF FLAG:2000
	PRINT 　　　　　　　　　　 可以从SHOP菜单追加
ELSE
	PRINT 　　　　　　　　　 不可以从SHOP菜单追加
ENDIF
[SKIPEND]
PRINTFORM [0] 角色头像
 IF FLAG:666
	PRINTL ON
ELSE
	PRINTL OFF
ENDIF
[SKIPEND]
PRINTFORM [2] 　察言观色的能力
IF TALENT:MASTER:132 == 0
	PRINT 　　　　　　　　　　　　 不理解对方的心情
ELSEIF TALENT:MASTER:132 == 1
	PRINT 　　　　　　　　　　　　 能揣摩对方的心情
ELSE
	PRINT 　　　　　　　　　　　　 完全看透对方的心情
ENDIF
PRINTFORM [35]　三围、身高的表示＆巨乳的上限设定

IF FLAG:2001
	PRINT 　　　　　　表示
	IF FLAG:2001 == 1
		PRINTL Ｆ
	ELSEIF FLAG:2001 == 2
		PRINTL Ｇ
	ELSEIF FLAG:2001 == 3
		PRINTL Ｈ
	ENDIF
ELSE
	PRINTL  　　　　　　　　　　不表示
ENDIF

FLAG:2002 = 0
[SKIPSTART]
PRINTFORM [36]　让角色的性格、体型等具有个性

IF FLAG:2002
	PRINTL  　　　　　　　　　　　　　　启用
ELSE
	PRINTL  　　　　　　　　　　　　　　固定
ENDIF
[SKIPEND]
PRINTFORM [37]　根据性格、体质等，按种类进行显示 
IF FLAG:2003
	PRINTL 　　　　　按照素质整理
ELSE
	PRINTL 　　　　　按登录番号顺序
ENDIF
PRINTFORML [38]　变更姓名 
PRINTFORM [39]　日常生活的描写
IF FLAG:2006
	PRINTL  　　　　　　　　　　　　　ON
ELSE
	PRINTL  　　　　　　　　　　　　　OFF
ENDIF
PRINTFORM [40]　调教者的怀孕机能
IF FLAG:2007
	PRINTL  　　　　　　　　　      ON
ELSE
	PRINTL  　　　　　　　　　      OFF
ENDIF
PRINTFORM [41]　调教对象的怀孕机能
IF FLAG:2008 == 1
	PRINTL  　　　　　　　        爱情度越高就越容易怀孕
ELSEIF FLAG:2008 == 2
	PRINTL  　　　　　　　        与爱情度无关
ELSE
	PRINTL  　　　　　　　        不怀孕
ENDIF
PRINTFORML [100] 返回


$INPUT_LOOP
INPUT
IF RESULT == 100
	RETURN 0
;ELSEIF RESULT == 1
;	CALL CONFIG_NEWTRAINER
ELSEIF RESULT == 0
	IF FLAG:666
		FLAG:666 = 0
	ELSE
		FLAG:666 = 1
	ENDIF
ELSEIF RESULT == 2
	CALL CONFIG_MINDREADING
ELSEIF RESULT == 35
	CALL CONFIG_3SIZE
;ELSEIF RESULT == 36
;	CALL CONFIG_PERSONALITY
ELSEIF RESULT == 37
	CALL CONFIG_SHOW_TALENT
ELSEIF RESULT == 38
	CALL CONFIG_RENAME_MASTER
ELSEIF RESULT == 39
	FLAG:2006 = !FLAG:2006
ELSEIF RESULT == 40
	FLAG:2007 = !FLAG:2007
ELSEIF RESULT == 41
	CALL CONFIG_MASTER_PREGNACY
ELSE
	GOTO INPUT_LOOP
ENDIF

RESTART
@CONFIG_SHOW_TALENT
IF FLAG:2003
	PRINTFORMW 将素质恢复到登録番号所示。
	FLAG:2003 = 0
ELSE
	PRINTFORMW 根据性格、体质等，按种类进行整理和显示素质。
	FLAG:2003 = 1
ENDIF
PRINTL 
@CONFIG_NEWTRAINER
PRINTFORML 是否允许从商店菜单购买新调教者？
PRINTFORML （除了店铺菜单以外，还可以把在探索过程中遇到的对象追加进调教者）
PRINTL [0] 是
PRINTL [1] 否
CALL INPUT_SELECT_2
IF R == 0
	FLAG:2000 = 1
	PRINTL 已启用。
ELSEIF R == 1
	FLAG:2000 = 0
	PRINTL 已禁用。
	PRINTL 如果在迷宫失败的话、可以追加调教者。
ELSE
	RESTART
ENDIF
@CONFIG_MINDREADING
PRINTFORML 设定察言观色的能力。
PRINTL [0] 不理解对方的心情
PRINTL [1] 能揣摩对方的心情
PRINTL [2] 完全看透对方的心情
CALL INPUT_SELECT_3
IF R >= 0 && R <= 2
	TALENT:MASTER:132 = R
ELSE
	RESTART
ENDIF

@CONFIG_3SIZE
A = FLAG:2001
PRINTFORML 决定＆显示三围和身高吗？
PRINTL 
PRINTFORML [0] 是
PRINTFORML [1] 否
CALL INPUT_SELECT_2
IF R == 0
	PRINTFORMW 决定显示三围和身高。
	PRINTL 
	PRINTFORML 继续设定巨乳的上限。请从下面选。
	PRINTL 
	PRINTFORML [0] 说到巨乳、大概是E～F杯
	PRINTFORML [1] 说到巨乳、大概是E～G杯
	PRINTFORML [2] 说到巨乳、大概是E～H杯
	CALL INPUT_SELECT_3
	FLAG:2001 = 1 + R
	IF FLAG:2001 == 1
		PRINT Ｅ～Ｆ杯
	ELSEIF FLAG:2001 == 2
		PRINT Ｅ～Ｇ杯
	ELSEIF FLAG:2001 == 3
		PRINT Ｅ～Ｈ杯
	ENDIF
	PRINTFORMW 作为巨乳了。
ELSEIF R == 1
	FLAG:2001 = 0
ENDIF
IF A != FLAG:2001 && A
	PRINTFORMW 设置已修改，请重新设置一次值。
	REPEAT CHARANUM
		CFLAG:COUNT:900 = 0
		CFLAG:COUNT:901 = 0
		CFLAG:COUNT:902 = 0
		CFLAG:COUNT:903 = 0
		CFLAG:COUNT:904 = 0
	REND
ENDIF
PRINTL 
@CONFIG_PERSONALITY

PRINTFORML  梦魔生产时素质会发生变动吗？
PRINTL [0] 是
PRINTL [1] 否
CALL INPUT_SELECT_2
IF R == 0
	FLAG:2002 = 1
	PRINTL 已启用。
ELSEIF R == 1
	FLAG:2002 = 0
	PRINTL 已禁用。
ELSE
	RESTART
ENDIF

@CONFIG_RENAME_MASTER
PRINTFORML 现在的名字：%CALLNAME:MASTER%
PRINTL 变更姓名吗？
PRINTL [0] 是
PRINTL [1] 否
CALL INPUT_SELECT_2
IF R == 0
	FLAG:2002 = 1
	PRINTL 请输入新名字
	INPUTS
	CALLNAME:MASTER = %RESULTS%
	PRINTFORMW 把名字改成%CALLNAME:MASTER%了
ENDIF

;調教対象の妊娠設定
@CONFIG_MASTER_PREGNACY
PRINTFORML 进行调教对象怀孕的设定
PRINTL [0] 不怀孕
PRINTL [1] 爱情度越高就越容易怀孕、监禁中不会怀孕
PRINTL [2] 奴隶不需要爱！与爱以及是否监禁无关
CALL INPUT_SELECT_3
IF R >= 0 && R <= 2
	FLAG:2008 = R
ELSE
	RESTART
ENDIF


