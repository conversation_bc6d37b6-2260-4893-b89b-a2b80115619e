﻿;────────────────────────────────────
;ラミア (蔦拘束41)
;────────────────────────────────────
@ACT_EXTRA_M4
LOCAL:41 = ABS(TFLAG:402 * 10)

;調教者の素質
;自制的/衝動的
IF TALENT:20
	LOCAL:41 -= 5
ELSEIF TALENT:21
	LOCAL:41 += 5
ENDIF
;好色/清楚
IF TALENT:36
	LOCAL:41 += 5
ELSEIF TALENT:37
	LOCAL:41 -= 5
ENDIF
;縛り上手
SIF TALENT:58
	LOCAL:41 += 5

;調教者の性格
;淫乱-貞淑軸
SELECTCASE CFLAG:153
	CASE IS > 40
		LOCAL:41 += 10
	CASE IS > 20
		LOCAL:41 += 7
	CASE IS > -20
		LOCAL:41 += 5
	CASE IS > -40
		LOCAL:41 += 3
ENDSELECT

;CALL ACT_EXTRA_ABLE41
;LOCAL:41 = RESULT && LOCAL:41 > -99 ? LOCAL:41 # -99

;技巧が低いと失敗
SIF GET_ABL(TARGET, 2) * BASE:TARGET:1 < RAND:150 * MAXBASE:TARGET:1
	LOCAL:41 = -99

;10 < LOCAL:41 < 25
SELECTCASE LOCAL:41
	CASE -99
		TFLAG:233 = 0
	CASE LOCAL:41
		TFLAG:233 = RAND:(MAX(LOCAL:41, 10)) > RAND:50 ? 1 # 0
		TEQUIP:47 = TFLAG:233 ? 1 # 0
ENDSELECT
RETURN TFLAG:233


@ACT_EXTRA_MESSAGE_M4
PRINTFORML 浮现出兴奋表情的%CALLNAME:TARGET%将尾巴缠上了%CALLNAME:MASTER%的身体…
TFLAG:402 = 0


;-----------------------------------------------------------
;ラミア の実行判定
;-----------------------------------------------------------
@ACTM_EXTRA_ABLE4
CALL ACT_EXTRA_ABLE41
RETURN RESULT

;-----------------------------------------------------------
;蛇体巻きつけ の実行判定
;-----------------------------------------------------------
@ACT_EXTRA_ABLE41
;調教者がラミアでない
SIF !TALENT:152
	RETURN 0
;アライメント変動が小さい
SIF ABS(TFLAG:402) < 3
	RETURN 0
;縄装着中
SIF TEQUIP:40
	RETURN 0
;既に蛇体巻きつけしている
SIF TEQUIP:47
	RETURN 0
RETURN 1
