﻿;────────────────────────────────────
;主人公カスタムメニュー
;────────────────────────────────────
@MASTER_CUSTOM
;-------------------------------------------------
;MASTER_CUSTOM2関連の追加処理
PRINTFORML 从一开始就能定制%CALLNAME:MASTER%的状态。
PRINTL 请选择想变更的项目。
PRINTL （请注意：极端的自定义会使难易度发生很大的变化。）
PRINTL 
PRINTL 请选择设定模式
PRINTL [0] 简易版　　[1] 详细版
$INPUT_LOOP2
INPUT
IF RESULT == 1
	JUMP MASTER_CUSTOM2
ELSEIF RESULT
	CLEARLINE 1
	GOTO INPUT_LOOP2
ENDIF
;-------------------------------------------------


$INPUT_LOOP1
CALL SHOW_CUSTOM

INPUT

IF RESULT == 0
	TFLAG:999 = 0
	RETURN 0
ELSEIF RESULT == 1
	IF TALENT:MASTER:122
		PRINTFORMW %CALLNAME:MASTER%成为了扶她
		TALENT:MASTER:122 = 0
		TALENT:MASTER:121 = 1
		SIF TALENT:MASTER:1
			TALENT:MASTER:0 = 1
		IF ABL:MASTER:1 > 0
			EXP:MASTER:1 = 10
			EXP:MASTER:3 = 10
		ELSEIF TALENT:MASTER:0 == 0
			EXP:MASTER:1 = 2
			EXP:MASTER:3 = 3
		ENDIF
	ELSEIF TALENT:MASTER:121
		PRINTFORMW %CALLNAME:MASTER%成为了女孩子
		SIF TALENT:MASTER:1
			TALENT:MASTER:1 = 0
		TALENT:MASTER:121 = 0
		TALENT:MASTER:123 = 0
		TALENT:MASTER:124 = 0
		EXP:MASTER:5 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%成为了男性
		TALENT:MASTER:122 = 1
		TALENT:MASTER:102 = 0
		TALENT:MASTER:103 = 0
		TALENT:MASTER:108 = 0
		TALENT:MASTER:109 = 0
		EXP:MASTER:1 = 0
		EXP:MASTER:3 = 0
		IF TALENT:MASTER:0
			TALENT:MASTER:0 = 0
			TALENT:MASTER:1 = 1
		ELSEIF ABL:MASTER:1 > 0
			EXP:MASTER:5 = 12 + TALENT:MASTER:60
		ELSE
			EXP:MASTER:5 = 3 + TALENT:MASTER:60
		ENDIF
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 2
	IF TALENT:MASTER:0 || TALENT:MASTER:1
		PRINTFORMW %CALLNAME:MASTER%只有一点性交的经验
		TALENT:MASTER:0 = 0
		TALENT:MASTER:1 = 0
		EXP:MASTER:7 = 1
		EXP:MASTER:0 = 4
		EXP:MASTER:8 = 5 + CFLAG:MASTER:5
		IF TALENT:MASTER:122 == 0
			EXP:MASTER:1 = 2
			EXP:MASTER:3 = 3
		ENDIF
		SIF TALENT:MASTER:122 || TALENT:MASTER:121
			EXP:MASTER:5 = 3 + TALENT:MASTER:60
	ELSEIF ABL:MASTER:1 > 0
		PRINTFORMW %CALLNAME:MASTER%还是纯真无垢的
		ABL:MASTER:1 = 0
		EXP:MASTER:7 = 0
		EXP:MASTER:0 = 0
		EXP:MASTER:8 = CFLAG:MASTER:5
		IF TALENT:MASTER:122 == 0
			TALENT:MASTER:0 = 1
			EXP:MASTER:1 = 0
			EXP:MASTER:3 = 0
		ELSEIF TALENT:MASTER:122 || TALENT:MASTER:121
			TALENT:MASTER:1 = 1
			EXP:MASTER:5 = TALENT:MASTER:60
		ENDIF
	ELSE
		PRINTFORMW %CALLNAME:MASTER%已经发生过好几次性交了
		ABL:MASTER:1 = 1
		EXP:MASTER:7 = 3
		EXP:MASTER:0 = 10
		EXP:MASTER:8 = 10 + CFLAG:MASTER:5
		IF TALENT:MASTER:122 == 0
			EXP:MASTER:1 = 10
			EXP:MASTER:3 = 10
		ENDIF
		SIF (TALENT:MASTER:122 || TALENT:MASTER:121) 
			EXP:MASTER:5 = 12 + TALENT:MASTER:60
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 3
	IF CFLAG:MASTER:5 == 0
		EXP:MASTER:8 += 5
		PRINTFORMW %CALLNAME:MASTER%对性有点兴趣
		CFLAG:MASTER:5 = 1
	ELSEIF CFLAG:MASTER:5 == 1
		EXP:MASTER:8 += 5
		PRINTFORMW %CALLNAME:MASTER%对性非常感兴趣
		CFLAG:MASTER:5 = 2
	ELSE
		PRINTFORMW %CALLNAME:MASTER%对于性知识一无所知
		EXP:MASTER:8 -= 10
		CFLAG:MASTER:5 = 0
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 4
	IF TALENT:MASTER:10
		PRINTFORMW %CALLNAME:MASTER%变得刚强了
		TALENT:MASTER:10 = 0
		TALENT:MASTER:12 = 1
	ELSEIF TALENT:MASTER:12
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:12 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得胆怯了
		TALENT:MASTER:10 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 5
	IF TALENT:MASTER:11
		PRINTFORMW %CALLNAME:MASTER%变得直率了
		TALENT:MASTER:11 = 0
		TALENT:MASTER:13 = 1
	ELSEIF TALENT:MASTER:13
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:13 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得反抗了
		TALENT:MASTER:11 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 6
	IF TALENT:MASTER:14
		PRINTFORMW %CALLNAME:MASTER%变得自大了
		TALENT:MASTER:14 = 0
		TALENT:MASTER:16 = 1
	ELSEIF TALENT:MASTER:16
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:16 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得老实了
		TALENT:MASTER:14 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 7
	IF TALENT:MASTER:15
		PRINTFORMW %CALLNAME:MASTER%的自尊变低了
		TALENT:MASTER:15 = 0
		TALENT:MASTER:17 = 1
	ELSEIF TALENT:MASTER:17
		PRINTFORMW %CALLNAME:MASTER%的自尊变得普通了
		TALENT:MASTER:17 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%的自尊变高了
		TALENT:MASTER:15 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 8
	IF TALENT:MASTER:20
		PRINTFORMW %CALLNAME:MASTER%变得冲动了
		TALENT:MASTER:20 = 0
		TALENT:MASTER:21 = 1
	ELSEIF TALENT:MASTER:21
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:21 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得自制了
		TALENT:MASTER:20 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 9
	IF TALENT:MASTER:22
		PRINTFORMW %CALLNAME:MASTER%变得性好奇了
		TALENT:MASTER:22 = 0
		TALENT:MASTER:23 = 1
	ELSEIF TALENT:MASTER:23
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:23 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得无动于衷了
		TALENT:MASTER:22 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 10
	IF TALENT:MASTER:24
		PRINTFORMW %CALLNAME:MASTER%变得富有感情了
		TALENT:MASTER:24 = 0
		TALENT:MASTER:25 = 1
	ELSEIF TALENT:MASTER:25
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:25 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得缺乏感情了
		TALENT:MASTER:24 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 11
	IF TALENT:MASTER:26
		PRINTFORMW %CALLNAME:MASTER%变得悲观了
		TALENT:MASTER:26 = 0
		TALENT:MASTER:27 = 1
	ELSEIF TALENT:MASTER:27
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:27 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得乐观了
		TALENT:MASTER:26 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 12
	IF TALENT:MASTER:28
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:28 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%拥有难以越过的底线了
		TALENT:MASTER:28 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 13
	IF TALENT:MASTER:29
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:29 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得喜欢引人注目了
		TALENT:MASTER:29 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 14
	IF TALENT:MASTER:30
		PRINTFORMW %CALLNAME:MASTER%变得淡漠贞操了
		TALENT:MASTER:30 = 0
		TALENT:MASTER:31 = 1
	ELSEIF TALENT:MASTER:31
		PRINTFORMW %CALLNAME:MASTER%的贞操观变得普通了
		TALENT:MASTER:31 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%对贞操的重视变强了
		TALENT:MASTER:30 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 15
	IF TALENT:MASTER:32
		PRINTFORMW %CALLNAME:MASTER%变得性开放了
		TALENT:MASTER:32 = 0
		TALENT:MASTER:33 = 1
	ELSEIF TALENT:MASTER:33
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:33 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得性压抑了
		TALENT:MASTER:32 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 16
	IF TALENT:MASTER:34
		PRINTFORMW %CALLNAME:MASTER%变得不易害羞了
		TALENT:MASTER:34 = 0
		TALENT:MASTER:35 = 1
	ELSEIF TALENT:MASTER:35
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:35 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得容易害羞了
		TALENT:MASTER:34 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 17
	IF TALENT:MASTER:40
		PRINTFORMW %CALLNAME:MASTER%的耐痛能力变强了
		TALENT:MASTER:40 = 0
		TALENT:MASTER:41 = 1
	ELSEIF TALENT:MASTER:41
		PRINTFORMW %CALLNAME:MASTER%的耐痛能力变普通了
		TALENT:MASTER:41 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%的耐痛能力变弱了
		TALENT:MASTER:40 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 18
	IF TALENT:MASTER:42
		PRINTFORMW %CALLNAME:MASTER%变得不容易湿了
		TALENT:MASTER:42 = 0
		TALENT:MASTER:43 = 1
	ELSEIF TALENT:MASTER:43
		PRINTFORMW %CALLNAME:MASTER%爱液的分泌量变得普通了
		TALENT:MASTER:43 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得容易湿了
		TALENT:MASTER:42 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 19
	IF TALENT:MASTER:50
		PRINTFORMW %CALLNAME:MASTER%的学习效率变差了
		TALENT:MASTER:50 = 0
		TALENT:MASTER:51 = 1
	ELSEIF TALENT:MASTER:51
		PRINTFORMW %CALLNAME:MASTER%的学习效率变普通了
		TALENT:MASTER:51 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%的学习效率变好了
		TALENT:MASTER:50 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 20
	IF TALENT:MASTER:60
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:60 = 0
		EXP:MASTER:10 = 0
		SIF TALENT:MASTER:122 || TALENT:MASTER:121
			EXP:MASTER:5 -= 1
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得容易自慰了
		TALENT:MASTER:60 = 1
		EXP:MASTER:10 = 10
		SIF TALENT:MASTER:122 || TALENT:MASTER:121
			EXP:MASTER:5 += 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 21
	IF TALENT:MASTER:61
		PRINTFORMW %CALLNAME:MASTER%变成污臭敏感了
		TALENT:MASTER:61 = 0
		TALENT:MASTER:62 = 1
	ELSEIF TALENT:MASTER:62
		PRINTFORMW %CALLNAME:MASTER%的嗅觉变普通了
		TALENT:MASTER:62 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变成污臭钝感了
		TALENT:MASTER:61 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 22
	IF TALENT:MASTER:63
		PRINTFORMW %CALLNAME:MASTER%变得被动了
		TALENT:MASTER:63 = 0
		TALENT:MASTER:65 = 1
	ELSEIF TALENT:MASTER:65
		PRINTFORMW %CALLNAME:MASTER%变得普通了
		TALENT:MASTER:65 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得自我奉献了
		TALENT:MASTER:63 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 23
	IF TALENT:MASTER:70
		PRINTFORMW %CALLNAME:MASTER%变成否定快感了
		TALENT:MASTER:70 = 0
		TALENT:MASTER:71 = 1
	ELSEIF TALENT:MASTER:71
		PRINTFORMW %CALLNAME:MASTER%对快感的接受度变得普通了
		TALENT:MASTER:71 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变成接受快感了
		TALENT:MASTER:70 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 24
	IF TALENT:MASTER:100
		PRINTFORMW %CALLNAME:MASTER%变得Ｃ钝感了
		TALENT:MASTER:100 = 0
		TALENT:MASTER:101 = 1
	ELSEIF TALENT:MASTER:101
		IF TALENT:MASTER:122 == 0
			PRINTFORMW %CALLNAME:MASTER%变得Ｖ敏感了
			TALENT:MASTER:101 = 0
			TALENT:MASTER:102 = 1
		ELSE
			PRINTFORMW %CALLNAME:MASTER%变得Ａ敏感了
			TALENT:MASTER:101 = 0
			TALENT:MASTER:104 = 1
		ENDIF
	ELSEIF TALENT:MASTER:102
		PRINTFORMW %CALLNAME:MASTER%变得Ｖ钝感了
		TALENT:MASTER:102 = 0
		TALENT:MASTER:103 = 1
	ELSEIF TALENT:MASTER:103
		PRINTFORMW %CALLNAME:MASTER%变得Ａ敏感了
		TALENT:MASTER:103 = 0
		TALENT:MASTER:104 = 1
	ELSEIF TALENT:MASTER:104
		PRINTFORMW %CALLNAME:MASTER%变得Ａ钝感了
		TALENT:MASTER:104 = 0
		TALENT:MASTER:105 = 1
	ELSEIF TALENT:MASTER:105
		PRINTFORMW %CALLNAME:MASTER%变得Ｂ敏感了
		TALENT:MASTER:105 = 0
		TALENT:MASTER:106 = 1
	ELSEIF TALENT:MASTER:106
		PRINTFORMW %CALLNAME:MASTER%变得Ｂ钝感了
		TALENT:MASTER:106 = 0
		TALENT:MASTER:107 = 1
	ELSEIF TALENT:MASTER:107
		PRINTFORMW %CALLNAME:MASTER%的敏感度变普通了
		TALENT:MASTER:107 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得Ｃ敏感了
		TALENT:MASTER:100 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 25
	IF TALENT:MASTER:108
		PRINTFORMW %CALLNAME:MASTER%变成巨乳了
		TALENT:MASTER:108 = 0
		TALENT:MASTER:109 = 1
	ELSEIF TALENT:MASTER:109
		PRINTFORMW %CALLNAME:MASTER%的胸围变普通了
		TALENT:MASTER:109 = 0
	ELSE
		IF TALENT:MASTER:122
			PRINTW 是男性吧？
		ELSE
			PRINTFORMW %CALLNAME:MASTER%变得贫乳了
			TALENT:MASTER:108 = 1
		ENDIF
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 26
	IF TALENT:MASTER:110
		PRINTFORMW %CALLNAME:MASTER%变成了高挑体型
		TALENT:MASTER:110 = 0
		TALENT:MASTER:111 = 1
	ELSEIF TALENT:MASTER:111
		PRINTFORMW %CALLNAME:MASTER%变成了普通体型
		TALENT:MASTER:111 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变成了矮小体型
		TALENT:MASTER:110 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 27
	IF TALENT:MASTER:112
		PRINTFORMW %CALLNAME:MASTER%的恢复速度变慢了
		TALENT:MASTER:112 = 0
		TALENT:MASTER:113 = 1
	ELSEIF TALENT:MASTER:113
		PRINTFORMW %CALLNAME:MASTER%的恢复速度变普通了
		TALENT:MASTER:113 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%的恢复速度变快了
		TALENT:MASTER:112 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 28 & (TALENT:MASTER:122 || TALENT:MASTER:121)
	IF TALENT:MASTER:123
		PRINTFORMW %CALLNAME:MASTER%变得晚泄了
		TALENT:MASTER:123 = 0
		TALENT:MASTER:124 = 1
	ELSEIF TALENT:MASTER:124
		PRINTFORMW %CALLNAME:MASTER%的射精速度变普通了
		TALENT:MASTER:124 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得早漏了
		TALENT:MASTER:123 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 29
	IF TALENT:MASTER:125
		PRINTFORMW %CALLNAME:MASTER%变得精力薄弱了
		TALENT:MASTER:125 = 0
		TALENT:MASTER:126 = 1
	ELSEIF TALENT:MASTER:126
		PRINTFORMW %CALLNAME:MASTER%的体力变得普通了
		TALENT:MASTER:126 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%变得精力绝伦了
		TALENT:MASTER:125 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 30
	IF TALENT:MASTER:116
		PRINTFORMW %CALLNAME:MASTER%能忍受尿意
		TALENT:MASTER:116 = 0
	ELSE
		PRINTFORMW %CALLNAME:MASTER%无法忍受尿意
		TALENT:MASTER:116 = 1
	ENDIF
	PRINTL 　
	GOTO INPUT_LOOP1
ELSEIF RESULT == 101
	SIF TFLAG:999 == 1
		TFLAG:999 = 0
	GOTO INPUT_LOOP1
ELSEIF RESULT == 109
	SIF TFLAG:999 == 0
		TFLAG:999 = 1
	GOTO INPUT_LOOP1
ELSEIF RESULT == 200
	RESTART
ELSE
	PRINTL 请输入正确的值
	PRINTL 　
	GOTO INPUT_LOOP1
ENDIF

;────────────────────────────────────
;主人公カスタムメニューの表示
;────────────────────────────────────
@SHOW_CUSTOM
DRAWLINE
IF TFLAG:999 == 0
	PRINTL [0] 自定义完成、进入下一项
	PRINTFORM [1] %CALLNAME:MASTER%的性別：
	IF TALENT:MASTER:122
		PRINTL 男性
	ELSEIF TALENT:MASTER:121
		PRINTL 扶她
	ELSE
		PRINTL 女孩子
	ENDIF
	PRINTFORM [2] %CALLNAME:MASTER%的性经验：
	IF TALENT:MASTER:0 || TALENT:MASTER:1
		PRINTL 无
	ELSEIF ABL:MASTER:1 > 0
		PRINTL 多
	ELSE
		PRINTL 少
	ENDIF
	PRINTFORM [3] %CALLNAME:MASTER%的性知识：
	IF CFLAG:MASTER:5 == 0
		PRINTL 全无
	ELSEIF CFLAG:MASTER:5 == 1
		PRINTL 少
	ELSE
		PRINTL 丰富
	ENDIF
	PRINTFORM [4] %CALLNAME:MASTER%的性格是：
	IF TALENT:MASTER:10
		PRINTL 胆怯　（刚强/普通）
	ELSEIF TALENT:MASTER:12
		PRINTL 刚强　（普通/胆怯）
	ELSE
		PRINTL 普通　（胆怯/刚强）
	ENDIF
	PRINTFORM [5] %CALLNAME:MASTER%的性格是：
	IF TALENT:MASTER:11
		PRINTL 反抗　（直率/普通）
	ELSEIF TALENT:MASTER:13
		PRINTL 直率　（普通/反抗）
	ELSE
		PRINTL 普通　（反抗/直率）
	ENDIF
	PRINTFORM [6] %CALLNAME:MASTER%的性格是：
	IF TALENT:MASTER:14
		PRINTL 老实　（自大/普通）
	ELSEIF TALENT:MASTER:16
		PRINTL 自大　（普通/老实）
	ELSE
		PRINTL 普通　（老实/自大）
	ENDIF
	PRINTFORM [7] %CALLNAME:MASTER%的自尊是：
	IF TALENT:MASTER:15
		PRINTL 高
	ELSEIF TALENT:MASTER:17
		PRINTL 低
	ELSE
		PRINTL 普通
	ENDIF
	PRINTFORM [8] %CALLNAME:MASTER%的精神是：
	IF TALENT:MASTER:20
		PRINTL 自制的　（冲动的/普通）
	ELSEIF TALENT:MASTER:21
		PRINTL 冲动的　（普通/自制的）
	ELSE
		PRINTL 普通　（自制的/冲动的）
	ENDIF
	PRINTFORM [9] %CALLNAME:MASTER%对周围：
	IF TALENT:MASTER:22
		PRINTL 无动于衷
	ELSEIF TALENT:MASTER:23
		PRINTL 性好奇
	ELSE
		PRINTL 对普通人感兴趣
	ENDIF
	PRINTFORM [10]%CALLNAME:MASTER%的感情丰富吗：
	IF TALENT:MASTER:24
		PRINTL 缺乏感情
	ELSEIF TALENT:MASTER:25
		PRINTL 感情丰富
	ELSE
		PRINTL 普通
	ENDIF
	PRINTFORM [11]%CALLNAME:MASTER%的人生观是：
	IF TALENT:MASTER:26
		PRINTL 乐观　（悲观/普通）
	ELSEIF TALENT:MASTER:27
		PRINTL 悲观　（普通/乐观）
	ELSE
		PRINTL 普通　（乐观/悲观）
	ENDIF
	PRINTFORM [12]%CALLNAME:MASTER%的交友关系是：
	IF TALENT:MASTER:28
		PRINTL 难以越过的底线　（普通）
	ELSE
		PRINTL 普通　（难以越过的底线）
	ENDIF
	PRINTFORM [13]%CALLNAME:MASTER%的表現欲是：
	IF TALENT:MASTER:29
		PRINTL 喜欢引人注目　（普通）
	ELSE
		PRINTL 普通　（喜欢引人注目）
	ENDIF
	PRINTFORM [14]%CALLNAME:MASTER%重视贞操吗：
	IF TALENT:MASTER:30
		PRINTL 重视
	ELSEIF TALENT:MASTER:31
		PRINTL 不怎么重视
	ELSE
		PRINTL 普通程度
	ENDIF
ELSE
	PRINTL [0] 自定义完成、进入下一项
	PRINTFORM [15]%CALLNAME:MASTER%的言行是：
	IF TALENT:MASTER:32
		PRINTL 性压抑　（性开放/普通）
	ELSEIF TALENT:MASTER:33
		PRINTL 性开放　（普通/性压抑）
	ELSE
		PRINTL 普通　（性压抑/性开放）
	ENDIF
	PRINTFORM [16]%CALLNAME:MASTER%的羞恥心是：
	IF TALENT:MASTER:34
		PRINTL 害羞
	ELSEIF TALENT:MASTER:35
		PRINTL 不知羞耻
	ELSE
		PRINTL 普通
	ENDIF
	PRINTFORM [17]%CALLNAME:MASTER%的身体：
	IF TALENT:MASTER:40
		PRINTL 怕痛　（耐痛/普通）
	ELSEIF TALENT:MASTER:41
		PRINTL 耐痛　（普通/怕痛）
	ELSE
		PRINTL 普通　（怕痛/耐痛）
	ENDIF
	PRINTFORM [18]%CALLNAME:MASTER%的体质：
	IF TALENT:MASTER:42
		PRINTL 容易湿　（不易湿/普通）
	ELSEIF TALENT:MASTER:43
		PRINTL 不易湿　（普通/容易湿）
	ELSE
		PRINTL 普通　（容易湿/不易湿）
	ENDIF
	PRINTFORM [19]%CALLNAME:MASTER%的学习效率是：
	IF TALENT:MASTER:50
		PRINTL 学得快
	ELSEIF TALENT:MASTER:51
		PRINTL 学得慢
	ELSE
		PRINTL 普通
	ENDIF
	PRINTFORM [20]%CALLNAME:MASTER%对于自慰是：
	IF TALENT:MASTER:60
		PRINTL 经常自慰
	ELSE
		PRINTL 很少自慰
	ENDIF
	PRINTFORM [21]%CALLNAME:MASTER%的嗅覚是：
	IF TALENT:MASTER:61
		PRINTL 污臭钝感
	ELSEIF TALENT:MASTER:62
		PRINTL 污臭敏感
	ELSE
		PRINTL 普通
	ENDIF
	PRINTFORM [22]%CALLNAME:MASTER%的主动性是：
	IF TALENT:MASTER:63
		PRINTL 自我奉献
	ELSEIF TALENT:MASTER:65
		PRINTL 被动
	ELSE
		PRINTL 普通
	ENDIF
	PRINTFORM [23]%CALLNAME:MASTER%能直率地接受快感吗：
	IF TALENT:MASTER:70
		PRINTL 接受快感
	ELSEIF TALENT:MASTER:71
		PRINTL 否定快感
	ELSE
		PRINTL 普通
	ENDIF
	PRINTFORM [24]%CALLNAME:MASTER%有特别敏感和迟钝的地方吗：
	IF TALENT:MASTER:100
		PRINTL Ｃ敏感
	ELSEIF TALENT:MASTER:101
		PRINTL Ｃ钝感
	ELSEIF TALENT:MASTER:102
		PRINTL Ｖ敏感
	ELSEIF TALENT:MASTER:103
		PRINTL Ｖ钝感
	ELSEIF TALENT:MASTER:104
		PRINTL Ａ敏感
	ELSEIF TALENT:MASTER:105
		PRINTL Ａ钝感
	ELSEIF TALENT:MASTER:106
		PRINTL Ｂ敏感
	ELSEIF TALENT:MASTER:107
		PRINTL Ｂ钝感
	ELSE
		PRINTL 没有特别的
	ENDIF
	PRINTFORM [25]%CALLNAME:MASTER%的胸围是：
	IF TALENT:MASTER:108
		PRINTL 贫乳
	ELSEIF TALENT:MASTER:109
		PRINTL 巨乳
	ELSE
		IF TALENT:MASTER:122
			PRINTL 男性
		ELSE
			PRINTL 普通
		ENDIF
	ENDIF
	PRINTFORM [26]%CALLNAME:MASTER%的体型是：
	IF TALENT:MASTER:110
		PRINTL 矮小
	ELSEIF TALENT:MASTER:111
		PRINTL 高挑
	ELSE
		PRINTL 普通
	ENDIF
	PRINTFORM [27]%CALLNAME:MASTER%的恢复速度是：
	IF TALENT:MASTER:112
		PRINTL 恢复快
	ELSEIF TALENT:MASTER:113
		PRINTL 恢复慢
	ELSE
		PRINTL 普通
	ENDIF
	IF TALENT:MASTER:122 || TALENT:MASTER:121
		PRINTFORM [28]%CALLNAME:MASTER%的射精速度是：
		IF TALENT:MASTER:123
			PRINTL 早漏
		ELSEIF TALENT:MASTER:124
			PRINTL 晚泄
		ELSE
			PRINTL 普通
		ENDIF
	ENDIF
	PRINTFORM [29]%CALLNAME:MASTER%的体力是：
	IF TALENT:MASTER:125
		PRINTL 精力绝伦
	ELSEIF TALENT:MASTER:126
		PRINTL 精力薄弱
	ELSE
		PRINTL 普通
	ENDIF
	PRINTFORM [30]%CALLNAME:MASTER%的尿道括约肌是：
	IF TALENT:MASTER:116
		PRINTL ゆるめ
	ELSE
		PRINTL 普通
	ENDIF
ENDIF
DRAWLINE
PRINTFORML   page {TFLAG:999+1} / 2 
PRINTL [101]上一页      [109]下一页     [200] - 设定模式变更

;────────────────────────────────────
;主人公のゲージのセットアップ
;────────────────────────────────────
@BASE_MASTER_SETUP
;主人公の射精/母乳/尿意ゲージをセットアップ
IF TALENT:MASTER:122 || TALENT:MASTER:121
	IF TALENT:MASTER:123
		MAXBASE:MASTER:2 = 1000
	ELSEIF TALENT:MASTER:124
		MAXBASE:MASTER:2 = 2000
	ELSE
		MAXBASE:MASTER:2 = 1500
	ENDIF
ELSE
	MAXBASE:MASTER:2 = 0
ENDIF
MAXBASE:MASTER:3 = 0
MAXBASE:MASTER:4 = 0
SIF TALENT:MASTER:114
	MAXBASE:MASTER:3 = 10000
SIF TALENT:MASTER:116
	MAXBASE:MASTER:4 = 10000

;主人公の体力/気力/理性ゲージをセットアップ
;デフォルト
IF FLAG:5 == 0
	MAXBASE:MASTER:0 = 2000
	MAXBASE:MASTER:1 = 1200
	MAXBASE:MASTER:5 = 1000
ENDIF

;痛みに強い/痛みに弱い
IF TALENT:MASTER:40
	MAXBASE:MASTER:0 -= 50
	MAXBASE:MASTER:1 -= 25
ELSEIF TALENT:MASTER:41
	MAXBASE:MASTER:0 += 50
	MAXBASE:MASTER:1 += 25
ENDIF
;小柄体型/巨躯
IF TALENT:MASTER:110
	MAXBASE:MASTER:0 -= 100
ELSEIF TALENT:MASTER:111
	MAXBASE:MASTER:0 += 100
ENDIF
;精力絶倫/精力薄弱
IF TALENT:MASTER:125
	MAXBASE:MASTER:0 += 25
	MAXBASE:MASTER:1 += 75
ELSEIF TALENT:MASTER:126
	MAXBASE:MASTER:0 -= 25
	MAXBASE:MASTER:1 -= 75
ENDIF
;臆病/気丈
IF TALENT:MASTER:10
	MAXBASE:MASTER:1 -= 50
	MAXBASE:MASTER:5 -= 50
ELSEIF TALENT:MASTER:12
	MAXBASE:MASTER:1 += 50
	MAXBASE:MASTER:5 += 50
ENDIF
;プライド高い/プライド低い
IF TALENT:MASTER:15
	MAXBASE:MASTER:5 += 50
ELSEIF TALENT:MASTER:17
	MAXBASE:MASTER:5 -= 50
ENDIF
;感情乏しい/感情豊か
IF TALENT:MASTER:24
	MAXBASE:MASTER:1 -= 25
	MAXBASE:MASTER:5 += 50
ELSEIF TALENT:MASTER:25
	MAXBASE:MASTER:1 += 25
	MAXBASE:MASTER:5 -= 50
ENDIF
;楽観的/悲観的
IF TALENT:MASTER:27
	MAXBASE:MASTER:1 -= 25
ELSEIF TALENT:MASTER:26
	MAXBASE:MASTER:1 += 25
ENDIF
;抑圧/解放
IF TALENT:MASTER:32
	MAXBASE:MASTER:1 -= 50
ELSEIF TALENT:MASTER:33
	MAXBASE:MASTER:1 += 50
ENDIF
;自制的/衝動的
IF TALENT:MASTER:20
	MAXBASE:MASTER:5 += 50
ELSEIF TALENT:MASTER:21
	MAXBASE:MASTER:5 -= 50
ENDIF
;自慰しやすい
SIF TALENT:MASTER:60
	MAXBASE:MASTER:5 -= 50
;倒錯的
SIF TALENT:MASTER:80
	MAXBASE:MASTER:5 -= 50
;幼稚
SIF TALENT:MASTER:88
	MAXBASE:MASTER:5 -= 50
;狂気
SIF TALENT:MASTER:89
	MAXBASE:MASTER:5 -= 200
;蓬莱人
SIF TALENT:MASTER:115
	MAXBASE:MASTER:0 += 100
;禁断の知識
SIF TALENT:MASTER:130
	MAXBASE:MASTER:5 += 100

;極端な数値をギャップ
SIF MAXBASE:MASTER:0 < 1000
	MAXBASE:MASTER:0 = 1000
SIF MAXBASE:MASTER:0 > 3000
	MAXBASE:MASTER:0 = 3000
SIF MAXBASE:MASTER:1 < 600
	MAXBASE:MASTER:1 = 600
SIF MAXBASE:MASTER:1 > 1800
	MAXBASE:MASTER:1 = 1800
SIF MAXBASE:MASTER:5 < 500
	MAXBASE:MASTER:5 = 500
SIF MAXBASE:MASTER:5 > 1500
	MAXBASE:MASTER:5 = 1500

CFLAG:MASTER:4 = 100

;ストックの設定
MAXBASE:MASTER:9 = 2
BASE:MASTER:9 = 2
;ゲージを初期化する
BASE:MASTER:0 = MAXBASE:MASTER:0
BASE:MASTER:1 = MAXBASE:MASTER:1
BASE:MASTER:2 = MAXBASE:MASTER:2
BASE:MASTER:3 = MAXBASE:MASTER:3
BASE:MASTER:4 = 0
BASE:MASTER:5 = MAXBASE:MASTER:5

;ついでに衣装の初期化を呼び出します
CALL CLOTHES_SETUP(MASTER)
