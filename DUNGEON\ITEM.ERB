﻿;探索中のメニュー「アイテム」のターミナル
@SHOW_ITEM
SIF !STRLENS(LOCALS:4)
	SPLIT "////BOOKPAGE/DAYDREAMER//MINTTEA/ROSETEA/EARLGREY/CHAI/BLEND/ROYAL/LONELYPERFUME/STONE/FIRE/STICK", "/", LOCALS
DRAWLINE
CALL PRINT_BASEL("体力", MASTER, 0)
CALL PRINT_BASEL("気力", MASTER, 1)
CALL PRINT_BASEL("理性", MASTER, 5)
FOR LOCAL, 504, 600
	SIF LOCAL != 506 && ITEM:LOCAL
		PRINTFORML [{LOCAL}] - %ITEMNAME:LOCAL, 18, LEFT% × {ITEM:LOCAL, 2}
NEXT
PRINTL [100] - 戻る
PRINTL [101] - バックパック整理
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 100
		RETURN 0
	CASE 101
		CALL BACKPACK
	CASE 504, 505, 507 TO 599
		SIF !ITEM:RESULT
			GOTO ERROR
		CALLFORM ITEM_USE_%LOCALS:(RESULT - 500)%
	CASEELSE
		$ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
RESTART


;[101] - バックパック整理
@BACKPACK
LOCAL:1 = 0
WHILE 1
	DRAWLINE
	PRINTL 何を捨てますか？
	FOR LOCAL, 500, 600
		SIF ITEM:LOCAL
			PRINTFORML [{LOCAL}] - %ITEMNAME:LOCAL, 18, LEFT% × {ITEM:LOCAL, 2}
	NEXT
	PRINTL [100] - 戻る
	$INPUT_LOOP
	INPUT
	SELECTCASE RESULT
		CASE 100
			RETURN LOCAL:1
		CASE 500 TO 599
			SIF !ITEM:RESULT
				GOTO ERROR
			LOCAL = RESULT
			CALL CHOICE(@"%ITEMNAME:LOCAL%を捨てます。よろしいですか？", "はい", "いいえ")
			SIF RESULT
				CONTINUE
			PRINTFORMW %ITEMNAME:LOCAL%を捨てました
			ITEM:LOCAL--
			LOCAL:1 = 1
		CASEELSE
			$ERROR
			CLEARLINE 1
			GOTO INPUT_LOOP
	ENDSELECT
WEND


;[504] - 遭難マニュアル
@ITEM_USE_BOOKPAGE
DRAWLINE
PRINTL 理性を20％回復させます
CALL CHOICE("使用しますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
ITEM:遭難マニュアル--
BASE:MASTER:5 = MIN(BASE:MASTER:5 + MAXBASE:MASTER:5 / 5, MAXBASE:MASTER:5)
RETURN 1


;[505] - 撤退用戦車
@ITEM_USE_DAYDREAMER
DRAWLINE
CALL CHOICE("拠点に帰還しますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
ITEM:撤退用戦車--
CALL ESCAPE_DUNGEON
RETURN 1


;[507] - パルシュキ
@ITEM_USE_MINTTEA
DRAWLINE
PRINTL 体力を30％、気力を30％回復させます
CALL CHOICE("使用しますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
ITEM:パルシュキ--
BASE:MASTER:0 = MIN(BASE:MASTER:0 + ACCESSORY_MAXBASE(0) * 3 / 10, ACCESSORY_MAXBASE(0))
BASE:MASTER:1 = MIN(BASE:MASTER:1 + ACCESSORY_MAXBASE(1) * 3 / 10, ACCESSORY_MAXBASE(1))
RETURN 1


;[508] - ピッツァ
@ITEM_USE_ROSETEA
DRAWLINE
PRINTL 気力を100％回復させます
CALL CHOICE("使用しますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
ITEM:ピッツァ--
BASE:MASTER:1 = ACCESSORY_MAXBASE(1)
RETURN 1


;[509] - チュロス
@ITEM_USE_EARLGREY
DRAWLINE
PRINTL 体力を40％回復させます
CALL CHOICE("使用しますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
ITEM:チュロス--
BASE:MASTER:0 = MIN(BASE:MASTER:0 + ACCESSORY_MAXBASE(0) * 2 / 5, ACCESSORY_MAXBASE(0))
RETURN 1


;[510] - 肉じゃが
@ITEM_USE_CHAI
DRAWLINE
PRINTL 体力を20％、気力を50％回復させます
CALL CHOICE("使用しますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
ITEM:肉じゃが--
BASE:MASTER:0 = MIN(BASE:MASTER:0 + ACCESSORY_MAXBASE(0) / 5, ACCESSORY_MAXBASE(0))
BASE:MASTER:1 = MIN(BASE:MASTER:1 + ACCESSORY_MAXBASE(1) / 2, ACCESSORY_MAXBASE(1))
RETURN 1


;[511] - ハンバーガー
@ITEM_USE_BLEND
DRAWLINE
PRINTL 体力を100％回復させます
CALL CHOICE("使用しますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
ITEM:ハンバーガー--
BASE:MASTER:0 = ACCESSORY_MAXBASE(0)
RETURN 1


;[512] - パスタ
@ITEM_USE_ROYAL
DRAWLINE
PRINTL 体力を75％、気力を100％回復させます
CALL CHOICE("使用しますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
ITEM:パスタ--
BASE:MASTER:0 = MIN(BASE:MASTER:0 + ACCESSORY_MAXBASE(0) * 3 / 4, ACCESSORY_MAXBASE(0))
BASE:MASTER:1 = ACCESSORY_MAXBASE(1)
RETURN 1


;[513] - 焦げ臭いスコーン
@ITEM_USE_LONELYPERFUME
DRAWLINE
CALL CHOICE("焦げ臭いスコーンを使いますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
ITEM:焦げ臭いスコーン--
CFLAG:MASTER:222 = 20
RETURN 1


;[514] - 思考砥ぎの石
@ITEM_USE_STONE
DRAWLINE
PRINTL アクセサリのレベルを上げます
CALL ACCESSORY_LIST_EQUIP
$INPUT_LOOP
INPUT
LOCAL:10 = RESULT
SELECTCASE LOCAL:10
	CASE 0
		SIF !DC:0:0
			GOTO ERROR
		CALL ACCESSORY_DATA_EQUIP
		CALL CHOICE("このアクセサリに使用しますか？", "はい", "いいえ")
		SIF RESULT
			RESTART
		DC:0:4++
		CALL ACCESSORY_PALAMRESET_EQUIP
		;FLAG再設定
		CALL ACCESSORY_SET
		CALL ACCESSORY_DATA_EQUIP
	CASE 1 TO 99
		SIF !DA:(--LOCAL:10):0
			GOTO ERROR
		CALL ACCESSORY_DATA(LOCAL:10)
		CALL CHOICE("このアクセサリに使用しますか？", "はい", "いいえ")
		SIF RESULT
			RESTART
		DA:(LOCAL:10):4++
		CALL ACCESSORY_PALAMRESET(LOCAL:10)
		CALL ACCESSORY_DATA(LOCAL:10)
	CASE 100
		RETURN 0
	CASEELSE
		$ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
ITEM:思考砥ぎの石--
WAIT
RETURN 1


;[515] - 思考焼きの炎
@ITEM_USE_FIRE
DRAWLINE
PRINTL アクセサリのエンチャントを消去します
CALL ACCESSORY_LIST_EQUIP
$INPUT_LOOP1
INPUT
LOCAL:10 = RESULT
SELECTCASE LOCAL:10
	CASE 0
		WHILE 1
			SIF !DC:0:0
				GOTO ERROR_LOOP1
			CALL ACCESSORY_DATA_EQUIP
			CALL CHOICE("このアクセサリに使用しますか？", "はい", "いいえ")
			SIF RESULT
				RESTART
			;消去するエンチャントの選択
			PRINTL 消去するエンチャントを選んでください
			FOR LOCAL, 0, 6
				SIF DC:0:(LOCAL * 2 + 11)
					PRINTFORML [{LOCAL, 3}] - [%ACCESSORY_ENCHANT(DC:0:(LOCAL * 2 + 11), DC:0:(LOCAL * 2 + 12))%]
			NEXT
			PRINTL [100] - 戻る
			$INPUT_LOOP2
			INPUT
			SIF RESULT == 100
				CONTINUE
			IF RESULT < 0 || 5 < RESULT || !DC:0:(11 + 2 * RESULT)
				CLEARLINE 1
				GOTO INPUT_LOOP2
			ENDIF
			;エンチャント消去
			DC:0:(11 + 2 * RESULT) = 0
			DC:0:(12 + 2 * RESULT) = 0
			;エンチャント位置を詰める
			FOR LOCAL, 0, 5
				SIF DC:0:(11 + 2 * LOCAL)
					CONTINUE
				SWAP DC:0:(11 + 2 * LOCAL), DC:0:(13 + 2 * LOCAL)
				SWAP DC:0:(12 + 2 * LOCAL), DC:0:(14 + 2 * LOCAL)
			NEXT
			;FLAG再設定
			CALL ACCESSORY_SET
			CALL ACCESSORY_DATA_EQUIP
			BREAK
		WEND
	CASE 1 TO 99
		LOCAL:10--
		WHILE 1
			SIF !DA:(LOCAL:10):0
				GOTO ERROR_LOOP1
			CALL ACCESSORY_DATA(LOCAL:10)
			CALL CHOICE("このアクセサリに使用しますか？", "はい", "いいえ")
			SIF RESULT
				RESTART
			PRINTL 消去するエンチャントを選んでください
			FOR LOCAL, 0, 6
				SIF DA:(LOCAL:10):(LOCAL * 2 + 11)
					PRINTFORML [{LOCAL, 3}] - [%ACCESSORY_ENCHANT(DA:(LOCAL:10):(LOCAL * 2 + 11), DA:(LOCAL:10):(LOCAL * 2 + 12))%]
			NEXT
			PRINTL [100] - 戻る
			$INPUT_LOOP3
			INPUT
			SIF RESULT == 100
				CONTINUE
			IF RESULT < 0 || 5 < RESULT || !DA:(LOCAL:10):(11 + 2 * RESULT)
				CLEARLINE 1
				GOTO INPUT_LOOP3
			ENDIF
			DA:(LOCAL:10):(11 + 2 * RESULT) = 0
			DA:(LOCAL:10):(12 + 2 * RESULT) = 0
			FOR LOCAL, 0, 5
				SIF DA:(LOCAL:10):(11 + 2 * LOCAL)
					CONTINUE
				SWAP DA:(LOCAL:10):(11 + 2 * LOCAL), DA:(LOCAL:10):(13 + 2 * LOCAL)
				SWAP DA:(LOCAL:10):(12 + 2 * LOCAL), DA:(LOCAL:10):(14 + 2 * LOCAL)
			NEXT
			CALL ACCESSORY_DATA(LOCAL:10)
			BREAK
		WEND
	CASE 100
		RETURN 0
	CASEELSE
		$ERROR_LOOP1
		CLEARLINE 1
		GOTO INPUT_LOOP1
ENDSELECT
ITEM:思考焼きの炎--
WAIT
RETURN 1

;[516] - 魔法のステッキ
@ITEM_USE_STICK
DRAWLINE
PRINTW ここで魔法を使うのは危険だ
RETURN 1

;探索中のメニュー「アクセサリ」のターミナル
@DUNGEON_ACCESSORY
PRINTL [  0] - アクセサリ情報
PRINTL [  1] - アクセサリ装備
PRINTL [100] - 戻る
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0
		CALL DUNGEON_ACCESSORY_SHOW
	CASE 1
		;アクセサリ装備はショップメニューと共通
		CALL ACCESSORY_EQUIP
	CASE 100
		RETURN 0
	CASEELSE
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
RESTART


;[  0] - アクセサリ情報
@DUNGEON_ACCESSORY_SHOW
DRAWLINE
PRINTL 所持品
FOR LOCAL, 0, 5
	SIF DA:LOCAL:0
		PRINTFORML [{LOCAL, 3}] - %STR:(905 + 4 * DA:LOCAL:1 + DA:LOCAL:2)%%STR:(900 + DA:LOCAL:1)%
NEXT
PRINTL [100] - 戻る
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0 TO 99
		SIF !DA:RESULT:0
			GOTO ERROR
		CALL ACCESSORY_DATA(RESULT)
		WAIT
		RESTART
	CASE 100
	CASEELSE
		$ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
