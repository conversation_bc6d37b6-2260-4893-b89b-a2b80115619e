﻿;独特など、SHOPの@ESCAPEと別枠で处理したい脱走イベント用のファイルです
@ESCAPE_EVENT

LOCAL:1 = 0
LOCAL:2 = 0
REPEAT CHARANUM
	;メローナ
	SIF NO:COUNT == 35
		LOCAL:1 = COUNT
	;ストゥーナ
	SIF NO:COUNT == 34
		LOCAL:2 = COUNT
REND
;メローナに捕まった(めんどくさいので条件はゆるゆる)
IF FLAG:1741 & 4 && NO:TARGET == 35
	IF CFLAG:801
		PRINTFORML %CALLNAME:MASTER%は何とか迷宮から脱出し、拠点までたどり着いた。
		PRINTFORML 久々の我が家でくつろいでいると、ドアをノックする音が聞こえる。
		PRINTFORML %CALLNAME:MASTER%がドアを開けると%CALLNAME:TARGET%が立っていた。
		PRINTFORMW どうやら尾行されていたようだ…
		PRINTFORMW %CALLNAME:TARGET%が拠点に住み着きました。
		;押しかけ口上
		CALL KOJO_EVENT(14)
		FLAG:1701 = 0
		RETURN 1
	ELSE
		PRINTFORMW しかし、つかまってしまった…
	ENDIF
ENDIF

;ストゥーナに捕まった(めんどくさいので条件はゆるゆる)
IF FLAG:1741 & 64 && NO:TARGET == 34
	IF CFLAG:801
		PRINTFORML %CALLNAME:MASTER%は何とか迷宮から脱出し、拠点までたどり着いた。
		PRINTFORML 久々の我が家でくつろいでいると、ドアをノックする音が聞こえる。
		PRINTFORML %CALLNAME:MASTER%がドアを開けると%CALLNAME:TARGET%が立っていた。
		PRINTFORMW どうやら尾行されていたようだ…
		PRINTFORMW %CALLNAME:TARGET%が拠点に住み着きました。
		;押しかけ口上
		CALL KOJO_EVENT(14)
		FLAG:1701 = 0
		RETURN 1
	ELSE
		PRINTFORMW しかし、つかまってしまった…
	ENDIF
ENDIF
;3姉妹に捕まった
IF FLAG:1746 & 4 && (NO:TARGET >= 38 && NO:TARGET <= 40)
	IF CFLAG:801
		PRINTFORML %CALLNAME:MASTER%は何とか迷宮から脱出し、拠点までたどり着いた。
		PRINTFORML 久々の我が家でくつろいでいると、ドアをノックする音が聞こえる。
		PRINTFORML %CALLNAME:MASTER%がドアを開けると%CALLNAME:TARGET%が立っていた。
		PRINTFORMW どうやら尾行されていたようだ…
		PRINTFORMW %CALLNAME:TARGET%が拠点に住み着きました。
		;押しかけ口上
		CALL KOJO_EVENT(14)
		FLAG:1701 = 0
		RETURN 1
	ELSE
		PRINTFORMW しかし、つかまってしまった…
	ENDIF
ENDIF
;マーシャにつかまった
IF FLAG:1742 & 4 && NO:TARGET == 32
	IF CFLAG:801
		PRINTFORML %CALLNAME:MASTER%は何とか迷宮から脱出し、拠点までたどり着いた。
		PRINTFORML 久々の我が家でくつろいでいると、ドアをノックする音が聞こえる。
		PRINTFORML %CALLNAME:MASTER%がドアを開けると%CALLNAME:TARGET%が立っていた。
		PRINTFORMW どうやら尾行されていたようだ…
		PRINTFORMW %CALLNAME:TARGET%が拠点に住み着きました。
		;押しかけ口上
		CALL KOJO_EVENT(14)
		FLAG:1701 = 0
		RETURN 1
	ELSE
		PRINTFORMW しかし、つかまってしまった…
	ENDIF
ENDIF

