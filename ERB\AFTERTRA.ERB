﻿;────────────────────────────────────
;調教終了の処理
;────────────────────────────────────
@EVENTEND
;ムラムラリセット
SIF TARGET > 0
	CFLAG:161 = 0
SIF ASSI:1 > 0
	CFLAG:(ASSI:1):161 = 0
SIF ASSI:2 > 0
	CFLAG:(ASSI:2):161 = 0
;コスプレ
IF CFLAG:12 == 3
	SELECTCASE CFLAG:79
		;ボンデージ
		CASE 1
			TALENT:施虐狂 -= 1
		;和服
		CASE 2
			TALENT:清楚 -= 1
		;メイド服
		CASE 3
			TALENT:自我奉献 -= 1
		;チャイナ服
		CASE 4
			TALENT:美脚 -= 1
		;バニースーツ
		CASE 7
			TALENT:兽耳 -= 1
	ENDSELECT
ENDIF
;MASTER側のCFLAGに誰にどれだけ搾られたかを記録
;一回で最大5000くらい？
CFLAG:MASTER:(2200 + NO:TARGET) += TFLAG:201 * (5 + CFLAG:10) / 5
;ASSIはTARGETの1/5
FOR LOCAL,0,9
	IF ASSI:(LOCAL + 1) > 0
		CFLAG:MASTER:(2200 + NO:(ASSI:(LOCAL + 1))) += TFLAG:201 * (5 + CFLAG:(ASSI:(LOCAL + 1)):10) / 25
	ENDIF
NEXT
;累積MP
SIF TARGET > 0
	CFLAG:204 += TFLAG:201
IF !FLAG:1710
	SIF ASSI:3 > 0
		DELCHARA (CHARANUM - 1)
	ASSI:3 = -1
	ASSI = ASSI:1

	CFLAG:TARGET:999 = 0
	SIF ASSI:1 > 0
		CFLAG:(ASSI:1):999 = 0
	SIF ASSI:2 > 0
		CFLAG:(ASSI:2):999 = 0
ENDIF
FLAG:10 = 0

IF FLAG:1700
	SIF ASSI > 0
		EXP:ASSI:8 += TFLAG:64 / 10
	SIF ASSI:2 > 0
		EXP:(ASSI:2):8 += TFLAG:64 / 10
	IF FLAG:1710
		CALL KOJO_EVENT(15, FLAG:1710 - 1)
		IF FLAG:1710 == 1
			PRINTFORML 总算逃脱了。
		ELSEIF FLAG:1710 == 2 || FLAG:1710 == 3
			
			PRINTFORML %CALLNAME:MASTER%用尽了力气…
		ENDIF
	ELSEIF FLAG:1701
		IF BASE:MASTER:5 <= 0 && BASE:MASTER:1 <= 0 && BASE:MASTER:2 <= 0
			CALL KOJO_EVENT(15, 1)
		ELSE
			CALL KOJO_EVENT(15, 2)
		ENDIF
		PRINTFORML %CALLNAME:TARGET%把力竭的%CALLNAME:MASTER%带回家了…
	ELSE
		CALL KOJO_EVENT(15)
		PRINTW 总算逃脱了。
		TARGET = -1
		ASSI = -1
		ASSI:1 = -1
		ASSI:2 = -1
		REPEAT FLAG:1703
			A = CHARANUM - 1
			DELCHARA A
		REND
		FLAG:1702 = 0
		
		;敵の再生産
		CALL DUNGEON_MOVE
		WHILE FLAG:1707 < 2
			A = 1800 + RAND:100
			SIF (FLAG:A & 1) || !(FLAG:A & 2)
				CONTINUE
			FLAG:A |= 8
			BREAK
		WEND
	ENDIF
ELSE
	PRINTW 调教结束了
	EXP:MASTER:8 += TFLAG:64 / 10
	EXP:8 += TFLAG:64 / 10
	;調教時間をチェックして記録
	SIF TFLAG:64 > FLAG:1211
		FLAG:1211 = TFLAG:64
	IF TFLAG:64 > 0
		SIF TFLAG:64 < FLAG:1212 || FLAG:1212 == 0
			FLAG:1212 = TFLAG:64
	ENDIF
	
;────────────────────────────────────
;母乳の価値の計算
;────────────────────────────────────
	SIF TALENT:MASTER:114 && TFLAG:50 > 0
		CALL SELL_MILK
	
;────────────────────────────────────
;レベルアップ。たくさん経験値ためても一回の調教は一度だけレベルアップできる仕様です
;────────────────────────────────────
	IF TFLAG:101 == 0
		IF CFLAG:MASTER:1 > CFLAG:MASTER:4
			CFLAG:MASTER:0 += 1
			CFLAG:MASTER:1 -= CFLAG:MASTER:4
			CFLAG:MASTER:4 += 100 + CFLAG:MASTER:0 * CFLAG:MASTER:0 * 10
			;レベル１０以降必要経験値は急に上がりますが、異常経験で軽減できます
			A = 250 + (CFLAG:MASTER:0 - 5) * CFLAG:MASTER:4 / 3 - EXP:MASTER:50 * 20
			SIF A > 0 && CFLAG:MASTER:0 > 10
				CFLAG:MASTER:4 += A
			PRINTFORMW %CALLNAME:MASTER%的调教等级变成了[{CFLAG:MASTER:0}]
			MAXBASE:MASTER:0 += 100 + 10 * RAND:6
			MAXBASE:MASTER:1 += 50 + 10 * RAND:6
		ENDIF
		
		IF CFLAG:1 > CFLAG:4
			CFLAG:0 += 1
			CFLAG:1 -= CFLAG:4
			CFLAG:4 += 100 + CFLAG:0 * CFLAG:0 * 10
			A = 250 + (CFLAG:0 - 5) * CFLAG:4 / 3 - EXP:MASTER:50 * 20
			SIF A > 0 && CFLAG:0 > 10
				CFLAG:4 += A
			PRINTFORMW %CALLNAME:TARGET%的调教等级变成了[{CFLAG:0}]
			MAXBASE:0 += 100 + 10 * RAND:6
			MAXBASE:1 += 50 + 10 * RAND:6
		ENDIF
		
		TFLAG:201 = MAX(TFLAG:201, 10)
		PRINTFORMW %CALLNAME:MASTER%从%CALLNAME:TARGET%那里吸取的精力转化成了{TFLAG:201}MP
		CFLAG:201 += TFLAG:201
		IF CFLAG:9 >= 500 && CFLAG:210 == 0
			CFLAG:210 = 1
			PRINTFORML %CALLNAME:TARGET%看着%CALLNAME:MASTER%的眼神有些奇怪…
			PRINTFORMW %CALLNAME:TARGET%似乎开始对%CALLNAME:MASTER%产生了好感
		ENDIF
		IF CFLAG:9 / 250 >= CFLAG:0 - CFLAG:MASTER:0 && CFLAG:210 == 1
			CFLAG:210 = 2
			PRINTFORML %CALLNAME:TARGET%看着%CALLNAME:MASTER%的眼神有些奇怪…
			PRINTFORMW %CALLNAME:TARGET%对%CALLNAME:MASTER%萌生了“相亲相爱”的感情
		ENDIF
	ENDIF
	
;────────────────────────────────────
;平時アライメントの変動
;────────────────────────────────────
	LOCAL = 10 * CFLAG:6 - CFLAG:90
	LOCAL:1 = (CFLAG:150 + CFLAG:152 + 200) / 2
	;夢魔の性格で倍率が決まる。
	;最小値50(Mデレ)、最大値150(Sツン)、素質変動しない/させないキャラではB = 100
	CFLAG:90 += LOCAL * (LOCAL >= 0 ? 200 - LOCAL:1 # LOCAL:1) / 500
	IF CFLAG:90 > 100 && MARK:5 == 0
		MARK:5 = 1
		CFLAG:162 = 1
	ELSEIF CFLAG:90 > 200 && MARK:5 == 1
		MARK:5 = 2
		CFLAG:162 = 1
	ELSEIF CFLAG:90 > 300 && MARK:5 == 2
		MARK:5 = 3
		CFLAG:162 = 1
	ELSEIF CFLAG:90 > 400 && MARK:5 == 3
		MARK:5 = 4
		CFLAG:162 = 1
	ELSEIF CFLAG:90 > 500 && MARK:5 == 4
		MARK:5 = 5
		CFLAG:162 = 1
	ELSEIF CFLAG:90 > 600 && MARK:5 == 5
		MARK:5 = 6
		CFLAG:162 = 1
	ELSEIF CFLAG:90 > 700 && MARK:5 == 6
		MARK:5 = 7
		CFLAG:162 = 1
	ELSEIF CFLAG:90 > 800 && MARK:5 == 7
		MARK:5 = 8
		CFLAG:162 = 1
	ELSEIF CFLAG:90 > 900 && MARK:5 == 8
		MARK:5 = 9
		CFLAG:162 = 1
	ELSEIF CFLAG:90 > 950 && MARK:5 == 9
		MARK:5 = 10
		CFLAG:162 = 1
	ENDIF
;────────────────────────────────────
;空射精ボーナス
;────────────────────────────────────
	IF TFLAG:129 > 0 
		IF MAXBASE:MASTER:2 < 2500 + CFLAG:MASTER:203 * 500
			MAXBASE:MASTER:2 = MIN(MAXBASE:MASTER:2 + TFLAG:129 * 100, 2500 + CFLAG:MASTER:203 * 500)
			PRINTFORML 因为过度地被榨取、%CALLNAME:MASTER%的性能力强化了
		ELSE
			PRINTFORML 性能力的强化达到了极限
		ENDIF
		TFLAG:129 = 0
	ENDIF
ENDIF

;────────────────────────────────────
;リセット
;────────────────────────────────────
;尿意ゲージを消す
MAXBASE:MASTER:4 = TALENT:MASTER:116 ? MAXBASE:MASTER:4 # 0
;調教で蓄積した疲弊を日常に加算
CFLAG:MASTER:11 = MIN(CFLAG:MASTER:11 + MAX(TFLAG:63, 0), 20)
SIF TARGET >= 0
	CFLAG:TARGET:11 += MAX(TFLAG:62, 0)

;────────────────────────────────────
;敗北時
;────────────────────────────────────
FLAG:1700 = FLAG:1701 ? 0 # FLAG:1700

BEGIN TURNEND
