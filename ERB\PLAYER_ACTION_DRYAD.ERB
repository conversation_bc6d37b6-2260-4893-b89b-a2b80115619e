﻿;────────────────────────────────────
;ドライアド (蔦拘束31)
;────────────────────────────────────
@ACT_EXTRA_M3
LOCAL:31 = TFLAG:402 < 0 ? ABS(TFLAG:402 * 20) # 0

;調教者の素質
;縛り上手
SIF TALENT:58
	LOCAL:31 += 5
;倒錯的
SIF TALENT:80
	LOCAL:31 += 5
;サド
SIF TALENT:83
	LOCAL:31 += 10
;意地悪/心根優しい
IF TALENT:86
	LOCAL:31 += 5
ELSEIF TALENT:87
	LOCAL:31 -= 5
ENDIF

;調教者の性格
;サド-マゾ軸
SELECTCASE CFLAG:150
	CASE IS > 40
		LOCAL:31 += 10
	CASE IS > 20
		LOCAL:31 += 7
	CASE IS > -20
		LOCAL:31 += 5
	CASE IS > -40
		LOCAL:31 += 3
ENDSELECT

;アライメント
SELECTCASE CFLAG:6
	CASE IS > 49
		LOCAL:31 += 0
	CASE IS > 39
		LOCAL:31 += 2
	CASE IS > 29
		LOCAL:31 += 4
	CASE IS > 19
		LOCAL:31 += 7
	CASE IS > 9
		LOCAL:31 += 10
	CASE IS > -1
		LOCAL:31 += 13
	CASE IS > -11
		LOCAL:31 += 17
	CASE IS > -21
		LOCAL:31 += 21
	CASE IS > -31
		LOCAL:31 += 25
	CASE IS > -41
		LOCAL:31 += 30
	CASE IS > -51
		LOCAL:31 += 35
	CASEELSE
		LOCAL:31 += 40
ENDSELECT

;苛立
SELECTCASE BASE:7
	CASE IS > 800
		LOCAL:31 += 20
	CASE IS > 600
		LOCAL:31 += 15
	CASE IS > 400
		LOCAL:31 += 10
	CASE IS > 200
		LOCAL:31 += 5
ENDSELECT

;調教者の状態
SELECTCASE TFLAG:60
	;怒り
	CASE 6
		LOCAL:31 += 10
		LOCAL:31 *= 2
	;狂乱
	CASE 8
		LOCAL:31 *= 2
ENDSELECT

;CALL ACT_EXTRA_ABLE31
;LOCAL:31 = RESULT && LOCAL:31 > -99 ? LOCAL:31 # -99

;技巧が低いと失敗
SIF GET_ABL(TARGET, 2) * BASE:TARGET:1 < RAND:150 * MAXBASE:TARGET:1
	LOCAL:31 = -99

;10 < LOCAL:31 < 75(性格、アライメントのみ) < 95(+苛立) < 210(+状態)
SELECTCASE LOCAL:31
	CASE -99
		TFLAG:232 = 0
	CASE LOCAL:31
		TFLAG:232 = RAND:(MAX(LOCAL:31, 10)) > RAND:100 ? 1 # 0
		TEQUIP:46 = TFLAG:232 ? 3 + RAND:4 # 0
ENDSELECT
RETURN TFLAG:232


@ACT_EXTRA_MESSAGE_M3
PRINTFORML %CALLNAME:MASTER%被%CALLNAME:TARGET%背后出现的藤蔓拘束住了四肢，无法动弹…


;-----------------------------------------------------------
;ドライアド の実行判定
;-----------------------------------------------------------
@ACTM_EXTRA_ABLE3
CALL ACT_EXTRA_ABLE31
RETURN RESULT

;-----------------------------------------------------------
;蔦で拘束 の実行判定
;-----------------------------------------------------------
@ACT_EXTRA_ABLE31
;調教対象がドライアドでない
SIF NO:TARGET != 3
	RETURN 0
;アライメント変動が大きくマイナスになっていない
SIF TFLAG:402 > -2
	RETURN 0
;縄装着中
SIF TEQUIP:40
	RETURN 0
;既に蔦で拘束している
SIF TEQUIP:46
	RETURN 0
RETURN 1
