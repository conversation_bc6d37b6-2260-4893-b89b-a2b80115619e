﻿@DUNGEON_SELECT
PRINTL 
;ご主人様が付いてきてくれる可能性
LOCAL:1 = 0
FOR LOCAL:5, 1, CHARANUM
	IF CFLAG:(LOCAL:5):95 && TALENT:(LOCAL:5):190
		LOCAL:(10 + LOCAL:1) = LOCAL:5
		LOCAL:1++
		SIF MARK:(LOCAL:5):2 == 10
			LOCAL:6 = LOCAL:5
	ENDIF
NEXT
DRAWLINE
PRINTL どこを探索しますか？
FOR LOCAL,0,6
	SIF FLAG:5 != 8 || GETBIT(FLAG:1900,LOCAL)
		PRINTFORML [{100+LOCAL}] - %STR:(801 + LOCAL)%
NEXT

PRINTL [200] - 戻る
$SELECT
INPUT
IF RESULT == 200
	BEGIN SHOP
ELSEIF RESULT >= 100 && RESULT <= 105 && (FLAG:5 != 8 || GETBIT(FLAG:1900,LOCAL))
	A = RESULT
	PRINTFORML 【%STR:(701 + A)%】
	PRINTL 
	;霧に魅入られたものの街は協力者が居ないと不可
	IF !LOCAL:1 && A == 105
		PRINTFORMW １人で%STR:806%を訪れるのは危険だろう
		RESTART
	ENDIF
	PRINTL [0] - 戻る
	PRINTL [1] - 探索を開始する
	IF FLAG:(1721 + RESULT - 100)
		PRINTL 転送機が使えます
		LOCAL = 1
		COUNT = 0
		WHILE LOCAL
			IF COUNT + 1 <= FLAG:(1721 + RESULT - 100)
				PRINTFORML [{COUNT + 2}] {5 * (COUNT + 1)}階から
				COUNT += 1
			ELSE
				LOCAL = 0
			ENDIF
		WEND
	ENDIF
	INPUT
	IF RESULT == 0
		RESTART
	ELSEIF RESULT > 0 && RESULT < COUNT + 2
		IF CFLAG:MASTER:11
			PRINTW 今日は疲れている…
			RETURN 0
		ENDIF
		;拠点の状況を保存
		FLAG:30 = CHARANUM
		FLAG:1706 = CFLAG:MASTER:12
		TARGET:10 = TARGET
		ASSI:10 = ASSI
		ASSI:11 = ASSI:1
		ASSI:12 = ASSI:2
		ASSI:14 = -1
		TARGET = -1
		ASSI = -1
		ASSI:1 = -1
		ASSI:2 = -1
		ASSI:4 = -1
		LOCAL:7 = RESULT

		IF LOCAL:1
			LOCAL:2 = RAND:(LOCAL:1)
			LOCAL:3 = LOCAL:(10 + LOCAL:2)
			IF TALENT:(LOCAL:3):5 && A != 105
				PRINTFORML %CALLNAME:(LOCAL:3)%は迷宮探索について行きたいと思ったようですが、
				PRINTFORMW 子供と二人で%CALLNAME:MASTER%の帰りを待つことにしたようです
			ELSE
				ASSI:4 = LOCAL:3
				SIF LOCAL:6 && CFLAG:MASTER:15 == 4
					ASSI:4 = LOCAL:6
				PRINTFORMW %CALLNAME:(ASSI:4)%が迷宮探索についてきてくれました
				CALL KOJO_EVENT_SUPPORT(7)
			ENDIF
		ENDIF
		
		REPEAT 50
			FLAG:(1650 + COUNT) = ITEM:COUNT
			ITEM:COUNT = 0
		REND
		FLAG:1700 = A - 99
		FLAG:1712 = 0
		IF LOCAL:7 == 1
			FLAG:1704 = 1
		ELSE
			FLAG:1704 = 5 * (LOCAL:7 - 1)
		ENDIF
		IF A == 100
			CALL DUNGEON_CAVE
		ELSEIF A == 101
			CALL DUNGEON_FOREST
		ELSEIF A == 102
			CALL DUNGEON_SEA
		ELSEIF A == 103
			CALL DUNGEON_LIBRARY
		ELSEIF A == 104
			CALL DUNGEON_DESERT
		ELSEIF A == 105
			CALL DUNGEON_TOWN
		ENDIF
		BASE:MASTER:5 = MAXBASE:MASTER:5
		CALL DUNGEON
	ENDIF
ELSE
	PRINTL それは無理だ。
	GOTO SELECT
ENDIF

;--------------------------------------------------------------------------------
@DUNGEON
;--------------------------------------------------------------------------------
;TFLAGをリセット。
;本当は全部リセットしたいが念のため0～499まで。いずれVARSETで書き換える
FOR LOCAL, 0, 500
	TFLAG:LOCAL = 0
NEXT
;階層
PRINTL 
DRAWLINE
$MENU
;現状復帰
SIF ASSI:14 > -1
	ASSI:4 = ASSI:14
;敵シンボルにつっこんだ場合先制フラグが立ちます
CALL DUNGEON_MOVE
FLAG:1781 = (FLAG:M & 0x000C) == 12

;体力の回復
LOCAL = ACCESSORY_MAXBASE(0) * FLAG:3129 / 300 + (CFLAG:MASTER:15 == 1 ? ACCESSORY_MAXBASE(0) / 500 # 0)
BASE:MASTER:0 = MIN(BASE:MASTER:0 + LOCAL, ACCESSORY_MAXBASE(0))
;気力の回復
LOCAL = ACCESSORY_MAXBASE(1) * (BASE:MASTER:5 + 100) * (FLAG:3127 + 5) * (CFLAG:MASTER:15 == 1 ? 3 # 2) / (200 * MAXBASE:MASTER:5)
BASE:MASTER:1 = MIN(BASE:MASTER:1 + LOCAL, ACCESSORY_MAXBASE(1))
;理性の減少
BASE:MASTER:5 = CFLAG:MASTER:15 == 1 ? BASE:MASTER:5 * (995 + FLAG:3128 * 3) / 1000 # BASE:MASTER:5 * (990 + FLAG:3128 * 3) / 1000
;状態異常フラグの減衰
FOR LOCAL, 0, 10
	CFLAG:MASTER:(220 + LOCAL) = MAX(CFLAG:MASTER:(220 + LOCAL) - 1, 0)
NEXT

CALL ENEMY_MOVE
;鈍足処理
SIF CFLAG:MASTER:221 || FLAG:M & 512
	CALL ENEMY_MOVE
CALL DUNGEON_MAP
;階段
IF FLAG:M & 4 && FLAG:M & 64
	PRINTW ここから次の階層に進めそうだ
	PRINTL [1] - 進む
	PRINTL [2] - 留まる
	DRAWLINE
	INPUT
	IF RESULT == 1
		CALL STAIRS
		GOTO MENU
	ENDIF
ENDIF
;上階段
IF FLAG:M & 4 && FLAG:M & 32
	PRINTW ここから拠点に帰れそうだ
	PRINTL [1] - 帰る
	PRINTL [2] - 留まる
	DRAWLINE
	INPUT
	IF RESULT == 1
		CALL ESCAPE_DUNGEON
		RETURN 0
	ENDIF
ENDIF
DRAWLINE

CALL DUNGEON_MOVE
IF FLAG:M & 4 && FLAG:M & 4096
	PRINTW 鍛冶屋を捕まえた
	DRAWLINE
	FLAG:M -= 8
	FLAG:M -= 4096
	SIF FLAG:1700 == 6
		FLAG:1712 = 1
	CALL BUTTERFLY
	RETURN 0
ENDIF
IF FLAG:M & 4 && FLAG:M & 8
	;敵の数をリセット
	FLAG:1702 = 0
	PRINTW 敵と遭遇した！
	DRAWLINE
	FLAG:M -= 8
	CALL ENEMY_BATTLE
	RETURN 0
ENDIF
;雑多な消費アイテムを拾います。
IF FLAG:M & 4 && FLAG:M & 1024
	CALL ITEM_RANDUM_GET
	PRINTFORMW %ITEMNAME:Z%を拾った
	ITEM:Z += 1
	FLAG:M -= 1024
	;ご主人様のアイテム発見能力
	IF ASSI:4 > 0
		IF TALENT:(ASSI:4):182 && !RAND:2
			CALL ITEM_RANDUM_GET
			PRINTFORMW %CALLNAME:(ASSI:4)%はさらに%ITEMNAME:Z%を見つけてきた
			CALL KOJO_EVENT_SUPPORT(3)
			ITEM:Z += 1
		ENDIF
	ENDIF
ENDIF
;大人のおもちゃ、アクセサリなどが入っています
;ミミックが異様にわかりやすい場合がありますが、いいよね
IF FLAG:M & 4 && FLAG:M & 16
	;ミミックの場合先に敵番号を決定する
	IF FLAG:M & 2048
		CALL ENEMY_GET
		LOCAL:1 = RESULT
		PRINTFORML %TOMATO_BOX(LOCAL:1)%を見つけた
	ELSE
		PRINTL 宝箱を見つけた
	ENDIF
	PRINTW 開錠を試みますか？
	A = SQRT (FLAG:1705 )
	PRINTL [1] - はい
	PRINTL [2] - いいえ
	DRAWLINE
	INPUT
	SIF RESULT != 1
		GOTO INPUT_MENU
	IF FLAG:M & 2048
		FLAG:M -= 16
		FLAG:M -= 2048
		FLAG:1702 = 0
		REPEAT 9
			A:(COUNT + 1) = -1
		REND
		FLAG:74 = 1
		PRINTFORML %TOMATO_BOX(LOCAL:1)%の中には敵が隠れていた！
		CALL ENEMY_SPECIES(LOCAL:1)
		PRINTFORMW %CALLNAME:(A:1)%Lv{CFLAG:(A:1):0}が現れた！
		CFLAG:TARGET:999 = 1
		BEGIN TRAIN
		RETURN 0
	ELSEIF RAND:((ABL:MASTER:開錠技能 + 1) * (1 + FLAG:3138)) >= RAND:A
		EXP:MASTER:開錠経験 += A
		PRINTL 宝箱を開錠した
		PRINTFORMW 開錠経験 +{A}
		CALL DUNGEON_MOVE
		FLAG:M -= 16
		CALL SKILL_UP
		CALL ITEM_GET
		;ご主人様のアイテム発見能力
		IF ASSI:4 > -1
			IF TALENT:(ASSI:4):182 && RAND:4 > 2
				PRINTFORMW %CALLNAME:(ASSI:4)%はさらに宝箱を漁り、何か見つけたようだ
				CALL KOJO_EVENT_SUPPORT(3)
				CALL ITEM_GET
			ENDIF
		ENDIF
	ELSE
		EXP:MASTER:開錠経験 += 1
		CALL DUNGEON_MOVE
		FLAG:M -= 16
		PRINTW トラップだ！
		PRINTFORMW 開錠経験 +1
		CALL TRAP
	ENDIF
	RESTART
	DRAWLINE
ENDIF

;設置罠
IF FLAG:M & 4 && FLAG:M & 8192
	LOCAL = 2
	SIF FLAG:3135
		LOCAL = 3
	SIF FLAG:3136
		LOCAL = 1
	IF RAND:LOCAL == 0
		FLAG:M -= 8192
		CALL TRAP
		RESTART
		DRAWLINE
	ENDIF
ENDIF
;ランダムイベント
IF FLAG:M & 4 && FLAG:M & 16384
;	CALL 
ENDIF

;バックパック容量オーバー
IF SUMARRAY(ITEM, 500, 600) > 20
	DO
		PRINTL バックパックの容量が足りない
		PRINTL 何を捨てますか？
		FOR LOCAL, 500, 600
			SIF ITEM:LOCAL
				PRINTFORML [{LOCAL}] - %ITEMNAME:LOCAL, 18, LEFT% × {ITEM:LOCAL, 2}
		NEXT
		$INPUT_LOOP
		INPUT
		LOCAL = RESULT
		SIF LOCAL < 500 || 599 < LOCAL || !ITEM:LOCAL
			GOTO INPUT_LOOP
		CALL CHOICE(@"%ITEMNAME:LOCAL%を捨てます。よろしいですか？", "はい", "いいえ")
	LOOP RESULT
	ITEM:LOCAL--
	PRINTFORMW %ITEMNAME:LOCAL%を捨てました
	CALL DUNGEON_MAP
	RESTART
ENDIF

;イベント
IF FLAG:M & 4 && FLAG:M & 128
	CALL EVENT
	IF FLAG:1711
		FLAG:1711 = 0
		IF FLAG:72 == 1
			FLAG:72 = 0
			;敵の数をリセット
			FLAG:1702 = 0
			CALL ENEMY_BATTLE
			RETURN 0
		ELSEIF FLAG:72 == 2 || FLAG:72 == 3
			SIF FLAG:72 == 2
				FLAG:71 = 1
			FLAG:72 = 0
			ASSI:14 = ASSI:4
			TARGET = ASSI:4
			FLAG:1714 = FLAG:1700
			FLAG:1700 = 0
			BEGIN TRAIN
			RETURN 0
		ELSEIF FLAG:72 == 4
			CALL ESCAPE_DUNGEON
			RETURN 0
		ENDIF
		CALL DUNGEON_MAP
		GOTO INPUT_MENU
	ELSE
		RETURN 0
	ENDIF
ENDIF

IF FLAG:1700
	PRINTFORM %NAME:MASTER%は
	SIF ASSI:4 > -1
		PRINTFORM %NAME:(ASSI:4)%と
	PRINTFORML %STR:(800 + FLAG:1700)%{FLAG:1704}階を探索している……
	CALL PRINT_BASEL("体力", MASTER, 0)
	CALL PRINT_BASEL("気力", MASTER, 1)
	SIF PENIS(MASTER)
		CALL PRINT_BASEL("精力", MASTER, 2)
	SIF TALENT:MASTER:母乳体質
		CALL PRINT_BASEL("母乳", MASTER, 3)
	CALL PRINT_BASEL("理性", MASTER, 5)
	IF PENIS(MASTER)
		PRINT 勃起
		BAR TFLAG:131, MAX(TFLAG:131, 1000), 32
		PRINTFORML ({TFLAG:131, 5}\@ TFLAG:131 > 1000 ? # / 1000 \@)
	ENDIF
	;状態異常フラグの表示
	PRINTFORML \@ CFLAG:MASTER:220 ? [敏感] # \@\@ CFLAG:MASTER:221 ? [鈍足] # \@\@ CFLAG:MASTER:222 ? [焦げ臭いスコーン] # \@
	DRAWLINE
	PRINTL 

	$INPUT_MENU
	PRINTL [7] - 北西　[8] - 北　　[9] - 北東　
	PRINTL [4] - 西　　[5] - 待機　[6] - 東　　
	PRINTL [1] - 南西　[2] - 南　　[3] - 南東　
	PRINTL [101] - 能力の表示
	PRINTL [200] - アイテム
	PRINTL [201] - アクセサリ
	PRINTL [202] - 迷宮の見方
	;ご主人様の脱出能力
	IF ASSI:4 > -1
		IF TALENT:(ASSI:4):181
			PRINTL [300] - 帰還
		ENDIF
	ENDIF
	IF FLAG:4
		PRINTL [300] - 帰還
		PRINTL [301] - フラグチェック
	ENDIF
	$INPUT_LOOP1
	INPUT
	IF RESULT == 9
		CALL DUNGEON_MOVE
		A = M - 10
		B = M - 9
		C = M + 1
		IF M < 1810 || M % 10 == 9 || FLAG:A & 1 || FLAG:B & 1 || FLAG:C & 1
			PRINTW *ゴンッ*
		ELSE
			FLAG:M -= 4
			M -= 9
			FLAG:M |= 4
		ENDIF
		GOTO MENU
	ELSEIF RESULT == 8
		CALL DUNGEON_MOVE
		A = M - 10
		IF M < 1810 || FLAG:A & 1
			PRINTW *ゴンッ*
		ELSE
			FLAG:M -= 4
			M -= 10
			FLAG:M |= 4
		ENDIF
		GOTO MENU
	ELSEIF RESULT == 7
		CALL DUNGEON_MOVE
		A = M - 10
		B = M - 1
		C = M - 11
		IF M < 1810 || M % 10 == 0 || FLAG:A & 1 || FLAG:B & 1 || FLAG:C & 1
			PRINTW *ゴンッ*
		ELSE
			FLAG:M -= 4
			M -= 11
			FLAG:M |= 4
		ENDIF
		GOTO MENU
	ELSEIF RESULT == 6
		CALL DUNGEON_MOVE
		A = M + 1
		IF M % 10 == 9 || FLAG:A & 1
			PRINTW *ゴンッ*
		ELSE
			FLAG:M -= 4
			M += 1
			FLAG:M |= 4
		ENDIF
		GOTO MENU
	ELSEIF RESULT == 5
		CALL DUNGEON_MOVE
		GOTO MENU
	ELSEIF RESULT == 4
		CALL DUNGEON_MOVE
		A = M - 1
		IF M % 10 == 0 || FLAG:A & 1
			PRINTW *ゴンッ*
		ELSE
			FLAG:M -= 4
			M -= 1
			FLAG:M |= 4
		ENDIF
		GOTO MENU
	ELSEIF RESULT == 3
		CALL DUNGEON_MOVE
		A = M + 1
		B = M + 10
		C = M + 11
		IF M >= 1890 || M % 10 == 9 || FLAG:A & 1 || FLAG:B & 1 || FLAG:C & 1
			PRINTW *ゴンッ*
		ELSE
			FLAG:M -= 4
			M += 11
			FLAG:M |= 4
		ENDIF
		GOTO MENU
	ELSEIF RESULT == 2
		CALL DUNGEON_MOVE
		A = M + 10
		IF M >= 1890 || FLAG:A & 1
			PRINTW *ゴンッ*
		ELSE
			FLAG:M -= 4
			M += 10
			FLAG:M |= 4
		ENDIF
		GOTO MENU
	ELSEIF RESULT == 1
		CALL DUNGEON_MOVE
		A = M - 1
		B = M + 10
		C = M + 9
		IF M >= 1890 || M % 10 == 0 || FLAG:A & 1 || FLAG:B & 1 || FLAG:C & 1
			PRINTW *ゴンッ*
		ELSE
			FLAG:M -= 4
			M += 9
			FLAG:M |= 4
		ENDIF
		GOTO MENU
	ELSEIF RESULT == 101
		CALL SHOW_CHARADATA
		RESTART
	ELSEIF RESULT == 200
		CALL SHOW_ITEM
		SIF FLAG:1700
		RESTART
	ELSEIF RESULT == 201
		CALL DUNGEON_ACCESSORY
		RESTART
	ELSEIF RESULT == 202
		CALL DICTIONARY_DUNGEON_MAIN
		RESTART
	ELSEIF RESULT == 300 && (FLAG:4 || (ASSI:4 > -1 && TALENT:(ASSI:4):181))
			CALL ESCAPE_DUNGEON
			RETURN 0
	ELSEIF RESULT == 301 && FLAG:4
		CALL FLAGCHECK
	ELSE
		GOTO INPUT_LOOP1
	ENDIF
ENDIF


;────────────────────────────────────
;戦闘処理
;────────────────────────────────────
@ENEMY_BATTLE
;アイテム初期化
FOR LOCAL, 0, 50
	ITEM:LOCAL = 0
NEXT
;遭遇した夢魔の登録番号を順に格納していくエリアを初期化
;@ENEMY_SPECIESでAに規定されているので、とりあえず放置
FOR LOCAL, 1, 10
	A:LOCAL = -1
NEXT
;TARGET生成
CALL ENEMY_GET
;ENEMY_GET.ERBに飛びます
CALL ENEMY_SPECIES(RESULT)
CFLAG:999 = 1
;ASSI:1、ASSI:2の生成
IF FLAG:1700 != 6
	FOR LOCAL, 1, 3
		IF (CFLAG:(A:LOCAL):0 * RAND:100 / 100) >= 1
			CALL ENEMY_GET_ASSI
			CFLAG:999 = 1
		ELSE
			A:(LOCAL + 1) = -1
			ASSI:LOCAL = -1
			BREAK
		ENDIF
	NEXT
ENDIF
;TARGETとASSIをセット
TARGET = A:1
SIF A:2 > 0
	ASSI = A:2
FOR LOCAL, 1, 10
	SIF A:LOCAL > 0
		PRINTFORMW %CALLNAME:(A:LOCAL)%Lv{CFLAG:(A:LOCAL):0}が現れた！
NEXT
LOCAL:10 = (2 * ABL:MASTER:技巧 + CFLAG:MASTER:0) * BASE:MASTER:0 / ACCESSORY_MAXBASE(0) + 1
LOCAL:11 = 2 * ABL:TARGET:2 + CFLAG:TARGET:0 + 1
IF FLAG:1781 && (RAND:(LOCAL:10) > RAND:(LOCAL:11) || RAND:(1 + FLAG:3132))
	FLAG:1781 = 0
	PRINTL 敵はおどろきとまどっている
	CALL CHOICE("逃げますか？", "はい", "いいえ")
	;ご主人様の逃走能力
	IF ASSI:4 > -1 && TALENT:(ASSI:4):180 && !RAND:4
		PRINTFORMW %CALLNAME:(ASSI:4)%は\@ RESULT == 1 ? 無理矢理 # \@%CALLNAME:MASTER%の手を引き、逃げ出した
		RESULT = 0
	ENDIF
	IF !RESULT
		PRINTW 何とか逃げ切れたようだ
		SIF ASSI:4 > -1
			CALL KOJO_EVENT_SUPPORT(1, 1)
		TARGET = -1
		ASSI   = -1
		ASSI:1 = -1
		ASSI:2 = -1
		LOCAL:2 = CHARANUM-1
		FOR LOCAL:3, 0, CHARANUM
			IF TALENT:(LOCAL:2):170
				IF LOCAL:2 != ASSI:4
					CFLAG:(LOCAL:2):92 = 1
					SIF !CFLAG:(LOCAL:2):95
						CFLAG:(LOCAL:2):93 = 1
				ENDIF
			ELSEIF !CFLAG:(LOCAL:2):95
				DELCHARA LOCAL:2
			ENDIF
			LOCAL:2--
		NEXT
		FLAG:1702 = 0
		;敵の再生産
		CALL DUNGEON_MOVE
		IF FLAG:1707 < 2 && FLAG:1700 != 6
			DO
				LOCAL = 1800 + RAND:100
			LOOP (FLAG:LOCAL & 1) || (FLAG:LOCAL & 2) == 0
			FLAG:LOCAL |= 8
		ENDIF
		RETURN 0
	ENDIF
ENDIF
BEGIN TRAIN


;────────────────────────────────────
;罠
;────────────────────────────────────
@TRAP
SELECTCASE RAND:3
	CASE 0
		PRINTL 催淫ガスだ！
		;ご主人様の罠回避能力
		IF ASSI:4 > -1
			IF TALENT:(ASSI:4):183 && RAND:10 > 7
				CALL KOJO_EVENT_SUPPORT(4)
				PRINTFORMW %CALLNAME:MASTER%はすんでのところで%CALLNAME:(ASSI:4)%に腕を引かれ、罠を回避した
				RETURN 0
			ENDIF
		ENDIF
		PRINTFORMW %NAME:MASTER%は敏感になってしまった…
		CFLAG:MASTER:220 = 10
	CASE 1
		PRINTL 粘性の高い液体が飛び散った！
		;ご主人様の罠回避能力
		IF ASSI:4 > -1
			IF TALENT:(ASSI:4):183 && RAND:10 > 7
				CALL KOJO_EVENT_SUPPORT(4, 1)
				PRINTFORMW %CALLNAME:MASTER%はすんでのところで%CALLNAME:(ASSI:4)%に腕を引かれ、罠を回避した
				RETURN 0
			ENDIF
		ENDIF
		PRINTFORMW %NAME:MASTER%は鈍足になってしまった…
		CFLAG:MASTER:221 = 10
	CASEELSE
		PRINTW ＊ おおっと テレポーター ＊
		;ご主人様の罠回避能力
		IF ASSI:4 > -1
			IF TALENT:(ASSI:4):183 && RAND:10 > 7
				CALL KOJO_EVENT_SUPPORT(4, 2)
				PRINTFORMW %CALLNAME:MASTER%はすんでのところで%CALLNAME:(ASSI:4)%に腕を引かれ、罠を回避した
				RETURN 0
			ENDIF
		ENDIF
		IF !RAND:500
			PRINTW いしのなかにいる
			QUIT
		ENDIF
		CALL DUNGEON_MOVE
		FLAG:M &= ~4
		DO
			LOCAL = 1800 + RAND:100
		LOOP FLAG:LOCAL & 1
		FLAG:LOCAL |= 4
ENDSELECT


;宝箱開錠成功
@ITEM_GET
$ITEM_GET
LOCAL = RAND:9
SELECTCASE LOCAL
	;調教用アイテム
	CASE 0, 1
		DO
			LOCAL = RAND:50
		LOOP LOCAL == 3 || LOCAL == 9 || !ITEMPRICE:LOCAL
		SIF FLAG:(1600 + LOCAL)++
			GOTO ITEM_GET
		PRINTFORMW %ITEMNAME:LOCAL%が入っていた
	;アクセサリ
	CASE 2, 3
		LOCAL:10 = ACCESSORY_COUNT(0)
		FOR LOCAL, 0, 100
			DA:(LOCAL:10):LOCAL = 0
		NEXT
		DA:(LOCAL:10):0 = 1
		;種類
		DA:(LOCAL:10):1 = RAND:5
		;銘
		DA:(LOCAL:10):2 = RAND:(4 - DA:(LOCAL:10):1 / 2)
		;通し番号
		DA:(LOCAL:10):3 = ++FLAG:1780
		;性能
		DA:(LOCAL:10):4 = RAND:(FLAG:1705 / 5 + 1) + 1
		CALL ACCESSORY_PALAMRESET(LOCAL:10)
		;エンチャント
		;エンチャント効果量の計算用。階層の難易度依存で0から3まで変動する
		LOCAL:2 = (100 - 500 / (FLAG:1705 + 5)) / 30
		;エンチャント付与ループ。1～2個のエンチャントを付加
		FOR LOCAL, 11, 13 + 2 * RAND:2, 2
			;エンチャント種別の決定。重複した場合は再抽選
			DO
				LOCAL:1 = 1 + RAND:38
			LOOP DA:(LOCAL:10):11 == LOCAL:1
			DA:(LOCAL:10):LOCAL = LOCAL:1
			;エンチャント効果量の決定
			SELECTCASE DA:(LOCAL:10):LOCAL
				CASE 1 TO 6
					DA:(LOCAL:10):(LOCAL + 1) = RAND:(MIN(1 + LOCAL:2, 2))
				CASE 7, 8
					DA:(LOCAL:10):(LOCAL + 1) = RAND:(1 + LOCAL:2 / 2)
				CASE 9 TO 13, 27 TO 29
					DA:(LOCAL:10):(LOCAL + 1) = RAND:(MAX(LOCAL:2, 1))
				CASE 14, 25, 26, 30, 31, 34
					DA:(LOCAL:10):(LOCAL + 1) = LOCAL:2 < 3 ? 0 # RAND:2
				CASE 15 TO 24
					DA:(LOCAL:10):(LOCAL + 1) = RAND:2
			ENDSELECT
		NEXT
		PRINTFORMW %STR:(905 + 4 * DA:(LOCAL:10):1 + DA:(LOCAL:10):2)%%STR:(900 + DA:(LOCAL:10):1)%が入っていた。
		LOCAL:10++
		WHILE LOCAL:10 >= 6
			PRINTL 持ち物がいっぱいです。
			PRINTL 何を捨てますか？
			FOR LOCAL, 0, 10
				SIF DA:LOCAL:0
					PRINTFORML [{LOCAL}] - %STR:(905 + 4 * DA:LOCAL:1 + DA:LOCAL:2)%%STR:(900 + DA:LOCAL:1)%
			NEXT
			$INPUT_LOOP
			INPUT
			SIF RESULT < 0 || RESULT > 10 || !DA:RESULT:0
				GOTO INPUT_LOOP
			LOCAL:11 = RESULT
			CALL ACCESSORY_DATA(LOCAL:11)
			CALL CHOICE("本当に捨てますか？", "はい", "いいえ")
			SIF RESULT
				CONTINUE
			FOR LOCAL, 0, 100
				DA:(LOCAL:11):LOCAL = 0
			NEXT
			CALL ACCESSORY_TRIM
			LOCAL:10--
		WEND
	;ブックシェルフ用本
	CASE 4, 5
		IF TALENT:MASTER:空気を読む程度の能力 < 2 && !RAND:2
			LOCAL = RAND:2
			PRINTFORMW "読む空気\@ !TALENT:MASTER:空気を読む程度の能力 ? 上巻 # 下巻 \@"が入っていた
			PRINTFORM %CALLNAME:MASTER%は"読む空気\@ !TALENT:MASTER:空気を読む程度の能力 ? 上巻 # 下巻 \@"を読み、
			IF TALENT:MASTER:空気を読む程度の能力
				PRINTW 空気を読む能力を強化した
				TALENT:MASTER:空気を読む程度の能力 = 2
			ELSE
				PRINTW 空気の読み方を身につけた
				TALENT:MASTER:空気を読む程度の能力 = 1
			ENDIF
		ELSE
			LOCAL = RAND:33
			GETBIT FLAG:1770, LOCAL
			SIF RESULT
				GOTO ITEM_GET
			PRINTFORMW "%ITEMNAME:(600 + LOCAL)%"が入っていた
			SETBIT FLAG:1770, LOCAL
		ENDIF
	;コスプレセット
	CASE 6,
		IF !(ITEM:70)
			PRINTFORMW コスプレセットが入っていた
			ITEM:70 = 1
		ELSE
			GOTO ITEM_GET
		ENDIF
	;現金/空
	CASEELSE
		LOCAL = (RAND:(SQRT(FLAG:1705)) + 1) * RAND:300 + (FLAG:1705 / 5) * 150
		PRINTFORMW \@ LOCAL ? %TOSTR(LOCAL, "\#,\#\#\#")%$入っていた。 # 何も入っていなかった… \@
		MONEY += LOCAL
ENDSELECT


;開錠技能レベルアップ判定
@SKILL_UP
SIF EXP:MASTER:開錠経験 <= POWER(1 + ABL:MASTER:開錠技能, 2) * 10
	RETURN 0
ABL:MASTER:開錠技能++
PRINTW 開錠技能レベルアップ


;鍛冶屋
@BUTTERFLY
CALL CHOICE("アクセサリの合成を行いますか？", "はい", "いいえ")
SIF RESULT
	RETURN 0
$BUTTERFLY_START
;ベースアクセサリ選択
WHILE 1
	PRINTL ベースとなるアクセサリを選んでください
	CALL ACCESSORY_LIST_EQUIP
	$INPUT_LOOP1
	INPUT
	LOCAL:10 = RESULT
	SELECTCASE LOCAL:10
		CASE 0
			SIF !DC:0:0
				GOTO LOOP1_ERROR
			CALL ACCESSORY_DATA_EQUIP
		CASE 1 TO 99
			SIF !DA:(LOCAL:10 - 1):0
				GOTO LOOP1_ERROR
			CALL ACCESSORY_DATA(LOCAL:10 - 1)
		CASE 100
			RESTART
		CASEELSE
			$LOOP1_ERROR
			CLEARLINE 1
			GOTO INPUT_LOOP1
	ENDSELECT
	CALL CHOICE("このアクセサリをベースにしますか？", "はい", "いいえ")
	SIF !RESULT
		BREAK
WEND
;サブアクセサリ選択
WHILE 1
	PRINTL 合成するアクセサリを選んでください
	SIF DC:0:0 && LOCAL:10
		PRINTFORML [  0] - %STR:(905 + 4 * DC:0:1 + DC:0:2)%%STR:(900 + DC:0:1)% 装備中
	FOR LOCAL, 0, 5
		SIF !DA:LOCAL:0 || LOCAL == LOCAL:10 - 1
			CONTINUE
		PRINTFORML [{LOCAL + 1, 3}] - %STR:(905 + 4 * DA:LOCAL:1 + DA:LOCAL:2)%%STR:(900 + DA:LOCAL:1)%
	NEXT
	PRINTL [100] - 戻る
	$INPUT_LOOP2
	INPUT
	LOCAL:11 = RESULT
	SELECTCASE LOCAL:11
		CASE LOCAL:10
			GOTO LOOP2_ERROR
		CASE 0
			SIF !DC:0:0
				GOTO LOOP2_ERROR
			CALL ACCESSORY_DATA_EQUIP
		CASE 1 TO 99
			SIF !DA:(LOCAL:11 - 1):0
				GOTO LOOP1_ERROR
			CALL ACCESSORY_DATA(LOCAL:11 - 1)
		CASE 100
			GOTO BUTTERFLY_START
		CASEELSE
			$LOOP2_ERROR
			CLEARLINE 1
			GOTO INPUT_LOOP2
	ENDSELECT
	CALL CHOICE("このアクセサリを合成しますか？", "はい", "いいえ")
	SIF !RESULT
		BREAK
WEND
;合成
;現在装備中のアクセサリをベースにした合成
IF !LOCAL:10
	LOCAL:11--
	;エンチャントの合成処理
	FOR LOCAL:0, 11, 23, 2
		;ベースのエンチャントに空きが無い→ループを抜ける
		;サブのエンチャントを終端まで探索→ループを抜ける
		SIF DC:0:21 || !DA:(LOCAL:11):(LOCAL:0)
			BREAK
		;ベースのエンチャントの空きを探索
		FOR LOCAL:1, 11, 23, 2
			SIF !DC:0:(LOCAL:1)
				BREAK
		NEXT
		LOCAL:1 -= 2
		;ベースにエンチャントを書き込む
		DC:0:(LOCAL:1) = DA:(LOCAL:11):(LOCAL:0)
		DC:0:(LOCAL:1 + 1) = DA:(LOCAL:11):(LOCAL:0 + 1)
	NEXT
	;アクセサリレベルの合成処理
	DC:0:4 += DA:(LOCAL:11):4 - 1
	;合成したアクセサリを消去
	FOR LOCAL, 0, 100
		DA:(LOCAL:11):LOCAL = 0
	NEXT
	;パラメータの更新
	CALL ACCESSORY_PALAMRESET_EQUIP
	;結果表示
	CALL ACCESSORY_DATA_EQUIP
;所持アクセサリをベースにした合成
ELSE
	LOCAL:10--
	;現在装備中のアクセサリをサブにした合成
	IF !LOCAL:11
		FOR LOCAL:0, 11, 23, 2
			SIF DA:(LOCAL:10):21 || !DC:0:(LOCAL:0)
				BREAK
			FOR LOCAL:1, 11, 23, 2
				SIF !DA:(LOCAL:10):(LOCAL:1)
					BREAK
			NEXT
			LOCAL:1 -= 2
			DA:(LOCAL:10):(LOCAL:1) = DC:0:(LOCAL:0)
			DA:(LOCAL:10):(LOCAL:1 + 1) = DC:0:(LOCAL:0 + 1)
		NEXT
		DA:(LOCAL:10):4 += DC:0:4 - 1
		FOR LOCAL, 0, 100
			DC:0:LOCAL = 0
		NEXT
	;所持アクセサリ同士の合成
	ELSE
		LOCAL:11--
		FOR LOCAL:0, 11, 23, 2
			SIF DA:(LOCAL:10):21 || !DA:(LOCAL:11):(LOCAL:0)
				BREAK
			FOR LOCAL:1, 11, 23, 2
				SIF !DA:(LOCAL:10):(LOCAL:1)
					BREAK
			NEXT
			LOCAL:1 -= 2
			DA:(LOCAL:10):(LOCAL:1) = DA:(LOCAL:11):(LOCAL:0)
			DA:(LOCAL:10):(LOCAL:1 + 1) = DA:(LOCAL:11):(LOCAL:0 + 1)
		NEXT
		DA:(LOCAL:10):4 += DA:(LOCAL:11):4 - 1
		FOR LOCAL, 0, 100
			DA:(LOCAL:11):LOCAL = 0
		NEXT
	ENDIF
	CALL ACCESSORY_PALAMRESET(LOCAL:10)
	CALL ACCESSORY_DATA(LOCAL:10)
ENDIF
;FLAG再設定
CALL ACCESSORY_SET
;アクセサリ番号をずらす
CALL ACCESSORY_TRIM

;------------------------------------------------
;アイテム発見
;------------------------------------------------
@ITEM_RANDUM_GET
LOCAL:1 = (100 - 500/(FLAG:1705 + 5))
SIF LOCAL:1 < 1
	LOCAL:1 = 1
;1/10で白旗
IF RAND:10 == 0
	Z = 506
;深層で上位互換の強化鎮静剤、ハンバーガー、パスタがでます
ELSEIF RAND:(LOCAL:1) > 60
	IF RAND:7 == 0
		Z = 513
	ELSEIF RAND:6 == 0
		Z = 504
	ELSEIF RAND:5 == 0
		Z = 506
	ELSEIF RAND:4 == 0
		Z = 505
	ELSEIF RAND:3 == 0
		Z = 501
	ELSEIF RAND:2 == 0
		Z = 511
	ELSE
		Z = 512
	ENDIF
;ほかはランダム
ELSE
	IF RAND:11 == 0
		Z = 516
	ELSEIF RAND:10 == 0
		Z = 514
	ELSEIF RAND:9 == 0
		Z = 515
	ELSEIF RAND:8 == 0
		Z = 513
	ELSEIF RAND:7 == 0
		Z = 505
	ELSEIF RAND:6 == 0
		Z = 500
	ELSEIF RAND:5 == 0
		Z = 504
	ELSEIF RAND:4 == 0
		Z = 507
	ELSEIF RAND:3 == 0
		Z = 508
	ELSEIF RAND:2 == 0
		Z = 509
	ELSE
		Z = 510
	ENDIF
ENDIF

