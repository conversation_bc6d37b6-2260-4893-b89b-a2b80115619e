﻿;ショップメニュー「饰品」のターミナル
@ACCESSORY
SIF !STRLENS(LOCALS)
	SPLIT "0/SHOW/EQUIP/STORAGE/SALE", "/", LOCALS
DRAWLINE
PRINTL [  1] - 饰品情报
PRINTL [  2] - 饰品装备
PRINTL [  3] - 仓库
PRINTL [  4] - 贩卖饰品
PRINTL [100] - 返回
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 1 TO 4
		CALLFORM ACCESSORY_%LOCALS:RESULT%
	CASE 100
		RETURN 0
	CASEELSE
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
RESTART


;[  1] - アクセサリ情報
@ACCESSORY_SHOW
DRAWLINE
IF DC:0:0
	PRINTL 现在装备中的饰品
	CALL ACCESSORY_DATA_EQUIP
ENDIF
;アクセサリ一覧表示
CALL ACCESSORY_LIST_STORAGE
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0 TO 99
		SIF !DA:RESULT:0
			GOTO ERROR
		CALL ACCESSORY_DATA(RESULT)
	CASE 100 TO 199
		SIF !DB:(RESULT - 100):0
			GOTO ERROR
		CALL ACCESSORY_DATA_STORAGE(RESULT - 100)
	CASE 200
		RETURN 0
	CASEELSE
		$ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
WAIT
RESTART


;[  2] - アクセサリ装備
@ACCESSORY_EQUIP
;アクセサリ所持数(所持)
LOCAL:10 = ACCESSORY_COUNT(0)
DRAWLINE
IF DC:0:0
	PRINTL 现在装备中的饰品
	CALL ACCESSORY_DATA_EQUIP
	PRINTL 
ENDIF
PRINTL 请选择要装备的饰品
PRINTL 
SIF LOCAL:10 >= 5
	PRINTL 持有物太多了
FOR LOCAL, 0, 5
	SIF DA:LOCAL:0
		PRINTFORML [{LOCAL, 3}] - %STR:(905 + 4 * DA:LOCAL:1 + DA:LOCAL:2)%%STR:(900 + DA:LOCAL:1)%
NEXT
SIF LOCAL:10 < 5 && DC:0:0
	PRINTL [100] - 取下饰品
PRINTL [101] - 返回
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0 TO 99
		SIF !DA:RESULT:0
			GOTO ERROR
		FOR LOCAL, 0, 100
			SWAP DA:RESULT:LOCAL, DC:0:LOCAL
		NEXT
	CASE 100
		SIF LOCAL:10 >= 5
			GOTO ERROR
		FOR LOCAL, 0, 100
			SWAP DA:(LOCAL:10):LOCAL, DC:0:LOCAL
		NEXT
	CASE 101
		RETURN 0
	CASEELSE
		$ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
;FLAG再設定
CALL ACCESSORY_SET
;移動させた所持アクセサリ以降の番号をひとつずらす
CALL ACCESSORY_TRIM
RESTART


;[  3] - 倉庫
@ACCESSORY_STORAGE
;アクセサリ所持数(所持/倉庫)
LOCAL:10 = ACCESSORY_COUNT(0)
LOCAL:11 = ACCESSORY_COUNT(1)
CALL ACCESSORY_LIST_STORAGE
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0 TO 99
		SIF DA:RESULT:0 !& LOCAL:11 < 20
			GOTO ERROR
		;倉庫の末端に所持アクセサリの情報を書き込み
		;移動させた所持アクセサリの情報の消去
		FOR LOCAL, 0, 100
			DB:(LOCAL:11):LOCAL = DA:RESULT:LOCAL
			DA:RESULT:LOCAL = 0
		NEXT
	CASE 100 TO 199
		SIF DB:(RESULT - 100):0 !& LOCAL:10 < 5
			GOTO ERROR
		FOR LOCAL, 0, 100
			DA:(LOCAL:10):LOCAL = DB:(RESULT - 100):LOCAL
			DB:(RESULT - 100):LOCAL = 0
		NEXT
	CASE 200
		RETURN 0
	CASEELSE
		$ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
;移動させた所持アクセサリ以降の番号をひとつずらす
CALL ACCESSORY_TRIM
RESTART


;[  4] - アクセサリ売却
@ACCESSORY_SALE
LOCAL:10 = ACCESSORY_COUNT(0)
LOCAL:11 = ACCESSORY_COUNT(1)
PRINTFORML 所持金${MONEY}
PRINTL $500 / 1个
CALL ACCESSORY_LIST_STORAGE
$INPUT_LOOP
INPUT
SELECTCASE RESULT
	CASE 0 TO 99
		SIF !DA:RESULT:0
			GOTO ERROR
		LOCAL:0 = RESULT
		CALL ACCESSORY_DATA(LOCAL:0)
		CALL CHOICE("要卖掉这个饰品吗？", "是", "否")
		SIF RESULT
			RESTART
		;売却した所持アクセサリの情報の消去
		FOR LOCAL:1, 0, 100
			DA:(LOCAL:0):(LOCAL:1) = 0
		NEXT
	CASE 100 TO 199
		SIF !DB:(RESULT - 100):0
			GOTO ERROR
		LOCAL:0 = RESULT - 100
		CALL ACCESSORY_DATA_STORAGE(LOCAL:0)
		CALL CHOICE("要卖掉这个饰品吗？", "是", "否")
		SIF RESULT
			RESTART
		FOR LOCAL:1, 0, 100
			DB:(LOCAL:0):(LOCAL:1) = 0
		NEXT
	CASE 200
		RETURN 0
	CASEELSE
		$ERROR
		CLEARLINE 1
		GOTO INPUT_LOOP
ENDSELECT
MONEY += 500
;売却した所持アクセサリ以降の番号をひとつずらす
CALL ACCESSORY_TRIM
RESTART
